import {
  ForbiddenException,
  HttpStatus,
  Injectable,
  Logger,
  Req,
  UnprocessableEntityException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  LocationType,
  Prisma,
  Role,
  WorklogTimerStatus,
  Department,
  Resource,
  UserPermissions,
} from '@prisma-clients/timesheet-backend';
import { PrismaService } from 'src/prisma.service';
import {
  ListAllResourcesReqDto,
  ListResourceResponsePayload,
  ListResourcesResponseDataDto,
} from './dto/resource.dto';
import { UserDetails } from 'src/auth/interface/auth-v2.interface';
import {
  IGetAllResourceInfoForReport,
  IGetAllResourceReportResponse,
  IGetAllWorklogsInfoForReport,
  IResourceInfo,
  IResourceReportInfo,
  ITaskDetails,
  ResourceReport,
  ResourceReportData,
  UniqueResourceId,
  ReactivateResourceResponse,
} from 'src/resource/interface/resource.interface';
import { EmployeeReportDto } from 'src/resource/dto/create-resource.dto';
import { SortByTotalMinutesOrDays } from 'src/common/enums/sortBy.enums';
import { SortOrder } from 'src/common/enums/sortOrder.enum';
import { BillableStatus } from 'src/common/enums/billable_status.enum';
import { startOfDay, endOfDay, startOfMonth, endOfMonth } from 'date-fns';
import { Attendance } from 'src/common/enums/attendance.enum';
import { NO_RESOURCE_ACCESS, RESOURCE_NOT_FOUND } from 'src/constants';
import { groupLeaveByResourceId, getLeaveStatusForMonth } from 'src/helpers';
import { HolidayService } from 'src/holiday/holiday.service';
import { createResourceReportV2 } from 'src/xlsx-file-generators-v2/resource-report-v2';
import { S3V2Service } from 'src/s3-v2/s3-v2.service';
import * as fs from 'fs';
import * as xlsx from 'xlsx';
import * as mime from 'mime';
import { DepartmentService } from 'src/department/department.service';
import { CreateManyDepartmentResponse } from 'src/department/interface/department.interface';
import { AMResourceService } from 'src/resource/am-resource.service';
import { AMResourceDto } from 'src/resource/dto/asset-management-resource.dto';
import { ValidLocation } from 'src/common/enums/location.enum';

@Injectable()
export class ResourceV2Service {
  private logger = new Logger('ResourceV2Service');

  constructor(
    private readonly prisma: PrismaService,
    private readonly holidayService: HolidayService,
    private readonly s3V2: S3V2Service,
    private readonly departmentService: DepartmentService,
    private readonly amResourceService: AMResourceService,
  ) {}

  /**
   * Retrieves a list of resources based on the provided filtering criteria.
   *
   * - Accepts a `ListAllResourcesReqDto` object containing optional filters.
   * - Constructs a `Prisma.ResourceWhereInput` object to filter resources based on:
   *   - `name`, `department`, `designation`, `location`, `role`, `id` and
   *   - `deleted`: Ensures only non-deleted resources are retrieved.
   *
   * - Fetchs resources from the database:
   *   - Selects relevant fields such as `id`, `name`, `email`, `role`, `phoneNumber`, and associated `department` details.
   *   - Orders the results alphabetically by `name`.
   */
  async listAllResourceV2(
    dto: ListAllResourcesReqDto,
  ): Promise<ListResourceResponsePayload> {
    try {
      const where: Prisma.ResourceWhereInput = {
        name: dto?.search
          ? { contains: dto?.search, mode: 'insensitive' }
          : undefined,
        department: {
          departmentName: dto?.department
            ? { in: dto?.department, mode: 'insensitive' }
            : undefined,
        },
        designation: dto?.designation
          ? { in: dto?.designation, mode: 'insensitive' }
          : undefined,
        location: dto?.location ? { in: dto?.location } : undefined,
        role: dto?.role ? { in: dto?.role } : undefined,
        id: dto?.resourceId ? dto?.resourceId : undefined,
        deleted: false,
      };

      const resource: ListResourcesResponseDataDto[] =
        await this.prisma.resource.findMany({
          where,
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            phoneNumber: true,
            department: {
              select: {
                id: true,
                departmentName: true,
              },
            },
            designation: true,
            managerId: true,
            location: true,
            kekaId: true,
            profilePicUrl: true,
            currency: true,
            expectedHours: true,
            expectedHoursFrequency: true,
            resourceContractor: true,
          },
          orderBy: {
            name: 'asc',
          },
        });

      this.logger.log('V2: Successfully retreived resources list.');

      return {
        statusCode: HttpStatus.OK,
        data: resource,
        count: resource.length,
        message: 'Successfully retrieved resource list',
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Finds a user by their email address.
   *
   * @param email - The email address of the user to find.
   * @returns A promise resolving to the user payload or null if not found.
   */
  async fetchUserByEmail(email: string): Promise<UserDetails> {
    try {
      return await this.prisma.resource.findFirst({ where: { email } });
    } catch (error) {
      throw new Error('Failed to fetch user by given email');
    }
  }

  /**
   * Uploads a new profile picture for the user.
   *
   * @param picture - The URL or base64-encoded string of the new profile picture.
   * @param userId - The unique identifier of the user.
   * @returns A promise that resolves when the profile picture is updated.
   */
  async uploadProfilePicture(picture: string, userId: string): Promise<void> {
    try {
      await this.prisma.resource.update({
        where: {
          id: userId,
        },
        data: {
          profilePicUrl: picture,
        },
      });
    } catch (error) {
      throw new Error('Failed to upload profile picture');
    }
  }

  async getResourceReportV2(
    dto: EmployeeReportDto,
    @Req() req: Request,
  ): Promise<ResourceReport> {
    try {
      const resourceReport = await this.generateEmployeeReportV2(dto, req);

      const resourceList = resourceReport.data.map((item) => {
        // Keep taskDetails for the new report structure
        return item;
      });

      // Note: We're not aggregating worklogs by date anymore since we want to show project-contract specific data
      // The aggregation will be handled in the report generator based on taskDetails

      const resourceReportResponse: ResourceReportData = {
        numberOfResource: resourceReport.count,
        resourceList: resourceList,
        mode: dto.mode,
      };

      const downloadUrl = await this.generateResourceReportUrlV2(
        resourceReportResponse,
      );

      this.logger.log('Successfully retrieved resource report');

      return {
        statusCode: HttpStatus.OK,
        data: {
          downloadUrl: downloadUrl,
        },
        message: 'Successfully retrieved resource report',
      };
    } catch (error) {
      this.logger.log(error);
      throw error;
    }
  }
  async generateEmployeeReportV2(
    dto: EmployeeReportDto,
    @Req() req: Request,
  ): Promise<IGetAllResourceReportResponse> {
    try {
      let {
        month,
        year,
        resourceIds,
        departmentIds,
        billableStatus,
        sortBy,
        sortOrder,
        resourceStatus,
      } = dto;
      const today: Date = new Date();
      const take: number = dto?.pageSize || 50;
      const skip: number = dto?.page ? (dto.page - 1) * take : 0;
      // Calculate the first and last day of the selected month
      const firstDayOfMonth: Date = new Date(Date.UTC(year, month - 1, 1));
      const lastDayOfMonth: Date = new Date(Date.UTC(year, month, 0));

      // Get user info

      /**
       * If user's role is admin or finance then return all resource
       * If user's role is manager then that user must get only those resources who are reporting to him  as
       * Project Manager or Contract Manager
       */
      const user = await this.prisma.resource.findUnique({
        where: { id: req['user']['payload']['id'] },
      });

      let resources: IGetAllResourceInfoForReport[] = [];
      if (user.role === Role.admin || user.role === Role.finance) {
        resources = await this.prisma.resource.findMany({
          where: {
            id: resourceIds?.length > 0 ? { in: resourceIds } : undefined,
            departmentId:
              departmentIds?.length > 0 ? { in: departmentIds } : undefined,
          },
          include: {
            Leaves: true,
            employeeWorkLogs: {
              where: {
                deleted: false,
                workDate: {
                  gte: firstDayOfMonth,
                  lte: lastDayOfMonth,
                },
                worklogTimerStatus: WorklogTimerStatus.stopped,
              },
            },
            department: true,
          },
          orderBy: {
            name: 'asc',
          },
        });
      }

      if (user.role === Role.manager) {
        const managerContracts = await this.prisma.contractResource.findMany({
          where: {
            resourceId: user.id,
            typeOfResource: { in: ['contractManager', 'projectManager'] },
          },
          select: {
            projectId: true,
          },
          distinct: ['resourceId', 'projectId'],
        });

        const projectIds = Array.from(
          new Set(
            managerContracts?.map(
              (contractResource) => contractResource?.projectId,
            ),
          ),
        )?.filter(Boolean);

        const resourceContracts = await this.prisma.contractResource.findMany({
          where: {
            projectId: { in: projectIds },
            typeOfResource: 'resource',
          },
          select: {
            resourceId: true,
          },
          distinct: ['resourceId', 'projectId'],
        });

        const filteredResourceIds = Array.from(
          new Set(resourceContracts?.map((res) => res?.resourceId)),
        )?.filter(Boolean);

        if (!resourceIds) {
          resourceIds = filteredResourceIds;
        }

        if (resourceIds) {
          const idsArray = Array.isArray(resourceIds)
            ? resourceIds
            : [resourceIds];
          const invalidResourceIds = idsArray?.filter(
            (resourceId) => !filteredResourceIds?.includes(resourceId),
          );
          if (invalidResourceIds.length > 0) {
            throw new ForbiddenException(NO_RESOURCE_ACCESS);
          }
        }

        resources = await this.prisma.resource.findMany({
          where: {
            id: { in: resourceIds },
            departmentId:
              departmentIds?.length > 0 ? { in: departmentIds } : undefined,
          },
          include: {
            employeeWorkLogs: {
              where: {
                deleted: false,
                workDate: {
                  gte: firstDayOfMonth,
                  lte: lastDayOfMonth,
                },
                worklogTimerStatus: WorklogTimerStatus.stopped,
              },
            },
            department: true,
          },
          orderBy: {
            name: 'asc',
          },
        });
      }

      // Retrieve all worklogs information for a given list of resources.
      const getAllWorklogsInfo: IGetAllWorklogsInfoForReport[] =
        await this.prisma.workLog.findMany({
          where: {
            employeeId: { in: resources?.map((res) => res?.id) },
            startDate: {
              gte: startOfDay(firstDayOfMonth).toISOString(),
              lte: endOfDay(lastDayOfMonth).toISOString(),
            },
            worklogTimerStatus: WorklogTimerStatus.stopped,
          },
          include: {
            task: {
              include: {
                project: true,
                contractResource: true,
                contract: {
                  include: {
                    resourceContractBillingRate: {
                      where: {
                        startDate: {
                          lte: new Date(year, month, 0, 23, 59, 59, 999), // End of the month
                        },
                        endDate: {
                          gte: new Date(year, month - 1, 1, 0, 0, 0), // Start of the month
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        });

      const isWeekOff = (date: Date): boolean => {
        // Assuming your week-off days are Saturday and Sunday
        const dayOfWeek = date.getDay();
        return dayOfWeek === 0 /* Sunday */ || dayOfWeek === 6 /* Saturday */;
      };

      const holidays = await this.holidayService.getHolidaysForResources(
        firstDayOfMonth,
        lastDayOfMonth,
        resources?.map((res) => res?.id),
      );

      const leaves = await this.prisma.leaves.findMany({
        where: {
          resourceId: { in: resourceIds },
          fromDate: { gte: startOfMonth(new Date(year, month - 1, 15)) },
          toDate: { lte: endOfMonth(new Date(year, month - 1, 15)) },
        },
      });

      // Group leave data by resourceId
      const groupedLeaveData = groupLeaveByResourceId(leaves);

      const report = resources?.map((res) => {
        // Create a map to group worklogs by date
        const worklogForDate: Map<string, IGetAllWorklogsInfoForReport[]> =
          new Map();
        // Filter worklogs for the current resource
        const logsForResource = getAllWorklogsInfo?.filter(
          (log) => log.employeeId === res?.id,
        );

        const resourceReport: IResourceInfo = {
          resourceId: res?.id,
          resourceName: res?.name,
          kekaId: res?.kekaId,
          department: res.department ? res.department.departmentName : null,
          totalMinutes: 0,
          totalDays: 0,
          billableStatus: BillableStatus.BOTH,
          isDeleted: res?.deleted,
          resourcesWorklog: [],
          taskDetails: [],
          leaveDetails: getLeaveStatusForMonth(
            groupedLeaveData[res?.id],
            res?.id,
            new Date(firstDayOfMonth),
          ),
        };

        // Grouping by projectId and taskId, resourceId
        const groupedWorklogs: { [key: string]: ITaskDetails } =
          getAllWorklogsInfo
            .filter((worklog) => worklog.employeeId === res.id)
            .reduce((result, worklog) => {
              const key = `${worklog.employeeId}-${worklog.projectId}-${worklog.taskId}-${worklog.workDate}`;

              if (!result[key]) {
                result[key] = {
                  projectId: worklog.projectId,
                  projectName: worklog.task?.project?.projectName || null,
                  taskId: worklog.taskId,
                  taskName: worklog.task?.taskName || null,
                  minutes: worklog.minutes,
                  workDate: worklog.startDate,
                  contractId: worklog.task?.contract?.id || null,
                  contractName:
                    worklog.task?.contract?.customContractId || null,
                  location: worklog.location || null,
                };
              } else {
                // If the key already exists, aggregate the minutes
                result[key].minutes += worklog.minutes;
              }

              return result;
            }, {});

        const taskDetails: ITaskDetails[] = Object.values(groupedWorklogs);

        // Sorting the taskDetails array by workDate in ascending order
        taskDetails.sort(
          (a, b) =>
            new Date(a.workDate).getTime() - new Date(b.workDate).getTime(),
        );

        resourceReport.taskDetails = taskDetails;

        // Iterate through the days of the month
        let currentDate: Date = new Date(firstDayOfMonth);
        // Iterate through the days of the month and initialize the map
        while (currentDate <= lastDayOfMonth) {
          // Use the current date as the key and an empty array as the value in the map
          worklogForDate?.set(currentDate.toISOString(), []);
          // Move to the next day
          currentDate?.setDate(currentDate.getDate() + 1);
        }
        // Group worklogs by date
        const daysInMonth = new Date(year, month, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
          const workDate = new Date(Date.UTC(year, month - 1, day, 0, 0, 0));

          const workStartDate = startOfDay(new Date(workDate)).toISOString();
          const workEndDate = endOfDay(new Date(workDate)).toISOString();

          const worklogForDay = logsForResource?.filter(
            (log) =>
              log?.startDate?.toISOString() >= workStartDate &&
              log?.startDate?.toISOString() <= workEndDate,
          );

          if (
            worklogForDate?.has(workDate.toISOString()) &&
            !worklogForDate?.get(workDate.toISOString()).length
          ) {
            worklogForDate?.set(workDate.toISOString(), worklogForDay);
          }
        }

        for (
          currentDate = new Date(firstDayOfMonth);
          currentDate <= lastDayOfMonth;
          currentDate?.setDate(currentDate?.getDate() + 1)
        ) {
          const workDate: string = currentDate.toISOString();
          const worklogs: IGetAllWorklogsInfoForReport[] =
            worklogForDate?.get(workDate) || [];

          if (worklogs?.length === 0) {
            // Check if the day is a week-off or company-off
            const isWeekOffDay: boolean = isWeekOff(currentDate);
            const isCompanyOffDay: boolean = holidays[res.id]?.some(
              (holiday) =>
                holiday.date.toISOString() === currentDate.toISOString(),
            );
            const defaultEntry: IResourceReportInfo = {
              workDate,
              minutes: 0,
              attendance: Attendance.NONE,
              isPresent: currentDate > today ? false : true,
              isOnLeave: false,
              isWeekOff: isWeekOffDay,
              isCompanyOff: isCompanyOffDay,
              billingStatus: BillableStatus.NOT_BILLABLE,
              isBillable: false,
            };

            resourceReport?.resourcesWorklog?.push(defaultEntry);
          } else {
            worklogs.forEach((worklog) => {
              let billingStatus: string = BillableStatus.NOT_BILLABLE;
              let isBillable: boolean = false;

              const hasBillableWorkLogs: boolean =
                worklog?.task?.contract?.resourceContractBillingRate?.some(
                  (rate) =>
                    rate?.resourceId === res?.id &&
                    rate?.contractId === worklog?.task?.contract?.id,
                );

              if (hasBillableWorkLogs) {
                billingStatus = BillableStatus.BILLABLE;
                isBillable = true;
              }

              const isOnLeave: boolean = worklog.isOnLeave;
              const isWeekOff: boolean = worklog.isWeekOff;
              const isCompanyOff: boolean = worklog.isCompanyOff;
              const location: LocationType = worklog.location;

              const reportEntry: IResourceReportInfo = {
                workDate,
                minutes: currentDate > today ? 0 : worklog.minutes,
                attendance:
                  currentDate > today
                    ? Attendance.NONE
                    : isOnLeave
                    ? Attendance.LEAVE
                    : Attendance.PRESENT,
                isPresent: currentDate > today ? false : true,
                isOnLeave,
                isWeekOff,
                isCompanyOff,
                billingStatus,
                isBillable,
                location,
              };

              resourceReport?.resourcesWorklog?.push(reportEntry);

              if (reportEntry?.minutes > 0) {
                resourceReport.totalMinutes += reportEntry?.minutes;
              }
            });
          }
        }
        // Calculate totalDays based on unique dates
        const uniqueDates: Set<string> = new Set(
          resourceReport?.resourcesWorklog
            .filter((entry) => entry?.minutes > 0)
            .map((entry) => entry?.workDate),
        );

        resourceReport.totalDays = uniqueDates.size;
        // Calculate BillableStatus based on all worklogs in the month
        const allBillingStatuses = resourceReport?.resourcesWorklog?.map(
          (entry) => entry?.billingStatus,
        );
        if (
          allBillingStatuses?.every(
            (status) => status === BillableStatus.BILLABLE,
          )
        ) {
          resourceReport.billableStatus = BillableStatus.BILLABLE;
        } else if (
          allBillingStatuses?.every(
            (status) => status === BillableStatus.NOT_BILLABLE,
          )
        ) {
          resourceReport.billableStatus = BillableStatus.NOT_BILLABLE;
        } else if (
          allBillingStatuses?.every((status) => status === BillableStatus.BOTH)
        ) {
          resourceReport.billableStatus = BillableStatus.BOTH;
        }
        return resourceReport;
      });
      // Filter resources based on the billableStatus query parameter
      let filteredReport = report;

      if (billableStatus === BillableStatus.BOTH) {
        filteredReport = report?.filter(
          (res) => res.billableStatus === BillableStatus.BOTH,
        );
      } else if (billableStatus === BillableStatus.BILLABLE) {
        filteredReport = report?.filter(
          (res) => res.billableStatus === BillableStatus.BILLABLE,
        );
      } else if (billableStatus === BillableStatus.NOT_BILLABLE) {
        filteredReport = report?.filter(
          (res) => res.billableStatus === BillableStatus.NOT_BILLABLE,
        );
      }

      //Filter resources based on there active and deactivate status query parameter
      if (resourceStatus === 'all') {
        filteredReport;
      } else if (resourceStatus === 'active') {
        filteredReport = filteredReport?.filter(
          (res) => res.isDeleted === false,
        );
      } else if (resourceStatus === 'inactive') {
        filteredReport = filteredReport?.filter(
          (res) => res.isDeleted === true,
        );
      }

      // Apply sorting to the filtered report
      if (sortBy === SortByTotalMinutesOrDays.TOTAL_MINUTES) {
        filteredReport?.sort((a, b) => {
          if (sortOrder === SortOrder.ASC) {
            return a?.totalMinutes - b?.totalMinutes;
          } else {
            return b?.totalMinutes - a?.totalMinutes;
          }
        });
      } else if (sortBy === SortByTotalMinutesOrDays.TOTAL_DAYS) {
        filteredReport?.sort((a, b) => {
          if (sortOrder === SortOrder.ASC) {
            return a?.totalDays - b?.totalDays;
          } else {
            return b?.totalDays - a?.totalDays;
          }
        });
      }
      const totalCount = filteredReport?.length;
      // Apply pagination to the filtered report
      const paginatedResponse = filteredReport?.slice(skip, skip + take);
      return {
        statusCode: HttpStatus.OK,
        data: dto.mode ? filteredReport : paginatedResponse,
        count: totalCount,
        message: 'Successfully retrieved resource report',
      };
    } catch (error) {
      throw error;
    }
  }
  async generateResourceReportUrlV2(inputs: ResourceReportData) {
    try {
      const dataInputs = {
        data: {
          numberOfResource: inputs.numberOfResource,
          mode: inputs.mode,
          resourceList: inputs.resourceList,
        },
      };
      // Extract year and month from the first resource's first worklog
      const firstWorklog = inputs.resourceList[0]?.resourcesWorklog[0];
      let year = new Date().getFullYear();
      let monthNum = new Date().getMonth();
      let month = '';
      if (firstWorklog) {
        const date = new Date(firstWorklog.workDate);
        year = date.getFullYear();
        monthNum = date.getMonth();
        month = date.toLocaleString('default', { month: 'long' });
      }
      const downloadUrl = await this.s3V2.generateAndUploadReport(
        dataInputs,
        createResourceReportV2,
        'Monthly_Resource_Report',
        year,
        month,
      );
      this.logger.log('Client report generated and uploaded successfully.');
      return downloadUrl;
    } catch (error) {
      this.logger.error('Error generating resource report:', error);
      throw error;
    }
  }

  // Importing keka data from xlsx file and load into database
  async importResourceFromXLSX(file: Express.Multer.File) {
    try {
      // Check the uploaded file is xlsx file or not
      const fileType: string = mime.getType(file.originalname);
      if (
        !file ||
        fileType !==
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ) {
        fs.unlinkSync(file.path);
        fs.rmdirSync(file.destination);

        fs.mkdirSync(file.destination);
        throw new UnprocessableEntityException(
          'Validation failed (expected file type is xlsx)',
        );
      }

      // Convert xlsx data file into json data.
      const workbook: xlsx.WorkBook = xlsx.readFile(file.path);
      const worksheet: xlsx.WorkSheet = workbook.Sheets[workbook.SheetNames[0]];
      const rows: any[][] = xlsx.utils.sheet_to_json(worksheet, {
        header: 1,
      });

      // Clean up rows (remove empty + footer)
      const cleanedRows = rows.filter(
        (row) =>
          row.length > 0 &&
          !(
            typeof row[0] === 'string' &&
            (row[0].toLowerCase().startsWith('footer') ||
              row[0].toLowerCase().startsWith('end of file'))
          ),
      );

      // Remove header row
      cleanedRows.shift();

      // Once we read the file and extract data, unlink the file and delete the folder.
      fs.unlinkSync(file.path);
      fs.rmdirSync(file.destination);

      fs.mkdirSync(file.destination);

      // Extract departments from cleaned rows (column index 5 for department)
      const manyDepartmentData: string[] = cleanedRows.map((row) => row[5]);
      const departments: CreateManyDepartmentResponse =
        await this.departmentService.createManyDepartment(manyDepartmentData);

      // Map xlsx data with resource model based on your data structure:
      // [kekaId, name, email, phone, location, department, designation, manager]
      this.logger.log('Mapping resources from cleaned rows...');
      const resources = cleanedRows.map((row, index) => {
        const department: Department = departments.data.find(
          (dept) => dept.departmentName === row[5],
        );

        if (!department) {
          throw new Error(`Department not found: ${row[5]}`);
        }

        const location = row[4].toLowerCase();
        const validLocations = Object.values(ValidLocation);

        if (!validLocations.includes(location)) {
          throw new Error(
            `Invalid location: ${
              row[4]
            }. Valid locations are: ${validLocations.join(', ')}`,
          );
        }

        // Format phone number with +91 prefix
        const phoneNumber = row[3] ? `+91${String(row[3])}` : null;

        return {
          name: row[1],
          email: row[2],
          departmentId: department.id,
          location: location,
          kekaId: row[0],
          phoneNumber: phoneNumber,
          designation: row[6],
          profilePicUrl: process.env.DEFAULT_PROFILE_PIC,
          managerName: row[7], // Store manager name for later processing
        };
      });

      const createdResource = [];
      for (const resource of resources) {
        const existingResource: Resource =
          await this.prisma.resource.findUnique({
            where: { kekaId: resource.kekaId },
          });
        if (existingResource) {
          // Find manager resource by name
          const managerResource = createdResource.find(
            (r) => r.name === resource.managerName,
          );

          const updatedData = {
            name: resource.name,
            email: resource.email,
            location: resource.location,
            departmentId: resource.departmentId,
            phoneNumber: resource.phoneNumber || null,
            designation: resource.designation,
            managerId: managerResource ? managerResource.id : null,
          };
          await this.prisma.resource.update({
            where: { id: existingResource.id },
            data: updatedData,
          });
          createdResource.push(existingResource);
        } else {
          const newResource: Resource = await this.prisma.resource.create({
            data: {
              name: resource.name,
              email: resource.email,
              departmentId: resource.departmentId,
              location: resource.location,
              kekaId: resource.kekaId,
              phoneNumber: resource.phoneNumber || null,
              designation: resource.designation,
              profilePicUrl: resource.profilePicUrl,
            },
          });
          createdResource.push(newResource);
        }

        try {
          const employeeRoleId =
            await this.amResourceService.getEmployeeRoleId();

          const amResourceDto = new AMResourceDto(
            resource.name,
            resource.email,
            employeeRoleId,
            resource.phoneNumber || '',
            resource.kekaId,
            resource.departmentId,
          );

          const amResource = await this.amResourceService.getResourceByKekaId(
            amResourceDto.employeeId,
          );

          if (!amResource) {
            await this.amResourceService.createAMResource(amResourceDto);
            this.logger.log(`Created AM resource for: ${resource.name}`);
          } else {
            await this.amResourceService.updateAMResource(amResourceDto);
            this.logger.log(`Updated AM resource for: ${resource.name}`);
          }
        } catch (amError) {
          this.logger.error(
            `Error processing AM resource for ${resource.name}:`,
            amError,
          );
          // Continue with other resources even if AM resource creation fails
        }
      }

      // Update managerId for each created resource
      for (const resource of createdResource) {
        try {
          const resourceData = resources.find(
            (resourceData) => resourceData.kekaId === resource.kekaId,
          );

          if (resourceData?.managerName) {
            const managerResource = createdResource.find(
              (managerRes) => managerRes.name === resourceData.managerName,
            );

            if (managerResource) {
              await this.prisma.resource.update({
                where: { id: resource.id },
                data: { managerId: managerResource.id },
              });
              this.logger.log(
                `Updated manager for ${resource.name} to ${managerResource.name}`,
              );
            } else {
              this.logger.warn(
                `Manager not found for ${resource.name}: ${resourceData.managerName}`,
              );
            }
          }
        } catch (managerError) {
          this.logger.error(
            `Error updating manager for ${resource.name}:`,
            managerError,
          );
          // Continue with other resources even if manager update fails
        }
      }

      // Get all resource and store ids in array
      const resourceList: Resource[] = await this.prisma.resource.findMany({});
      const resourceIdList: string[] = resourceList.map(
        (resource) => resource.id,
      );

      // Get user perm records which matches resource list array and store ids in another array
      const userPermList: UserPermissions[] =
        await this.prisma.userPermissions.findMany({
          where: { resourceId: { in: resourceIdList } },
        });
      const userPermResourceIdList: string[] = userPermList.map(
        (resource) => resource.resourceId,
      );

      // Filter elements which are common and keep unique ids and map resourceid
      const uniqueResourceIds: UniqueResourceId[] = resourceIdList
        .filter((element) => !userPermResourceIdList.includes(element))
        .map((id) => ({
          resourceId: id,
        }));

      if (uniqueResourceIds.length > 0) {
        try {
          await this.prisma.userPermissions.createMany({
            data: uniqueResourceIds,
          });
          this.logger.log(
            `Created user permissions for ${uniqueResourceIds.length} resources`,
          );
        } catch (permissionError) {
          this.logger.error(
            'Error creating user permissions:',
            permissionError,
          );
          // Continue even if permissions creation fails
        }
      }

      this.logger.log(`resources added successfully`);
      this.logger.log(`resource user permission record(s) added successfully`);

      const newlyCreatedResources = await this.prisma.resource.findMany({
        select: {
          id: true,
          name: true,
          email: true,
          kekaId: true,
          phoneNumber: true,
          managerId: true,
          location: true,
          designation: true,
        },
      });
      return {
        statusCode: HttpStatus.CREATED,
        data: newlyCreatedResources,
        message: `resources added successfully`,
      };
    } catch (error) {
      this.logger.error('Error in importResourceFromXLSX:', error);
      this.logger.error('Error stack:', error.stack);
      throw error;
    }
  }

  // Reactivating resource
  async reactivateResource(
    resourceId: string,
  ): Promise<ReactivateResourceResponse> {
    try {
      //  Check if project is present in database or not
      const resource: Resource = await this.prisma.resource.findUnique({
        where: { id: resourceId },
      });
      // If project not found then throw error
      if (!resource) {
        this.logger.log(RESOURCE_NOT_FOUND);
        throw new Error(RESOURCE_NOT_FOUND);
      }
      // Reactivate project
      const reactivatedResource = await this.prisma.resource.update({
        where: { id: resourceId },
        data: {
          deleted: false,
        },
      });
      await this.amResourceService.reactivateAMResource(resource.kekaId);

      this.logger.log('Successfully reactivated');
      return {
        statusCode: HttpStatus.OK,
        data: reactivatedResource,
        message: 'Successfully reactivated',
      };
    } catch (error) {
      // If unable to reactivate resource then throw error 500
      throw new InternalServerErrorException('Something went wrong');
    }
  }
}
