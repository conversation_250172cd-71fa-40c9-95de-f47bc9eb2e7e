import * as ExcelJS from 'exceljs';
import { minutesToHours } from 'date-fns';
import { getColumnName } from './client-report';
import type { LeaveDetail } from './client-report';
import { LocationType } from '@prisma-clients/timesheet-backend';
import {
  COLOR_CLIENT_OFFICE,
  COLOR_CODECRAFT_OFFICE,
  COLOR_REMOTE_HYBRID,
  KEKA_ID_COL_WIDTH,
  SL_NO_COL_WIDTH,
  DEPARTMENT_COL_WIDTH,
  RESOURCE_COL_WIDTH,
  ReportGenerateMode,
  INITIAL_COLUMNS,
  RESOURCE_REPORT_SHEET_NAME,
} from 'src/constants';

/**
 * Represents a single day's worklog entry for a resource.
 */
interface WorklogEntry {
  workDate: string;
  minutes: number;
  attendance: string;
  isPresent: boolean;
  isOnLeave: boolean;
  isWeekOff: boolean;
  isCompanyOff: boolean;
  billingStatus: string;
  isBillable: boolean;
  location?: LocationType;
}

/**
 * Represents a resource's summary and all worklog/leave details for the report.
 */
export interface ResourceEntry {
  resourceId: string;
  resourceName: string;
  kekaId: string;
  department: string;
  totalMinutes: number;
  totalDays: number;
  isDeleted: boolean;
  billableStatus: 'billable' | 'not-billable' | 'both';
  resourcesWorklog: WorklogEntry[];
  leaveDetails: LeaveDetail[];
}

/**
 * The main input structure for generating the resource report.
 * Contains the reporting mode and a list of all resources to include.
 */
export interface ResourceReport {
  data: {
    numberOfResource: number;
    mode: ReportGenerateMode;
    resourceList: ResourceEntry[];
  };
}

/**
 * Generates an Excel resource report.
 * @param responseData The resource report data.
 * @returns A Promise resolving to an Buffer containing the report.
 */
export async function createResourceReport(
  responseData: ResourceReport,
): Promise<Uint8Array> {
  try {
    const { year, month, monthName } = getReportPeriod(responseData);
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet(RESOURCE_REPORT_SHEET_NAME);
    const border = getDefaultBorder();

    setupSheetHeaders(sheet, year, month, monthName, responseData, border);
    populateResourceData(sheet, responseData, border);
    addLegend(sheet, border);

    const buffer = await workbook.xlsx.writeBuffer();
    return new Uint8Array(buffer);
  } catch (error) {
    throw new Error(
      `Failed to generate resource report: ${(error as Error).message}`,
    );
  }
}

/**
 * Extracts the reporting period (year, month, month name) from the resource report data.
 * @param responseData The resource report data.
 * @returns An object containing year, month, and monthName.
 */
export function getReportPeriod(responseData: ResourceReport): {
  year: number;
  month: number;
  monthName: string;
} {
  const firstWorklog = responseData.data.resourceList
    .at(0)
    ?.resourcesWorklog.at(0);
  const startDate = firstWorklog ? new Date(firstWorklog.workDate) : new Date();
  const year = startDate.getFullYear();
  const month = startDate.getMonth() + 1;
  const monthName = new Intl.DateTimeFormat('en-US', { month: 'long' }).format(
    startDate,
  );
  return { year, month, monthName };
}

/**
 * Sets up the sheet headers, including the month/year, column headers, and column widths.
 * @param sheet The Excel worksheet.
 * @param year The report year.
 * @param month The report month.
 * @param monthName The report month name.
 * @param responseData The resource report data.
 * @param border The border style to apply.
 */
function setupSheetHeaders(
  sheet: ExcelJS.Worksheet,
  year: number,
  month: number,
  monthName: string,
  responseData: ResourceReport,
  border: ExcelJS.Borders,
): void {
  // Set month and year in the first row
  const firstRow = sheet.getCell('A1');
  firstRow.value = `${monthName}-${year}`;
  firstRow.font = { bold: true };
  firstRow.border = border;

  const daysInMonth = new Date(year, month, 0).getDate();
  const dayNumbers = Array.from({ length: daysInMonth }, (_, i) => i + 1);

  const headerRow = [
    'Sl.No',
    'Keka ID',
    'Resource',
    'Department',
    ...dayNumbers,
    'Worked Hours',
    'Worked Days',
  ];

  headerRow.forEach((header, colNum) => {
    const cell = sheet.getCell(2, colNum + 1);
    cell.value = header;
    cell.font = { bold: true };
    cell.border = border;
  });

  sheet.getColumn('A').width = SL_NO_COL_WIDTH;
  sheet.getColumn('B').width = KEKA_ID_COL_WIDTH;
  sheet.getColumn('C').width = RESOURCE_COL_WIDTH;
  sheet.getColumn('D').width = DEPARTMENT_COL_WIDTH;

  for (let colNum = 5; colNum < daysInMonth + 5; colNum++) {
    const colLetter = getColumnName(colNum);
    sheet.getColumn(colLetter).width = 3;
  }

  const totalHoursColName = getColumnName(daysInMonth + 5);
  sheet.getColumn(totalHoursColName).width = 15;

  const totalDaysColName = getColumnName(daysInMonth + 6);
  sheet.getColumn(totalDaysColName).width = 15;

  const firstRowRange = `A1:${totalDaysColName}1`;
  sheet.mergeCells(firstRowRange);
  const firstCell = sheet.getCell(firstRowRange.split(':')[0]);
  firstCell.alignment = { horizontal: 'center' };
}

/**
 * Populates the resource data into the sheet.
 * @param sheet The Excel worksheet.
 * @param responseData The resource report data.
 * @param border The border style to apply.
 */
function populateResourceData(
  sheet: ExcelJS.Worksheet,
  responseData: ResourceReport,
  border: ExcelJS.Borders,
): void {
  const firstWorklogDate = new Date(
    responseData.data.resourceList[0]?.resourcesWorklog[0]?.workDate,
  );
  const year = firstWorklogDate.getFullYear();
  const month = firstWorklogDate.getMonth() + 1; // Months are 0-indexed
  const numDaysInMonth = new Date(year, month, 0).getDate(); // Get the last day of the month

  responseData.data.resourceList.forEach((resource, idx) => {
    const rowIndex = idx + 3; // Start from the third row
    const totalHours = minutesToHours(resource.totalMinutes);
    const totalDays = resource.totalDays;

    sheet.getCell(rowIndex, 1).value = idx + 1; // Sl.No
    sheet.getCell(rowIndex, 2).value = resource.kekaId; // Keka ID
    sheet.getCell(rowIndex, 3).value = resource.resourceName; // Resource Name
    sheet.getCell(rowIndex, 4).value = resource.department; // Department

    resource.resourcesWorklog.forEach((worklog) => {
      const workDate = new Date(worklog.workDate).getDate();
      const cell = sheet.getCell(rowIndex, workDate + 4);
      cell.border = border;
      cell.value = worklog.minutes > 0 ? minutesToHours(worklog.minutes) : ' ';

      // Add logic to set cell colors based on location
      if (worklog.location === 'codecraftOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CODECRAFT_OFFICE }, // Yellow for office
        };
      } else if (worklog.location === 'clientOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CLIENT_OFFICE }, //Green for client office
        };
      } else if (worklog.location === 'remoteHybrid') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_REMOTE_HYBRID }, // Blue for hybrid
        };
      }
    });

    const initialColumns = INITIAL_COLUMNS; // Number of columns before the date columns (e.g., Sl.No, Keka ID, Resource, Department)
    const workedHoursColIndex = initialColumns + numDaysInMonth + 1; // Column index for "Worked Hours"
    const workedDaysColIndex = initialColumns + numDaysInMonth + 2; // Column index for "Worked Days"

    sheet.getCell(rowIndex, workedHoursColIndex).value = totalHours; // Worked Hours
    sheet.getCell(rowIndex, workedDaysColIndex).value = totalDays; // Worked Days
  });
}

/**
 * Adds a legend to the Excel sheet explaining the color codes and symbols.
 * @param sheet The Excel worksheet.
 * @param border The border style to apply.
 */
export function addLegend(
  sheet: ExcelJS.Worksheet,
  border: ExcelJS.Borders,
): void {
  const startRow = sheet.lastRow?.number + 3 || 10;
  sheet.mergeCells(startRow, 1, startRow, 2);
  sheet.getCell(startRow, 1).value = 'Location:';
  sheet.getCell(startRow, 1).font = { bold: true };

  sheet.getCell(startRow + 1, 1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: COLOR_CODECRAFT_OFFICE },
  };
  sheet.getCell(startRow + 1, 1).border = border;
  sheet.getCell(startRow + 1, 2).value = 'CodeCraft Office';
  sheet.getCell(startRow + 1, 2).font = { bold: true };
  sheet.getCell(startRow + 1, 2).border = border;

  sheet.getCell(startRow + 2, 1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: COLOR_CLIENT_OFFICE },
  };
  sheet.getCell(startRow + 2, 1).border = border;
  sheet.getCell(startRow + 2, 2).value = 'Client Office';
  sheet.getCell(startRow + 2, 2).font = { bold: true };
  sheet.getCell(startRow + 2, 2).border = border;

  sheet.getCell(startRow + 3, 1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: COLOR_REMOTE_HYBRID },
  };
  sheet.getCell(startRow + 3, 1).border = border;
  sheet.getCell(startRow + 3, 2).value = 'Hybrid';
  sheet.getCell(startRow + 3, 2).font = { bold: true };
  sheet.getCell(startRow + 3, 2).border = border;
}

/**
 * Returns the default border style for Excel cells.
 * @returns An ExcelJS.Borders object with thin borders.
 */
export function getDefaultBorder(): ExcelJS.Borders {
  return {
    left: { style: 'thin' },
    right: { style: 'thin' },
    top: { style: 'thin' },
    bottom: { style: 'thin' },
    diagonal: {}, // Add an empty object for diagonal as it's optional
  };
}
