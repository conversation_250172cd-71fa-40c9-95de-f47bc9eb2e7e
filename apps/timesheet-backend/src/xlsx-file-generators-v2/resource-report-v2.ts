import * as ExcelJS from 'exceljs';
import { minutesToHours } from 'date-fns';
import { getColumnName } from 'src/xlsx-file-generators/client-report';
import type { LeaveDetail } from 'src/xlsx-file-generators/client-report';
import { LocationType } from '@prisma-clients/timesheet-backend';
import {
  getReportPeriod,
  addLegend,
  getDefaultBorder,
} from 'src/xlsx-file-generators/resource-report';
import {
  SL_NO_COL_WIDTH,
  KEKA_ID_COL_WIDTH,
  RESOURCE_COL_WIDTH,
  DEPARTMENT_COL_WIDTH,
  PROJECT_COL_WIDTH,
  CONTRACT_COL_WIDTH,
  DATE_COL_WIDTH,
  WORKED_HOURS_COL_WIDTH,
  COLOR_CODECRAFT_OFFICE,
  COLOR_CLIENT_OFFICE,
  COLOR_REMOTE_HYBRID,
  ReportGenerateMode,
  INITIAL_COLUMNS,
  RESOURCE_REPORT_SHEET_NAME,
} from 'src/constants';
import { ITaskDetails } from 'src/resource/interface/resource.interface';

/**
 * Represents a single day's worklog entry for a resource.
 */
interface WorklogEntry {
  workDate: string;
  minutes: number;
  attendance: string;
  isPresent: boolean;
  isOnLeave: boolean;
  isWeekOff: boolean;
  isCompanyOff: boolean;
  billingStatus: string;
  isBillable: boolean;
  location?: LocationType;
}

/**
 * Represents a resource's summary and all worklog/leave details for the report.
 */
export interface ResourceEntry {
  resourceId: string;
  resourceName: string;
  kekaId: string;
  department: string;
  totalMinutes: number;
  totalDays: number;
  isDeleted: boolean;
  billableStatus: 'billable' | 'not-billable' | 'both';
  resourcesWorklog: WorklogEntry[];
  leaveDetails: LeaveDetail[];
  taskDetails?: ITaskDetails[];
}

/**
 * The main input structure for generating the resource report.
 * Contains the reporting mode and a list of all resources to include.
 */
export interface ResourceReport {
  data: {
    numberOfResource: number;
    mode: ReportGenerateMode;
    resourceList: ResourceEntry[];
  };
}

/**
 * Generates an Excel resource report.
 * @param responseData The resource report data.
 * @returns A Promise resolving to an Buffer containing the report.
 */
export async function createResourceReportV2(
  responseData: ResourceReport,
): Promise<Uint8Array> {
  try {
    const { year, month, monthName } = getReportPeriod(responseData);
    const workbook = new ExcelJS.Workbook();
    const sheet = workbook.addWorksheet(RESOURCE_REPORT_SHEET_NAME);
    const border = getDefaultBorder();

    setupSheetHeadersV2(sheet, year, month, monthName, responseData, border);
    populateResourceDataV2(sheet, responseData, border);
    addLegend(sheet, border);

    const buffer = await workbook.xlsx.writeBuffer();
    return new Uint8Array(buffer);
  } catch (error) {
    throw new Error(
      `Failed to generate resource report: ${(error as Error).message}`,
    );
  }
}

/**
 * Sets up the sheet headers, including the month/year, column headers, and column widths.
 * @param sheet The Excel worksheet.
 * @param year The report year.
 * @param month The report month.
 * @param monthName The report month name.
 * @param responseData The resource report data.
 * @param border The border style to apply.
 */
function setupSheetHeadersV2(
  sheet: ExcelJS.Worksheet,
  year: number,
  month: number,
  monthName: string,
  responseData: ResourceReport,
  border: ExcelJS.Borders,
): void {
  // Set month and year in the first row
  const firstRow = sheet.getCell('A1');
  firstRow.value = `${monthName}-${year}`;
  firstRow.font = { bold: true };
  firstRow.border = border;

  const daysInMonth = new Date(year, month, 0).getDate();
  const dayNumbers = Array.from({ length: daysInMonth }, (_, i) => i + 1);

  const headerRow = [
    'Sl.No',
    'Keka ID',
    'Resource',
    'Department',
    'Project',
    'Contract',
    ...dayNumbers,
    'Worked Hours',
  ];

  headerRow.forEach((header, colNum) => {
    const cell = sheet.getCell(2, colNum + 1);
    cell.value = header;
    cell.font = { bold: true };
    cell.border = border;
  });

  sheet.getColumn('A').width = SL_NO_COL_WIDTH;
  sheet.getColumn('B').width = KEKA_ID_COL_WIDTH;
  sheet.getColumn('C').width = RESOURCE_COL_WIDTH;
  sheet.getColumn('D').width = DEPARTMENT_COL_WIDTH;
  sheet.getColumn('E').width = PROJECT_COL_WIDTH;
  sheet.getColumn('F').width = CONTRACT_COL_WIDTH;

  for (let colNum = 7; colNum < daysInMonth + 7; colNum++) {
    const colLetter = getColumnName(colNum);
    sheet.getColumn(colLetter).width = DATE_COL_WIDTH;
  }

  const totalHoursColName = getColumnName(daysInMonth + 7);
  sheet.getColumn(totalHoursColName).width = WORKED_HOURS_COL_WIDTH;

  const firstRowRange = `A1:${totalHoursColName}1`;
  sheet.mergeCells(firstRowRange);
  const firstCell = sheet.getCell(firstRowRange.split(':')[0]);
  firstCell.alignment = { horizontal: 'center' };
}

/**
 * Populates the resource data into the sheet.
 * @param sheet The Excel worksheet.
 * @param responseData The resource report data.
 * @param border The border style to apply.
 */
function populateResourceDataV2(
  sheet: ExcelJS.Worksheet,
  responseData: ResourceReport,
  border: ExcelJS.Borders,
): void {
  const firstWorklogDate = new Date(
    responseData.data.resourceList[0]?.resourcesWorklog[0]?.workDate,
  );
  const year = firstWorklogDate.getFullYear();
  const month = firstWorklogDate.getMonth() + 1; // Months are 0-indexed
  const numDaysInMonth = new Date(year, month, 0).getDate(); // Get the last day of the month

  let rowIndex = 3; // Start from the third row
  let slNo = 1;

  responseData.data.resourceList.forEach((resource) => {
    // Group task details by project and contract
    const projectContractGroups = groupTaskDetailsByProjectAndContract(
      resource.taskDetails || [],
    );

    if (projectContractGroups.length === 0) {
      // If no task details, create a single row with basic info
      addResourceRow(
        sheet,
        resource,
        null,
        null,
        rowIndex,
        slNo,
        numDaysInMonth,
        border,
      );
      rowIndex++;
      slNo++;
    } else {
      // Create a row for each project-contract combination
      projectContractGroups.forEach((group) => {
        addResourceRow(
          sheet,
          resource,
          group.projectName,
          group.contractName,
          rowIndex,
          slNo,
          numDaysInMonth,
          border,
          group.worklogsByDate,
        );
        rowIndex++;
        slNo++;
      });
    }
  });
}

/**
 * Groups task details by project and contract
 */
function groupTaskDetailsByProjectAndContract(
  taskDetails: ITaskDetails[],
): Array<{
  projectName: string;
  contractName: string;
  worklogsByDate: Map<string, { minutes: number; location?: string }>;
}> {
  const groups = new Map<
    string,
    {
      projectName: string;
      contractName: string;
      worklogsByDate: Map<string, { minutes: number; location?: string }>;
    }
  >();

  taskDetails.forEach((task) => {
    const key = `${task.projectId}-${task.contractId || 'no-contract'}`;
    const projectName = task.projectName || 'Unknown Project';
    const contractName = task.contractName || task.contractId || 'No Contract';

    if (!groups.has(key)) {
      groups.set(key, {
        projectName,
        contractName,
        worklogsByDate: new Map(),
      });
    }

    const group = groups.get(key)!;
    const dateKey = new Date(task.workDate).toISOString().split('T')[0];
    const existingData = group.worklogsByDate.get(dateKey) || { minutes: 0 };
    group.worklogsByDate.set(dateKey, {
      minutes: existingData.minutes + task.minutes,
      location: existingData.location || task.location, // Keep the first location encountered for the day
    });
  });

  return Array.from(groups.values());
}

/**
 * Adds a single resource row to the sheet
 */
function addResourceRow(
  sheet: ExcelJS.Worksheet,
  resource: ResourceEntry,
  projectName: string | null,
  contractName: string | null,
  rowIndex: number,
  slNo: number,
  numDaysInMonth: number,
  border: ExcelJS.Borders,
  worklogsByDate?: Map<string, { minutes: number; location?: string }>,
): void {
  let totalHours = 0;

  sheet.getCell(rowIndex, 1).value = slNo; // Sl.No
  sheet.getCell(rowIndex, 2).value = resource.kekaId; // Keka ID
  sheet.getCell(rowIndex, 3).value = resource.resourceName; // Resource Name
  sheet.getCell(rowIndex, 4).value = resource.department; // Department
  sheet.getCell(rowIndex, 5).value = projectName || 'N/A'; // Project
  sheet.getCell(rowIndex, 6).value = contractName || 'N/A'; // Contract

  // Populate daily worklog data
  if (worklogsByDate) {
    // Use the grouped worklog data for this project-contract combination
    worklogsByDate.forEach((data, dateKey) => {
      const workDate = new Date(dateKey);
      const dayOfMonth = workDate.getDate();
      const cell = sheet.getCell(rowIndex, dayOfMonth + 6); // +6 because we have 6 initial columns now
      cell.border = border;
      cell.value = data.minutes > 0 ? minutesToHours(data.minutes) : ' ';
      totalHours += data.minutes; // Accumulate total minutes for this project-contract combination

      // Apply color coding based on location
      if (data.location === 'codecraftOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CODECRAFT_OFFICE }, // Yellow for office
        };
      } else if (data.location === 'clientOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CLIENT_OFFICE }, // Green for client office
        };
      } else if (data.location === 'remoteHybrid') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_REMOTE_HYBRID }, // Blue for hybrid
        };
      }
    });
  } else {
    // Use the original aggregated worklog data (fallback for resources without task details)
    resource.resourcesWorklog.forEach((worklog) => {
      const workDate = new Date(worklog.workDate).getDate();
      const cell = sheet.getCell(rowIndex, workDate + 6); // +6 because we have 6 initial columns now
      cell.border = border;
      cell.value = worklog.minutes > 0 ? minutesToHours(worklog.minutes) : ' ';
      totalHours += worklog.minutes; // Accumulate total minutes
      // Add logic to set cell colors based on location
      if (worklog.location === 'codecraftOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CODECRAFT_OFFICE }, // Yellow for office
        };
      } else if (worklog.location === 'clientOffice') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_CLIENT_OFFICE }, // Green for client office
        };
      } else if (worklog.location === 'remoteHybrid') {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: COLOR_REMOTE_HYBRID }, // Blue for hybrid
        };
      }
    });
  }

  const workedHoursColIndex = INITIAL_COLUMNS + numDaysInMonth + 1; // Column index for "Worked Hours"
  sheet.getCell(rowIndex, workedHoursColIndex).value =
    minutesToHours(totalHours); // Worked Hours for this project-contract combination
}
