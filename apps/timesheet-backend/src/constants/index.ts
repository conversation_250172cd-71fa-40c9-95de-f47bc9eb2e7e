export const PROJECT_NOT_FOUND = 'Project not found.';

export const INTERNAL_SERVER_ERROR = 'Something went wrong';

export const WORKLOG_NOT_FOUND = 'Invalid worklog id';

export const TASK_NOT_FOUND = 'Invalid Task Id';

export const MANAGER_NOT_FOUND = 'Invalid Manager Id';

export const EMPLOYEE_NOT_FOUND = 'Invalid Employee Id';

export const CLIENT_NOT_FOUND = 'Invalid Client Id';

export const CLIENT_EXISTS = 'Client name already exists';

export const WORKLOG_BAD_REQUEST = 'Cannot delete an approved work log';

export const RESOURCE_NOT_FOUND_CONTACT_ADMIN =
  'Resource not found please contact admin';

export const DEPARTMENT_EXISTS =
  'Department which you are trying to create is already exists';

export const ACCESS_DENIED = 'Access denied';

export const PROJECT_RESOURCE_NOT_FOUND = 'Project resource not found';

export const DEPARTMENTS_NOT_FOUND = 'Departments not found';

export const DEPARTMENT_NOT_FOUND = 'Department not found';

export const DEPARTMENTS_NAME_REQUIRED = 'Departments name should not be empty';

export const RESOURCE_NOT_FOUND = 'Resource not found';

export const KEKAID_ALREADY_EXISTS = 'KekaId already exists';

export const EMAIL_ALREADY_EXISTS = 'Email already exists';

export const TASK_ALREADY_EXISTS = 'Task already exists';

export const PROJECTRESOURCE_NOT_FOUND = 'Project resource not found';

export const REPORTING_MANAGER_NOT_FOUND = 'Reporting manager not found';

export const RESOURCES_NOT_FOUND = 'Resource not found';

export const CONTRACT_NOT_FOUND = 'Contract not found';

export const ASSIGNED_RESOURCE_NOT_FOUND = 'Assigner id not found';

export const TASK_ALREADY_DELETED = 'Task is already deleted';

export const INVALID_DOMAIN = 'only codecraft domain is allowed ';

export const PROJECT_NAME_EXISTS = 'Project name already exists';

export const PROJECT_MANAGER_NOT_FOUND = 'Project manager not found';

export const S3_DEFAULT_PROFILE_FOLDER = 'default-profile';

export const WORKLOG_SUBMITTED_REMARK = 'Worklog submitted';
export const WORKLOG_REVISED_REMARK = 'Worklog revised';

export const LEAVEDATA_NOT_FOUND = 'LeaveData not found';

export const LEAVE_ID_NOT_FOUND = 'Leave not found';

export const LEAVE_ENTRY_EXISTS = 'Leave is already applied for selected dates';

export const REGEX = {
  ALPHA_NUMERIC_WITH_SPECIAL_CHARS:
    /^([\w\d!@#$%^&()+{}\[\]:;<>,.?\\/-]+\s?)+$/,
};

export const CONTRACT_MANAGER_ASSIGNED_HISTORY =
  'Contract Created - Contract Manager(s) Assigned';

export const CONTRACT_CREATED_HISTORY = 'Contract Created';

export const CONTRACT_MANAGER_ADDED =
  'Contract Updated - Contract Manager(s) Added';

export const CONTRACT_MANAGER_REMOVED =
  'Contract Updated - Contract Manager(s) Removed';

export const CONTRACT_DEACTIVATED = 'Contract Deactivated';
export const CONTRACT_REACTIVATED = 'Contract Activated';
export const CONTRACT_COMPLETED = 'Contract Completed';

export const RESOURCES_ADDED_CONTRACT_HISTORY =
  'Contract Updated - Resource(s) Added';

export const RESOURCES_REMOVED_CONTRACT_HISTORY =
  'Contract Updated - Resource(s) Removed';

export const CONTRACT_CUSTOM_CONTRACT_ID_UPDATED_HISTORY =
  'Contract Updated - Contract Id updated';

export const CONTRACT_DATES_UPDATED_HISTORY =
  'Contract Updated - Contract Dates updated';

export const CONTRACT_RESOURCE_BILLING_RATE_ADDED =
  'Contract Updated - Resource billing rate added';

export const CONTRACT_RESOURCE_BILLING_RATE_UPDATED =
  'Contract Updated - Resource billing rate updated';

export const CANNOT_RENEW_ACTIVE_CONTRACT = 'Cannot renew active contract';

export const NO_CLIENT_ACCESS = 'You do not have access to the this client';

export const NO_PROJECT_ACCESS = 'You do not have access to this project';

export const NO_CONTRACT_ACCESS = 'You do not have access to this contract';

export const NO_RESOURCE_ACCESS = 'You do not have access to this resource';

export const INVALID_UUIDS = 'One or more invalid UUID format(s)';

export const MAX_HOURS_CAN_BE_LOGGED = 16;
export const MINUTES_IN_DAY: number = MAX_HOURS_CAN_BE_LOGGED * 60;
export const MILLISECONDS_IN_DAY: number = 24 * 60 * 60 * 1000;

export const WORK_LOG_DURATION_EXCEEDED = `Work log duration cannot exceed ${MAX_HOURS_CAN_BE_LOGGED} hours`;

export const ACTUAL_SHIFT_START_TIME = 9; //9:00 AM
export const MIN_SHIFT_START_TIME = 6; //6:00 AM
export const MAX_SHIFT_HOURS = 14; //to manage consistent startDate and endDate
export const MAX_SHIFT_IN_MINUTES = MAX_SHIFT_HOURS * 60;

export const ISO_8601_REGEX =
  /^(19[7-9]\d|20\d{2})[-:](0[1-9]|1[0-2])[-:](0[1-9]|[12][0-9]|3[01])T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;

export const REPORT_GENERATE_MODE = {
  cappedHours: 'cappedHours',
  attendance: 'attendance',
  actualHours: 'actualHours',
} as const;

export type ReportGenerateMode = keyof typeof REPORT_GENERATE_MODE;

export const TASK_NAME_EXISTS = 'Task name already exists';

export const LEAVE_STATUS_APPROVED = 'Approved';

export const LEAVES_NOT_FOUND =
  'The provided XLSX file does not contain any leaves to upload.';

export const MIN_YEAR = 1000;
export const MAX_YEAR = 9999;

export const NOTIFICATION_NOT_FOUND = 'Invalid notification id';

export const SUNDAY = 0;
export const SATURDAY = 6;

export const SL_NO_COL_WIDTH = 6;
export const KEKA_ID_COL_WIDTH = 12;
export const RESOURCE_COL_WIDTH = 30;
export const DEPARTMENT_COL_WIDTH = 38;
export const PROJECT_COL_WIDTH = 35;
export const CONTRACT_COL_WIDTH = 25;
export const DATE_COL_WIDTH = 3;
export const WORKED_HOURS_COL_WIDTH = 15;

// Cell fill color constants for worklog locations
export const COLOR_CODECRAFT_OFFICE = 'FFD700'; // Yellow
export const COLOR_CLIENT_OFFICE = '00FF00'; // Green
export const COLOR_REMOTE_HYBRID = '87CEEB';
export const NATIONAL_HOLIDAY = '7DD3FC'; // Blue
export const WEEK_OFF = 'FEF08A'; //  Yellow
export const FULL_DAY = 'FCA5A5'; // Red

// Number of columns
export const INITIAL_COLUMNS = 6; // Updated to include Project and Contract columns
export const RESOURCE_REPORT_SHEET_NAME = 'Resource Report';
