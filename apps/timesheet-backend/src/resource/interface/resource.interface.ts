import { HttpStatus } from '@nestjs/common';
import {
  Resource,
  Location,
  Role,
  ResourceBillingRate,
  Prisma,
  LocationType,
} from '@prisma-clients/timesheet-backend';
import { CreateManyResourceDto } from '../dto/create-resource.dto';
import { BillableStatus } from 'src/common/enums/billable_status.enum';
import { ReportGenerateMode } from 'src/constants';

export interface CreateResource {
  statusCode: HttpStatus;
  data: Resource;
  message: string;
}

export interface GetEmployees {
  statusCode: HttpStatus;
  data: Employee[];
  message: string;
}

export interface Employee {
  id: string;
  name: string;
  email: string;
  role: Role | null;
  phoneNumber: string | null;
  departmentId: string | null;
  designation: string | null;
  managerId: string | null;
  location: Location;
  kekaId: string;
  createdAt: Date;
  updatedAt: Date;
  deleted: boolean;
}

export interface ResourceData extends Resource {
  manager: {
    name: string;
  };
  department: {
    departmentName: string;
  };
}

export interface ProjectDto {
  id: string;
  projectName: string;
  contactName: string;
  contactEmail: string;
  contactPhoneNumber: string;
  description: string;
  startDate: Date;
  endDate: Date;
  resources: number;
  clientName: string;
  billable: boolean;
  totalHours: number;
  projectStatus: string;
}

export interface DisplayUserData {
  statusCode: HttpStatus;
  data: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
    location: Location;
    jobTitle: string;
    department: string;
    reportingTo: string;
    employeeNo: string;
    profilePicurl: string | null;
    deleted: boolean;
  };
  message: string;
}

export interface UniqueResourceId {
  resourceId: string;
}

export interface UpdateResource {
  statusCode: HttpStatus;
  data: Resource;
  message: string;
}

export interface DeleteResourceResponse {
  statusCode: HttpStatus;
  data: Resource;
  message: string;
}

export interface CreateManyResource {
  statusCode: HttpStatus;
  data: CreateManyResourceDto[];
  message: string;
}
export interface ProjectHoursPerDay {
  projectId: string;
  projectName: string;
  billable: boolean;
  hoursPerDay: {
    workDate: Date;
    hours: number;
  }[];
}

export interface OverviewEmployeePayload extends Resource {
  totalHoursSpent: number;
  billableHours: number;
  stackedBarChartData?: ProjectHoursPerDay[];
}

export interface OverviewEmployeeReportResponse {
  statusCode: HttpStatus;
  data: OverviewEmployeePayload;
  message: string;
}

export interface UpdateResourceBillingResponse {
  statusCode: HttpStatus;
  data: ResourceBillingRate;
  message: string;
}

export interface CreateResourceBillingResponse {
  statusCode: HttpStatus;
  data: ResourceBillingRate;
  message: string;
}

export interface ListAllResourceResponse {
  statusCode: HttpStatus;
  data: GetAllResourcesData[];
  count: number;
  message: string;
}

export type GetAllResourcesData = Prisma.ResourceGetPayload<{
  select: {
    id: true;
    name: true;
    email: true;
    role: true;
    canAccessAssetManagement: true;
    phoneNumber: true;
    department: {
      select: {
        id: true;
        departmentName: true;
      };
    };
    designation: true;
    managerId: true;
    location: true;
    kekaId: true;
    createdAt: true;
    updatedAt: true;
    profilePicUrl: true;
    currency: true;
    expectedHours: true;
    expectedHoursFrequency: true;
    resourceContractor: true;
    deleted: true;
    resourceContractBillingRate: {
      select: {
        id: true;
        rate: true;
        interval: true;
        currency: true;
        contractId: true;
        resourceId: true;
      };
    };
  };
}>;

export interface RetrievedDashboardPermission {
  UserPermissions: {
    id: string;
    tracker: boolean;
    projects: boolean;
    resourceId: string;
    timesheet: boolean;
    history: boolean;
    clients: boolean;
    overview: boolean;
    teams: boolean;
    category: boolean;
  }[];
}

export interface IGetResourceDashboardPermissionsResponse {
  statusCode: HttpStatus;
  data: RetrievedDashboardPermission;
  message: string;
}

export type IGetAllResourceInfoForReport = Prisma.ResourceGetPayload<{
  include: {
    employeeWorkLogs: true;
    department: true;
  };
}>;

export type IGetAllWorklogsInfoForReport = Prisma.WorkLogGetPayload<{
  include: {
    task: {
      include: {
        project: true;
        contractResource: true;
        contract: {
          include: {
            resourceContractBillingRate: true;
          };
        };
      };
    };
  };
}>;

export interface IResourceReportInfo {
  workDate: string;
  minutes: number;
  attendance: string;
  isPresent: boolean;
  isOnLeave: boolean;
  isWeekOff: boolean;
  isCompanyOff: boolean;
  billingStatus: string;
  isBillable: boolean;
  location?: LocationType;
}

export interface ITaskDetails {
  projectId: string;
  projectName: string;
  taskName: string;
  taskId: string;
  minutes: number;
  workDate: Date;
  contractId?: string | null;
  contractName?: string | null;
  location?: string | null;
}

export interface IleaveDetails {
  date: string;
  leaveStatus: number;
}

export interface IResourceInfo {
  resourceId: string;
  resourceName: string;
  kekaId: string;
  department: string;
  totalMinutes: number;
  totalDays: number;
  billableStatus: BillableStatus;
  isDeleted: boolean;
  resourcesWorklog: IResourceReportInfo[];
  taskDetails?: ITaskDetails[];
  leaveDetails: IleaveDetails[];
}

export interface IGetAllResourceReportResponse {
  statusCode: HttpStatus;
  data: IResourceInfo[];
  count: number;
  message: string;
}

export interface ReactivateResourceResponse {
  statusCode: HttpStatus;
  data: Resource;
  message: string;
}

export interface GetAllResourceUnderManagerResponse {
  statusCode: HttpStatus;
  data: Resource[];
  count: number;
  message: string;
}

export interface ResourceReport {
  statusCode: number;
  data: {
    downloadUrl: string;
  };
  message: string;
}

export interface ResourceReportData {
  numberOfResource: number;
  resourceList: IResourceInfo[];
  mode: ReportGenerateMode;
}
