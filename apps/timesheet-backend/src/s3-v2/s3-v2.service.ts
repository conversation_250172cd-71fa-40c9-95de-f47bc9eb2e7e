import { Injectable, Logger } from '@nestjs/common';
import { S3 } from 'aws-sdk';
import { PrismaService } from 'src/prisma.service';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { randomUUID } from 'node:crypto';

@Injectable()
export class S3V2Service {
  private s3: S3;
  private logger = new Logger('S3V2Service');
  private readonly s3Client = new S3Client({});
  constructor(private readonly prisma: PrismaService) {
    this.s3 = new S3({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });
  }

  /**
   * Generates a report using the provided report generator function and uploads it to S3.
   * @template T The type of the data used to generate the report.
   * @param data The data required to generate the report.
   * @param reportGenerator A function that generates the report as a Uint8Array.
   * @param prefix A prefix for the file name to identify the report type.
   * @param year The year to include in the file name.
   * @param month The month to include in the file name (as a string, e.g., 'June').
   * @returns A Promise resolving to the S3 URL of the uploaded report.
   * @throws An error if the report generation or upload fails.
   */
  async generateAndUploadReport<T>(
    data: T,
    reportGenerator: (data: T) => Promise<Uint8Array>,
    prefix: string,
    year: number,
    month: string,
  ): Promise<string> {
    try {
      // Generate the report using the provided generator function
      const reportBuffer = await reportGenerator(data);

      // Generate a unique file name with UUID to prevent caching issues
      const fileId = randomUUID();
      const fileName = `${prefix}_${month}_${year}_${fileId}.xlsx`;
      // Create the S3 upload command
      const command = new PutObjectCommand({
        Bucket: process.env.REPORTS_LAMBDA_BUCKET_NAME,
        Key: fileName,
        Body: reportBuffer,
        ContentType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      await this.s3Client.send(command);
      // Return the S3 URL of the uploaded report
      return `${process.env.REPORTS_CLOUD_FRONT_URL}/${fileName}`;
    } catch (error) {
      throw new Error(
        `${prefix} report generation or upload failed: ${error.message}`,
      );
    }
  }
}
