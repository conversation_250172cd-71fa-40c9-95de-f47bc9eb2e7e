import * as ExcelJS from 'exceljs';
import { minutesToHours } from 'date-fns';
import { convertToIndianZonedTime } from '@/utils/date-time';
import type { LeaveDetail } from './client-report';
import { getColumnName, getTypeOfLeave } from './client-report';

interface WorklogEntry {
  workDate: string;
  minutes: number;
  attendance: string;
  isPresent: boolean;
  isOnLeave: boolean;
  isWeekOff: boolean;
  isCompanyOff: boolean;
  billingStatus: string;
  isBillable: boolean;
}

export interface ResourceEntry {
  resourceId: string;
  resourceName: string;
  kekaId: string;
  department: string;
  totalMinutes: number;
  totalDays: number;
  isDeleted: boolean;
  billableStatus: 'billable' | 'not-billable' | 'both';
  resourcesWorklog: WorklogEntry[];
  leaveDetails: LeaveDetail[];
}

export interface ResourceReport {
  data: {
    numberOfResource: number;
    mode: 'actualHours' | 'attendance';
    resourceList: ResourceEntry[];
  };
}

export async function createResourceReport(
  responseData: ResourceReport,
): Promise<ExcelJS.Buffer> {
  const firstWorklog = responseData.data.resourceList
    .at(0)
    ?.resourcesWorklog.at(0);
  const startDate = firstWorklog ? new Date(firstWorklog.workDate) : new Date();
  const year: number = startDate.getFullYear();
  const month: number = startDate.getMonth() + 1;
  const monthName: string = new Intl.DateTimeFormat('en-US', {
    month: 'long',
  }).format(startDate);

  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Resource Report');

  const border: ExcelJS.Borders = {
    diagonal: {
      color: {},
    },
    left: { style: 'thin' },
    right: { style: 'thin' },
    top: { style: 'thin' },
    bottom: { style: 'thin' },
  };

  sheet.eachRow((row) => {
    row.eachCell((cell) => {
      cell.border = border;
    });
  });

  // Set month and year in the first row
  const firstRow = sheet.getCell('A1');
  firstRow.value = `${monthName}-${year}`;
  firstRow.font = { bold: true };
  firstRow.border = border;

  // Set column headers in the second row
  const daysInMonth = new Date(year, month, 0).getDate();

  const dayNumbers = Array.from({ length: daysInMonth }, (_, i) => i + 1);

  const headerRow = [
    'Sl.No',
    'Keka ID',
    'Resource',
    'Department',
    ...dayNumbers,
    'Worked Hours',
    'Worked Days',
  ];

  headerRow.forEach((header, colNum) => {
    const cell = sheet.getCell(2, colNum + 1);
    cell.value = header;
    cell.font = { bold: true };
    cell.border = border;
    if (header === 'P') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '86EFAC' },
      };
    } else if (header === 'L') {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FCA5A5' },
      };
    }
  });

  sheet.getColumn('A').width = 6;
  sheet.getColumn('B').width = 12;
  sheet.getColumn('C').width = 30;
  sheet.getColumn('D').width = 38;

  const numDaysInMonth = new Date(year, month, 0).getDate();

  for (let colNum = 5; colNum < numDaysInMonth + 5; colNum++) {
    const colLetter = getColumnName(colNum);
    sheet.getColumn(colLetter).width = 3;
  }

  const totalHoursColName = getColumnName(numDaysInMonth + 5);
  sheet.getColumn(totalHoursColName).width = 15;

  const totalDaysColName = getColumnName(numDaysInMonth + 6);
  sheet.getColumn(totalDaysColName).width = 15;

  const firstRowRange = `A1:${totalDaysColName}1`;
  sheet.mergeCells(firstRowRange);
  const firstCell = sheet.getCell(firstRowRange.split(':')[0]);
  firstCell.alignment = { horizontal: 'center' };

  // Iterate over resourceList
  responseData.data.resourceList.forEach((resource, idx) => {
    const resourceName = resource.resourceName || '';
    const department = resource.department || '';
    const totalDays = resource.totalDays;
    const totalHours = minutesToHours(resource.totalMinutes);
    const kekaId = resource.kekaId;
    const billableStatus = resource.billableStatus;

    // Add resource data to the sheet
    sheet.getCell(idx + 3, 1).value = idx + 1;
    sheet.getCell(idx + 3, 1).border = border;

    sheet.getCell(idx + 3, 2).value = kekaId;
    sheet.getCell(idx + 3, 2).border = border;

    sheet.getCell(idx + 3, 3).value =
      billableStatus === 'billable' || billableStatus === 'both'
        ? `${resourceName} ($$)`
        : resourceName;
    sheet.getCell(idx + 3, 3).border = border;

    sheet.getCell(idx + 3, 4).value = department;
    sheet.getCell(idx + 3, 4).border = border;

    // Add work log data to the sheet
    resource.resourcesWorklog.forEach((worklog) => {
      const workDate = new Date(worklog.workDate).getDate();
      const { isWeekOff, isCompanyOff } = worklog;
      const hours = minutesToHours(worklog.minutes);
      const isPresent = worklog.isPresent;

      const mode = responseData.data.mode;

      const leaveDetailForCurrentWorkDate = resource.leaveDetails.find(
        (detail) =>
          convertToIndianZonedTime(new Date(detail.date)).toDateString() ===
          new Date(worklog.workDate).toDateString(),
      );

      //if is on leave then determine what type of leave is it whether full day leave or half day leave
      const typeOfLeave = getTypeOfLeave(
        leaveDetailForCurrentWorkDate?.leaveStatus,
      );

      const cell = sheet.getCell(idx + 3, workDate + 4);
      cell.border = border;

      // Determine the value for full day present based on the mode
      let fullDayPresent: number | 'P' | '';
      let halfDayPresent: number | 'H' | '';
      let fullDayLeave: number | 'L' | '';

      switch (mode) {
        case 'attendance':
          fullDayPresent = 'P';
          halfDayPresent = 'H';
          fullDayLeave = 'L';
          break;
        default:
          fullDayPresent = hours;
          halfDayPresent = hours === 0 ? '' : hours;
          fullDayLeave = hours === 0 ? '' : hours;
          break;
      }

      if (isWeekOff) {
        if (worklog.minutes > 0) {
          cell.value = fullDayPresent;
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FEF08A' },
          };
        } else {
          // Yellow color
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FEF08A' },
          };
        }
      } else if (isCompanyOff) {
        if (worklog.minutes > 0) {
          cell.value = fullDayPresent;
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '7DD3FC' },
          };
        } else {
          // Blue color
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '7DD3FC' },
          };
        }
      } else if (typeOfLeave === 'fullday') {
        // Red color
        cell.value = fullDayLeave;
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FCA5A5' },
        };
      } else if (typeOfLeave === 'halfday') {
        // Brown color(half day)
        cell.value = halfDayPresent;
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C8A58B' },
        };
      } else if (!isPresent && hours === 0) {
        // green color with no values
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E2E8F0' },
        };
      } else if (hours > 0) {
        // Green color
        cell.value = fullDayPresent;
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '86EFAC' },
        };
      } else {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '86EFAC' },
        };
      }
    });
    // Column "P"
    sheet.getCell(idx + 3, numDaysInMonth + 5).value = totalHours;
    sheet.getCell(idx + 3, numDaysInMonth + 5).border = border;

    sheet.getCell(idx + 3, numDaysInMonth + 6).value = totalDays;
    sheet.getCell(idx + 3, numDaysInMonth + 6).border = border;
  });

  function addLegend(excelSheet: ExcelJS.Worksheet, startRow: number): void {
    // Add note in the first row of the legend
    excelSheet.getCell(startRow, 1).value = 'Note';
    excelSheet.getCell(startRow, 1).font = { bold: true };

    // Present
    excelSheet.getCell(startRow + 1, 1).value = 'P';
    excelSheet.getCell(startRow + 1, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '86EFAC' }, // Green color
    };
    excelSheet.getCell(startRow + 1, 1).border = border;

    excelSheet.getCell(startRow + 1, 2).value = 'Present';
    excelSheet.getCell(startRow + 1, 2).font = { bold: true };
    excelSheet.getCell(startRow + 1, 2).border = border;

    // Leave
    excelSheet.getCell(startRow + 2, 1).value = 'L';
    excelSheet.getCell(startRow + 2, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FCA5A5' }, // Red color
    };
    excelSheet.getCell(startRow + 2, 1).border = border;

    excelSheet.getCell(startRow + 2, 2).value = 'Leave';
    excelSheet.getCell(startRow + 2, 2).font = { bold: true };
    excelSheet.getCell(startRow + 2, 2).border = border;

    // Half a day
    excelSheet.getCell(startRow + 3, 1).value = 'H';
    excelSheet.getCell(startRow + 3, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'C8A58B' }, // Brown color
    };
    excelSheet.getCell(startRow + 3, 1).border = border;

    excelSheet.getCell(startRow + 3, 2).value = 'Half day';
    excelSheet.getCell(startRow + 3, 2).font = { bold: true };
    excelSheet.getCell(startRow + 3, 2).border = border;

    // Weekend
    excelSheet.getCell(startRow + 4, 1).border = border;
    excelSheet.getCell(startRow + 4, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FEF08A' }, // Yellow color
    };

    excelSheet.getCell(startRow + 4, 2).value = 'Week off';
    excelSheet.getCell(startRow + 4, 2).font = { bold: true };
    excelSheet.getCell(startRow + 4, 2).border = border;

    // National holidays
    excelSheet.getCell(startRow + 5, 1).border = border;
    excelSheet.getCell(startRow + 5, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '7DD3FC' }, // Blue color
    };

    excelSheet.getCell(startRow + 5, 2).value = 'Holiday';
    excelSheet.getCell(startRow + 5, 2).font = { bold: true };
    excelSheet.getCell(startRow + 5, 2).border = border;

    // Yet to approve, pending worklogs
    excelSheet.getCell(startRow + 6, 1).border = border;
    excelSheet.getCell(startRow + 6, 1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E2E8F0' }, // Grey color
    };

    excelSheet.getCell(startRow + 6, 2).value = 'No worklogs';
    excelSheet.getCell(startRow + 6, 2).font = { bold: true };
    excelSheet.getCell(startRow + 6, 2).border = border;
  }

  const legendStartRow: number = (sheet.lastRow?.number ?? 200) + 3;
  addLegend(sheet, legendStartRow);

  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
}
