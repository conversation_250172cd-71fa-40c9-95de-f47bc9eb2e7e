import { redirect } from 'next/navigation';
import Image from 'next/image';
import { Audiowide } from 'next/font/google';
import { cn } from 'utils';
import { auth } from '@/config/auth';
import CodeCraftLogo from '@/assets/icons/cc_logo.svg';
import AssetManagementImage from '@/assets/images/asset-managment.jpeg';
import { GoogleSignInButton } from './google-sign-in-button';

const audiowide = Audiowide({
  weight: ['400'],
  subsets: ['latin'],
});

export default async function LoginPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}): Promise<React.JSX.Element> {
  const session = await auth();

  const redirectTo = searchParams.redirect || '/';

  if (session) {
    redirect(redirectTo);
  }

  return (
    <section className="grid max-h-screen w-full grid-cols-3">
      <div className="col-span-2 max-h-screen">
        <Image
          alt="Asset Management Image"
          className="max-h-full w-full"
          priority
          src={AssetManagementImage}
        />
      </div>
      <div className="flex flex-col items-center justify-between bg-slate-200 py-16">
        <h1
          className={cn(
            'text-3xl font-extrabold uppercase',
            audiowide.className,
          )}
        >
          <span className="text-primary-500">Asset</span> Management
        </h1>

        <div className="grid place-items-center gap-4">
          <p className=" font-bold text-slate-950">Sign In</p>
          <GoogleSignInButton />
        </div>

        <div>
          <span className="mb-2 block text-center text-sm font-bold text-slate-500">
            from
          </span>
          <CodeCraftLogo />
        </div>
      </div>
    </section>
  );
}
