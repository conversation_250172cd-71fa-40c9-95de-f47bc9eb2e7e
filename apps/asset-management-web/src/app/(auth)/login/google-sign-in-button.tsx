'use client';

import { signIn } from 'next-auth/react';
import { Button } from 'ui';
import { useState } from 'react';
import Google from '@/assets/icons/google.svg';

export function GoogleSignInButton(): React.JSX.Element {
  const [isDisabled, setIsDisabled] = useState(false);
  return (
    <Button
      className="my-auto flex items-center gap-2 text-base font-bold text-slate-200"
      isDisabled={isDisabled}
      onPress={() => {
        setIsDisabled(true);
        void signIn('google');
      }}
    >
      <span className="rounded-full bg-white p-1">
        <Google />
      </span>
      <span>Google</span>
    </Button>
  );
}
