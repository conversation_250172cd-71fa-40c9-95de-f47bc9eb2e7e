import React from 'react';
import { getStatus } from '@/services/status';
import { EditStatus } from './edit-status';

interface EditStatusPageProps {
  params: {
    id: string;
  };
}

export default async function EditStatusPage({
  params: { id },
}: EditStatusPageProps): Promise<React.JSX.Element> {
  const getStatusbyid = await getStatus(id);
  if (getStatusbyid.type === 'error') {
    return <>Id not found</>;
  }
  return <EditStatus id={id} initialValues={getStatusbyid.data} />;
}
