'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { editStatus } from '@/services/status';
import type { StatusRequestData, StatusResponse } from '@/types/status';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { statusRequestSchema } from '@/schemas/status';
import { StatusForm } from '../../StatusForm';

interface EditStatusProps {
  /**
   * Unique identifier for the status
   */
  id: string;
  /**
   * Initial values for the status form
   */
  initialValues: StatusResponse;
}

export function EditStatus({
  id,
  initialValues,
}: EditStatusProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<StatusRequestData>();

  async function updateStatus(formData: FormData): Promise<void> {
    const parsedResult = statusRequestSchema.safeParse(
      Object.fromEntries(formData),
    );

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const statusResponse = await editStatus(id, parsedResult.data);
    if (statusResponse.type === 'error') {
      if (statusResponse.errors.errorMessages) {
        statusResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Status updated successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Status</h1>
      <StatusForm
        errors={fieldErrors}
        initialValues={initialValues}
        onSubmit={updateStatus}
      />
    </div>
  );
}
