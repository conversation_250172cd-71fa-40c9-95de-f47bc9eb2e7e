import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { StatusForm } from './status-form';

const meta: Meta<typeof StatusForm> = {
  title: 'components/Status/StatusForm',
  component: StatusForm,
};

export default meta;

type Story = StoryObj<typeof StatusForm>;

const initialValues = {
  id: '1',
  name: 'Nazal',
  color: '#000000',
  note: '',
  assets: [],
};

export const StatusFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-1/2">
        <h1 className="asset-management-form-heading">Create Status</h1>
        <StatusForm />
      </div>
    );
  },
};

export const StatusFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-1/2">
        <h1 className="asset-management-form-heading">Edit Status</h1>
        <StatusForm initialValues={initialValues} />
      </div>
    );
  },
};
