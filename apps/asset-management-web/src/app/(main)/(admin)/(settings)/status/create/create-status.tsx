'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { createStatus } from '@/services/status';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { StatusRequestData } from '@/types/status';
import { statusRequestSchema } from '@/schemas/status';
import { StatusForm } from '../StatusForm';

export function CreateStatus(): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<StatusRequestData>();
  async function addStatus(formData: FormData): Promise<void> {
    const parsedResult = statusRequestSchema.safeParse(
      Object.fromEntries(formData),
    );

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const statusResponse = await createStatus(parsedResult.data);

    if (statusResponse.type === 'error') {
      if (statusResponse.errors.errorMessages) {
        statusResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Status created successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Status</h1>
      <StatusForm errors={fieldErrors} onSubmit={addStatus} />
    </div>
  );
}
