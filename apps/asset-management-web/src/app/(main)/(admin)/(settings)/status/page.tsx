import React from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from 'ui';
import { EditButton, CreateButton, DeleteButton } from '@/components/client';
import { getAllStatus } from '@/services/status';
import { statusRoutes } from '@/constants/routes';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { Routes, type TableHeading } from '@/types';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';

export default async function Status(): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Assets', className: 'text-right' },
    { title: 'Color', className: 'ml-3 md:ml-8' },
    { title: 'Notes' },
  ];

  const renderAction = await getActionColumnByRole('status', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  const statusResponse = await getAllStatus();

  if (statusResponse.type === 'error') {
    return <>Something Went wrong</>;
  }

  return (
    <section className="flex w-full flex-col gap-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Status</h1>
        <CreateButton href={statusRoutes.CREATE} label="Create Status" />
      </div>
      <Table className="asset-management-table">
        <TableHeader className="text-base">
          {tableHeaders.map((heading) => (
            <TableColumn className="last:text-center" key={heading.title}>
              <p className={heading.className}>{heading.title}</p>
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody>
          {statusResponse.data && statusResponse.data.length > 0 ? (
            statusResponse.data.map((status) => (
              <TableRow className="text-slate-700 [&>td]:py-4" key={status.id}>
                <TableCell className="first-letter:uppercase">
                  {status.name}
                </TableCell>
                <TableCell className="text-right">
                  {status.assets.length}
                </TableCell>
                <TableCell className="ml-3 flex items-center md:ml-8">
                  <span
                    className="mr-1 inline-block h-3 w-3 rounded-sm"
                    style={{ backgroundColor: status.color }}
                  />
                  {status.color}
                </TableCell>
                <TableCell className="max-w-[200px] first-letter:uppercase">
                  <HoverCard>
                    <div className=" flex flex-wrap">
                      <HoverCardTrigger className=" truncate">
                        {displayDataOrDefault(status.note)}
                      </HoverCardTrigger>
                    </div>
                    {status.note ? (
                      <HoverCardContent>{status.note}</HoverCardContent>
                    ) : null}
                  </HoverCard>
                </TableCell>
                {renderAction ? (
                  <TableCell className="flex items-center justify-center gap-3">
                    <EditButton href={`${statusRoutes.EDIT}/${status.id}`} />

                    <DeleteEntity
                      entityId={status.id}
                      entityName={status.name}
                      entityType={Routes.STATUS}
                    >
                      <DeleteButton isDisabled={status.assets.length > 0} />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </section>
  );
}
