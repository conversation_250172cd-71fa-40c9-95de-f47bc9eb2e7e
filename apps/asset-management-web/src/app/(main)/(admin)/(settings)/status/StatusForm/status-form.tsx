'use client';
import type { ChangeEvent } from 'react';
import React, { useState } from 'react';
import { SubmitButton, Label, Textarea, Input } from 'ui';
import type { StatusRequestData, StatusResponse } from '@/types/status';
import type { FieldErrors } from '@/types';

interface StatusFormProps {
  /**
   * Optional initial values for the form fields.
   * If provided, the form will be pre-populated with these values for editing.
   */
  initialValues?: StatusResponse;
  /**
   * Callback function triggered when the form is submitted.
   */
  onSubmit?: (formData: FormData) => Promise<void>;

  errors?: FieldErrors<StatusRequestData>;
}

/**
 * Form component for creating or editing a status item.
 */
export function StatusForm({
  initialValues,
  onSubmit,
  errors,
}: StatusFormProps): React.JSX.Element {
  const [selectedColor, setSelectedColor] = useState<string | undefined>(
    initialValues?.color ?? '#000000',
  );

  const handleColorChange = (event: ChangeEvent<HTMLInputElement>): void => {
    const newColor = event.target.value;
    setSelectedColor(newColor);
  };

  return (
    <form action={onSubmit} className="asset-management-form">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="name" required>
            Name
          </Label>
          <Input
            defaultValue={initialValues?.name ?? ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            name="name"
            placeholder="Enter name"
          />
          {errors?.name ? <p>{errors.name[0]}</p> : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="color" required>
            Color
          </Label>
          <div className="focus-within:ring-primary-400 mt-2 flex  items-center gap-2 rounded-md border border-solid border-slate-400  focus-within:border-transparent focus-within:ring-2">
            <Input
              className="!my-0 w-4/5 rounded-r-none border-0 focus:ring-0"
              isInvalidInput={Boolean(errors?.color)}
              name="color"
              placeholder="Pick color"
              readOnly
              required
              type="text"
              value={selectedColor}
            />
            <Input
              className="!my-0 h-9 w-1/5 cursor-pointer overflow-hidden rounded-md rounded-l-none border-none p-0"
              id="color"
              onChange={handleColorChange}
              placeholder="Pick color"
              required
              type="color"
              value={selectedColor}
            />
            {errors?.color ? <p>{errors.color[0]}</p> : null}
          </div>
        </fieldset>
      </div>

      <fieldset>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          defaultValue={initialValues?.note ?? ''}
          id="note"
          name="note"
          placeholder="Enter notes"
          rows={4}
        />
        {errors?.note ? <p>{errors.note[0]}</p> : null}
      </fieldset>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
