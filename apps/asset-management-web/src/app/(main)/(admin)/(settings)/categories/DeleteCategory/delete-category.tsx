'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogTrigger,
  <PERSON>dal,
  <PERSON>dalHeading,
  ModalOverlay,
} from 'ui';
import { toast } from 'sonner';
import { DeleteButton } from '@/components/client';
import { deleteCategory } from '@/services/categories';

interface DeleteButtonProps {
  /**
   * Specifies whether the delete icon button is disabled or enabled.
   */
  isDisabled?: boolean;
  /**
   * The unique identifier for the manufacturer item to be deleted.
   */
  id: string;
}

type CloseHandler = () => void;

/**
 * A delete button component that, when clicked, triggers a modal for deleting a manufacturer item.
 */

export function DeleteCategory({
  id,
  isDisabled,
}: DeleteButtonProps): React.JSX.Element {
  const handleClick = async (close: CloseHandler): Promise<void> => {
    const categoryDeleteResponse = await deleteCategory(id);

    if (categoryDeleteResponse.type === 'error') {
      if (categoryDeleteResponse.errors.errorMessages) {
        categoryDeleteResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }
    toast.success('Category deleted successfully');
    close();
  };
  return (
    <DialogTrigger>
      <DeleteButton isDisabled={isDisabled} />
      <ModalOverlay>
        <Modal className="delete-modal w-1/3">
          <Dialog>
            {({ close }: { close: CloseHandler }) => (
              <>
                <ModalHeading slot="title">Delete Category</ModalHeading>
                <p>Are you sure you want to delete this Category?</p>
                <div className="delete-modal-footer">
                  <Button onPress={close} variant="outlined">
                    Close
                  </Button>
                  <Button
                    intent="danger"
                    onPress={() => {
                      void handleClick(close);
                    }}
                  >
                    Delete
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
