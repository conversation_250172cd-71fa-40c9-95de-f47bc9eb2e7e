'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { createCategory } from '@/services/categories';
import { categoryFormSchema } from '@/schemas/categories';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { CategoryFormData } from '@/types/categories';
import { toUpperSnakeCase } from '@/utils/string-parser';
import { CategoriesForm } from '../CategoriesForm';

export function CreateCategory(): React.JSX.Element {
  const router = useRouter();

  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<CategoryFormData>();

  async function handleCategoryForm(formData: FormData): Promise<void> {
    formData.set(
      'typeOfCategory',
      toUpperSnakeCase(formData.get('typeOfCategory')?.toString() || ''),
    );
    const parsedCategory = categoryFormSchema.safeParse(
      Object.fromEntries(formData),
    );
    if (!parsedCategory.success) {
      setFieldErrors(parsedCategory.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const categoryResponse = await createCategory(parsedCategory.data);

    if (categoryResponse.type === 'error') {
      if (categoryResponse.errors.errorMessages) {
        categoryResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }
    toast.success('Category created successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Create Category</h1>
      <CategoriesForm errors={fieldErrors} onSubmit={handleCategoryForm} />
    </div>
  );
}
