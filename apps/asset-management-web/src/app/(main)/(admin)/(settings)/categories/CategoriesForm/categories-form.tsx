import { SubmitButton, Input, Label, Textarea } from 'ui';
import { categoryTypes, requiredFieldErrorMessage } from '@/constants';
import type { CategoryFormData } from '@/types/categories';
import type { FieldErrors } from '@/types';
import { PrimitiveComboBox } from '@/components/client';

export interface CategoriesFormProps {
  /**
   * Optional initial values for the form fields.
   * If provided, the form will be pre-populated with these values for editing.
   */
  initialValues?: CategoryFormData;
  /**
   * Callback function triggered when the form is submitted.
   */
  onSubmit?: (formData: FormData) => Promise<void>;
  /**
   *
   */
  errors?: FieldErrors<CategoryFormData>;
}

/**
 * Form component for creating or editing a category item.
 */
export function CategoriesForm({
  initialValues,
  onSubmit,
  errors,
}: CategoriesFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="name" required>
            Category Name
          </Label>
          <Input
            defaultValue={initialValues?.name ?? ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            maxLength={32}
            name="name"
            placeholder="Enter name"
          />
          {errors?.name ? <p>{errors.name[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="typeOfCategory" required>
            Category Type
          </Label>
          <PrimitiveComboBox
            disabled={Boolean(initialValues)}
            id="typeOfCategory"
            initialValue={initialValues?.typeOfCategory}
            isInvalidInput={Boolean(errors?.typeOfCategory)}
            name="typeOfCategory"
            placeholder="Choose category type"
            values={categoryTypes}
          />
          {errors?.typeOfCategory ? (
            <p className=" text-xs text-red-600">{requiredFieldErrorMessage}</p>
          ) : null}
        </fieldset>
      </div>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          defaultValue={initialValues?.note ?? ''}
          id="note"
          name="note"
          placeholder="Enter notes"
          rows={4}
        />
      </fieldset>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
