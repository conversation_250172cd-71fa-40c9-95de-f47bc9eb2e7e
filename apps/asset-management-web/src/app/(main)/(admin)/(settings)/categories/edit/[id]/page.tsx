import { getCategory } from '@/services/categories';
import type { ServerActionResponse } from '@/types';
import type { CategoryData } from '@/types/categories';
import { EditCategory } from './edit-categories'; 

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditCategoryPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categoryResponse: ServerActionResponse<CategoryData | null> =
    await getCategory(id);

  if (categoryResponse.type === 'error') {
    return <>Something went wrong</>;
  }
  if (!categoryResponse.data) {
    return <>Category not found</>;
  }

  return <EditCategory id={id} initialValues={categoryResponse.data} />;
}
