import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HoverCardTrigger,
  HoverCardContent,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import {
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { categoryRoutes } from '@/constants/routes';
import { getAllCategories } from '@/services/categories';
import {
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { convertUnderscoresToSpaces } from '@/utils/string-parser';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';

export default async function CategoryList({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const categories = await getAllCategories(queryParams);

  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Type Of Category' },
    { title: 'Note' },
  ];

  const renderAction = await getActionColumnByRole('categories', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  if (categories.type === 'error') {
    return <>Something Went wrong</>;
  }

  const totalPages = getNumberOfPages(categories.count, searchParams?.limit);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Categories</h1>
        <div className="flex gap-3">
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, type of category"
          />
          <CreateButton href={categoryRoutes.CREATE} label="Create Category" />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className="last:text-center" key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {categories.data && categories.data.length > 0 ? (
            categories.data.map((category) => (
              <TableRow key={category.id}>
                <TableCell>{category.name}</TableCell>
                <TableCell className="start-case">
                  {convertUnderscoresToSpaces(category.typeOfCategory)}
                </TableCell>
                <TableCell className="max-w-xs">
                  <HoverCard>
                    <div className=" flex flex-wrap">
                      <HoverCardTrigger className=" truncate">
                        {displayDataOrDefault(category.note)}
                      </HoverCardTrigger>
                    </div>
                    {category.note ? (
                      <HoverCardContent align="start">
                        {category.note}
                      </HoverCardContent>
                    ) : null}
                  </HoverCard>
                </TableCell>
                {renderAction ? (
                  <TableCell className="flex justify-center gap-3">
                    <EditButton
                      href={`${categoryRoutes.EDIT}/${category.id}`}
                    />
                    <DeleteEntity
                      entityId={category.id}
                      entityName={category.name}
                      entityType={Routes.CATEGORIES}
                    >
                      <DeleteButton />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={categories.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
