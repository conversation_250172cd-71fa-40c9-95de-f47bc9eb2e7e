import type { Meta, StoryObj } from '@storybook/react';
import type { TypeOfCategory } from '@/types/categories';
import { CategoriesForm } from './categories-form';

const initialValues = {
  id: '123',
  name: 'Company A',
  typeOfCategory: 'ACCESSORY' as TypeOfCategory,
  note: '',
};

const meta: Meta<typeof CategoriesForm> = {
  title: 'components/Categories/CategoriesForm',
  component: CategoriesForm,
};

export default meta;

type Story = StoryObj<typeof CategoriesForm>;

export const CategoriesFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="asset-management-form-heading">Create Category</h1>
        <CategoriesForm />
      </div>
    );
  },
};

export const CategoriesFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className=" asset-management-form-heading ">Edit Category</h1>
        <CategoriesForm initialValues={initialValues} />
      </div>
    );
  },
};
