'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { LocationRequestData } from '@/types/locations';
import { LocationRequestSchema } from '@/schemas/locations';
import { createLocation } from '@/services/locations';
import { LocationForm } from '../LocationForm';

export function CreateLocation(): React.JSX.Element {
  const router = useRouter();

  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<LocationRequestData>();

  async function handleLocationForm(formData: FormData): Promise<void> {
    const parsedLocation = LocationRequestSchema.safeParse(
      Object.fromEntries(formData),
    );
    if (!parsedLocation.success) {
      setFieldErrors(parsedLocation.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const locationResponse = await createLocation(parsedLocation.data);

    if (locationResponse.type === 'error') {
      if (locationResponse.errors.errorMessages) {
        locationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }
    toast.success('Location created successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Create Location</h1>
      <LocationForm errors={fieldErrors} onSubmit={handleLocationForm} />
    </div>
  );
}
