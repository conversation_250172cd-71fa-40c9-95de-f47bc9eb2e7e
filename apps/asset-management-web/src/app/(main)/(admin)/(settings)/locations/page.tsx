import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import {
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { locationRoutes } from '@/constants/routes';
import {
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { getNumberOfPages } from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { getAllLocations } from '@/services/locations';

export default async function LocationList({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const locations = await getAllLocations(queryParams);

  const tableHeaders: TableHeading[] = [{ title: 'Name' }];

  const renderAction = await getActionColumnByRole('locations', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  if (locations.type === 'error') {
    return <>Something Went wrong</>;
  }

  const totalPages = getNumberOfPages(locations.count, searchParams?.limit);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Locations</h1>
        <div className="flex gap-3">
          <Search className="w-[18.5rem]" placeholder="Search location name" />
          <CreateButton href={locationRoutes.CREATE} label="Create Location" />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className="last:text-center" key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {locations.data.length > 0 ? (
            locations.data.map((location) => (
              <TableRow key={location.id}>
                <TableCell>{location.name}</TableCell>
                {renderAction ? (
                  <TableCell className="flex justify-center gap-3">
                    <EditButton
                      href={`${locationRoutes.EDIT}/${location.id}`}
                    />
                    <DeleteEntity
                      entityId={location.id}
                      entityName={location.name}
                      entityType={Routes.LOCATION}
                    >
                      <DeleteButton />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={locations.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
