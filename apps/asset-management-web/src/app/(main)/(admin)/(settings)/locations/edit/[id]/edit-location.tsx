'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { LocationRequestData } from '@/types/locations';
import { LocationRequestSchema } from '@/schemas/locations';
import { editLocation } from '@/services/locations';
import { LocationForm } from '../../LocationForm';

interface EditLocationProps {
  /**
   * Unique identifier for the location
   */
  id: string;
  /**
   * Initial values for the location form
   */
  initialValues: LocationRequestData;
}

export function EditLocation({
  id,
  initialValues,
}: EditLocationProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<LocationRequestData>();
  async function handleLocationForm(formData: FormData): Promise<void> {
    const parsedLocation = LocationRequestSchema.safeParse(
      Object.fromEntries(formData),
    );
    if (!parsedLocation.success) {
      setFieldErrors(parsedLocation.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const locationResponse = await editLocation(id, parsedLocation.data);

    if (locationResponse.type === 'error') {
      if (locationResponse.errors.errorMessages) {
        locationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Location updated successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Edit Location</h1>
      <LocationForm
        errors={fieldErrors}
        initialValues={initialValues}
        onSubmit={handleLocationForm}
      />
    </div>
  );
}
