import { SubmitButton, Input, Label } from 'ui';
import type { FieldErrors } from '@/types';
import type { LocationRequestData } from '@/types/locations';

export interface LocationsFormProps {
  initialValues?: LocationRequestData;
  onSubmit?: (formData: FormData) => Promise<void>;
  errors?: FieldErrors<LocationRequestData>;
}

/**
 * Form component for creating or editing a location.
 */
export function LocationForm({
  initialValues,
  onSubmit,
  errors,
}: LocationsFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <fieldset>
          <Label htmlFor="name" required>
            Location Name
          </Label>
          <Input
            defaultValue={initialValues?.name ?? ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            maxLength={32}
            name="name"
            placeholder="Enter name"
          />
          {errors?.name ? <p>{errors.name[0]}</p> : null}
        </fieldset>
      </div>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
