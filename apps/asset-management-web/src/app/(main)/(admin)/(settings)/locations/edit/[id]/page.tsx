import type { ServerActionResponse } from '@/types';
import { getLocationById } from '@/services/locations';
import { LocationResponse } from '@/types/locations';
import { EditLocation } from './edit-location';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditLocationPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const locationResponse: ServerActionResponse<LocationResponse | null> =
    await getLocationById(id);

  if (locationResponse.type === 'error') {
    return <>Something went wrong</>;
  }
  if (!locationResponse.data) {
    return <>No Data found</>;
  }

  return <EditLocation id={id} initialValues={locationResponse.data} />;
}
