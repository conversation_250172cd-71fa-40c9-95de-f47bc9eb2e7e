import React from 'react';
import { Input, Label, SubmitButton } from 'ui';
import type {
  CustomFieldEntityType,
  FieldGroupInitialValueType,
  FieldGroupRequestType,
} from '@/types/custom-field';
import { type FieldErrors, type Option } from '@/types';
import { ENTITIES } from '@/constants';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';
import { CustomField } from '@/utils/custom-field';

/**
 * Props of the FieldGroupForm component.
 */
export interface FieldGroupFormProps {
  /**
   * Function to handle the submission of the form data.
   */
  handleSubmit?: (formData: FormData) => Promise<void>;

  /**
   * Initial custom fields associated with the field group.
   */
  initialCustomFields?: string[];

  /**
   * Initial values of the field group to pre-fill the form fields.
   */
  initialValues?: FieldGroupInitialValueType;

  /**
   * Errors related to the field group form fields.
   */
  errors?: FieldErrors<FieldGroupRequestType>;

  /**
   * The available custom fields from which the user can select.
   */
  customFields?: Option[] | undefined;
}
function FieldGroupForm({
  handleSubmit,
  initialValues,
  errors,
  customFields,
  initialCustomFields,
}: FieldGroupFormProps): React.JSX.Element {
  /**
   * Retrieves an array of custom field IDs that are in use within the all the Entities of the provided initial values object.
   */

  const customFieldIdsInUse: string[] = ENTITIES.flatMap((entity) => {
    // Check if initialValues exist and if it contains values for the current entity
    const entityValues =
      initialValues && (initialValues[entity] as CustomFieldEntityType[]);
    // If entityValues is false or empty, return an empty array
    if (!entityValues) {
      return [];
    }

    // For each entity, iterate over its custom fields, extract their IDs, and map them
    return entityValues.flatMap((item: CustomFieldEntityType) =>
      // Extract the keys  from the customFields.data object and map them to an array
      Object.keys(item.customFields.data ?? {}).flatMap((key) =>
        // Filter the initialValues.customFields array to find the corresponding custom field object by its ID
        (initialValues.customFields || [])
          .filter((field: CustomField) => field.id === key)
          // Map the filtered custom field object to its ID
          .map((field) => field.id),
      ),
    );
  });

  return (
    <form action={handleSubmit} className=" asset-management-form">
      <fieldset>
        <Label htmlFor="field-group-name" required>
          Field Group Name
        </Label>
        <Input
          defaultValue={initialValues?.fieldGroupName}
          id="field-group-name"
          isInvalidInput={Boolean(errors?.fieldGroupName)}
          name="fieldGroupName"
          placeholder="Enter field group name"
          type="text"
        />
        {errors?.fieldGroupName?.length ? (
          <p>{errors.fieldGroupName[0]}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="custom-fields">Custom Fields</Label>
        <MultiSelectComboBox
          disabledOptions={customFieldIdsInUse}
          id="custom-fields"
          name="customFieldIds"
          options={customFields ? customFields : []}
          placeholder="Choose custom fields"
          selectedValues={initialCustomFields}
        />
      </fieldset>
      <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
    </form>
  );
}

export default FieldGroupForm;
