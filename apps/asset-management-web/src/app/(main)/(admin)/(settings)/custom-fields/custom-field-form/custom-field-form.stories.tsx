import type { Meta, StoryObj } from '@storybook/react';
import CustomFieldForm from './custom-field-form';

const meta: Meta<typeof CustomFieldForm> = {
  title: 'components/custom-fields/custom-field-form',
  component: CustomFieldForm,
};

export default meta;

type Story = StoryObj<typeof CustomFieldForm>;

export const CustomFieldFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-2 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Create Custom Field</h1>
        <CustomFieldForm />
      </div>
    );
  },
};

export const CustomFieldFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-2 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Create Custom Field</h1>
        <CustomFieldForm
          initialValues={{
            id: '1',
            fieldName: 'Quantity',
            fieldType: 'Number',
            placeholderText: 'Enter Quantity',
            FieldGroups: [
              {
                id: '386e4b09-6b8a-4f5f-90b5-92262bcebd64',
                fieldGroupName: 'Hardware information',
                isDeleted: false,
                createdAt: '2024-04-17T04:59:57.654Z',
                updatedAt: '2024-04-17T04:59:57.654Z',
                assets: [
                  {
                    id: 'ff881299-9100-49a1-b1a7-6b2f78afb4cb',
                    customFields: {
                      fieldgroups: ['386e4b09-6b8a-4f5f-90b5-92262bcebd64'],
                      data: { '8f671c62-b1f6-48b8-a6ea-fe5695d0a1df': '16 gb' },
                    },
                  },
                ],
                consumables: [],
                licenses: [],
                accessories: [],
                appliances: [],
                policies: [],
              },
            ],
          }}
        />
      </div>
    );
  },
};
