'use client';
import React from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { CustomFieldRequestSchema } from '@/schemas/custom-field';
import { createCustomField } from '@/services/custom-field';
import type { CustomFieldRequestType } from '@/types/custom-field';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, invalidDataErrorMessage } from '@/constants';
import CustomFieldForm from '../custom-field-form/custom-field-form';

/**
 * Props for the CreateCustomFieldForm component.
 */
interface CreateCustomFieldFormProps {
  /** The field groups available for selection. */
  fieldGroups?: Option[] | undefined;
}

function CreateCustomFieldForm({
  fieldGroups,
}: CreateCustomFieldFormProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<CustomFieldRequestType>();

  /**
   * Handles the creation of a custom field.
   */
  async function handleCreateCustomField(formData: FormData): Promise<void> {
    formData.set(
      'fieldType',
      formData.get('fieldType')?.toString().toUpperCase() || '',
    );

    const fieldGroupIdsArray = Array.from(formData.entries())
      .filter(([name]) => name.startsWith('fieldGroupIds'))
      .map(([, value]) => value as string);

    const fieldGroupIdsObject = { fieldGroupIds: fieldGroupIdsArray };
    const parsedResult = CustomFieldRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      ...fieldGroupIdsObject,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      toast.error(invalidDataErrorMessage);
      return;
    }
    resetFieldErrors();

    const customFieldResponse = await createCustomField(parsedResult.data);

    if (customFieldResponse.type === 'error') {
      if (customFieldResponse.errors.errorMessages) {
        customFieldResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Custom Field created successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto my-2 h-fit w-[450px]">
      <h1 className="asset-management-form-heading">Create Custom Field</h1>
      <CustomFieldForm
        errors={fieldErrors}
        fieldGroups={fieldGroups}
        handleSubmit={handleCreateCustomField}
      />
    </div>
  );
}

export default CreateCustomFieldForm;
