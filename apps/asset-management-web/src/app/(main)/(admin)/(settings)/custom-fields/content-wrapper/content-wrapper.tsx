import { CreateButton } from '@/components/client';

interface ContentWrapperProps {
  /**
   * The heading for the content wrapper, typically representing field groups or custom fields.
   */
  heading: string;

  /**
   * The label for the CreateButton component, usually indicating the action to add new items.
   */
  label: string;

  /**
   * The href used in the CreateButton component, typically pointing to the page for adding new items.
   */
  pathName: string;

  /**
   * The children nodes representing the table containing lists of custom fields and field groups.
   */
  children: React.ReactNode;
}

/**
 * ContentWrapper component renders a container for content with a heading, optional label,
 * and tables of field group and custom field data.
 */
export function ContentWrapper({
  heading,
  label,
  pathName,
  children,
}: ContentWrapperProps): React.JSX.Element {
  return (
    <div className=" h-74 min-h-full ">
      <div className=" flex min-h-full flex-col space-y-4">
        <div className="flex flex-row justify-between">
          <span className=" -mt-1 text-xl font-semibold text-neutral-700">
            {heading}
          </span>
          <span>
            <CreateButton href={pathName} label={label} />
          </span>
        </div>

        {children}
      </div>
    </div>
  );
}
