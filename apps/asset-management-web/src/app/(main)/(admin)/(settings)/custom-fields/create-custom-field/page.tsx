import React from 'react';
import { getAllFieldGroupsOptions } from '@/services/custom-field';
import CreateCustomFieldForm from './create-custom-field';
import { ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED } from '@/constants';

async function CustomFieldCreatePage(): Promise<React.JSX.Element> {
  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }
  return <CreateCustomFieldForm fieldGroups={fieldGroups.data} />;
}

export default CustomFieldCreatePage;
