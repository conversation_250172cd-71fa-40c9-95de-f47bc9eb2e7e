'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { FieldGroupRequestSchema } from '@/schemas/custom-field';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { FieldGroupRequestType } from '@/types/custom-field';
import { createFieldGroup } from '@/services/custom-field';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, invalidDataErrorMessage } from '@/constants';
import FieldGroupForm from '../field-group-form/field-group-form';

/**
 * Props for the CreateFieldSetForm component.
 */
interface CreateFieldSetFormProps {
  /**
   * The available custom fields.
   */
  customFields?: Option[] | undefined;
}

/**
 * A form component for creating a new field group.
 */
function CreateFieldGroupForm({
  customFields,
}: CreateFieldSetFormProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<FieldGroupRequestType>();

  /**
   *  Handles the submission of the create field group form.
   */
  async function handleCreateFieldGroup(formData: FormData): Promise<void> {
    const customFieldIdsArray = Array.from(formData.entries())
      .filter(([name]) => name.startsWith('customFieldIds'))
      .map(([, value]) => value as string);

    const customFieldIdsObject = { customFieldIds: customFieldIdsArray };

    const parsedResult = FieldGroupRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      ...customFieldIdsObject,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      toast.error(invalidDataErrorMessage);
      return;
    }
    resetFieldErrors();

    const fieldGroupResponse = await createFieldGroup(parsedResult.data);

    if (fieldGroupResponse.type === 'error') {
      if (fieldGroupResponse.errors.errorMessages) {
        fieldGroupResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Field group created successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto my-2 h-fit w-[450px]">
      <h1 className="asset-management-form-heading">Create Field Group</h1>
      <FieldGroupForm
        customFields={customFields}
        errors={fieldErrors}
        handleSubmit={handleCreateFieldGroup}
      />
    </div>
  );
}

export default CreateFieldGroupForm;
