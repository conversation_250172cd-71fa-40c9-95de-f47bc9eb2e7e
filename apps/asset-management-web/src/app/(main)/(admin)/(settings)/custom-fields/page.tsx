import React from 'react';
import {
  Ho<PERSON><PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { DeleteButton, EditButton } from '@/components/client';
import { getAllCustomFields, getAllFieldGroups } from '@/services/custom-field';
import { Routes, type TableHeading } from '@/types';
import {
  customFieldEndpoints,
  fieldGroupEndpoints,
} from '@/constants/endpoints';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { ContentWrapper } from './content-wrapper/content-wrapper';
import { ENTITIES } from '@/constants';
import { displayDataOrDefault } from '@/utils/helper-functions';

/**
 * renders tables for field groups and custom fields within a content wrapper.
 */
async function CustomFieldPage(): Promise<React.JSX.Element> {
  const fieldSetTableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Quantity of Custom Fields' },
    { title: 'Used By Custom Fields' },
  ];

  const customFieldTableHeaders: TableHeading[] = [
    { title: 'Field Name' },
    { title: 'Input Type' },
    { title: 'Placeholder Texts' },
  ];

  const renderAction = await getActionColumnByRole('customFields', [
    'update',
    'delete',
  ]);
  renderAction ? fieldSetTableHeaders.push(renderAction) : null;
  renderAction ? customFieldTableHeaders.push(renderAction) : null;

  const customFields = await getAllCustomFields();
  if (customFields.type === 'error') {
    return (
      <>Failed to fetch custom fields at the moment plaese try again later</>
    );
  }

  const fieldGroups = await getAllFieldGroups();
  if (fieldGroups.type === 'error') {
    return (
      <>Failed to fetch field groups at the moment please try again later</>
    );
  }

  /**
   * Check if a custom field is associated with any field group
   */
  const deleteCustomField = (fieldName: string): boolean | undefined => {
    return fieldGroups.data?.some(
      (res) => res.customFields?.some((field) => field.fieldName === fieldName),
    );
  };

  /**
   * Check if a field group is associated with any asset
   * id - The ID of the field group to check
   * @returns True if the field group contains assets, otherwise false
   */
  const deleteFieldGroups = (id: string): boolean => {
    const fieldGroup = fieldGroups.data?.find((group) => group.id === id);
    return (
      Boolean(fieldGroup) &&
      ENTITIES.some((key) =>
        fieldGroup
          ? (fieldGroup[key as keyof typeof fieldGroup]?.length ?? 0) > 0
          : false,
      )
    );
  };

  return (
    <div className=" flex  flex-col  gap-6">
      <ContentWrapper
        heading="Field Groups"
        label=" Create Field Group"
        pathName={`${fieldGroupEndpoints.BASE}${fieldGroupEndpoints.CREATE_FIELD_GROUP}`}
      >
        <Table className="asset-management-table !max-h-52 overflow-auto">
          <TableHeader className="asset-management-table-heading  ">
            {fieldSetTableHeaders.map((heading) => {
              return (
                <TableColumn key={heading.title}>{heading.title}</TableColumn>
              );
            })}
          </TableHeader>
          <TableBody>
            {fieldGroups.data && fieldGroups.data.length > 0 ? (
              fieldGroups.data.map((fieldGroup) => (
                <TableRow key={fieldGroup.id}>
                  <TableCell>{fieldGroup.fieldGroupName}</TableCell>
                  <TableCell>{fieldGroup.customFields?.length}</TableCell>
                  <TableCell className="max-w-[100px] truncate">
                    <HoverCard>
                      <HoverCardTrigger>
                        {fieldGroup.customFields?.length
                          ? fieldGroup.customFields.map(
                              (customField, index) => (
                                <span key={customField.id}>
                                  <span>{customField.fieldName}</span>
                                  {fieldGroup.customFields
                                    ? index !==
                                        fieldGroup.customFields.length - 1 &&
                                      ', '
                                    : '-'}
                                </span>
                              ),
                            )
                          : '-'}
                      </HoverCardTrigger>
                      <HoverCardContent>
                        {fieldGroup.customFields?.length
                          ? fieldGroup.customFields.map(
                              (customField, index) => (
                                <span key={customField.id}>
                                  <span>{customField.fieldName}</span>
                                  {fieldGroup.customFields
                                    ? index !==
                                        fieldGroup.customFields.length - 1 &&
                                      ', '
                                    : '-'}
                                </span>
                              ),
                            )
                          : '-'}
                      </HoverCardContent>
                    </HoverCard>
                  </TableCell>
                  {renderAction ? (
                    <TableCell className="flex space-x-2">
                      <EditButton
                        className="text-primary-500 h-4 w-4"
                        href={`${fieldGroupEndpoints.BASE}${fieldGroupEndpoints.EDIT_FIELD_GROUP}/${fieldGroup.id}`}
                      />

                      <DeleteEntity
                        entityId={fieldGroup.id}
                        entityName={fieldGroup.fieldGroupName}
                        entityType={Routes.FIELDGROUP}
                      >
                        <DeleteButton
                          isDisabled={deleteFieldGroups(fieldGroup.id)}
                        />
                      </DeleteEntity>
                    </TableCell>
                  ) : null}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell className="text-lg font-bold text-slate-600">
                  No Data Found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </ContentWrapper>
      <ContentWrapper
        heading="Custom Fields"
        label="Create Custom Field"
        pathName={`${customFieldEndpoints.BASE}${customFieldEndpoints.CREATE_CUSTOM_FIELD}`}
      >
        <Table className="asset-management-table !max-h-52  overflow-auto">
          <TableHeader className="asset-management-table-heading">
            {customFieldTableHeaders.map((heading) => {
              return (
                <TableColumn key={heading.title}>{heading.title}</TableColumn>
              );
            })}
          </TableHeader>
          <TableBody>
            {customFields.data && customFields.data.length > 0 ? (
              customFields.data.map((customField) => (
                <TableRow key={customField.id}>
                  <TableCell>{customField.fieldName}</TableCell>
                  <TableCell className="start-case">
                    {customField.fieldType}
                  </TableCell>
                  <TableCell className="max-w-[10px]">
                    {displayDataOrDefault(customField.placeholderText)}
                  </TableCell>
                  {renderAction ? (
                    <TableCell className="flex space-x-2">
                      <EditButton
                        className="text-primary-500 h-4 w-4"
                        href={`${customFieldEndpoints.BASE}${customFieldEndpoints.EDIT_CUSTOM_FIELD}/${customField.id}`}
                      />

                      <DeleteEntity
                        entityId={customField.id}
                        entityName={customField.fieldName}
                        entityType={Routes.CUSTOMFIELD}
                      >
                        <DeleteButton
                          isDisabled={deleteCustomField(customField.fieldName)}
                        />
                      </DeleteEntity>
                    </TableCell>
                  ) : null}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell className="text-lg font-bold text-slate-600">
                  No Data Found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </ContentWrapper>
    </div>
  );
}

export default CustomFieldPage;
