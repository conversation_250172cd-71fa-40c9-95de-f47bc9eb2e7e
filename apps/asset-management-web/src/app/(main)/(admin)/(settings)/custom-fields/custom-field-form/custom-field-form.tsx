import React from 'react';
import { Input, Label, SubmitButton } from 'ui';
import { PrimitiveComboBox } from '@/components/client';
import type {
  CustomFieldEntityType,
  CustomFieldIntialValueType,
  CustomFieldRequestType,
  FieldGroupType,
} from '@/types/custom-field';
import { type FieldErrors, type Option } from '@/types';
import { ENTITIES } from '@/constants';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

export interface CustomFieldFormProps {
  /**
   * Function to handle the creation of custom fields.
   */
  handleSubmit?: (formData: FormData) => Promise<void>;

  /**
   * Initial values for the form fields.
   */
  initialValues?: CustomFieldIntialValueType;

  /**
   * Errors related to the form fields.
   */
  errors?: FieldErrors<CustomFieldRequestType>;

  /**
   * Options for field groups.
   */
  fieldGroups?: Option[] | undefined;

  /**
   * Initial selected field groups.
   */
  initialFieldGroups?: string[];
}

/**
 * CustomFieldForm component for creating or editing custom fields.
 */
function CustomFieldForm({
  handleSubmit,
  initialValues,
  errors,
  fieldGroups,
  initialFieldGroups,
}: CustomFieldFormProps): React.JSX.Element {
  /**
   * Checks if a field group has any assets.
   */
  const hasAssets = (fieldGroupId: string): boolean => {
    const fieldGroup: FieldGroupType | undefined =
      initialValues?.FieldGroups?.find(
        (group: FieldGroupType) => group.id === fieldGroupId,
      );

    if (!fieldGroup) {
      return false;
    }

    return isCustomFieldUsed() || false;
  };

  /**
   * Checks if the custom field is used in any of the assets within the field groups.
   */
  const isCustomFieldUsed = (): boolean => {
    return (
      initialFieldGroups?.some((fieldGroupId) => {
        const fieldGroup = initialValues?.FieldGroups?.find(
          (group: FieldGroupType) => group.id === fieldGroupId,
        );

        const customFieldsInUse: string[] = ENTITIES.flatMap((entity) =>
          (fieldGroup?.[entity] as CustomFieldEntityType[]).flatMap((asset) =>
            Object.keys(asset.customFields.data || {}),
          ),
        );

        return customFieldsInUse.includes(initialValues?.id || '');
      }) ?? false
    );
  };

  /**
   * Filters the list of field group IDs to include only those that have assets.
   */
  const disabledFieldGroups = initialFieldGroups?.filter((fieldGroupId) => {
    return hasAssets(fieldGroupId);
  });

  return (
    <form action={handleSubmit} className=" asset-management-form">
      <fieldset>
        <Label htmlFor="field-name" required>
          Field Name
        </Label>
        <Input
          defaultValue={initialValues?.fieldName}
          id="field-name"
          isInvalidInput={Boolean(errors?.fieldName)}
          name="fieldName"
          placeholder="Enter field name"
          type="text"
        />
        {errors?.fieldName?.length ? <p>{errors.fieldName[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="input-type" required>
          Input Type
        </Label>
        <PrimitiveComboBox
          disabled={isCustomFieldUsed()}
          id="input-type"
          initialValue={initialValues?.fieldType}
          isInvalidInput={Boolean(errors?.fieldType)}
          name="fieldType"
          placeholder="Choose input type"
          values={['Number', 'Text']}
        />
        {errors?.fieldType?.length ? <p>{errors.fieldType[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="placeholder-text">Placeholder Text</Label>
        <Input
          defaultValue={initialValues?.placeholderText}
          id="placeholder-text"
          isInvalidInput={Boolean(errors?.placeholderText)}
          name="placeholderText"
          placeholder="Enter placeholder text"
          type="text"
        />
        {errors?.placeholderText?.length ? (
          <p>{errors.placeholderText[0]}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="fieldGroupIds">Field Groups</Label>
        <MultiSelectComboBox
          disabledOptions={disabledFieldGroups}
          id="fieldGroupIds"
          name="fieldGroupIds"
          options={fieldGroups ? fieldGroups : []}
          placeholder="Choose field groups"
          selectedValues={initialFieldGroups}
        />
      </fieldset>
      <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
    </form>
  );
}

export default CustomFieldForm;
