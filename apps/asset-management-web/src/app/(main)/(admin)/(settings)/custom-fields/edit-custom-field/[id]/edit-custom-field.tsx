'use client';
import React from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { CustomFieldRequestSchema } from '@/schemas/custom-field';
import { editCustomField } from '@/services/custom-field';
import type {
  CustomFieldIntialValueType,
  CustomFieldRequestType,
} from '@/types/custom-field';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, invalidDataErrorMessage } from '@/constants';
import CustomFieldForm from '../../custom-field-form/custom-field-form';

interface EditCustomFieldProps {
  /**
   * The ID of the custom field being edited.
   */
  id: string;

  /**
   * Initial values of the custom field to pre-fill the form fields.
   */
  initialValues?: CustomFieldIntialValueType;

  /**
   * The available field groups from which the user can select.
   */
  fieldGroups: Option[] | undefined;

  /**
   * The initial field groups associated with the custom field.
   */
  initialFieldGroups: string[];
}

function EditCustomFieldForm({
  id,
  initialValues,
  fieldGroups,
  initialFieldGroups,
}: EditCustomFieldProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<CustomFieldRequestType>();

  /**
   * Handles the editing of a custom field based on the provided form data.
   */
  async function handleEditCustomField(formData: FormData): Promise<void> {
    formData.set(
      'fieldType',
      formData.get('fieldType')?.toString().toUpperCase() || '',
    );
    const fieldGroupIdsArray = Array.from(formData.entries())
      .filter(([name]) => name.startsWith('fieldGroupIds'))
      .map(([, value]) => value as string);

    const fieldGroupIdsObject = { fieldGroupIds: fieldGroupIdsArray };

    const parsedResult = CustomFieldRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      ...fieldGroupIdsObject,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      toast.error(invalidDataErrorMessage);
      return;
    }
    resetFieldErrors();

    const customFieldResponse = await editCustomField(parsedResult.data, id);

    if (customFieldResponse.type === 'error') {
      if (customFieldResponse.errors.errorMessages) {
        customFieldResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Custom Field edited successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto my-2 h-fit w-[450px]">
      <h1 className="asset-management-form-heading">Edit Custom Field</h1>
      <CustomFieldForm
        errors={fieldErrors}
        fieldGroups={fieldGroups}
        handleSubmit={handleEditCustomField}
        initialFieldGroups={initialFieldGroups}
        initialValues={initialValues}
      />
    </div>
  );
}

export default EditCustomFieldForm;
