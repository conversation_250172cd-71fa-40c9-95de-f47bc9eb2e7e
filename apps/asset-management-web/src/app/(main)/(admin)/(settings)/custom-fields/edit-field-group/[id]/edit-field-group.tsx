'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type {
  FieldGroupInitialValueType,
  FieldGroupRequestType,
} from '@/types/custom-field';
import { editFieldGroup } from '@/services/custom-field';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { FieldGroupRequestSchema } from '@/schemas/custom-field';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, invalidDataErrorMessage } from '@/constants';
import FieldGroupForm from '../../field-group-form/field-group-form';

/**
 * Props for the EditFieldGroupForm component.
 */
interface EditFieldGroupProps {
  /**
   * The ID of the field group to be edited.
   */
  id: string;

  /**
   * Initial values of the field group to pre-fill the form fields.
   */
  initialValues?: FieldGroupInitialValueType;

  /**
   * The available custom fields from which the user can select.
   */
  customFields: Option[] | undefined;

  /**
   * Initial custom fields associated with the field group.
   */
  initialCustomFields: string[];
}

function EditFieldGroupForm({
  id,
  initialValues,
  customFields,
  initialCustomFields,
}: EditFieldGroupProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<FieldGroupRequestType>();

  /**
   * Handles the submission of the edit field group form.
   * */
  async function handleEditFieldGroup(formData: FormData): Promise<void> {
    const customFieldIdsArray = Array.from(formData.entries())
      .filter(([name]) => name.startsWith('customFieldIds'))
      .map(([, value]) => value as string);

    const customFieldIdsObject = { customFieldIds: customFieldIdsArray };

    const parsedResult = FieldGroupRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      ...customFieldIdsObject,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      toast.error(invalidDataErrorMessage);
      return;
    }
    resetFieldErrors();

    const fieldGroupResponse = await editFieldGroup(parsedResult.data, id);

    if (fieldGroupResponse.type === 'error') {
      if (fieldGroupResponse.errors.errorMessages) {
        fieldGroupResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Field Group edited successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto my-2 h-fit w-[450px]">
      <h1 className="asset-management-form-heading">Edit Field Group</h1>
      <FieldGroupForm
        customFields={customFields}
        errors={fieldErrors}
        handleSubmit={handleEditFieldGroup}
        initialCustomFields={initialCustomFields}
        initialValues={initialValues}
      />
    </div>
  );
}

export default EditFieldGroupForm;
