import type { Meta, StoryObj } from '@storybook/react';
import FieldGroupForm from './field-group-form';

const meta: Meta<typeof FieldGroupForm> = {
  title: 'components/custom-fields/field-group-form',
  component: FieldGroupForm,
};

export default meta;

type Story = StoryObj<typeof FieldGroupForm>;

export const FieldGroupFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-2 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Create Field Group</h1>
        <FieldGroupForm />
      </div>
    );
  },
};

export const FieldGroupFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-2 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Create Field Group</h1>
        <FieldGroupForm
          initialValues={{
            id: '1',
            fieldGroupName: 'Purchase History',
          }}
        />
      </div>
    );
  },
};
