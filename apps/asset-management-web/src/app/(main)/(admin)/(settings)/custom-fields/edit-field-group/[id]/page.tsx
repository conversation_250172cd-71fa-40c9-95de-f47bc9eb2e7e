import React from 'react';
import {
  getAllCustomFieldOptions,
  getFieldGroup,
} from '@/services/custom-field';
import EditFieldGroupForm from './edit-field-group';

interface EditProps {
  params: {
    id: string;
  };
}
async function EditFieldGroupPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const fieldGroupDetails = await getFieldGroup(id);
  if (fieldGroupDetails.type === 'error') {
    return <>Failed to fetch field group details</>;
  }

  const customFields = await getAllCustomFieldOptions();

  if (customFields.type === 'error') {
    return <>Not able to load custom fields. Please try again!</>;
  }

  let customFieldIds: string[] = [];
  if (fieldGroupDetails.data.customFields) {
    customFieldIds = fieldGroupDetails.data.customFields.map(
      (customField) => customField.id,
    );
  }

  return (
    <EditFieldGroupForm
      customFields={customFields.data}
      id={id}
      initialCustomFields={customFieldIds}
      initialValues={fieldGroupDetails.data}
    />
  );
}

export default EditFieldGroupPage;
