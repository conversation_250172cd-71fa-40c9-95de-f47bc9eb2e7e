import React from 'react';
import {
  getCustomField,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import EditCustomFieldForm from './edit-custom-field';

interface EditProps {
  params: {
    id: string;
  };
}

async function CustomFieldEditPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const customFieldDetails = await getCustomField(id);
  if (customFieldDetails.type === 'error') {
    return <>Failed to fetch custom field details</>;
  }

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>Not able to load fieldsets. Please try again!</>;
  }

  let fieldGroupIds: string[] = [];
  if (customFieldDetails.data.FieldGroups) {
    fieldGroupIds = customFieldDetails.data.FieldGroups.map(
      (fieldgroup) => fieldgroup.id,
    );
  }

  return (
    <EditCustomFieldForm
      fieldGroups={fieldGroups.data}
      id={id}
      initialFieldGroups={fieldGroupIds}
      initialValues={customFieldDetails.data}
    />
  );
}

export default CustomFieldEditPage;
