import React from 'react';
import { getAllCustomFieldOptions } from '@/services/custom-field';
import CreateFieldGroupForm from './create-field-group';

export default async function CreateFieldGroupPage(): Promise<React.JSX.Element> {
  const customFields = await getAllCustomFieldOptions();

  if (customFields.type === 'error') {
    return <>Not able to load cutsom fields. Please try again!</>;
  }
  return <CreateFieldGroupForm customFields={customFields.data} />;
}
