import {
  Label,
  Input,
  SubmitButton,
  Textarea,
  ImageInputWithPreview,
} from 'ui';
import type { AssetModelAlterData } from '@/types/asset-model';
import { OptionsComboBox } from '@/components/client';
import type { FieldErrors } from '@/types';
import { allowedImageType } from '@/constants';
import type { BaseFormData } from '../types';

interface AssetModelsFormProps extends BaseFormData {
  /**
   * Initial value for the form fields.
   */
  initialValues?: AssetModelAlterData;
  /**
   * Callback function triggered on form submission.
   */
  onSubmit?: (formData: FormData) => Promise<void>;
  errors?: FieldErrors<AssetModelAlterData>;
  /**
   * Optional callback function to update the status of the defualt image is provided or not.
   */
  setIsImage?: (isImage: boolean) => void;
}

export function AssetModelForm({
  initialValues,
  onSubmit,
  categories,
  manufacturers,
  errors,
  setIsImage,
}: AssetModelsFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="modelName" required>
                Model Name
              </Label>
              <Input
                defaultValue={initialValues?.modelName || ''}
                id="modelName"
                isInvalidInput={Boolean(errors?.modelName)}
                name="modelName"
                placeholder="e.g. Bluetooth Keyboard"
                type="text"
              />
              {errors?.modelName?.length ? <p>{errors.modelName[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="modelNumber">Model Number</Label>
              <Input
                defaultValue={initialValues?.modelNumber || ''}
                id="modelNumber"
                name="modelNumber"
                placeholder="e.g. 1233221"
                type="text"
              />
              {errors?.modelNumber?.length ? (
                <p>{errors.modelNumber[0]}</p>
              ) : null}
            </fieldset>
          </div>
          <fieldset>
            <Label htmlFor="categoryId">Category</Label>
            <OptionsComboBox
              id="categoryId"
              initialValue={initialValues?.categoryId}
              name="categoryId"
              options={categories}
            />
          </fieldset>
          <fieldset>
            <Label htmlFor="manufacturerId">Manufacturer</Label>
            <OptionsComboBox
              id="manufacturerId"
              initialValue={initialValues?.manufacturerId}
              name="manufacturerId"
              options={manufacturers}
            />
          </fieldset>
        </div>
      </div>
      <div>
        <h2>Additional Information</h2>
        <div className="space-y-3">
          <fieldset>
            <Label htmlFor="note">Notes</Label>
            <Textarea
              defaultValue={initialValues?.note || ''}
              id="note"
              name="note"
              placeholder="Include any additional information in this note."
              rows={4}
              className="transition-height mt-1 block min-h-[35px] w-full ease-in-out"
            />
            {errors?.note?.length ? <p>{errors.note[0]}</p> : null}
          </fieldset>
          <fieldset>
            <Label htmlFor="assetModelImageUrl">Select Image</Label>
            <ImageInputWithPreview
              accept={allowedImageType.join(',')}
              className="mt-2"
              defaultValue={initialValues?.assetModelImageUrl || ''}
              id="assetModelImageUrl"
              name="assetModelImageUrl"
              setIsImage={setIsImage}
            />
            {errors?.assetModelImageUrl?.length ? (
              <p>{errors.assetModelImageUrl[0]}</p>
            ) : null}
          </fieldset>
        </div>
      </div>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
