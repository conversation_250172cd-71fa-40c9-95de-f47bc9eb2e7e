import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from 'ui';
import { getAssetModel } from '@/services/asset-models';
import { assetModelRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { AssetModelDetailsInfoTable } from './AssetModelDetailsInfo';
import { AssetModelHistory } from './AssetModelHistory/asset-model-history';
import { DeleteAssetsModelButton } from './delete-assets-model-button';
import { AssetModelInfo } from './AssetModelInfo/asset-model-info';

interface AssetModelDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function AssetModelDetailsPage({
  params: { id },
}: AssetModelDetailsPageProps): Promise<React.JSX.Element> {
  const assetModelInfo = await getAssetModel(id);
  if (assetModelInfo.type === 'error') {
    return (
      <>
        Something went wrong!, Could not load asset details page. Please try
        again
      </>
    );
  }

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {assetModelInfo.data.modelName || ''}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={[]}
            entityId={id}
            entityName={assetModelInfo.data.modelName}
            routes={assetModelRoutes}
          />
          <DeleteAssetsModelButton
            id={id}
            name={assetModelInfo.data.modelName}
          />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="assets">Assets</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <AssetModelInfo data={assetModelInfo.data} />
          </TabsContent>
          <TabsContent value="assets">
            <AssetModelDetailsInfoTable id={id} />
          </TabsContent>
          <TabsContent value="history">
            <AssetModelHistory id={id} />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
