'use client';
import { useRouter } from 'next/navigation';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { OutlinedDeleteButton } from '@/components/client';
import { Routes } from '@/types';

export function DeleteAssetsModelButton({
  id,
  name,
}: {
  id: string;
  name: string;
}): React.JSX.Element {
  const router = useRouter();
  return (
    <DeleteEntity
      entityId={id}
      entityName={name}
      entityType={Routes.ASSETMODEL}
      onDelete={() => {
        router.push('/assets-model');
      }}
    >
      <OutlinedDeleteButton />
    </DeleteEntity>
  );
}
