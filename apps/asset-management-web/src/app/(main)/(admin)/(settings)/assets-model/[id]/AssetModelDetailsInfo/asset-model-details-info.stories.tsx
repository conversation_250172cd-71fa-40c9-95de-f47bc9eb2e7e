import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AssetModelDetailsInfoTable } from './asset-model-details-info';

const meta: Meta<typeof AssetModelDetailsInfoTable> = {
  title: 'components/AssetModel/AssetModelDetailsInfoTable',
  component: AssetModelDetailsInfoTable,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AssetModelDetailsInfoTable>;

export const DefaultAssignmentInfoTable: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
