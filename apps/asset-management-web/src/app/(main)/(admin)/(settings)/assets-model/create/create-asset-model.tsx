'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { FormSchema } from '@/schemas/asset-model';
import { createAssetModel } from '@/services/asset-models';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type {
  AssetModelAlterData,
  AssetModelAlterResponseData,
} from '@/types/asset-model';
import { EntityType, type ServerActionResponse } from '@/types';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { AssetModelForm } from '../AssetModelForm';
import type { BaseFormData } from '../types';

export function CreateAssetModel({
  categories,
  manufacturers,
}: BaseFormData): React.JSX.Element {
  const router = useRouter();

  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssetModelAlterData>();

  const addAssetModel = async (formData: FormData): Promise<void> => {
    const { assetModelImageUrl, ...assetModelDetials } =
      Object.fromEntries(formData);
    const parsedData = FormSchema.safeParse({
      ...assetModelDetials,
      assetModelImageUrl: '',
    });

    if (!parsedData.success) {
      setFieldErrors(parsedData.error.flatten().fieldErrors);
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    resetFieldErrors();

    let uploadedAssetModelImageUrl = '';
    if (
      isImage &&
      assetModelImageUrl instanceof File &&
      assetModelImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', assetModelImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.AssetModel,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }

      uploadedAssetModelImageUrl = generateCloudFrontUrl(
        EntityType.AssetModel,
        imageUploadResponse.data.fileName,
      );
    }

    const createAssetModelData = {
      ...parsedData.data,
      assetModelImageUrl: uploadedAssetModelImageUrl,
    };

    const assetModelResponse: ServerActionResponse<AssetModelAlterResponseData> =
      await createAssetModel(createAssetModelData);
    if (assetModelResponse.type === 'error') {
      if (assetModelResponse.errors.errorMessages) {
        assetModelResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(
        'Something went wrong!. Could not create asset. Please try again',
      );
      return;
    }

    toast.success('Asset model created successfully');
    router.back();
  };

  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Asset Model</h1>
      <AssetModelForm
        categories={categories}
        errors={fieldErrors}
        manufacturers={manufacturers}
        onSubmit={addAssetModel}
        setIsImage={setIsImage}
      />
    </div>
  );
}
