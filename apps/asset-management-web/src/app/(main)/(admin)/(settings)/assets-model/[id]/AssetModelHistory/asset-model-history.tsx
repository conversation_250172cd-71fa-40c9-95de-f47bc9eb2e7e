import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionTrigger,
  AccordionItem,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getAssetModelHistoryInfo } from '@/services/asset-models';
import { parseDate } from '@/utils/date-utils';
import { generateUpdateText } from '@/utils/helper-functions';

const tableHeaders = ['Actions Performed', 'Date', 'Action By'];

interface AssetModelHistoryProps {
  id: string;
}

export async function AssetModelHistory({
  id,
}: AssetModelHistoryProps): Promise<React.JSX.Element> {
  const assetModelHistoryInfo = await getAssetModelHistoryInfo(id);

  if (assetModelHistoryInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {assetModelHistoryInfo.data.map((history) => (
          <TableRow key={history.log.userId}>
            <TableCell>
              {(() => {
                switch (history.action) {
                  case 'CREATED':
                    return (
                      <p>
                        Asset Model{' '}
                        <span className="font-semibold">created</span>
                      </p>
                    );
                  case 'UPDATED':
                    return history.log.updatedFields &&
                      history.log.updatedFields.length > 0 ? (
                      <Accordion className="w-72">
                        <AccordionItem
                          className="text-xs"
                          value={`item-${history.log.userId}`}
                        >
                          <AccordionTrigger>
                            <p>
                              <span className="font-semibold">Updated</span>(
                              {history.log.updatedFields.length} fields)
                            </p>
                          </AccordionTrigger>
                          <AccordionContent className="space-y-1">
                            {history.log.updatedFields.map(generateUpdateText)}
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <p>No fields updated</p>
                    );

                  default:
                    return null;
                }
              })()}
            </TableCell>
            <TableCell>
              {parseDate('MMM dd, yyyy')(history.date)}
            </TableCell>
            <TableCell>{history.log.name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
