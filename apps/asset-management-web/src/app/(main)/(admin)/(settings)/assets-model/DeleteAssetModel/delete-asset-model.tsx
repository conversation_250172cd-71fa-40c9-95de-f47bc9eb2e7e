'use client';

import {
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dalHeading,
  ModalOverlay,
  Button,
} from 'ui';
import React from 'react';
import { toast } from 'sonner';
import { DeleteButton } from '@/components/client';
import { deleteAssetModel } from '@/services/asset-models';

interface DeleteButtonProps {
  /**
   * Specifies whether the delete icon button is disabled or enabled.
   */
  isDisabled?: boolean;
  /**
   * The unique identifier for the AssetModel item to be deleted.
   */
  id: string;
}

type CloseHandler = () => void;

/**
 * A delete button component that, when clicked, triggers a modal for deleting a AssetModel item.
 */

export function DeleteAssetModel({
  id,
  isDisabled,
}: DeleteButtonProps): React.JSX.Element {
  const handleClick = async (close: CloseHandler): Promise<void> => {
    const modelDeleteResponse = await deleteAssetModel(id);

    if (modelDeleteResponse.type === 'error') {
      if (modelDeleteResponse.errors.errorMessages) {
        modelDeleteResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(
        'Something went wrong, Could not delete asset model. Please try again!',
      );
    }
    toast.success('Asset model deleted successfully');
    close();
  };
  return (
    <DialogTrigger>
      <DeleteButton isDisabled={isDisabled} />
      <ModalOverlay>
        <Modal className="delete-modal w-1/3">
          <Dialog>
            {({ close }: { close: CloseHandler }) => (
              <>
                <ModalHeading slot="title">Delete AssetModel</ModalHeading>
                <p>Are you sure you want to delete this AssetModel?</p>
                <div className="delete-modal-footer">
                  <Button onPress={close} variant="outlined">
                    Close
                  </Button>
                  <Button
                    intent="danger"
                    onPress={() => {
                      void handleClick(close);
                    }}
                  >
                    Delete
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
