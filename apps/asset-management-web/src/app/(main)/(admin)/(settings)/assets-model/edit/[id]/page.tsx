import { getAssetModel } from '@/services/asset-models';
import { getAllCategoriesOptions } from '@/services/categories';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { EditAssetModel } from './edit-asset-model';
import { AssetModelAlterData } from '@/types/asset-model';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditAssetModelPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('ASSET');
  const manufacturers = await getAllManufacturersOptions();
  const AssetModelInfo = await getAssetModel(id);

  if (AssetModelInfo.type === 'error') {
    return (
      <>Something went wrong, could not load edit page. Please try again!</>
    );
  }
  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }

  const { manufacturer, category, ...rest } = AssetModelInfo.data;

  const intitialValues: AssetModelAlterData = {
    ...rest,
    categoryId: category?.id ?? undefined,
    manufacturerId: manufacturer?.id ?? undefined,
  };
  return (
    <EditAssetModel
      categories={categories.data}
      id={id}
      initialValues={intitialValues}
      manufacturers={manufacturers.data}
    />
  );
}
