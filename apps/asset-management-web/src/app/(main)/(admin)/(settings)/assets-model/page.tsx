import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { Codesandbox } from 'lucide-react';
import Link from 'next/link';
import { getAllAssetModels } from '@/services/asset-models';
import { assetModelRoutes } from '@/constants/routes';
import {
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import {
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { Pagination } from '@/components/Pagination/pagination';
import { initialRowsPerPage } from '@/constants';
import {
  getNumberOfPages,
  displayDataOrDefault,
} from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';

export default async function AssetModelPage({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  const renderAction = await getActionColumnByRole('assetsModel', [
    'update',
    'delete',
  ]);

  const tableHeaders: TableHeading[] = [
    { title: 'Image' },
    { title: 'Name' },
    { title: 'Category' },
    { title: 'Model No' },
    { title: 'Manufacturer' },
  ];

  renderAction ? tableHeaders.push(renderAction) : null;
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: parseInt(searchParams?.page ?? '1'),
    limit: parseInt(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const assetModels = await getAllAssetModels(queryParams);

  if (assetModels.type === 'error') {
    return (
      <>
        Something went wrong. Could not load Asset model list. Please try again
      </>
    );
  }
  const totalPages = getNumberOfPages(assetModels.count, searchParams?.limit);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-700">Asset Models</h1>
        <div className="flex gap-3">
          <Search
            className="w-72"
            placeholder="Search name, category, model..."
          />
          <CreateButton
            href={assetModelRoutes.CREATE}
            label="Create Asset Model"
          />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn key={heading.title}>{heading.title}</TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {assetModels.data && assetModels.data.length > 0 ? (
            assetModels.data.map((assetModel) => (
              <TableRow key={assetModel.modelName}>
                <TableCell className="text-center">
                  {assetModel.assetModelImageUrl ? (
                    <img
                      alt={assetModel.modelName}
                      className="img-cell-style"
                      height={10}
                      src={assetModel.assetModelImageUrl}
                      width={30}
                    />
                  ) : (
                    <Codesandbox className="h-7 w-7" />
                  )}
                </TableCell>
                <TableCell>
                  <Link
                    className="text-primary-600"
                    href={`${assetModelRoutes.MAIN}/${assetModel.id}`}
                  >
                    {assetModel.modelName}
                  </Link>
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(assetModel.category?.name)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(assetModel.modelNumber)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(assetModel.manufacturer?.name)}
                </TableCell>
                {renderAction ? (
                  <TableCell className="flex gap-2">
                    <EditButton
                      href={`${assetModelRoutes.EDIT}/${assetModel.id}`}
                    />
                    <DeleteEntity
                      entityId={assetModel.id}
                      entityName={assetModel.modelName}
                      entityType={Routes.ASSETMODEL}
                    >
                      <DeleteButton
                        isDisabled={Boolean(assetModel.totalAssets)}
                      />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={assetModels.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
