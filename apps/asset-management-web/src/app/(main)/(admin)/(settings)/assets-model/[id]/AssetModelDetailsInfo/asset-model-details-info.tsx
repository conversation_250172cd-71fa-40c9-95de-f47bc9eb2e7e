import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getAssetModeldetailsInfo } from '@/services/asset-models';
import { parseDate } from '@/utils/date-utils';

const tableHeaders = [
  'Name',
  'Tag',
  'SL. NO',
  'Status',
  'Location',
  'Warranty',
  'EOW',
];

interface AssetModelCloneInfoTableProps {
  id: string;
}
export async function AssetModelDetailsInfoTable({
  id,
}: AssetModelCloneInfoTableProps): Promise<React.JSX.Element> {
  const assetModelCloneInfo = await getAssetModeldetailsInfo(id);
  return (
    <Table className="asset-management-table-md rounded-none shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {assetModelCloneInfo.map((assetModel) => (
          <TableRow key={assetModel.id}>
            <TableCell>{assetModel.assetName}</TableCell>
            <TableCell>{assetModel.assetTag}</TableCell>
            <TableCell>{assetModel.serialNumber}</TableCell>
            <TableCell>{assetModel.assetStausId}</TableCell>
            <TableCell>{assetModel.location}</TableCell>
            <TableCell className="text-right">{assetModel.warranty}</TableCell>
            <TableCell>
              {assetModel.endOfLife
                ? parseDate('MMM dd, yyyy')(assetModel.endOfLife)
                : '-'}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
