'use client';
import React from 'react';
import type { AssetModelGetResponseData } from '@/types/asset-model';
import { InfoItem } from '@/components/InfoItems/info-item';

interface AssetModelInfoProps {
  /** Details about a particular Asset model. */
  data: AssetModelGetResponseData;
}

/**
 * This is a React component that displays detailed information about an asset model, including model name, model number, manufacturer, category, and notes.
 */
export function AssetModelInfo({
  data,
}: AssetModelInfoProps): React.JSX.Element {
  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        <InfoItem name="Model Name" value={data.modelName} />
        <InfoItem name="Model Number" value={data.modelNumber} />
        <InfoItem name="Manufacturer" value={data.manufacturer?.name} />
        <InfoItem name="Category" value={data.category?.name} />
        <InfoItem name="Note" value={data.note} />
      </div>
      {data.assetModelImageUrl ? (
        <div className="flex w-1/4 flex-shrink-0 justify-end">
          <img
            alt="asset-model"
            className="h-[200px] w-auto rounded object-cover shadow"
            src={data.assetModelImageUrl}
            width={400}
            height={400}
          />
        </div>
      ) : null}
    </div>
  );
}
