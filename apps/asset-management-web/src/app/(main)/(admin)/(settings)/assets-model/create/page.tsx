import { getAllCategoriesOptions } from '@/services/categories';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { CreateAssetModel } from './create-asset-model';

export default async function CreateAccessoryPage(): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('ASSET');
  const manufacturers = await getAllManufacturersOptions();
  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }
  return (
    <CreateAssetModel
      categories={categories.data}
      manufacturers={manufacturers.data}
    />
  );
}
