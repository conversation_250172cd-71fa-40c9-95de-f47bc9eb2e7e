import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AssetModelHistory } from './asset-model-history';

const meta: Meta<typeof AssetModelHistory> = {
  title: 'components/AssetModel/AssetModelHistory',
  component: AssetModelHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AssetModelHistory>;

export const DefaultAssetModelHistory: Story = {
  args: {
    id: 'e249a236-3c8f-452c-b09f-1b513d3e342b',
  },
};
