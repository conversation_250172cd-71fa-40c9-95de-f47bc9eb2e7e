'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { AssetModelAlterData } from '@/types/asset-model';
import { FormSchema } from '@/schemas/asset-model';
import { editAssetModel } from '@/services/asset-models';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType } from '@/types';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import type { BaseFormData } from '../../types';
import { AssetModelForm } from '../../AssetModelForm';

interface EditAssetModelProps extends BaseFormData {
  id: string;
  initialValues: AssetModelAlterData;
}
export function EditAssetModel({
  id,
  categories,
  manufacturers,
  initialValues,
}: EditAssetModelProps): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssetModelAlterData>();

  const editAsset = async (formData: FormData): Promise<void> => {
    const { assetModelImageUrl, ...assetModelDetials } =
      Object.fromEntries(formData);
    const parsedData = FormSchema.safeParse({
      ...assetModelDetials,
      assetModelImageUrl: '',
    });

    if (!parsedData.success) {
      setFieldErrors(parsedData.error.flatten().fieldErrors);
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }

    resetFieldErrors();

    let uploadedAssetModelImageUrl = initialValues.assetModelImageUrl;
    if (
      isImage &&
      assetModelImageUrl instanceof File &&
      assetModelImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', assetModelImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.AssetModel,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }

      uploadedAssetModelImageUrl = generateCloudFrontUrl(
        EntityType.AssetModel,
        imageUploadResponse.data.fileName,
      );
    }

    const updateAssetModelData = {
      ...parsedData.data,
      assetModelImageUrl: isImage ? uploadedAssetModelImageUrl : '',
    };

    const assetModelResponse = await editAssetModel(id, updateAssetModelData);

    if (assetModelResponse.type === 'error') {
      if (assetModelResponse.errors.errorMessages) {
        assetModelResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(
        'Something went wrong!, Could not edit asset details. Please try again',
      );
      return;
    }

    toast.success('Asset model edited successfully');
    router.back();
  };

  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Asset Model</h1>
      <AssetModelForm
        categories={categories}
        errors={fieldErrors}
        initialValues={initialValues}
        manufacturers={manufacturers}
        onSubmit={editAsset}
        setIsImage={setIsImage}
      />
    </div>
  );
}
