import type { Meta, StoryObj } from '@storybook/react';
import { AssetModelForm } from './asset-model-form';

const categories = [
  {
    displayName: 'Laptop',
    value: 'laptop',
  },
  {
    displayName: 'Tablet',
    value: 'tablet',
  },
  {
    displayName: 'Phone',
    value: 'phone',
  },
  {
    displayName: 'Keyboard',
    value: 'keyboard',
  },
  {
    displayName: 'Mouse',
    value: 'mouse',
  },
];

const manufacturers = [
  {
    displayName: 'Apple',
    value: 'apple',
  },
  {
    displayName: 'Dell',
    value: 'dell',
  },
  {
    displayName: 'Samsung',
    value: 'samsung',
  },
  {
    displayName: 'HP',
    value: 'hp',
  },
  {
    displayName: 'Google',
    value: 'google',
  },
];

const meta: Meta<typeof AssetModelForm> = {
  title: 'components/AssetModel/AssetModelForm',
  component: AssetModelForm,
};

export default meta;

type Story = StoryObj<typeof AssetModelForm>;

export const AssetModelsFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="mb-6 text-center text-2xl font-bold">Add AssetModel</h1>
        <AssetModelForm categories={categories} manufacturers={manufacturers} />
      </div>
    );
  },
};

export const AssetModelsFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="mb-6 text-center text-2xl font-bold">Edit AssetModel</h1>
        <AssetModelForm
          categories={categories}
          initialValues={{
            modelName: 'Example Model',
            modelNumber: '123ABC',
            note: 'Sample note',
            assetModelImageUrl: 'https://example.com/model_image.jpg',
            categoryId: '42ca8525-4311-4dbf-abd0-479773cea8c1',
            manufacturerId: '42ca8525-4311-4dbf-abd0-479773cea8c1',
          }}
          manufacturers={manufacturers}
        />
      </div>
    );
  },
};
