'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { GetRoleResponse, RolesFormData } from '@/types/roles';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { rolesFormSchema } from '@/schemas/roles';
import { updateRole } from '@/services/roles';
import { permissionsFormDataToJsonData } from '@/utils/formdata-to-json';
import { RolesForm } from '../../RoleForm/role-form';

interface EditRoleProps {
  /**
   * Unique identifier for the asset
   */
  id: string;
  initialValues?: GetRoleResponse;
}
export default function EditRole({
  id,
  initialValues,
}: EditRoleProps): React.JSX.Element {
  const router = useRouter();

  const {
    fieldErrors: rolesFieldErrors,
    setFieldErrors: setRolesFieldErrors,
    resetFieldErrors: resetRolesFieldErrors,
  } = useFieldErrors<RolesFormData>();

  async function handleEdit(formData: FormData): Promise<void> {
    // Function to convert form data to Json format expected by the API
    const data = permissionsFormDataToJsonData(formData);

    const parsedResult = rolesFormSchema.safeParse(data);

    if (Object.keys(data.permissions).length === 0) {
      toast.error("Roles can't be updated with empty permissions");
      return;
    }

    if (!parsedResult.success) {
      setRolesFieldErrors(parsedResult.error.flatten().fieldErrors);
    } else {
      resetRolesFieldErrors();
    }

    if (!parsedResult.success) {
      return;
    }

    const roleResponse = await updateRole(id, parsedResult.data);

    if (roleResponse.type === 'error') {
      if (roleResponse.errors.errorMessages) {
        roleResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('something went wrong');
      return;
    }

    toast.success('Role updated successfully');
    router.back();
  }

  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2 min-w-fit">
      <h1 className="asset-management-form-heading">Edit Role</h1>
      <RolesForm
        initialValues={initialValues}
        mode="edit"
        onSubmit={handleEdit}
        roleFieldErrors={rolesFieldErrors}
      />
    </div>
  );
}
