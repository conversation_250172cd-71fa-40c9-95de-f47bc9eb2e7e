import React from 'react';
import { getRoleById } from '@/services/roles';
import EditRole from './edit-role';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditRolePage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const roleInfoResponse = await getRoleById(id);
  if (roleInfoResponse.type === 'error') {
    return <>Role not found</>;
  }

  return (
    <div>
      <EditRole id={id} initialValues={roleInfoResponse.data} />
    </div>
  );
}