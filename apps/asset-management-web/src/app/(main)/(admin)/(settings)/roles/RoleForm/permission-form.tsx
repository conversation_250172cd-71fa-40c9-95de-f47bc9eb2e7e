'use client';

import { Checkbox, TableCell, TableRow } from 'ui';
import { actions, readAction } from '@/constants';
import { convertCamelToTitleCase } from '@/utils/string-parser';
import { RouteType } from '@/types';
import type { PermissionRecord, Actions, DispatchAction } from '@/types';

const dependencyEntity: Record<string, readonly string[]> = {
  [RouteType.Assets]: [
    RouteType.Suppliers,
    RouteType.AssetsModel,
    RouteType.Status,
    RouteType.Users,
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Accessories]: [
    RouteType.Categories,
    RouteType.Suppliers,
    RouteType.Manufacturers,
    RouteType.Users,
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Appliances]: [
    RouteType.Categories,
    RouteType.Suppliers,
    RouteType.Manufacturers,
    RouteType.Users,
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Consumables]: [
    RouteType.Categories,
    RouteType.Suppliers,
    RouteType.Manufacturers,
    RouteType.Users,
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Licenses]: [
    RouteType.Categories,
    RouteType.Suppliers,
    RouteType.Manufacturers,
    RouteType.Users,
    RouteType.CustomsField,
  ] as const,
  [RouteType.AssetsModel]: [
    RouteType.Categories,
    RouteType.Manufacturers,
    RouteType.CustomsField,
  ] as const,
  [RouteType.Suppliers]: [
    'department',
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Policies]: [
    RouteType.Suppliers,
    RouteType.Manufacturers,
    RouteType.Users,
    RouteType.CustomsField,
    RouteType.Locations,
  ] as const,
  [RouteType.Services]: [RouteType.Assets, RouteType.Appliances] as const,
} as const;

// Function to get dependent entities for a given title
const getDependentEntities = (title: string): string[] => {
  const dependentEntities: string[] = [];
  for (const dependentEntity of Object.keys(dependencyEntity)) {
    const dependencies = dependencyEntity[dependentEntity];
    if (dependencies.includes(title)) {
      dependentEntities.push(dependentEntity);
    }
  }
  return dependentEntities;
};

export default function PermissionSection({
  title,
  initialValues,
  dispatch,
  mode,
}: {
  title: string;
  initialValues: PermissionRecord;
  dispatch?: React.Dispatch<DispatchAction>;
  mode?: string;
}): React.JSX.Element {
  const initialEntityPermissions: Actions[] =
    (initialValues[title] as Actions[] | undefined) || [];

  const handleEntityPermissionChange = (
    event: React.MouseEvent<HTMLButtonElement>,
  ): Actions[] => {
    const checkbox = event.currentTarget;
    const checkboxIdParts = checkbox.id.split('-');
    const checkboxType = checkboxIdParts[checkboxIdParts.length - 1];
    const isChecked = checkbox.ariaChecked;

    let updatedEntityPermission: Actions[] = [];

    if (isChecked === 'false') {
      updatedEntityPermission = Array.from(
        new Set([...initialEntityPermissions, 'read', checkboxType]),
      ) as Actions[];
    } else {
      updatedEntityPermission = initialEntityPermissions.filter(
        (permission) => permission !== checkboxType,
      );
    }
    return updatedEntityPermission;
  };

  const handleCheckboxChange = (
    event: React.MouseEvent<HTMLButtonElement>,
    value: string,
  ): void => {
    const updatedEntityPermission = handleEntityPermissionChange(event);
    const updatedRolePermission = { ...initialValues };

    // Update dependent entities' permissions if create or update action is selected
    if (
      updatedEntityPermission.includes('create') ||
      updatedEntityPermission.includes('update')
    ) {
      const dependencies = dependencyEntity[title] as RouteType[] | undefined;

      if (dependencies) {
        dependencies.forEach((dependency) => {
          const dependencyPermissions = (updatedRolePermission[dependency] as
            | Actions[]
            | undefined) || ['read'];

          // Dispatch action to update dependent entities' permissions
          if (dispatch) {
            dispatch({
              type: 'UPDATE_PERMISSIONS',
              payload: {
                title: dependency,
                permissions: Array.from(
                  new Set([...dependencyPermissions, 'read']),
                ) as Actions[],
              },
            });
          }
        });
      }
    }
    // Update dependent entities' permissions if create or update action is unselected
    if (
      value !== 'read' &&
      value !== 'delete' &&
      !(
        updatedEntityPermission.includes('create') ||
        updatedEntityPermission.includes('update')
      )
    ) {
      let updatedPermissions = {
        ...initialValues,
        [title]: updatedEntityPermission,
      };
      const dependencies = dependencyEntity[title] as RouteType[] | undefined;

      if (dependencies) {
        for (const dependency of dependencies) {
          const dependentEntities = getDependentEntities(dependency);
          const isEntityDependent = dependentEntities.some((entity) => {
            const updatedInitialValues = {
              ...initialValues,
              [title]: updatedEntityPermission,
            };
            return ['create', 'update'].some((action: Actions) =>
              (Object.prototype.hasOwnProperty.call(
                updatedInitialValues,
                entity,
              )
                ? updatedInitialValues[entity]
                : []
              ).includes(action),
            );
          });

          let dependencyPermissions = updatedPermissions[dependency];

          if (!isEntityDependent) {
            dependencyPermissions = ['create', 'update', 'delete'].some(
              (action: Actions) => dependencyPermissions.includes(action),
            )
              ? (Array.from(
                  new Set([...dependencyPermissions, 'read']),
                ) as Actions[])
              : dependencyPermissions.filter((action) => action !== 'read');

            updatedPermissions = {
              ...updatedPermissions,
              [dependency]: dependencyPermissions,
            };
          }

          if (dispatch) {
            dispatch({
              type: 'UPDATE_MULTIPLE_PERMISSIONS',
              payload: {
                title: dependency,
                allPermissions: {
                  ...updatedPermissions,
                },
              },
            });
          }
        }
      }
    }
    // Dispatch action to update entitiy' permissions
    if (dispatch) {
      dispatch({
        type: 'UPDATE_PERMISSIONS',
        payload: { title, permissions: updatedEntityPermission },
      });
    }
  };

  // Function to determine if read checkbox should be disabled
  const shouldDisableReadCheckbox = (value: Actions): boolean => {
    if (value === 'read') {
      return ['create', 'update', 'delete'].some((action: Actions) =>
        initialEntityPermissions.includes(action),
      );
    }
    return false;
  };

  // Function to determine if dependency checkboxes should be disabled
  const shouldDisableDependencyCheckboxes = (value: Actions): boolean => {
    const disableCheckbox = shouldDisableReadCheckbox(value);
    const dependentEntities = getDependentEntities(title);

    if (value === 'read') {
      for (const dependentEntity of dependentEntities) {
        const dependentPermissions =
          (initialValues[dependentEntity] as Actions[] | undefined) || [];

        if (
          dependentPermissions.includes('create') ||
          dependentPermissions.includes('update')
        ) {
          return true;
        }
      }
    }
    return disableCheckbox;
  };

  return (
    <TableRow>
      <TableCell className="text-left">
        {convertCamelToTitleCase(title)}
      </TableCell>
      {(['dashboard', 'users'].includes(title) ? readAction : actions).map(
        (value: Actions) => (
          <TableCell className="text-center" key={`${title}-${value}`}>
            <Checkbox
              checked={initialEntityPermissions.includes(value) || false}
              id={`${title}-${value}`}
              name={`${title}-${value}`}
              onClick={(event) => {
                mode ? handleCheckboxChange(event, value) : undefined;
              }}
              readonly={mode ? shouldDisableDependencyCheckboxes(value) : false}
            />
          </TableCell>
        ),
      )}
    </TableRow>
  );
}
