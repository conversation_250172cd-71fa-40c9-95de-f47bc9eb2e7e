import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import type { TableHeading } from '@/types';
import { getRoleById } from '@/services/roles';
import PermissionSection from '../RoleForm/permission-form';
import { convertWordsToTitleCase } from '@/utils/string-parser';

const tableHeaders: TableHeading[] = [
  { title: 'Routes' },
  { title: 'Read' },
  { title: 'Create' },
  { title: 'Update' },
  { title: 'Delete' },
];
interface LicenseDetailsPageProps {
  /** The unique identifier of the license. */
  params: { id: string };
}

/**
 * This page displays detailed information about a specific license, including tabs for sections like Info, Assignments, Finance, and History.
 * It also provides buttons for assigning, unassigning, editing, and deleting the license.
 */
export default async function roleDetailsPage({
  params: { id },
}: LicenseDetailsPageProps): Promise<React.JSX.Element> {
  const roleResponse = await getRoleById(id);

  if (roleResponse.type === 'error') {
    return <>Role not found!</>;
  }

  const roleInfo = roleResponse.data;

  return (
    <section className="page ">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {convertWordsToTitleCase(roleInfo.name)}
        </h1>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {Object.keys(roleInfo.permissions).length > 0 ? (
            Object.keys(roleInfo.permissions).map((entity) => {
              return (
                <PermissionSection
                  initialValues={roleInfo.permissions}
                  key={entity}
                  title={entity}
                />
              );
            })
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Permissions Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </section>
  );
}
