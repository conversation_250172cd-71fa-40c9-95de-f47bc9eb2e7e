'use client';

import {
  Label,
  SubmitButton,
  Input,
  Table,
  TableColumn,
  TableHeader,
  TableBody,
} from 'ui';
import { useReducer } from 'react';
import type {
  DispatchAction,
  FieldErrors,
  PermissionRecord,
  TableHeading,
} from '@/types';
import type { GetRoleResponse, RolesFormData } from '@/types/roles';
import { routesForPermission } from '@/constants';
import PermissionSection from './permission-form';

interface CreateRoleFormProps {
  initialValues?: GetRoleResponse;
  mode: 'create';
  roleFieldErrors?: FieldErrors<RolesFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
}

interface EditRoleFormProps {
  initialValues?: GetRoleResponse;
  mode: 'edit';
  roleFieldErrors?: FieldErrors<RolesFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
}

const tableHeaders: TableHeading[] = [
  { title: 'Routes' },
  { title: 'Read' },
  { title: 'Create' },
  { title: 'Update' },
  { title: 'Delete' },
];

export function RolesForm({
  mode,
  onSubmit,
  roleFieldErrors,
  initialValues,
}: CreateRoleFormProps | EditRoleFormProps): React.JSX.Element {
  const reducer = (
    state: PermissionRecord,
    action: DispatchAction,
  ): PermissionRecord => {
    switch (action.type) {
      case 'UPDATE_PERMISSIONS':
        return {
          ...state,
          [action.payload.title]: action.payload.permissions || [],
        };
      case 'UPDATE_MULTIPLE_PERMISSIONS':
        return {
          ...state,
          ...action.payload.allPermissions,
        };
      default:
        return state;
    }
  };

  const [state, dispatch] = useReducer(
    reducer,
    initialValues?.permissions || {},
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <fieldset>
        <Label className="py-0.5" htmlFor="name" required>
          Name
        </Label>
        <Input
          defaultValue={initialValues?.name ?? ''}
          id="name"
          isInvalidInput={Boolean(roleFieldErrors?.name)}
          name="name"
          placeholder="e.g. Admin"
          type="text"
        />
        {roleFieldErrors?.name ? <p>{roleFieldErrors.name[0]}</p> : null}
      </fieldset>
      <Table>
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn
                className={`${heading.className} text-sm text-slate-600`}
                key={heading.title}
              >
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>

        <TableBody>
          {routesForPermission.map((route) => {
            return (
              <PermissionSection
                dispatch={dispatch}
                initialValues={state}
                key={route}
                mode={mode}
                title={route}
              />
            );
          })}
        </TableBody>
      </Table>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
