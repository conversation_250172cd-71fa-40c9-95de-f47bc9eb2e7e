import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import Link from 'next/link';
import {
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { roleRoutes } from '@/constants/routes';
import {
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { getNumberOfPages } from '@/utils/helper-functions';
import { defaultRoles, initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { getAllRoles } from '@/services/roles';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { auth } from '@/config/auth';

export default async function RolePage({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  const session = await auth();

  const tableHeaders: TableHeading[] = [
    { title: 'Name', className: 'text-left' },
    {
      title: session?.user.role.name === 'admin' ? 'Action' : '',
      className: 'text-center',
    },
  ];

  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const roles = await getAllRoles(queryParams);

  if (roles.type === 'error') {
    return <>Not able to load/get Role, please try again!</>;
  }

  const totalPages = getNumberOfPages(roles.count, searchParams?.limit);
  return (
    <section className="page ">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Roles</h1>
        <div className="flex gap-3">
          <Search className="w-[18.5rem]" placeholder="Search name..." />
          <CreateButton href={roleRoutes.CREATE} label="New Role" />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {roles.data.length > 0 ? (
            roles.data.map((role) => (
              <TableRow key={role.id}>
                <TableCell className="first-letter:uppercase">
                  <Link
                    className="text-primary-600 hover:underline "
                    href={`${roleRoutes.MAIN}/${role.id}`}
                  >
                    {role.name}
                  </Link>
                </TableCell>

                <TableCell className="flex justify-around">
                  <div className="flex items-center justify-center gap-2">
                    {!defaultRoles.includes(
                      role.name.replace(' ', '_').toUpperCase(),
                    ) ? (
                      <>
                        <EditButton href={`${roleRoutes.EDIT}/${role.id}`} />
                        <DeleteEntity
                          entityId={role.id}
                          entityName={role.name}
                          entityType={Routes.ROLE}
                        >
                          <DeleteButton />
                        </DeleteEntity>
                      </>
                    ) : null}
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={roles.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
