'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { RolesFormData } from '@/types/roles';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { rolesFormSchema } from '@/schemas/roles';
import { createRole } from '@/services/roles';
import { permissionsFormDataToJsonData } from '@/utils/formdata-to-json';
import { RolesForm } from '../RoleForm/role-form';

export function CreateRole(): React.JSX.Element {
  const router = useRouter();
  const {
    fieldErrors: rolesFieldErrors,
    setFieldErrors: setRolesFieldErrors,
    resetFieldErrors: resetRolesFieldErrors,
  } = useFieldErrors<RolesFormData>();

  async function addRoles(formData: FormData): Promise<void> {
    // Function to convert form data to Json format expected by the API
    const data = permissionsFormDataToJsonData(formData);

    const parsedResult = rolesFormSchema.safeParse(data);

    if (Object.keys(data.permissions).length === 0) {
      toast.error("Roles can't be created with empty permissions");
      return;
    }

    if (!parsedResult.success) {
      setRolesFieldErrors(parsedResult.error.flatten().fieldErrors);
    } else {
      resetRolesFieldErrors();
    }

    if (!parsedResult.success) {
      return;
    }
    const roleResponse = await createRole(parsedResult.data);

    if (roleResponse.type === 'error') {
      if (roleResponse.errors.errorMessages) {
        roleResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('something went wrong');
      return;
    }

    toast.success('Role created successfully');
    router.back();
  }

  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2 min-w-fit">
      <h1 className="asset-management-form-heading">Create Role</h1>
      <RolesForm
        mode="create"
        onSubmit={addRoles}
        roleFieldErrors={rolesFieldErrors}
      />
    </div>
  );
}
