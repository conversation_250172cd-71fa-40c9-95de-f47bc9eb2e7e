import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { GlobalNotificationForm } from './global-notification-form';
import { CategoryType } from '@/types';

const meta: Meta<typeof GlobalNotificationForm> = {
  title: 'components/GlobalNotification/GlobalNotificationForm',
  component: GlobalNotificationForm,
};

export default meta;

type Story = StoryObj<typeof GlobalNotificationForm>;

export const GlobalNotificationFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-1/2">
        <h1 className="asset-management-form-heading">
          Create Global Notification
        </h1>
        <GlobalNotificationForm users={[]} />
      </div>
    );
  },
};

export const GlobalNotificationFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-1/2">
        <h1 className="asset-management-form-heading">
          Edit Global Notification
        </h1>
        <GlobalNotificationForm
          initialValues={{
            id: '1',
            name: 'Global Notification 1',
            typeOfCategory: CategoryType.APPLIANCE,
          }}
          users={[]}
        />
      </div>
    );
  },
};
