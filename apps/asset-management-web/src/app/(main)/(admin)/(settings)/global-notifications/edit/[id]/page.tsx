import React from 'react';
import { EditGlobalNotification } from './edit-global-notification';
import { getGlobalNotification } from '@/services/global-notification';
import { getAllUsersOptions } from '@/services/users';

interface EditGlobalNotificationPageProps {
  params: {
    id: string;
  };
}

export default async function EditPage({
  params: { id },
}: EditGlobalNotificationPageProps): Promise<React.JSX.Element> {
  const getNotificationById = await getGlobalNotification(id);
  const users = await getAllUsersOptions();
  if (getNotificationById.type === 'error') {
    return <>Id not found</>;
  }
  if (users.type === 'error') {
    return <>Not able to load users. Please try again!</>;
  }
  return (
    <EditGlobalNotification
      id={id}
      initialValues={getNotificationById.data}
      users={users.data}
    />
  );
}
