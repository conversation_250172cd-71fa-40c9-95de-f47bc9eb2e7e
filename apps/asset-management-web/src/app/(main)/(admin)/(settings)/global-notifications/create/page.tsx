import React from 'react';
import { CreateGlobalNotification } from './create-global-notification';
import { getAllUsersOptions } from '@/services/users';

export default async function CreateStatusPage(): Promise<React.JSX.Element> {
  const users = await getAllUsersOptions();
  if (users.type === 'error') {
    return <>Not able to load users. Please try again!</>;
  }
  return <CreateGlobalNotification users={users.data} />;
}
