'use client';
import React from 'react';
import { SubmitButton, Label, Input } from 'ui';
import {
  GlobalNotificationData,
  GlobalNotificationRequestDto,
} from '@/types/global-notification';
import { PrimitiveComboBox } from '@/components/client';
import {
  ActionTypes,
  categoryTypes,
  requiredFieldErrorMessage,
} from '@/constants';
import { FieldErrors, Option } from '@/types';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';
import { toStartCase } from '@/utils/string-parser';

interface GlobalNotificationFormProps {
  /**
   * Optional initial values for the form fields.
   * If provided, the form will be pre-populated with these values for editing.
   */
  initialValues?: Partial<GlobalNotificationData>;
  /**
   * Callback function triggered when the form is submitted.
   */
  onSubmit?: (formData: FormData) => Promise<void>;

  errors?: FieldErrors<GlobalNotificationRequestDto>;

  users: Option[];
}

/**
 * Form component for creating or editing a status item.
 */
export function GlobalNotificationForm({
  initialValues,
  onSubmit,
  errors,
  users,
}: GlobalNotificationFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div className="grid grid-cols-2 gap-5">
        <fieldset>
          <Label htmlFor="name" required>
            Name
          </Label>
          <Input
            defaultValue={initialValues?.name ?? ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            name="name"
            placeholder="Enter name"
          />
          {errors?.name ? <p>{errors.name[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="typeOfCategory" required>
            Category Type
          </Label>
          <PrimitiveComboBox
            id="typeOfCategory"
            initialValue={initialValues?.typeOfCategory}
            isInvalidInput={Boolean(errors?.typeOfCategory)}
            name="typeOfCategory"
            placeholder="Choose category type"
            values={categoryTypes}
          />
          {errors?.typeOfCategory ? (
            <p className=" text-xs text-red-600">{requiredFieldErrorMessage}</p>
          ) : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="operations" required>
            List of Actions
          </Label>
          <MultiSelectComboBox
            id="operations"
            name="actions"
            options={ActionTypes.map((v) => ({
              displayName: toStartCase(v),
              value: v,
            }))}
            placeholder="Select"
            selectedValues={initialValues?.actions ?? []}
          />
          {errors?.actions ? <p>{errors.actions[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="users">Notify</Label>
          <MultiSelectComboBox
            id="users"
            name="users"
            options={users}
            placeholder="Choose users to be notified"
            selectedValues={initialValues?.users?.map((u) => u.id) ?? []}
          />
          {errors?.userIds ? <p>{errors.userIds[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="Additional Email">Additional Email</Label>
          <Input
            defaultValue={initialValues?.additionalEmails ?? ''}
            id="additionalEmails"
            isInvalidInput={Boolean(errors?.additionalEmails)}
            name="additionalEmails"
            placeholder="Enter additional email"
          />
          {errors?.additionalEmails ? (
            <p>{errors.additionalEmails[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
