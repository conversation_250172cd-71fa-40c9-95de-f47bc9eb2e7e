'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { GlobalNotificationForm } from '../../GlobalNotificationForm';
import {
  GlobalNotificationData,
  GlobalNotificationRequestDto,
} from '@/types/global-notification';
import { globalNotificationRequestSchema } from '@/schemas/global-notification';
import { updateGlobalNotification } from '@/services/global-notification';
import { Option } from '@/types';
import { toUpperSnakeCase } from '@/utils/string-parser';
import { reduceFormData } from 'utils';

interface EditGlobalNotificationProps {
  /**
   * Unique identifier for the status
   */
  id: string;
  /**
   * Initial values for the status form
   */
  initialValues: GlobalNotificationData;

  users: Option[];
}

export function EditGlobalNotification({
  id,
  initialValues,
  users,
}: EditGlobalNotificationProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<GlobalNotificationRequestDto>();

  const updateNotification = async (formData: FormData): Promise<void> => {
    formData.set(
      'typeOfCategory',
      toUpperSnakeCase(formData.get('typeOfCategory')?.toString() || ''),
    );
    const actions = reduceFormData('actions', formData);
    const userIds = reduceFormData('users', formData);
    const parsedResult = globalNotificationRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      actions,
      userIds,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const notificationResponse = await updateGlobalNotification(
      parsedResult.data,
      id,
    );
    if (notificationResponse.type === 'error') {
      if (notificationResponse.errors.errorMessages) {
        notificationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Global notification updated successfully');
    router.back();
  };

  return (
    <div className="shadow-container m-auto h-fit w-1/2">
      <h1 className="asset-management-form-heading">
        Edit Global Notification
      </h1>
      <GlobalNotificationForm
        errors={fieldErrors}
        initialValues={initialValues}
        onSubmit={updateNotification}
        users={users}
      />
    </div>
  );
}
