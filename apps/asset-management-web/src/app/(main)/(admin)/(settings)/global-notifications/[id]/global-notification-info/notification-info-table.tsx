import React from 'react';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { toStartCase } from '@/utils/string-parser';
import { InfoItem } from '@/components/InfoItems/info-item';
import { GlobalNotificationData } from '@/types/global-notification';

interface GlobalNotificationInfoTableProps {
  globalNotificationData: GlobalNotificationData;
}

interface GlobalNotificationInfoType {
  displayName: string;
  value: string | string[] | null;
}

export function GlobalNotificationInfoTable({
  globalNotificationData,
}: GlobalNotificationInfoTableProps): React.JSX.Element {
  const globalNotificationInfo: GlobalNotificationInfoType[] = [
    {
      displayName: 'Name',
      value: globalNotificationData.name,
    },
    {
      displayName: 'Category',
      value: globalNotificationData.typeOfCategory,
    },
    {
      displayName: 'Operations',
      value: globalNotificationData.actions
        .map((action) => toStartCase(action))
        .join(', '),
    },
    {
      displayName: 'Users',
      value: globalNotificationData.users.map((user) => user.name).join(', '),
    },
  ];

  return (
    <div className="space-y-5 px-6 pb-6 pt-4 text-xs text-slate-600 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
      {globalNotificationInfo.map((globalNotification) => (
        <InfoItem
          key={globalNotification.displayName}
          name={globalNotification.displayName}
          value={
            Array.isArray(globalNotification.value)
              ? displayDataOrDefault(globalNotification.value.join(', '))
              : displayDataOrDefault(globalNotification.value)
          }
        />
      ))}
    </div>
  );
}
