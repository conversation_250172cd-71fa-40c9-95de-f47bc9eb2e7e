'use client';
import React from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { GlobalNotificationForm } from '../GlobalNotificationForm';
import { createGlobalNotification } from '@/services/global-notification';
import { globalNotificationRequestSchema } from '@/schemas/global-notification';
import { GlobalNotificationRequestDto } from '@/types/global-notification';
import { Option } from '@/types';
import { reduceFormData } from 'utils';
import { toUpperSnakeCase } from '@/utils/string-parser';

export interface CreateGlobalNotificationProps {
  users: Option[];
}

export function CreateGlobalNotification({
  users,
}: CreateGlobalNotificationProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<GlobalNotificationRequestDto>();
  const addGlobalNotification = async (formData: FormData): Promise<void> => {
    formData.set(
      'typeOfCategory',
      toUpperSnakeCase(formData.get('typeOfCategory')?.toString() || ''),
    );
    const actions = reduceFormData('actions', formData);
    const userIds = reduceFormData('users', formData);

    const parsedResult = globalNotificationRequestSchema.safeParse({
      ...Object.fromEntries(formData),
      actions,
      userIds,
    });
    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const notificationResponse = await createGlobalNotification(
      parsedResult.data,
    );
    if (notificationResponse.type === 'error') {
      if (notificationResponse.errors.errorMessages) {
        notificationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }

      toast.error('Something went wrong');
      return;
    }
    toast.success('Global notification created successfully');
    router.back();
  };

  return (
    <div className="shadow-container m-auto h-fit w-1/2">
      <h1 className="asset-management-form-heading">
        Create Global Notification
      </h1>
      <GlobalNotificationForm
        errors={fieldErrors}
        onSubmit={addGlobalNotification}
        users={users}
      />
    </div>
  );
}
