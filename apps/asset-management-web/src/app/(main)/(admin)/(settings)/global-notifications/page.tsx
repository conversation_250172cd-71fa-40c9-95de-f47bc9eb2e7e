import React from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from 'ui';
import { CreateButton, DeleteButton, EditButton } from '@/components/client';
import { globalNotificationRoutes } from '@/constants/routes';
import { Routes, SearchParams, type TableHeading } from '@/types';
import { getActionColumnByRole } from '@/services/roles';
import { getAllGlobalNotifications } from '@/services/global-notification';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { toStartCase } from '@/utils/string-parser';
import Link from 'next/link';
import { getNumberOfPages } from '@/utils/helper-functions';
import { Pagination } from '@/components/Pagination/pagination';

export default async function GlobalNotification({
  searchParams,
}: {
  searchParams: SearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Entity' },
    { title: 'Operations' },
    { title: 'Users' },
    { title: 'Additional Email' },
  ];

  const renderAction = await getActionColumnByRole('globalNotifications', [
    'update',
    'delete',
  ]);

  if (renderAction) {
    tableHeaders.push(renderAction);
  }
  const notificationResponse = await getAllGlobalNotifications();
  if (notificationResponse.type === 'error') {
    return <>Something Went wrong</>;
  }
  const totalPages = getNumberOfPages(
    notificationResponse.count,
    searchParams.limit,
  );
  return (
    <section className="page flex w-full flex-col gap-6">
      <div className=" flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          Global Notification
        </h1>
        <CreateButton
          href={globalNotificationRoutes.CREATE}
          label="Create notifications"
        />
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading text-base">
          {tableHeaders.map((heading) => (
            <TableColumn key={heading.title} className="last:text-center">
              <p className={heading.className}>{heading.title}</p>
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody>
          {notificationResponse.data && notificationResponse.data.length > 0 ? (
            notificationResponse.data.map((notification) => (
              <TableRow
                className="text-slate-700 [&>td]:py-4"
                key={notification.id}
              >
                <TableCell className="first-letter:uppercase">
                  <Link
                    className="text-primary-600 first-letter:uppercase"
                    href={`${globalNotificationRoutes.MAIN}/${notification.id}`}
                  >
                    {notification.name}
                  </Link>
                </TableCell>
                <TableCell>
                  {toStartCase(notification.typeOfCategory)}
                </TableCell>
                <TableCell>
                  {toStartCase(notification.actions.join(', '))}
                </TableCell>
                <TableCell>
                  {notification.users.map((u) => u.name).join(', ')}
                </TableCell>
                <TableCell>
                  {notification.additionalEmails.map((u) => u).join(', ')}
                </TableCell>
                {renderAction ? (
                  <TableCell className="flex items-center justify-center gap-3">
                    <EditButton
                      href={`${globalNotificationRoutes.EDIT}/${notification.id}`}
                    />

                    <DeleteEntity
                      entityId={notification.id}
                      entityName={notification.name}
                      entityType={Routes.NOTIFICATIONS}
                    >
                      <DeleteButton />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={notificationResponse.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
