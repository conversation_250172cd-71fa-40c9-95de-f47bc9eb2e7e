import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from 'ui';
import { GlobalNotificationInfoTable } from './global-notification-info/notification-info-table';
import { getGlobalNotification } from '@/services/global-notification';
import { toStartCase } from '@/utils/string-parser';

/**
 *  Props for the EvaluationList component.
 */
interface GlobalNotificationProps {
  /** Parameters for the page, containing the supplier ID. */
  params: {
    /** The ID of the supplier. */
    id: string;
  };
}

/**
 * Retrieves and displays the list of evaluations for a supplier.
 * params - The parameters containing the supplier ID.
 * */
export default async function GlobalNotificationList({
  params: { id },
}: GlobalNotificationProps): Promise<React.JSX.Element> {
  /**
   * Retrieves supplier information
   */
  const globalNotificationInfoResponse = await getGlobalNotification(id);

  if (globalNotificationInfoResponse.type === 'error') {
    return <>Something went wrong</>;
  }

  return (
    <section className="w-full space-y-6 px-8">
      <div className="flex w-4/5 items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {toStartCase(globalNotificationInfoResponse.data.name)}
        </h1>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-4/5 rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <GlobalNotificationInfoTable
              globalNotificationData={globalNotificationInfoResponse.data}
            />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
