import {
  Ho<PERSON><PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  ToolTip,
  ToolTipContent,
  ToolTipTrigger,
} from 'ui';
import { InfoButton } from '@/components/ActionButtons/InfoButton/info-button';
import { supplierRoutes } from '@/constants/routes';
import { EditButton } from '@/components/client';
import type { AllEvaluationResponseType } from '@/types/evaluation';
import type { TableHeading } from '@/types';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { parseDate } from '@/utils/date-utils';

/**
 * Props for the EvaluationInfoTable component.
 */
interface EvaluationInfoTableProps {
  /**
   * The evaluation data to be displayed in the table.
   */
  evaluationData: AllEvaluationResponseType;
}

/**
 * Component for displaying evaluation information in a table format.
 * evaluationData - The evaluation data to be displayed.
 * */
export function EvaluationInfoTable({
  evaluationData,
}: EvaluationInfoTableProps): React.JSX.Element {
  const tableHeaders: TableHeading[] = [
    { title: 'Service Type' },
    { title: 'Evaluation Date' },
    { title: 'Evaluated By' },
    { title: 'Total Score', className: 'text-right' },
    { title: 'Percentage', className: 'text-right' },
    { title: 'Remarks', className: 'text-right' },
    { title: 'Note' },
    { title: 'Actions' },
  ];

  /**
   * Formats the percentage based on the total score.
   */
  const formatPercentage = (
    totalScore: number,
    maxPossibleScore: number,
  ): string => {
    if (maxPossibleScore === 0) {
      return '0'; // Avoid division by zero, return 0 when maxScore is 0
    }
    const percentage = (totalScore / maxPossibleScore) * 100;
    return percentage % 1 === 0 ? percentage.toFixed(0) : percentage.toFixed(2);
  };

  const getRemarkFromPercentage = (
    totalScore: number,
    maxPossibleScore: number,
  ): string => {
    const percentage = parseFloat(
      formatPercentage(totalScore, maxPossibleScore),
    );
    if (percentage >= 0 && percentage <= 20) return 'Very Poor';
    if (percentage > 20 && percentage <= 40) return 'Poor';
    if (percentage > 40 && percentage <= 60) return 'Average';
    if (percentage > 60 && percentage <= 80) return 'Good';
    if (percentage > 80 && percentage <= 100) return 'Excellent';

    return '-';
  };
  return (
    <Table className="asset-management-table-md rounded-none shadow-none">
      <TableHeader className="asset-management-table-heading text-base">
        {tableHeaders.map((heading) => (
          <TableColumn key={heading.title}>
            <p className={heading.className}>{heading.title}</p>
          </TableColumn>
        ))}
      </TableHeader>
      <TableBody>
        {evaluationData.map((evaluation) => (
          <TableRow className="[&>td]:py-4" key={evaluation.id}>
            <TableCell>
              {displayDataOrDefault(evaluation.serviceType)}
            </TableCell>
            <TableCell>
              {evaluation.date
                ? parseDate('MMM dd, yyyy')(evaluation.date)
                : '-'}
            </TableCell>
            <TableCell>{displayDataOrDefault(evaluation.user?.name)}</TableCell>
            <TableCell className="text-right">
              {evaluation.totalScore}
            </TableCell>
            <TableCell className="text-right">
              {`${formatPercentage(
                evaluation.totalScore,
                evaluation.maxPossibleScore,
              )}%`}
            </TableCell>
            <TableCell className="text-right">
              {getRemarkFromPercentage(
                evaluation.totalScore,
                evaluation.maxPossibleScore,
              )}
            </TableCell>
            <TableCell className="max-w-[200px]">
              <HoverCard>
                <HoverCardTrigger className="line-clamp-2">
                  {displayDataOrDefault(evaluation.note)}
                </HoverCardTrigger>
                {evaluation.note ? (
                  <HoverCardContent>{evaluation.note}</HoverCardContent>
                ) : null}
              </HoverCard>
            </TableCell>
            <TableCell className="flex-row items-center justify-center gap-3">
              <div className="flex gap-2">
                <ToolTip>
                  <ToolTipTrigger>
                    <InfoButton
                      className=" text-yellow-500"
                      href={`${supplierRoutes.BASE}/${evaluation.id}${supplierRoutes.DISPLAY_EVALUATION}?evaluationId=${evaluation.id}`}
                    />
                  </ToolTipTrigger>
                  <ToolTipContent className=" z-10">view</ToolTipContent>
                </ToolTip>
                <ToolTip>
                  <ToolTipTrigger>
                    <EditButton
                      href={`${supplierRoutes.BASE}/${evaluation.id}${supplierRoutes.EDIT_EVALUATION}?evaluationId=${evaluation.id}`}
                    />
                  </ToolTipTrigger>
                  <ToolTipContent className=" z-10">edit</ToolTipContent>
                </ToolTip>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
