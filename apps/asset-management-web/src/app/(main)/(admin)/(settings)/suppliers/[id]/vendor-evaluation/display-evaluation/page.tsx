import { getEvaluation } from '@/services/evaluation';
import { Evaluation } from './display-evaluation';

/**
 *  Props for the DisplayEvaluationPage component.
 */
interface EvaluationPageProps {
  params: {
    id: string;
  };
}

/**
 * Renders the page for displaying evaluation details.
 */
export default async function EvaluationPage({
  params: { id },
}: EvaluationPageProps): Promise<React.JSX.Element> {
  const evaluationResponse = await getEvaluation(id);

  if (evaluationResponse.type === 'error') {
    return <>Something went wrong</>;
  }
  const {
    date,
    serviceType,
    securityAdherence,
    ndaProactive,
    productQuality,
    responsiveness,
    deadlineAdherence,
    deliveryProactiveness,
    invoiceClarity,
    overallExperience,
    supplier: { id: supplierId },
    note,
  } = evaluationResponse.data;
  const supplierName = evaluationResponse.data.supplier.name;

  /**
   * Constructing initial data object with evaluation details for rendering.
   */
  const initialData = {
    date: date ?? '',
    serviceType,
    securityAdherence,
    ndaProactive,
    productQuality,
    responsiveness,
    deadlineAdherence,
    deliveryProactiveness,
    invoiceClarity,
    overallExperience,
    supplierId,
    note,
  };

  return <Evaluation initialValues={initialData} supplierName={supplierName} />;
}
