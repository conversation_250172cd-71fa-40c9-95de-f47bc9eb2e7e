'use client';

import {
  SubmitButton,
  Label,
  Input,
  Textarea,
  ImageInputWithPreview,
} from 'ui';
import type { SupplierAlterData } from '@/types/supplier';
import type { FieldErrors, Option } from '@/types';
import {
  vendorSelectionCriteria,
  evaluationFrequencies,
  agreements,
  allowedImageType,
  transactionFrequency,
} from '@/constants';
import { OptionsComboBox, PrimitiveComboBox } from '@/components/client';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

interface SupplierFormProps {
  /**
   * Optional initial values for the form fields.
   * If provided, the form will be pre-populated with these values for editing.
   */
  initialValues?: SupplierAlterData;
  /**
   * Callback function triggered when the form is submitted.
   */
  onSubmit?: (formData: FormData) => Promise<void>;
  errors?: FieldErrors<SupplierAlterData>;
  departments: Option[];
  /**
   * Optional callback function to update the status of the defualt image is provided or not.
   */
  setIsImage?: (isImage: boolean) => void;
  users: Option[] | null;
  locations: Option[];
}

/**
 * Form component for creating or editing a supplier item.
 */
export function SupplierForm({
  initialValues,
  onSubmit,
  errors,
  setIsImage,
  departments,
  users,
  locations,
}: SupplierFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="name" required>
            Name
          </Label>
          <Input
            defaultValue={initialValues?.name || ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            name="name"
            placeholder="Enter supplier name"
          />
          {errors?.name?.length ? <p>{errors.name[0]}</p> : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="contactName" required>
            Contact Name
          </Label>
          <Input
            defaultValue={initialValues?.contactName || ''}
            id="contactName"
            isInvalidInput={Boolean(errors?.contactName)}
            name="contactName"
            placeholder="Enter contact name"
          />
          {errors?.contactName?.length ? <p>{errors.contactName[0]}</p> : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="contactPhoneNumber" required>
            Phone Number
          </Label>
          <Input
            defaultValue={initialValues?.contactPhoneNumber || ''}
            id="contactPhoneNumber"
            isInvalidInput={Boolean(errors?.contactPhoneNumber)}
            name="contactPhoneNumber"
            placeholder="Enter phone number"
            type="number"
          />
          {errors?.contactPhoneNumber?.length ? (
            <p>{errors.contactPhoneNumber[0]}</p>
          ) : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="contactEmail">Email</Label>
          <Input
            defaultValue={initialValues?.contactEmail || ''}
            id="contactEmail"
            name="contactEmail"
            placeholder="Enter contact email"
          />
          {errors?.contactEmail?.length ? (
            <p>{errors.contactEmail[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="contactEmail" required>
            Service Type
          </Label>
          <Input
            defaultValue={initialValues?.serviceType || ''}
            id="serviceType"
            isInvalidInput={Boolean(errors?.serviceType)}
            name="serviceType"
            placeholder="Enter service type"
          />
          {errors?.serviceType?.length ? <p>{errors.serviceType[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="evaluationFrequency">Evaluation Frequency</Label>
          <PrimitiveComboBox
            id="evaluationFrequency"
            initialValue={initialValues?.evaluationFrequency}
            name="evaluationFrequency"
            placeholder="Choose evaluation frequency"
            values={evaluationFrequencies}
          />
          {errors?.evaluationFrequency ? (
            <p>{errors.evaluationFrequency[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="agreement">Zip Code</Label>
          <Input
            defaultValue={initialValues?.zipCode || ''}
            id="zipCode"
            name="zipCode"
            placeholder="Enter zip code"
          />
        </fieldset>
        <fieldset>
          <Label htmlFor="agreement">Agreements</Label>
          <MultiSelectComboBox
            id="agreement"
            name="agreement"
            options={agreements}
            placeholder="Choose an agreement"
            selectedValues={initialValues?.agreement ?? []}
          />
        </fieldset>
      </div>
      <fieldset>
        <Label htmlFor="transactionFrequency">Transaction Frequency</Label>
        <OptionsComboBox
          id="transactionFrequency"
          initialValue={initialValues?.transactionFrequency}
          name="transactionFrequency"
          options={transactionFrequency}
          placeholder="Choose transaction frequency"
        />
        {errors?.departmentId ? <p>{errors.departmentId[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="departmentId">Department</Label>
        <OptionsComboBox
          id="departmentId"
          initialValue={initialValues?.departmentId}
          name="departmentId"
          options={departments}
          placeholder="Choose department"
        />
        {errors?.departmentId ? <p>{errors.departmentId[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="location">Service Location</Label>
        <MultiSelectComboBox
          id="location"
          name="location"
          options={locations}
          placeholder="Select locations"
          selectedValues={initialValues?.location ?? []}
        />
        {errors?.location?.length ? <p>{errors.location[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="address">Address</Label>
        <Textarea
          defaultValue={initialValues?.address || ''}
          id="address"
          name="address"
          placeholder="Enter address"
          rows={4}
        />
        {errors?.address?.length ? <p>{errors.address[0]}</p> : null}
      </fieldset>

      <fieldset>
        <Label htmlFor="selectionCriteria">Selection Criteria</Label>
        <MultiSelectComboBox
          detailedInfo
          id="selectionCriteria"
          name="selectionCriteria"
          options={vendorSelectionCriteria}
          placeholder="Choose selection criterias"
          selectedValues={initialValues?.selectionCriteria ?? []}
        />
        {errors?.selectionCriteria ? (
          <p>{errors.selectionCriteria[0]}</p>
        ) : null}
      </fieldset>
      {users ? (
        <fieldset>
          <Label htmlFor="notifyTo">Notify</Label>
          <MultiSelectComboBox
            id="notifyTo"
            name="notifyTo"
            options={users}
            placeholder="Choose users to be notified"
            selectedValues={initialValues?.notifyTo ?? []}
          />
        </fieldset>
      ) : null}
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          defaultValue={initialValues?.note || ''}
          id="note"
          name="note"
          placeholder="Enter notes"
          rows={4}
        />
      </fieldset>
      <fieldset>
        <Label htmlFor="supplierImageUrl">Select Image</Label>
        <ImageInputWithPreview
          accept={allowedImageType.join(',')}
          className="mt-2"
          defaultValue={initialValues?.supplierImageUrl || ''}
          id="supplierImageUrl"
          name="supplierImageUrl"
          setIsImage={setIsImage}
        />
        {errors?.supplierImageUrl?.length ? (
          <p>{errors.supplierImageUrl[0]}</p>
        ) : null}
      </fieldset>

      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
