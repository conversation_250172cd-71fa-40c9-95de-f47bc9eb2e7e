import { getSupplier } from '@/services/suppliers';
import { CreateEvaluationForm } from './create-evaluation';

/**
 * Props for CreateEvaluationPage
 */
interface CreateEvaluationPageProps {
  /** Parameters for the page, containing the supplier ID. */
  params: {
    /** The ID of the supplier. */
    id: string;
  };
}

/**
 * Renders the page for creating a new evaluation.
 */
export default async function CreateEvaluationPage({
  params: { id },
}: CreateEvaluationPageProps): Promise<React.JSX.Element> {
  const supplierInfo = await getSupplier(id);
  if (supplierInfo.type === 'error') {
    return <>Not able to load supplier info Please try again!</>;
  }

  const supplierName = supplierInfo.data.name;
  const serviceType = supplierInfo.data.serviceType;
  const supplierData = {
    supplierName,
    serviceType,
    supplierId: id,
  };
  return <CreateEvaluationForm supplierData={supplierData} />;
}
