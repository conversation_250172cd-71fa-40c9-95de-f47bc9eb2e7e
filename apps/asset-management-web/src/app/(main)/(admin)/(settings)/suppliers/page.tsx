import React from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
  Selector,
} from 'ui';
import Link from 'next/link';
import {
  EditButton,
  CreateButton,
  Search,
  DeleteButton,
} from '@/components/client';
import { getAllSupplier } from '@/services/suppliers';
import { supplierRoutes } from '@/constants/routes';
import {
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage, transactionFrequency } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { toStartCase } from '@/utils/string-parser';

interface SupplierSearchParams extends SearchParams {
  transactionFrequency?: string;
}

export default async function ListSupplier({
  searchParams,
}: {
  searchParams?: SupplierSearchParams;
}): Promise<React.JSX.Element> {
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    transactionFrequency:
      searchParams?.transactionFrequency?.toUpperCase() ?? '',
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Contact' },
    { title: 'Email' },
    { title: 'Phone Number', className: 'text-right' },
    { title: 'Transaction Frequency' },
    { title: 'Service type' },
    { title: 'Service location' },
    { title: 'Zipcode' },
    { title: 'Department' },
  ];

  const renderAction = await getActionColumnByRole('suppliers', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  const supplierList = await getAllSupplier(queryParams);

  if (supplierList.type === 'error') {
    return (
      <p>
        Something went wrong!, Could not load supplier list this time. Please
        try again
      </p>
    );
  }

  const totalPages = getNumberOfPages(supplierList.count, searchParams?.limit);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="mr-16 text-3xl font-semibold text-slate-600">
          Suppliers
        </h1>

        <div className="flex gap-3">
          <Selector
            options={transactionFrequency.map(({ value }) => value)}
            placeholder="Transaction Frequency"
            queryParam="transactionFrequency"
          />
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, contact, service type"
          />
          <CreateButton
            href={supplierRoutes.CREATE}
            label="Create Supplier"
          />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn
                className={`last:text-center ${heading.className} `}
                key={heading.title}
              >
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {supplierList.data.length > 0 ? (
            supplierList.data.map((supplier) => (
              <TableRow key={supplier.id}>
                <TableCell>
                  <Link
                    className="text-primary-600 first-letter:uppercase"
                    href={`${supplierRoutes.BASE}/${supplier.id}`}
                  >
                    {supplier.name}
                  </Link>
                </TableCell>
                <TableCell>{supplier.contactName}</TableCell>
                <TableCell className="max-w-[10rem]">
                  <HoverCard>
                    <div className=" flex flex-wrap">
                      <HoverCardTrigger className="truncate">
                        {displayDataOrDefault(supplier.contactEmail)}
                      </HoverCardTrigger>
                    </div>
                    {supplier.contactEmail ? (
                      <HoverCardContent align="start">
                        {supplier.contactEmail}
                      </HoverCardContent>
                    ) : null}
                  </HoverCard>
                </TableCell>
                <TableCell className="text-right">
                  {supplier.contactPhoneNumber}
                </TableCell>
                <TableCell>
                  {supplier.transactionFrequency
                    ? toStartCase(supplier.transactionFrequency)
                    : '-'}
                </TableCell>
                <TableCell>{supplier.serviceType}</TableCell>
                <TableCell>
                  {supplier.location
                    .map((location) => location.name)
                    .join(', ')}
                </TableCell>
                <TableCell>{displayDataOrDefault(supplier.zipCode)}</TableCell>
                <TableCell>
                  {displayDataOrDefault(supplier.evaluatorDepartment?.name)}
                </TableCell>
                {renderAction ? (
                  <TableCell>
                    <div className="flex items-center justify-center gap-2">
                      <EditButton
                        href={`${supplierRoutes.EDIT}/${supplier.id}`}
                      />
                      <DeleteEntity
                        entityId={supplier.id}
                        entityName={supplier.name}
                        entityType={Routes.SUPPLIERS}
                      >
                        <DeleteButton id={supplier.id} />
                      </DeleteEntity>
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={supplierList.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
