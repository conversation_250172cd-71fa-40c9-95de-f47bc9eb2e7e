'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type { EvaluationRequestDataType } from '@/types/evaluation';
import { evaluationFormSchema } from '@/schemas/evaluation';
import { editEvaluation } from '@/services/evaluation';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { EvaluationForm } from '../evaluation-form';

/**
 * Props for the EditEvaluation component.
 */
interface EditEvaluationFormProps {
  /** Unique identifier of the evaluated data being edited */
  id: string;

  /** Initial values for the evaluation form being edited */
  initialValues: EvaluationRequestDataType;

  /** Name of the supplier */
  supplierName: string;
}

/**
 * Component responsible for rendering a form used to edit an evaluation.
 */
export function EditEvaluationForm({
  id,
  initialValues,
  supplierName,
}: EditEvaluationFormProps): React.JSX.Element {
  const router = useRouter();

  /**
   * Handles the submission of edited evaluation data.
   */
  const editEvaluationData = async (formData: FormData): Promise<void> => {
    const evaluationFormData = Object.fromEntries(formData);

    const {
      securityAdherence = 0,
      ndaProactive = 0,
      productQuality = 0,
      responsiveness = 0,
      deadlineAdherence = 0,
      deliveryProactiveness = 0,
      invoiceClarity = 0,
      overallExperience = 0,
      supplierId,
      serviceType,
      note,
    } = evaluationFormData;
    const currentDate = new Date();
    const createdAt = currentDate.toISOString();
    const evaluationData = {
      securityAdherence: Number(securityAdherence),
      ndaProactive: Number(ndaProactive),
      productQuality: Number(productQuality),
      responsiveness: Number(responsiveness),
      deadlineAdherence: Number(deadlineAdherence),
      deliveryProactiveness: Number(deliveryProactiveness),
      invoiceClarity: Number(invoiceClarity),
      overallExperience: Number(overallExperience),
      date: createdAt,
      supplierId,
      serviceType,
      note,
    };

    const parsedResult = evaluationFormSchema.safeParse({
      ...evaluationData,
    });

    if (!parsedResult.success) {
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }

    const evaluationResponse = await editEvaluation(id, parsedResult.data);

    if (evaluationResponse.type === 'error') {
      if (evaluationResponse.errors.errorMessages) {
        evaluationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }
    toast.success('Evaluation data updated successfully');
    router.back();
  };

  return (
    <div className="shadow-container evaluation-form-style">
      <h1 className="asset-management-form-heading">Edit Evaluation</h1>
      <EvaluationForm
        initialValues={initialValues}
        mode="edit"
        onSubmit={editEvaluationData}
        supplierData={{ supplierName, serviceType: initialValues.serviceType }}
      />
    </div>
  );
}
