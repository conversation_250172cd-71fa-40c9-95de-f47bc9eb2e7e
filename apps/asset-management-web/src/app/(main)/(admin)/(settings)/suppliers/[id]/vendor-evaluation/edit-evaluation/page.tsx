import { getEvaluation } from '@/services/evaluation';
import { getAllSuppliersOptions, getSupplier } from '@/services/suppliers';
import { EditEvaluationForm } from './edit-evaluation';

/**
 * Props for the EditEvaluationPage component.
 */
interface EditProps {
  params: {
    id: string;
  };
}

/**
 * Renders the page for editing an evaluation.
 *
 * This component fetches evaluation data and suppliers' options, then renders the EditEvaluation component with the appropriate initial data.
 */
export default async function EditEvaluationPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const suppliers = await getAllSuppliersOptions();
  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }
  const evaluationResponse = await getEvaluation(id);

  if (evaluationResponse.type === 'error') {
    return <>Something went wrong</>;
  }
  const {
    date,
    serviceType,
    securityAdherence,
    ndaProactive,
    productQuality,
    responsiveness,
    deadlineAdherence,
    deliveryProactiveness,
    invoiceClarity,
    overallExperience,
    supplier: { id: supplierId },
    note,
  } = evaluationResponse.data;

  const initialData = {
    date: date ?? '',
    serviceType,
    securityAdherence,
    ndaProactive,
    productQuality,
    responsiveness,
    deadlineAdherence,
    deliveryProactiveness,
    invoiceClarity,
    overallExperience,
    supplierId,
    note,
  };

  const supplierInfo = await getSupplier(initialData.supplierId);
  if (supplierInfo.type === 'error') {
    return <h1>Failed to fetch supplier information</h1>;
  }
  const supplierName = supplierInfo.data.name;

  return (
    <EditEvaluationForm
      id={id}
      initialValues={initialData}
      supplierName={supplierName}
    />
  );
}
