'use client';

import React, { useReducer } from 'react';
import { SubmitButton, Input, Label, Textarea } from 'ui';
import { cn } from 'utils';
import type { SupplierDataType } from '../../../types';
import Table from './eval-table';

/**
 * Type representing the state of scores for each subsection.
 */
type ScoresState = Record<string, Record<string, number>>;

/**
 * Action type for updating scores.
 */
interface ScoresAction {
  /**
   * The type of action.
   */
  type: 'UPDATE_SCORE';

  /**
   * The identifier for the table related to the score update.
   */
  table: string;

  /**
   * The identifier for the subsection within the table.
   */
  subSection: string;

  /**
   * The new score value to be updated.
   */
  score: number;
}

/**
 * Interface representing a main section with subsections.
 */
interface MainSection {
  /**
   * The unique key identifying the main section.
   */
  key: number;

  /**
   * The name of the main section.
   */
  mainSection: string;

  /**
   * An array of objects representing subsections, each containing a display name and a schema
   */
  subsections: { display: string; schema: string }[];
}

/**
 * Props for EvaluationForm component
 */
interface EvaluationFormProps {
  /**
   * Initial values for the form fields.
   * These values are used to pre-populate the form.
   */
  initialValues?: Record<string, string | number | null>;

  /**
   * Mode of the form.
   * Determines how the form should behave:
   * - 'read': Display form fields in read-only mode.
   * - 'edit': Allow editing of form fields.
   * - 'create': Display an empty form for creating new evaluations.
   */
  mode: 'read' | 'edit' | 'create';

  /**
   * Function called when the form is submitted.
   * This function handles the submission of form data.
   */
  onSubmit?: (formData: FormData) => Promise<void>;

  /** Data containing suppliername and service type. */
  supplierData?: SupplierDataType;
}

/**
 * Array of main sections with subsections for the evaluation form.
 */
const mainSectionsData: MainSection[] = [
  {
    key: 1,
    mainSection: 'Adherence to vendor info-sec policy',
    subsections: [
      { display: 'Adherence Requirement', schema: 'securityAdherence' },
      { display: 'Proactive NDA', schema: 'ndaProactive' },
    ],
  },
  {
    key: 2,
    mainSection: 'Quality Performance',
    subsections: [
      {
        display: 'Quality of product supplied, or service delivered',
        schema: 'productQuality',
      },
      {
        display:
          'Vendor responded in a timely manner to all questions and service or delivery-related issues',
        schema: 'responsiveness',
      },
    ],
  },
  {
    key: 3,
    mainSection: 'Schedule Commitment',
    subsections: [
      {
        display: 'Vendor was able to maintain the deadline agreed',
        schema: 'deadlineAdherence',
      },
      {
        display: 'Vendor proactiveness in meeting urgency in delivery schedule',
        schema: 'deliveryProactiveness',
      },
    ],
  },
  {
    key: 4,
    mainSection: 'General',
    subsections: [
      {
        display:
          'Vendor issued complete invoices in a clear, accurate, and organized format per agreed timelines to facilitate timely payment',
        schema: 'invoiceClarity',
      },
      {
        display: 'Overall experience with vendor service',
        schema: 'overallExperience',
      },
    ],
  },
];

/**
 * Reducer function for updating scores state.
 * state - The current scores state.
 * action - The action dispatched to update scores state.
 * @returns The updated scores state.
 */
const scoresReducer = (
  state: ScoresState,
  action: ScoresAction,
): ScoresState => {
  switch (action.type) {
    case 'UPDATE_SCORE':
      return {
        ...state,
        [action.table]: {
          ...state[action.table],
          [action.subSection]: action.score,
        },
      };
    default:
      return state;
  }
};

/**
 * Component for rendering an evaluation form.
 */
export function EvaluationForm({
  initialValues,
  onSubmit,
  mode,
  supplierData,
}: EvaluationFormProps): React.JSX.Element {
  /**
   * Determine if the form is in read-only mode
   */
  const isReadOnly = mode === 'read';

  /**
   * Initializes the initial scores state for each subsection of the main sections in the evaluation form.
   * This object represents the initial state of scores.
   */
  const initialScores: ScoresState = mainSectionsData.reduce(
    (accumulator, { key, subsections }) => {
      /**
       * Initialize scores object for the current main section
       */
      accumulator[`scores${key}`] = subsections.reduce(
        /**
         * Reducer function to iterate over the subsections and initialize scores.
         * subAccumulator - The accumulator object for subsection scores.
         * subSection - The current subsection data.
         * @returns The updated accumulator object with scores for the current subsection.
         */
        (subAccumulator, { schema }) => {
          /**
           * Assign default score value or 0 if not provided in initialValues
           */
          subAccumulator[schema] = initialValues?.[schema] || 0;
          return subAccumulator;
        },
        {},
      );
      return accumulator;
    },
    {},
  );

  /**
   * Use reducer to manage scores state
   */
  const [scores, dispatch] = useReducer(scoresReducer, initialScores);

  /**
   * Calculates the total score for a specific main section based on its subsections' scores.
   */
  const calculateTotalScore = (tableData: string): number =>
    Object.values(scores[tableData]).reduce((sum, score) => sum + score, 0);

  /**
   * Calculates the total possible score based on the maximum score per subsection and sections with scores.
   */
  const calculateTotalPossibleScore = (): number => {
    const maxScorePerSubsection = 10;
    const sectionsWithScores = mainSectionsData.filter(({ key }) =>
      Object.values(scores[`scores${key}`]).some((score) => score > 0),
    );
    return sectionsWithScores.length * maxScorePerSubsection;
  };

  const totalScore = mainSectionsData.reduce(
    (sum, { key }) => sum + calculateTotalScore(`scores${key}`),
    0,
  );

  /**
   * Calculates the total percentage of the evaluation based on the total score and the maximum score.
   */
  const calculateTotalPercentage = (): number => {
    if (maxScore === 0) {
      return 0; // Avoid division by zero, return 0 when maxScore is 0
    }
    return (totalScore / maxScore) * 100;
  };

  const maxScore = calculateTotalPossibleScore();

  const totalPercentage = calculateTotalPercentage();

  let formattedTotalPercentage: string;

  /**
   * Formats the total percentage into a string with or without decimal places.
   */
  if (totalPercentage % 1 === 0) {
    formattedTotalPercentage = totalPercentage.toFixed(0); // No decimal places
  } else {
    formattedTotalPercentage = totalPercentage.toFixed(2); // Two decimal places
  }

  return (
    <>
      <div className="evaluation-header-style ">
        <fieldset>
          <Label>Company Name:</Label>
          <Label>{supplierData?.supplierName}</Label>
        </fieldset>
        <fieldset>
          <Label>Service Type:</Label>
          <Label>{supplierData?.serviceType}</Label>
        </fieldset>
      </div>
      <form action={onSubmit} className="asset-management-form">
        <div className={cn('grid grid-cols-4 gap-4', { hidden: isReadOnly })}>
          <div className={cn('col-span-1', { hidden: mode === 'edit' })}>
            <fieldset className="hidden">
              <Input
                disabled={isReadOnly}
                id="supplierId"
                name="supplierId"
                value={initialValues?.supplierId ?? supplierData?.supplierId}
              />
            </fieldset>
          </div>
          <div className="col-span-1">
            <fieldset className="hidden">
              <Input
                defaultValue={initialValues?.serviceType ?? ''}
                id="serviceType"
                name="serviceType"
                type="text"
                value={
                  initialValues?.serviceType
                    ? initialValues.serviceType
                    : supplierData?.serviceType
                }
              />
            </fieldset>
          </div>
        </div>
        <fieldset>
          <div className="mb-8 w-full">
            {/* Render Table for Each Main Section  */}
            {mainSectionsData.map(({ key, mainSection, subsections }) => (
              <Table
                handleScoreChange={(subSection, score) => {
                  if (!isReadOnly) {
                    dispatch({
                      type: 'UPDATE_SCORE',
                      table: `scores${key}`,
                      subSection,
                      score: Number(score),
                    });
                  }
                }}
                isReadOnly={isReadOnly}
                key={key}
                mainSection={mainSection}
                scores={scores[`scores${key}`]}
                subsections={subsections}
              />
            ))}
          </div>
          <div className="mb-8 w-full">
            <div className="rounded-10 mb-5 w-full rounded-lg bg-slate-100 px-10 py-4 shadow-lg">
              <h4 className="border-b border-gray-300 pb-4 text-sm font-semibold text-black">
                Note
              </h4>
              <section className="pt-3">
                <Textarea
                  defaultValue={initialValues?.note ?? ''}
                  disabled={isReadOnly}
                  id="note"
                  name="note"
                  placeholder="Enter note or comment"
                />
              </section>
            </div>
          </div>
          <div className=" grid w-full grid-cols-2 justify-around px-2 text-sm font-semibold">
            <span className="justify-self-start">
              Total Score: {totalScore} / {maxScore}
            </span>
            <span className="justify-self-end ">
              Total Percentage: {formattedTotalPercentage}%
            </span>
          </div>
        </fieldset>
        <div>
          {mode === 'read' ? null : (
            <SubmitButton type="submit">
              {mode === 'edit' ? 'Edit' : 'Submit'}
            </SubmitButton>
          )}
        </div>
      </form>
    </>
  );
}
