import React from 'react';

/**
 * Props for the Table component.
 */
interface TableProps {
  /**
   * The main section of the table.
   */
  mainSection: string;

  /**
   * An array of subsections, each containing a display name and schema.
   */
  subsections: { display: string; schema: string }[];

  /**
   * Scores for each subsection.
   */
  scores: Record<string, number>;

  /**
   * Indicates whether the table should be displayed in a read-only mode.
   */
  isReadOnly?: boolean;
  /**
   * Function to handle score changes.
   */
  handleScoreChange: (subSection: string, score: number) => void;
}

/**
 * Component representing a table with mainSection, subsections and scores.
 */
function Table({
  mainSection,
  subsections,
  scores,
  isReadOnly,
  handleScoreChange,
}: TableProps): React.JSX.Element {
  /**
   * Calculates the subtotal of the scores.
   */
  const calculateSubTotal = (): number => {
    const subSection1Score = scores[subsections[0].schema] || 0;
    const subSection2Score = scores[subsections[1].schema] || 0;
    return subSection1Score + subSection2Score;
  };

  return (
    <div className="rounded-10 mb-5 w-full rounded-lg bg-slate-100 px-10 py-4 shadow-lg">
      <table className=" w-full" style={{ borderCollapse: 'collapse' }}>
        <thead>
          <tr className="grid grid-cols-2 items-center border-b border-gray-300 pb-4 text-sm text-gray-700">
            <th className="max-w-100 justify-self-start  text-left ">
              {mainSection}
            </th>
            <th className="max-w-100 justify-self-end  text-left">Score</th>
          </tr>
        </thead>
        <tbody className="gap-15 flex w-full flex-col pt-2 text-sm">
          {subsections.map((subSection, index) => (
            <tr className="flex items-center justify-between " key={index}>
              <td className="w-[56%] whitespace-normal py-3 text-slate-600">
                {subSection.display}
              </td>
              {[1, 2, 3, 4, 5].map((score) => (
                <td className="min-w-48 py-3 text-center" key={score}>
                  {isReadOnly ? (
                    // Render a plain div with the selected value in read-only mode
                    <div
                      className={`h-8 w-8 rounded-full pt-2 ${
                        scores[subSection.schema] === score
                          ? 'bg-primary-500 text-white'
                          : 'bg-zinc-300'
                      } cursor-default rounded-full transition duration-300`}
                    >
                      {score}
                    </div>
                  ) : (
                    // Render the editable checkbox in edit mode
                    <label>
                      <input
                        checked={scores[subSection.schema] === score}
                        id={`${subSection.schema}_${score}`}
                        name={subSection.schema}
                        onChange={() => {
                          const currentScore = scores[subSection.schema];
                          const newScore = currentScore === score ? 0 : score;
                          handleScoreChange(subSection.schema, newScore);
                        }}
                        style={{ display: 'none' }}
                        type="checkbox"
                        value={score ? score : 0}
                      />
                      <div
                        className={`h-8 w-8 rounded-full pt-2 ${
                          scores[subSection.schema] === score
                            ? 'bg-primary-500 hover:bg-primary-200 text-white'
                            : 'hover:bg-primary-200 bg-zinc-300'
                        } cursor-pointer rounded-full transition duration-300`}
                      >
                        {score}
                      </div>
                    </label>
                  )}
                </td>
              ))}
              <td className="min-w-[50px] py-3 text-center">
                {scores[subSection.schema] === 0
                  ? '-'
                  : scores[subSection.schema]}
              </td>
            </tr>
          ))}
          <tr className="min-w-50 flex justify-end pt-3 font-semibold text-stone-700">
            <td className="text-center">{`${calculateSubTotal()}/10`}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}
export default Table;
