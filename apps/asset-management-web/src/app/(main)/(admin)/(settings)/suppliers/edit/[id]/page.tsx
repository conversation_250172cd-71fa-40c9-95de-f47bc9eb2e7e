import React from 'react';
import { toast } from 'sonner';
import { getSupplier } from '@/services/suppliers';
import { getAllDepartments } from '@/services/departments';
import { getAllUsersOptions } from '@/services/users';
import { EditSupplier } from './edit-supplier';
import { getAllLocationsOptions } from '@/services/locations';
import { SupplierAlterData } from '@/types/supplier';

interface EditSupplierPageProps {
  params: {
    id: string;
  };
}
export default async function EditSupplierPage({
  params: { id },
}: EditSupplierPageProps): Promise<React.JSX.Element> {
  const supplier = await getSupplier(id);
  const departments = await getAllDepartments();
  const locationResponse = await getAllLocationsOptions();

  if (departments.type === 'error') {
    return <>Not able to load departments. Please try again!</>;
  }

  if (locationResponse.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }

  if (supplier.type === 'error') {
    if (supplier.errors.errorMessages) {
      supplier.errors.errorMessages.forEach((err) => toast.error(err));
    }
    return (
      <>
        Something went wrong!, could not edit supplier this time, please try
        again
      </>
    );
  }

  const users = await getAllUsersOptions();
  if (users.type === 'error') {
    return <>Not able to laod users. Please try again!</>;
  }
  const { evaluatorDepartment, location, ...rest } = supplier.data;

  const initialValues: SupplierAlterData = {
    ...rest,
    location: location.map((item) => item.id),
    departmentId: evaluatorDepartment?.id ?? undefined,
  };
  return (
    <EditSupplier
      departments={departments.data}
      id={id}
      initialValues={initialValues}
      locations={locationResponse.data}
      users={users.data}
    />
  );
}
