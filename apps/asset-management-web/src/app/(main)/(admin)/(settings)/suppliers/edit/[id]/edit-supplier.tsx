'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import type { SupplierAlterData } from '@/types/supplier';
import { FormSchema } from '@/schemas/supplier';
import { editSupplier } from '@/services/suppliers';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { toUpperSnakeCase } from '@/utils/string-parser';
import { EntityType, type Option } from '@/types';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { SupplierForm } from '../../SupplierForm';

interface EditSupplierProps {
  /**
   * Unique identifier for the supplier
   */
  id: string;
  /**
   * Initial values for the supplier form
   */
  initialValues: SupplierAlterData;
  departments: Option[];
  users: Option[];
  locations: Option[];
}

export function EditSupplier({
  id,
  initialValues,
  departments,
  users,
  locations,
}: EditSupplierProps): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<SupplierAlterData>();

  async function handleSupplierForm(formData: FormData): Promise<void> {
    const selectionCriteria = reduceFormData('selectionCriteria', formData);
    const agreement = reduceFormData('agreement', formData);
    const location = reduceFormData('location', formData);
    const notifyTo = reduceFormData('notifyTo', formData);

    formData.set(
      'evaluationFrequency',
      toUpperSnakeCase(formData.get('evaluationFrequency')?.toString() || ''),
    );
    const { supplierImageUrl, ...supplierDetails } =
      Object.fromEntries(formData);

    const supplierData = {
      ...supplierDetails,
      supplierImageUrl: '',
      selectionCriteria,
      agreement,
      location,
      notifyTo,
    };

    const parseData = FormSchema.safeParse(supplierData);

    if (!parseData.success) {
      setFieldErrors(parseData.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    let uploadedSupplierImageUrl = initialValues.supplierImageUrl;
    if (
      isImage &&
      supplierImageUrl instanceof File &&
      supplierImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', supplierImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Supplier,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedSupplierImageUrl = generateCloudFrontUrl(
        EntityType.Supplier,
        imageUploadResponse.data.fileName,
      );
    }

    const updateSupplierData = {
      ...parseData.data,
      supplierImageUrl: isImage ? uploadedSupplierImageUrl : '',
    };

    const response = await editSupplier(id, updateSupplierData);

    if (response.type === 'error') {
      if (response.errors.errorMessages) {
        response.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error(
        'Something went wrong, could not edit supplier this time, please try again',
      );
      return;
    }

    toast.success('Supplier edited successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Supplier</h1>
      <SupplierForm
        departments={departments}
        errors={fieldErrors}
        initialValues={initialValues}
        locations={locations}
        onSubmit={handleSupplierForm}
        setIsImage={setIsImage}
        users={users}
      />
    </div>
  );
}
