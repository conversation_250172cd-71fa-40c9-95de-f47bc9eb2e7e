import React from 'react';
import { getAllDepartments } from '@/services/departments';
import { getAllUsersOptions } from '@/services/users';
import { CreateSupplier } from './create-supplier';
import { getAllLocationsOptions } from '@/services/locations';

export default async function CreateSupplierPage(): Promise<React.JSX.Element> {
  const departments = await getAllDepartments();
  const users = await getAllUsersOptions();
  const locations = await getAllLocationsOptions();

  if (departments.type === 'error') {
    return <>Not able to load departments. Please try again!</>;
  }
  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }

  if (users.type === 'error') {
    return <>Not able to laod users. Please try again!</>;
  }

  return (
    <CreateSupplier
      departments={departments.data}
      users={users.data}
      locations={locations.data}
    />
  );
}
