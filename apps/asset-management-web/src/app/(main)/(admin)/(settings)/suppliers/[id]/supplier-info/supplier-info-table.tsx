import React from 'react';
import { displayDataOrDefault } from '@/utils/helper-functions';
import type { SupplierResponseData } from '@/types/supplier';
import { toStartCase } from '@/utils/string-parser';
import { InfoItem } from '@/components/InfoItems/info-item';

interface SupplierInfoTableProps {
  supplierData: SupplierResponseData;
}

interface SupplierInfoType {
  displayName: string;
  value: string | string[] | null;
}

export function SupplierInfoTable({
  supplierData,
}: SupplierInfoTableProps): React.JSX.Element {
  const supplierInfo: SupplierInfoType[] = [
    {
      displayName: 'Name',
      value: supplierData.name,
    },
    {
      displayName: 'Address',
      value: supplierData.address ?? '',
    },
    {
      displayName: 'Location',
      value: supplierData.location.map((location) => location.name).join(', '),
    },
    {
      displayName: 'Contact Name',
      value: supplierData.contactName,
    },
    {
      displayName: 'Contact Email',
      value: supplierData.contactEmail ?? '',
    },
    {
      displayName: 'Contact Number',
      value: supplierData.contactPhoneNumber,
    },
    {
      displayName: 'Agreement',
      value: supplierData.agreement,
    },
    {
      displayName: 'Evaluation Department',
      value: supplierData.evaluatorDepartment?.name ?? '',
    },
    {
      displayName: 'Evaluation Frequency',
      value: toStartCase(supplierData.evaluationFrequency),
    },
    {
      displayName: 'Transaction Frequency',
      value: supplierData.transactionFrequency
        ? toStartCase(supplierData.transactionFrequency)
        : '-',
    },
    {
      displayName: 'Selection Criteria',
      value: supplierData.selectionCriteria ?? '',
    },
    {
      displayName: 'Notes',
      value: supplierData.note,
    },
  ];

  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        {supplierInfo.map((supplier) => (
          <InfoItem
            key={supplier.displayName}
            name={supplier.displayName}
            value={
              Array.isArray(supplier.value)
                ? displayDataOrDefault(supplier.value.join(', '))
                : displayDataOrDefault(supplier.value)
            }
          />
        ))}
      </div>
      {supplierData.supplierImageUrl ? (
        <div className="flex w-1/4 flex-shrink-0 justify-end">
          <img
            alt="supplier-name"
            className="h-[200px] w-auto rounded object-cover shadow"
            src={supplierData.supplierImageUrl}
            width={400}
            height={400}
          />
        </div>
      ) : null}
    </div>
  );
}
