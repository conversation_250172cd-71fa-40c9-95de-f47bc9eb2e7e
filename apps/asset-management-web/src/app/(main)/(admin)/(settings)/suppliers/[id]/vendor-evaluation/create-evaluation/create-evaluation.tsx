'use client';

import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { createEvaluation } from '@/services/evaluation';
import { evaluationFormSchema } from '@/schemas/evaluation';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { EvaluationForm } from '../evaluation-form';
import type { SupplierDataType } from '../../../types';

/**
 * Props for CreateEvaluationForm
 */
interface CreateEvaluationFormProps {
  /** Data containing suppliername and service type. */
  supplierData: SupplierDataType;
}

/**
 * Renders the component for submitting a new evaluation
 */
export function CreateEvaluationForm({
  supplierData,
}: CreateEvaluationFormProps): React.JSX.Element {
  const router = useRouter();

  /**
   * Handles the submission of evaluation form data.
   */
  const addFeedback = async (formData: FormData): Promise<void> => {
    const evaluationFormData = Object.fromEntries(formData);

    const {
      securityAdherence = 0,
      ndaProactive = 0,
      productQuality = 0,
      responsiveness = 0,
      deadlineAdherence = 0,
      deliveryProactiveness = 0,
      invoiceClarity = 0,
      overallExperience = 0,
      supplierId,
      serviceType,
      note,
    } = evaluationFormData;

    const currentDate = new Date();
    const createdAt = currentDate.toISOString();

    const evaluationData = {
      securityAdherence: Number(securityAdherence),
      ndaProactive: Number(ndaProactive),
      productQuality: Number(productQuality),
      responsiveness: Number(responsiveness),
      deadlineAdherence: Number(deadlineAdherence),
      deliveryProactiveness: Number(deliveryProactiveness),
      invoiceClarity: Number(invoiceClarity),
      overallExperience: Number(overallExperience),
      date: createdAt,
      supplierId,
      serviceType,
      note,
    };

    const parsedResult = evaluationFormSchema.safeParse({
      ...evaluationData,
    });

    if (!parsedResult.success) {
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }

    const evaluationResponse = await createEvaluation(parsedResult.data);
    if (evaluationResponse.type === 'error') {
      if (evaluationResponse.errors.errorMessages) {
        evaluationResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(
        'Something went wrong!, could not evaluate this time. Please try again',
      );
      return;
    }

    toast.success('Evaluated Successfully');
    router.back();
  };

  return (
    <div className="shadow-container evaluation-form-style ">
      <h1 className="asset-management-form-heading">
        Vendor Performance Evaluation Form
      </h1>
      <EvaluationForm
        mode="create"
        onSubmit={addFeedback}
        supplierData={supplierData}
      />
    </div>
  );
}
