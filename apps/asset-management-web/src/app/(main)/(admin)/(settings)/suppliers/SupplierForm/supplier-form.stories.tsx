import type { Meta, StoryObj } from '@storybook/react';
import type { SupplierAlterData } from '@/types/supplier';
import { SupplierForm } from './supplier-form';

const departments = [
  {
    displayName: 'Accounts',
    value: '1',
  },
  {
    displayName: 'HR',
    value: '2',
  },
  {
    displayName: 'Operations',
    value: '3',
  },
];
const locations = [
  { displayName: 'Mangalore', value: '1' },
  { displayName: 'Bangalore', value: '2' },
];

const initialValues: SupplierAlterData = {
  name: 'Abc',
  address: '123 Main Street',
  location: ['Mangalore'],
  zipCode: '98765',
  agreement: ['AMC'],
  serviceType: 'Internet',
  evaluationFrequency: 'Quarterly',
  contactName: '<PERSON>',
  contactEmail: '<EMAIL>',
  contactPhoneNumber: '**********',
  selectionCriteria: [
    'Quality records of previously demonstrated capabilities',
  ],
  supplierImageUrl: 'https://example.com/supplier-image.jpg',
  note: 'This is a dummy note for the supplier.',
  departmentId: '1',
};

const meta: Meta<typeof SupplierForm> = {
  title: 'components/Supplier/SupplierForm',
  component: SupplierForm,
};

export default meta;

type Story = StoryObj<typeof SupplierForm>;

export const DefaultCreateSupplierForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="mb-10 text-center text-3xl font-bold text-slate-800">
          Create Supplier
        </h1>
        <SupplierForm
          departments={departments}
          locations={locations}
          users={[]}
        />
      </div>
    );
  },
};

export const DefaultEditSupplierForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="mb-10 text-center text-3xl font-bold text-slate-800">
          Edit Supplier
        </h1>
        <SupplierForm
          departments={departments}
          initialValues={initialValues}
          locations={locations}
          users={null}
        />
      </div>
    );
  },
};
