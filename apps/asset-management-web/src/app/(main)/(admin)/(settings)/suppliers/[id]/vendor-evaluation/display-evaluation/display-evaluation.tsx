'use client';

import type { EvaluationRequestDataType } from '@/types/evaluation';
import { EvaluationForm } from '../evaluation-form';

/**
 * Props for the DisplayEvaluation component.
 */
interface EvaluationProps {
  /**
   * Initial values for the evaluation form
   */
  initialValues: EvaluationRequestDataType;

  /**
   * Name of the evaluated vendor
   */
  supplierName: string;
}

/**
 * Component for displaying evaluated data in read-only mode.
 */
export function Evaluation({
  initialValues,
  supplierName,
}: EvaluationProps): React.JSX.Element {
  return (
    <div className="shadow-container evaluation-form-style">
      <h1 className="asset-management-form-heading">
        Vendor Performance Score Card
      </h1>
      <EvaluationForm
        initialValues={initialValues}
        mode="read"
        supplierData={{ supplierName, serviceType: initialValues.serviceType }}
      />
    </div>
  );
}
