import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import { getPolicydetailsInfo } from '@/services/suppliers';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { policyRoutes } from '@/constants/routes';
import Link from 'next/link';
import { capitalizeFirstLetter } from 'utils';

const tableHeaders = [
  'Name',
  'Company',
  'Start Date',
  'End Date',
  'Type of Policy',
  'Status',
  'Locations',
];

interface SupplierPolicyInfoTableProps {
  id: string;
}
export async function SupplierPolicyInfoTable({
  id,
}: SupplierPolicyInfoTableProps): Promise<React.JSX.Element> {
  const policyDetailsInfo = await getPolicydetailsInfo(id);
  return (
    <Table className="asset-management-table-md rounded-none shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {policyDetailsInfo.map((data) => (
          <TableRow key={data.id}>
            <TableCell>
              <Link
                className="text-primary-600 first-letter:uppercase"
                href={`${policyRoutes.MAIN}/${data.id}`}
              >
                {data.name}
              </Link>
            </TableCell>
            <TableCell>{displayDataOrDefault(data.companyId)}</TableCell>
            <TableCell>
              {data.startDate ? parseDate('MMM dd, yyyy')(data.startDate) : '-'}
            </TableCell>
            <TableCell>
              {data.endDate ? parseDate('MMM dd, yyyy')(data.endDate) : '-'}
            </TableCell>
            <TableCell>
              {capitalizeFirstLetter(data.typeOfPolicy ?? '')}
            </TableCell>
            <TableCell>{capitalizeFirstLetter(data.status ?? '')}</TableCell>
            <TableCell>
              {data.locations?.length ? data.locations.join(', ') : '-'}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
