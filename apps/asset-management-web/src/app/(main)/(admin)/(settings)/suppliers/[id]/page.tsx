import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>l<PERSON>ipContent,
  ToolTipTrigger,
} from 'ui';
import Link from 'next/link';
import { PackageCheck } from 'lucide-react';
import { getSupplier } from '@/services/suppliers';
import { getAllEvaluations } from '@/services/evaluation';
import { supplierRoutes } from '@/constants/routes';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { SupplierInfoTable } from './supplier-info/supplier-info-table';
import { EvaluationInfoTable } from './vendor-evaluation/evaluation-info-table';
import { SupplierPolicyInfoTable } from './SupplierPolicyInfo/supplier-policy-info';

/**
 *  Props for the EvaluationList component.
 */
interface EvaluationListProps {
  /** Parameters for the page, containing the supplier ID. */
  params: {
    /** The ID of the supplier. */
    id: string;
  };
}

/**
 * Retrieves and displays the list of evaluations for a supplier.
 * params - The parameters containing the supplier ID.
 * */
export default async function EvaluationList({
  params: { id },
}: EvaluationListProps): Promise<React.JSX.Element> {
  /**
   * Retrieves supplier information
   */
  const supplierInfoResponse = await getSupplier(id);

  if (supplierInfoResponse.type === 'error') {
    return <>Something went wrong</>;
  }

  const { name: supplierName, id: supplierId } = supplierInfoResponse.data;

  /**
   * Retrieve all evaluations for the supplier
   */
  const evaluationResponse = await getAllEvaluations(supplierId);

  if (evaluationResponse.type === 'error') {
    return (
      <>
        Unable to retrieve Evaluation data at the moment. Please try again
        later!
      </>
    );
  }

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {supplierName}
        </h1>
        <div className="flex gap-2">
          <ToolTip>
            <Button asChild className="h-11 w-12 p-0" variant="outlined">
              <Link
                href={`${supplierRoutes.BASE}/${id}${supplierRoutes.CREATE_EVALUATION}`}
              >
                <ToolTipTrigger asChild className="h-full w-full">
                  <div className="flex h-full w-full cursor-pointer items-center justify-center">
                    <PackageCheck className="w-5" />
                  </div>
                </ToolTipTrigger>
              </Link>
            </Button>
            <ToolTipContent className="start-case z-10">
              Evaluate
            </ToolTipContent>

            <ActionPanel
              actions={['document']}
              entityId={supplierId}
              entityName={supplierName}
              routes={supplierRoutes}
            />
          </ToolTip>
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="policy">Policy</TabsTrigger>
            <TabsTrigger value="evaluation">Evaluation</TabsTrigger>
            <TabsTrigger value="document">Document</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <SupplierInfoTable supplierData={supplierInfoResponse.data} />
          </TabsContent>
          <TabsContent value="policy">
            <SupplierPolicyInfoTable id={id} />
          </TabsContent>
          <TabsContent value="evaluation">
            <EvaluationInfoTable
              evaluationData={evaluationResponse.data ?? []}
            />
          </TabsContent>
          <TabsContent value="document">
            <DocumentTable
              categoryType={CategoryType.SUPPLIER}
              entityType={EntityType.Supplier}
              id={id}
            />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
