'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import type {
  ManufacturerAlterData,
  ManufacturerResponseData,
} from '@/types/manufacturer';
import { editManufacturer } from '@/services/manufacturers';
import { FormSchema } from '@/schemas/manufacturer';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType } from '@/types';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { ManufacturerForm } from '../../ManufacturerForm/manufacturer-form';

interface EditManufacturerProps {
  /**
   * Unique identifier for the manufacturer
   */
  id: string;
  /**
   * Initial values for the manufacturer form
   */
  initialValues: ManufacturerResponseData;
}
export function EditManufacturer({
  id,
  initialValues,
}: EditManufacturerProps): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<ManufacturerAlterData>();

  async function handleManufacturerForm(formData: FormData): Promise<void> {
    const { manufacturerImageUrl, ...manufacturerDetails } =
      Object.fromEntries(formData);
    const parsedData = FormSchema.safeParse({
      ...manufacturerDetails,
      manufacturerImageUrl: '',
    });

    if (!parsedData.success) {
      setFieldErrors(parsedData.error.flatten().fieldErrors);
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    resetFieldErrors();

    let uploadedManufacturerImageUrl = initialValues.manufacturerImageUrl;
    if (
      isImage &&
      manufacturerImageUrl instanceof File &&
      manufacturerImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', manufacturerImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Manufacturer,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedManufacturerImageUrl = generateCloudFrontUrl(
        EntityType.Manufacturer,
        imageUploadResponse.data.fileName,
      );
    }
    const updateConsumbaleData = {
      ...parsedData.data,
      manufacturerImageUrl: isImage ? uploadedManufacturerImageUrl : '',
    };

    const response = await editManufacturer(id, updateConsumbaleData);

    if (response.type === 'error') {
      if (response.errors.errorMessages) {
        response.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error(
        'Something went wrong, could not edit manufacturer this time, please try again',
      );
      return;
    }

    toast.success('Manufacturer edited successfully');
    router.back();
  }

  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Manufacturer</h1>
      <ManufacturerForm
        errors={fieldErrors}
        initialValues={initialValues}
        onSubmit={handleManufacturerForm}
        setIsImage={setIsImage}
      />
    </div>
  );
}
