import type { <PERSON>a, StoryObj } from '@storybook/react';
import type { ManufacturerData } from '@/types/manufacturer';
import { ManufacturerForm } from './manufacturer-form';

const initialValues: ManufacturerData = {
  id: '123',
  name: 'Company A',
  contactName: '<PERSON>',
  contactEmail: '<EMAIL>',
  contactPhoneNumber: '**********',
  manufacturerImageUrl: 'https://imageurl.com',
  note: 'Initial contact made, awaiting response.',

  assetModels: [{ id: 'asset-model-1' }, { id: 'asset-model-2' }],
  accessories: [{ id: 'accessory-1' }, { id: 'accessory-2' }],
  consumables: [{ id: 'consumable-1' }, { id: 'consumable-2' }],
  softwareLicenses: [
    { id: 'software-license-1' },
    { id: 'software-license-2' },
  ],
  appliances: [{ id: 'appliance-1' }, { id: 'appliance-2' }],
  Policy: [{ id: 'policy-1' }, { id: 'policy-2' }],
  Insurance: [{ id: 'insurance-1' }, { id: 'insurance-2' }],
};

const meta: Meta<typeof ManufacturerForm> = {
  title: 'components/Manufacturer/ManufacturerForm',
  component: ManufacturerForm,
};

export default meta;

type Story = StoryObj<typeof ManufacturerForm>;

export const DefaulCratetManufacturerForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="mb-10 text-center text-3xl font-bold text-slate-800">
          Create Manufacturer
        </h1>
        <ManufacturerForm />
      </div>
    );
  },
};

export const DefaultEditManufacturerForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="mb-10 text-center text-3xl font-bold text-slate-800">
          Edit Manufacturer
        </h1>
        <ManufacturerForm initialValues={initialValues} />
      </div>
    );
  },
};
