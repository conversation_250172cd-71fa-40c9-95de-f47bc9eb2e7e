import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from 'ui';
import { Codesandbox } from 'lucide-react';
import {
  type GetAllQueryParams,
  type TableHeading,
  type SearchParams,
  Routes,
} from '@/types';
import {
  EditButton,
  CreateButton,
  Search,
  DeleteButton,
} from '@/components/client';
import { manufacturerRoutes } from '@/constants/routes';
import { getAllManufacturer } from '@/services/manufacturers';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { ManufacturerResponseData } from '@/types/manufacturer';

export default async function ManufacturerList({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const manufacturerList = await getAllManufacturer(queryParams);

  if (manufacturerList.type === 'error') {
    return (
      <p>
        Something went wrong!, Could not load manufacturers list this time.
        Please try again
      </p>
    );
  }

  const totalPages = getNumberOfPages(
    manufacturerList.count,
    searchParams?.limit,
  );

  const tableHeaders: TableHeading[] = [
    { title: 'Image' },
    { title: 'Name' },
    { title: 'Contact Name' },
    { title: 'Contact Email' },
    { title: 'Phone Number', className: 'text-right' },
    { title: 'Notes' },
  ];

  const renderAction = await getActionColumnByRole('manufacturers', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  const isManufacturerInUse = ({
    Insurance,
    Policy,
    accessories,
    appliances,
    assetModels,
    consumables,
    softwareLicenses,
  }: ManufacturerResponseData): boolean => {
    const dependents = [
      ...Insurance,
      ...Policy,
      ...accessories,
      ...appliances,
      ...assetModels,
      ...consumables,
      ...softwareLicenses,
    ];
    return dependents.length > 0;
  };

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Manufacturers</h1>
        <div className="flex gap-3">
          <Search
            className="w-72"
            placeholder="Search by Name, contact, email"
          />
          <CreateButton
            href={manufacturerRoutes.CREATE}
            label="Create Manufacturer"
          />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {manufacturerList.data.length > 0 ? (
            manufacturerList.data.map((manufacturer) => (
              <TableRow key={manufacturer.id}>
                <TableCell className="text-center ">
                  {manufacturer.manufacturerImageUrl ? (
                    <img
                      alt={manufacturer.name}
                      className="img-cell-style"
                      height={10}
                      src={manufacturer.manufacturerImageUrl}
                      width={30}
                    />
                  ) : (
                    <Codesandbox className="h-7 w-7" />
                  )}
                </TableCell>
                <TableCell>{manufacturer.name}</TableCell>
                <TableCell>
                  {displayDataOrDefault(manufacturer.contactName)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(manufacturer.contactEmail)}
                </TableCell>
                <TableCell className="text-right">
                  {displayDataOrDefault(manufacturer.contactPhoneNumber)}
                </TableCell>
                <TableCell className="max-w-[200px]">
                  <HoverCard>
                    <HoverCardTrigger className="line-clamp-1">
                      {displayDataOrDefault(manufacturer.note)}
                    </HoverCardTrigger>
                    {manufacturer.note ? (
                      <HoverCardContent>{manufacturer.note}</HoverCardContent>
                    ) : null}
                  </HoverCard>
                </TableCell>
                {renderAction ? (
                  <TableCell className="flex items-center justify-center gap-3">
                    <EditButton
                      href={`${manufacturerRoutes.EDIT}/${manufacturer.id}`}
                    />
                    <DeleteEntity
                      entityId={manufacturer.id}
                      entityName={manufacturer.name}
                      entityType={Routes.MANUFACTURERS}
                    >
                      <DeleteButton
                        isDisabled={isManufacturerInUse(manufacturer)}
                      />
                    </DeleteEntity>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={manufacturerList.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
