'use client';

import {
  SubmitButton,
  Label,
  Textarea,
  Input,
  ImageInputWithPreview,
} from 'ui';
import type {
  ManufacturerAlterData,
  ManufacturerResponseData,
} from '@/types/manufacturer';
import type { FieldErrors } from '@/types';
import { allowedImageType } from '@/constants';

interface ManufacturerFormProps {
  /**
   * Optional initial values for the form fields.
   * If provided, the form will be pre-populated with these values for editing.
   */
  initialValues?: ManufacturerResponseData;
  /**
   * Callback function triggered when the form is submitted.
   */
  onSubmit?: (formData: FormData) => Promise<void>;

  errors?: FieldErrors<ManufacturerAlterData>;
  /**
   * Optional callback function to update the status of the defualt image is provided or not.
   */
  setIsImage?: (isImage: boolean) => void;
}

/**
 * Form component for creating or editing a manufacturer item.
 */
export function ManufacturerForm({
  initialValues,
  onSubmit,
  errors,
  setIsImage,
}: ManufacturerFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="name" required>
            Name
          </Label>
          <Input
            defaultValue={initialValues?.name || ''}
            id="name"
            isInvalidInput={Boolean(errors?.name)}
            name="name"
            placeholder="Enter name"
          />
          {errors?.name?.length ? <p>{errors.name[0]}</p> : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="contactName">Contact Name</Label>
          <Input
            defaultValue={initialValues?.contactName || ''}
            id="contactName"
            name="contactName"
            placeholder="Enter contact name"
          />
          {errors?.contactName?.length ? <p>{errors.contactName[0]}</p> : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="contactEmail">Contact Email</Label>
          <Input
            defaultValue={initialValues?.contactEmail || ''}
            id="contactEmail"
            name="contactEmail"
            placeholder="Enter email"
          />
          {errors?.contactEmail?.length ? (
            <p>{errors.contactEmail[0]}</p>
          ) : null}
        </fieldset>

        <fieldset>
          <Label htmlFor="contactPhoneNumber">Phone Number</Label>
          <Input
            defaultValue={initialValues?.contactPhoneNumber || ''}
            id="contactPhoneNumber"
            isInvalidInput={Boolean(errors?.contactPhoneNumber)}
            name="contactPhoneNumber"
            placeholder="Enter phone number"
            type="number"
          />
          {errors?.contactPhoneNumber?.length ? (
            <p>{errors.contactPhoneNumber[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          defaultValue={initialValues?.note || ''}
          id="note"
          name="note"
          placeholder="Enter notes"
          rows={4}
        />
        {errors?.note?.length ? <p>{errors.note[0]}</p> : null}
      </fieldset>

      <fieldset>
        <Label htmlFor="manufacturerImageUrl">Select Image</Label>
        <ImageInputWithPreview
          accept={allowedImageType.join(',')}
          className="mt-2"
          defaultValue={initialValues?.manufacturerImageUrl || ''}
          id="manufacturerImageUrl"
          name="manufacturerImageUrl"
          setIsImage={setIsImage}
        />
        {errors?.manufacturerImageUrl?.length ? (
          <p>{errors.manufacturerImageUrl[0]}</p>
        ) : null}
      </fieldset>
      <div>
        <SubmitButton>{initialValues ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
