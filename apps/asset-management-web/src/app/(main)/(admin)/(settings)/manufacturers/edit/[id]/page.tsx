import React from 'react';
import { toast } from 'sonner';
import { getManufacturer } from '@/services/manufacturers';
import { EditManufacturer } from './edit-manufacturer';

interface EditManufacturerPageProps {
  params: {
    id: string;
  };
}

export default async function EditManufacturerPage({
  params: { id },
}: EditManufacturerPageProps): Promise<React.JSX.Element> {
  const manufacturer = await getManufacturer(id);
  if (manufacturer.type === 'error') {
    if (manufacturer.errors.errorMessages) {
      manufacturer.errors.errorMessages.forEach((err) => toast.error(err));
    }
    return (
      <>
        Something went wrong!, could not edit manufacturer this time, please try
        again
      </>
    );
  }

  return <EditManufacturer id={id} initialValues={manufacturer.data} />;
}
