'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { createManufacturer } from '@/services/manufacturers';
import { FormSchema } from '@/schemas/manufacturer';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { ManufacturerAlterData } from '@/types/manufacturer';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType } from '@/types';
import { INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { ManufacturerForm } from '../ManufacturerForm';

export function CreateManufacturer(): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<ManufacturerAlterData>();

  async function handleManufacturerForm(formData: FormData): Promise<void> {
    const { manufacturerImageUrl, ...manufacturerDetails } =
      Object.fromEntries(formData);
    const parsedData = FormSchema.safeParse({
      ...manufacturerDetails,
      manufacturerImageUrl: '',
    });

    if (!parsedData.success) {
      setFieldErrors(parsedData.error.flatten().fieldErrors);
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }

    resetFieldErrors();

    let uploadedManufacturerImageUrl = '';
    if (
      isImage &&
      manufacturerImageUrl instanceof File &&
      manufacturerImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', manufacturerImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Manufacturer,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedManufacturerImageUrl = generateCloudFrontUrl(
        EntityType.Manufacturer,
        imageUploadResponse.data.fileName,
      );
    }

    const createManufacturerData = {
      ...parsedData.data,
      manufacturerImageUrl: uploadedManufacturerImageUrl,
    };

    const response = await createManufacturer(createManufacturerData);

    if (response.type === 'error') {
      if (response.errors.errorMessages) {
        response.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error(
        'Something went wrong!, could not create manufacturer this time. Please try again',
      );
      return;
    }

    toast.success('Manufacturer created successfully!');
    router.back();
  }

  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Manufacturer</h1>
      <ManufacturerForm
        errors={fieldErrors}
        onSubmit={handleManufacturerForm}
        setIsImage={setIsImage}
      />
    </div>
  );
}
