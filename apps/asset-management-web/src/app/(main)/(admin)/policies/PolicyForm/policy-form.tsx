import { Label, Input, Textarea, SubmitButton } from 'ui';
import { OptionsComboBox, PrimitiveComboBox } from '@/components/client';
import type { PolicyFormData } from '@/types/policies';
import type { FieldErrors, Option } from '@/types';
import { policyTypes, status } from '@/constants';
import {
  renderCustomFieldGroups,
  type CustomField,
} from '@/utils/custom-field';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

interface CreatePolicyFormProps {
  mode: 'create';
  initialPolicyInfo?: Partial<PolicyFormData>;
  policyFieldErrors?: FieldErrors<PolicyFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  vendors: Option[];
  manufacturers: Option[];
  location: Option[];
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditPolicyFormProps {
  mode: 'edit' | 'renew';
  initialPolicyInfo?: PolicyFormData;
  policyFieldErrors?: FieldErrors<PolicyFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  vendors: Option[];
  manufacturers: Option[];
  location: Option[];
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

export function PolicyForm(
  props: CreatePolicyFormProps | EditPolicyFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    mode,
    vendors,
    manufacturers,
    location,
    initialPolicyInfo,
    policyFieldErrors,
    setSelectedData,
    fieldGroups,
    customFields,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialPolicyInfo,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-7">
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="name" required>
                Name
              </Label>
              <Input
                defaultValue={initialPolicyInfo?.name ?? ''}
                id="name"
                isInvalidInput={Boolean(policyFieldErrors?.name)}
                name="name"
                placeholder="Policy name"
                type="text"
              />
              {policyFieldErrors?.name ? (
                <p>{policyFieldErrors.name[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="typeOfPolicy" required>
                Type of Policy
              </Label>
              <PrimitiveComboBox
                id="typeOfPolicy"
                initialValue={initialPolicyInfo?.typeOfPolicy}
                isInvalidInput={Boolean(policyFieldErrors?.typeOfPolicy)}
                name="typeOfPolicy"
                placeholder="Choose policy type"
                values={policyTypes}
              />
              {policyFieldErrors?.typeOfPolicy ? (
                <p>{policyFieldErrors.typeOfPolicy[0]}</p>
              ) : null}
            </fieldset>
          </div>
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="vendorId">Supplier</Label>
              <OptionsComboBox
                id="vendorId"
                initialValue={initialPolicyInfo?.vendorId}
                name="vendorId"
                options={vendors}
                placeholder="Choose supplier"
              />
            </fieldset>
            <fieldset>
              <Label htmlFor="companyId">Company</Label>
              <OptionsComboBox
                id="companyId"
                initialValue={initialPolicyInfo?.companyId}
                name="companyId"
                options={manufacturers}
                placeholder="Choose company"
              />
            </fieldset>
            <fieldset>
              <Label htmlFor="startDate" required>
                Start Date
              </Label>
              <Input
                defaultValue={initialPolicyInfo?.startDate}
                id="startDate"
                isInvalidInput={Boolean(policyFieldErrors?.startDate)}
                name="startDate"
                type="date"
              />
              {policyFieldErrors?.startDate ? (
                <p>{policyFieldErrors.startDate}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="endDate" required>
                End Date
              </Label>
              <Input
                defaultValue={initialPolicyInfo?.endDate}
                id="endDate"
                isInvalidInput={Boolean(policyFieldErrors?.endDate)}
                name="endDate"
                type="date"
              />
              {policyFieldErrors?.endDate ? (
                <p>{policyFieldErrors.endDate}</p>
              ) : null}
            </fieldset>
          </div>
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="status">Status</Label>
              <PrimitiveComboBox
                id="status"
                initialValue={initialPolicyInfo?.status ?? 'Active'}
                name="status"
                placeholder="Choose status"
                values={status}
              />
              {policyFieldErrors?.status ? (
                <p>{policyFieldErrors.status}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="location" required>
                Location
              </Label>
              <MultiSelectComboBox
                id="locations"
                name="locations"
                options={location}
                placeholder="Choose location"
                selectedValues={initialPolicyInfo?.locations ?? []}
              />
              {policyFieldErrors?.locations ? (
                <p>{policyFieldErrors.locations}</p>
              ) : null}
            </fieldset>
          </div>
        </div>
      </div>
      <div>
        <h2 className="mb-4 text-sm font-semibold uppercase text-slate-500">
          Additional Information
        </h2>
        {displayCustomFieldGroups}
        <div className="space-y-4">
          <fieldset>
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              defaultValue={initialPolicyInfo?.notes ?? ''}
              id="notes"
              name="notes"
              placeholder="Include any additional information in this note."
              rows={4}
            />
            {policyFieldErrors?.notes ? <p>{policyFieldErrors.notes}</p> : null}
          </fieldset>
        </div>
      </div>
      <div>
        {mode === 'edit' ? <SubmitButton>Save</SubmitButton> : null}
        {mode === 'renew' ? <SubmitButton>Renew</SubmitButton> : null}
        {mode === 'create' ? <SubmitButton>Create</SubmitButton> : null}
      </div>
    </form>
  );
}
