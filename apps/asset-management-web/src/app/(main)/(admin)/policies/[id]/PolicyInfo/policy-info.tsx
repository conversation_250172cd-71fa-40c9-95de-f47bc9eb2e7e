import React from 'react';
import { parseDate } from '@/utils/date-utils';
import type { PolicyData } from '@/types/policies';
import { InfoItem } from '@/components/InfoItems/info-item';
import { capitalizeFirstLetter } from '@/utils/string-parser';
import type { CustomFieldMap } from '@/utils/custom-field';
import { mapCustomFieldNamesToValues } from '@/utils/custom-field';

interface PolicyInfoProps {
  /** Details about a particular policy. */
  data: PolicyData;
}

/**
 * This is a React component that displays detailed information about policy.
 */
export async function PolicyInfo({
  data,
}: PolicyInfoProps): Promise<React.JSX.Element> {
  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    data.customFields?.data,
  );
  return (
    <div className="space-y-5 px-6 pb-6 pt-4 text-xs text-slate-600 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
      <InfoItem
        name="Type Of Policy"
        value={capitalizeFirstLetter(data.typeOfPolicy)}
      />
      <InfoItem name="Vendor" value={data.vendor?.name} />
      <InfoItem name="Company" value={data.company?.name} />
      <InfoItem
        name="Location"
        value={data.locations.map((location) => location.name).join(', ')}
      />
      <InfoItem
        name="Start Date"
        value={
          data.startDate ? parseDate('MMM dd, yyyy')(data.startDate) : null
        }
      />
      <InfoItem
        name="End Date"
        value={data.endDate ? parseDate('MMM dd, yyyy')(data.endDate) : null}
      />
      <InfoItem
        name="Status"
        value={capitalizeFirstLetter(data.status ?? '')}
      />
      <InfoItem name="Created By" value={data.createdBy?.name} />
      <InfoItem
        name="Created Date"
        value={
          data.createdAt ? parseDate('MMM dd, yyyy')(data.createdAt) : null
        }
      />
      <InfoItem name="Notes" value={data.notes} />
      {Object.entries(customFieldMap).map(([fieldName, value]) => (
        <InfoItem key={fieldName} name={fieldName} value={value} />
      ))}
    </div>
  );
}
