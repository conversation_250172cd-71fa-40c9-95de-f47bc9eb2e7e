import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionTrigger,
  AccordionItem,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import { generateUpdateText } from '@/utils/helper-functions';
import { getPolicyHistoryInfo } from '@/services/policies';

const tableHeaders = ['Actions Performed', 'Date', 'Action By'];
interface PolicyHistoryProps {
  id: string;
}

/**
 * Component representing a table displaying the history of actions performed on policy.
 * AccessoryHistoryProps represents the props for the PolicyHistory component.
 * returns a Promise resolving to the JSX element representing the policy history table.
 */
export async function PolicyHistory({
  id,
}: PolicyHistoryProps): Promise<React.JSX.Element> {
  const policyHistoryInfo = await getPolicyHistoryInfo(id);

  if (policyHistoryInfo.type === 'error') {
    return <>Something went wrong</>;
  }

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {policyHistoryInfo.data.map((history) => (
          <TableRow key={history.log.userId}>
            <TableCell>
              {(() => {
                switch (history.action) {
                  case 'CREATED':
                    return (
                      <p>
                        Policy <span className="font-semibold">created</span>
                      </p>
                    );
                  case 'UPDATED':
                    return history.log.updatedFields &&
                      history.log.updatedFields.length > 0 ? (
                      <Accordion className="w-72">
                        <AccordionItem
                          className="text-xs"
                          value={`item-${history.log.userId}`}
                        >
                          <AccordionTrigger>
                            <p>
                              <span className="font-semibold">Updated</span>(
                              {history.log.updatedFields.length} fields)
                            </p>
                          </AccordionTrigger>
                          <AccordionContent className="space-y-1">
                            {history.log.updatedFields.map(generateUpdateText)}
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <p>No fields updated</p>
                    );
                  case 'RENEWED':
                    return (
                      <p>
                        Policy <span className="font-semibold">renewed</span>
                      </p>
                    );
                }
              })()}
            </TableCell>
            <TableCell>{parseDate('MMM dd, yyyy')(history.date)}</TableCell>
            <TableCell>{history.log.name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
