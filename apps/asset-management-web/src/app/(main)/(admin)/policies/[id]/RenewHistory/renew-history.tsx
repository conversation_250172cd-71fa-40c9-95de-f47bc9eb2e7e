import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { type TableHeading } from '@/types';
import { getPolicy, getPolicyHistoryInfo } from '@/services/policies';
import { parseDate } from '@/utils/date-utils';
import { capitalizeFirstLetter } from '@/utils/string-parser';
import type { PolicyData } from '@/types/policies';

export default async function RenewHistory({
  policyId,
}: {
  policyId: string;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Type Of Policy' },
    { title: 'Vendor' },
    { title: 'Company' },
    { title: 'Start Date' },
    { title: 'End Date' },
    { title: 'Location' },
    { title: 'Status' },
  ];

  const policyHistoryInfo = await getPolicyHistoryInfo(policyId);
  if (policyHistoryInfo.type === 'error') {
    return <>Something Went Wrong!!</>;
  }

  const policyInfo = await getPolicy(policyId);
  if (policyInfo.type === 'error') {
    return <>Something Went Wrong!</>;
  }

  const policyCreateData: PolicyData = policyInfo.data;

  const renewFieldsList = policyHistoryInfo.data
    .filter((item) => {
      return item.action === 'RENEWED';
    })
    .map((item) => item.log.updatedFields);

  const renewHistoryList: PolicyData[] = [];
  const renewedData = policyCreateData;

  renewFieldsList.forEach((renewedEntries) => {
    renewedEntries?.forEach((entry) => {
      renewedData[entry.fieldUpdated] = entry.from;
    });
    renewHistoryList.push(structuredClone(renewedData));
  });

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {renewHistoryList.length > 0 ? (
          renewHistoryList.map((policy) => (
            <TableRow key={policy.id}>
              <TableCell className="first-letter:uppercase">
                {policy.name}
              </TableCell>
              <TableCell>
                {capitalizeFirstLetter(policy.typeOfPolicy)}
              </TableCell>
              <TableCell>{displayDataOrDefault(policy.vendor?.name)}</TableCell>
              <TableCell>
                {displayDataOrDefault(policy.company?.name)}
              </TableCell>
              <TableCell className="start-case">
                {policy.startDate
                  ? parseDate('MMM dd, yyyy')(policy.startDate)
                  : '-'}
              </TableCell>
              <TableCell className="text-left">
                {policy.endDate
                  ? parseDate('MMM dd, yyyy')(policy.endDate)
                  : '-'}
              </TableCell>
              <TableCell>
                {policy.locations.map((location) => location.name).join(', ')}
              </TableCell>
              <TableCell className="text-left">
                {capitalizeFirstLetter(policy.status ?? '')}
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
