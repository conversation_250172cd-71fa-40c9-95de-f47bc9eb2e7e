import { format, parseISO } from 'date-fns';
import { getReminder } from '@/services/reminder';
import type { ReminderFormData } from '@/types/reminder';
import EditReminder from '@/components/EditReminder/edit-reminder';
import { CategoryType } from '@/types';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { getAllUsersOptions } from '@/services/users';
import calculateInitialTimeAndUnit from '@/utils/date-utils';

interface EditReminderPageProps {
  params: {
    id: string;
    reminderId: string;
  };
}

export default async function EditReminderPage({
  params: { reminderId },
}: EditReminderPageProps): Promise<React.JSX.Element> {
  const reminderInfo = await getReminder(reminderId, CategoryType.POLICY);

  if (reminderInfo.type === 'error') {
    return <>Reminder not found</>;
  }

  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  const { endDate } = reminderInfo.data;
  const { time, unit } = calculateInitialTimeAndUnit(
    endDate,
    reminderInfo.data.startDate,
  );

  const editFormData: ReminderFormData = {
    ...reminderInfo.data,
    endDate: format(parseISO(endDate), 'yyyy-MM-dd'),
    time: time.toString(),
    unit,
  };

  return (
    <EditReminder
      categoryType={CategoryType.POLICY}
      initialValues={editFormData}
      reminderId={reminderId}
      users={usersResponse.data}
    />
  );
}
