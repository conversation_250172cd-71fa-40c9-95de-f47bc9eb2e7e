import React from 'react';
import { <PERSON>ptop, Save, Keyboard, Droplet, Users, AirVent } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { format } from 'date-fns';
import Link from 'next/link';
import { DashboardCard } from '@/components/DashboardCard/dashboard-card';
import {
  assetsRoutes,
  licensesRoutes,
  accessoriesRoutes,
  applianceRoutes,
  consumableRoutes,
} from '@/constants/routes';
import { getAllEntityCount, getEntityUpdates } from '@/services/dashboard';
import { Actions, type TableHeading } from '@/types';
import { getUserPermissions } from '@/services/roles';
import { EntityExpiries } from './entity-expiries';

const iconClass = 'text-primary-500 mt-0.5';
const tableStyle = 'asset-management-table !max-h-full';

const applianceTableHeaders: TableHeading[] = [
  { title: 'Name' },
  { title: 'Model No' },
  { title: 'Location' },
  { title: 'Next Service Date' },
];

export default async function Dashboard(): Promise<React.JSX.Element> {
  const permissions = await getUserPermissions();
  const entityCount = await getAllEntityCount();
  const entityUpdates = await getEntityUpdates();

  const assetsPermission = permissions.assets as Actions[] | undefined;
  const licensesPermission = permissions.licenses as Actions[] | undefined;

  if (entityCount.type === 'error') {
    return (
      <>
        Unable to retrieve entity counts at the moment. Please try again later!
      </>
    );
  }
  if (entityUpdates.type === 'error') {
    return (
      <>
        Unable to retrieve entity updates at the moment. Please try again later!
      </>
    );
  }

  /**
   * Array containing data for Dashboard Cards.
   * */
  const dashboardCardData = [
    {
      category: 'Accessories',
      icon: <Keyboard className={iconClass} size={14} />,
      id: 5,
      linkHref: accessoriesRoutes.BASE,
      totalCount: entityCount.data?.accessories?.totalQuantity ?? 0,
      assignedCount: entityCount.data?.accessories?.assigned ?? 0,
    },
    {
      category: 'Assets',
      icon: <Laptop className={iconClass} size={14} />,
      id: 1,
      linkHref: assetsRoutes.MAIN,
      totalCount: entityCount.data?.assets?.totalCount ?? 0,
      assignedCount: entityCount.data?.assets?.assigned ?? 0,
    },
    {
      category: 'Consumables',
      icon: <Droplet className={iconClass} size={14} />,
      id: 2,
      linkHref: consumableRoutes.CONSUMABLES,
      totalCount: entityCount.data?.consumables?.totalQuantity ?? 0,
      assignedCount: entityCount.data?.consumables?.assigned ?? 0,
    },
    {
      category: 'Appliances',
      icon: <AirVent className={iconClass} size={14} />,
      id: 4,
      linkHref: applianceRoutes.MAIN,
      totalCount: entityCount.data?.appliances?.totalCount ?? 0,
    },
    {
      category: 'Licenses',
      icon: <Save className={iconClass} size={14} />,
      id: 3,
      linkHref: licensesRoutes.MAIN,
      totalCount: entityCount.data?.licenses?.totalQuantity ?? 0,
      assignedCount: entityCount.data?.licenses?.assigned ?? 0,
    },
    {
      category: 'Users',
      icon: <Users className={iconClass} size={14} />,
      id: 6,
      linkHref: '/users',
      totalCount: entityCount.data?.users?.totalCount ?? 0,
    },
  ];

  const dashboardCards = dashboardCardData
    .filter((cardData) => {
      return (
        Object.prototype.hasOwnProperty.call(
          permissions,
          cardData.category.toLowerCase(),
        ) && permissions[cardData.category.toLowerCase()].includes('read')
      );
    })
    .map((filteredCardData) => (
      <DashboardCard
        assignedCount={filteredCardData.assignedCount}
        category={filteredCardData.category}
        icon={filteredCardData.icon}
        key={filteredCardData.id}
        linkHref={filteredCardData.linkHref}
        totalCount={filteredCardData.totalCount}
      />
    ));

  const entityUpdatesData = entityUpdates.data;
  const expiredAssets = entityUpdatesData?.assets?.expired;
  const expiredLicenses = entityUpdatesData?.licenses?.expired;
  const assetsAboutToExpire = entityUpdatesData?.assets?.upcomingExpiry;
  const licensesAboutToExpire = entityUpdatesData?.licenses?.upcomingExpiry;
  const applianceDueForService = entityUpdatesData?.appliances;

  return (
    <div className="page mx-auto h-full">
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
        {dashboardCards}
      </div>
      <div className="flex flex-col gap-8 pb-4 text-neutral-600">
        {((assetsPermission && permissions.assets.includes('read')) ||
          (licensesPermission && permissions.licenses.includes('read'))) &&
        ((expiredAssets && expiredAssets.length > 0) ||
          (expiredLicenses && expiredLicenses.length > 0)) ? (
          <EntityExpiries
            assetsExpiry={expiredAssets ?? null}
            licensesExpiry={expiredLicenses ?? null}
            permissions={permissions}
            useCase="expired"
          />
        ) : null}
        {((assetsPermission && permissions.assets.includes('read')) ||
          (licensesPermission && permissions.licenses.includes('read'))) &&
        ((assetsAboutToExpire && assetsAboutToExpire.length > 0) ||
          (licensesAboutToExpire && licensesAboutToExpire.length > 0)) ? (
          <EntityExpiries
            assetsExpiry={assetsAboutToExpire ?? null}
            licensesExpiry={licensesAboutToExpire ?? null}
            permissions={permissions}
            useCase="upcoming-expiry"
          />
        ) : null}

        {Object.prototype.hasOwnProperty.call(permissions, 'appliances') &&
        permissions.appliances.includes('read') &&
        applianceDueForService &&
        applianceDueForService.length > 0 ? (
          <>
            <p className="-mb-4 text-2xl font-bold">
              Appliances due for service
            </p>
            <div className="w-full">
              <Table className={tableStyle}>
                <TableHeader className="asset-management-table-heading">
                  {applianceTableHeaders.map((heading) => {
                    return (
                      <TableColumn
                        className={heading.className}
                        key={heading.title}
                      >
                        {heading.title}
                      </TableColumn>
                    );
                  })}
                </TableHeader>
                <TableBody>
                  {applianceDueForService.map((applianceService) => {
                    return applianceService.appliances.map((service) => {
                      return (
                        <TableRow key={service.id}>
                          <TableCell>
                            <Link
                              className="text-primary-600 hover:underline"
                              href={`${applianceRoutes.MAIN}/${service.id}`}
                            >
                              {service.name}
                            </Link>
                          </TableCell>
                          <TableCell>{service.modelNumber}</TableCell>
                          <TableCell className="start-case">
                            {service.location.name}
                          </TableCell>
                          <TableCell>
                            {applianceService.nextServiceDate
                              ? format(
                                  new Date(applianceService.nextServiceDate),
                                  'iiii, d MMMM yyyy',
                                )
                              : ''}
                          </TableCell>
                        </TableRow>
                      );
                    });
                  })}
                </TableBody>
              </Table>
            </div>
          </>
        ) : null}
      </div>
    </div>
  );
}
