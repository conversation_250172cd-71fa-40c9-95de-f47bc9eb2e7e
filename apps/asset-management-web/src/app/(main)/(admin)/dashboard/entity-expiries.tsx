'use client';
import { useState } from 'react';
import { differenceInDays } from 'date-fns';
import Link from 'next/link';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from 'ui';
import { cn } from 'utils';
import { assetsRoutes, licensesRoutes } from '@/constants/routes';
import type { TableHeading } from '@/types';
import type { GetAllAssetsResponse } from '@/types/assets';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { parseDate } from '@/utils/date-utils';
import type { LicenseList } from '@/types/licenses';
import { PaginationClient } from '@/components/Pagination/pagination-client';
import type { Permissions } from '@/types/roles';

interface ExpiredEntitiesProps {
  assetsExpiry: GetAllAssetsResponse | null;
  licensesExpiry: LicenseList[] | null;
  useCase: 'expired' | 'upcoming-expiry';
  permissions: Permissions;
}

const assetTableHeaders: TableHeading[] = [
  { title: 'Name' },
  { title: 'Assignee' },
  { title: 'Tag' },
  { title: 'Serial No' },
  { title: 'Status' },
  { title: 'Model' },
  { title: 'Location' },
  { title: 'EOW' },
];

const licensesTableHeader: TableHeading[] = [
  { title: 'Name' },
  { title: 'Product Key' },
  { title: 'Licensee' },
  { title: 'Manufacturer' },
  { title: 'Total', className: 'text-right' },
  { title: 'Available', className: 'text-right' },
  { title: 'Expiration' },
];

export function EntityExpiries({
  assetsExpiry,
  licensesExpiry,
  useCase,
  permissions,
}: ExpiredEntitiesProps): React.JSX.Element {
  const tableStyle = 'asset-management-table !max-h-full';

  // State to manage pagination
  const [currentPageAssets, setCurrentPageAssets] = useState<number>(1);
  const [currentPageLicenses, setCurrentPageLicenses] = useState<number>(1);
  const [currentPerPageAssets, setCurrentPerPageAssets] =
    useState<string>('10');
  const [currentPerPageLicenses, setCurrentPerPageLicenses] =
    useState<string>('10');

  //Pagination logic
  const paginatedAssets =
    assetsExpiry?.slice(
      (currentPageAssets - 1) * Number(currentPerPageAssets),
      currentPageAssets * Number(currentPerPageAssets),
    ) || [];
  const paginatedLicenses =
    licensesExpiry?.slice(
      (currentPageLicenses - 1) * Number(currentPerPageLicenses),
      currentPageLicenses * Number(currentPerPageLicenses),
    ) || [];

  // Handle page change for assets
  const handleAssetsPageChange = (page: number) => {
    setCurrentPageAssets(page);
  };

  const handleAssetsRowPerPageChange = (page: string) => {
    setCurrentPerPageAssets(page);
  };

  // Handle page change for licenses
  const handleLicensesPageChange = (page: number) => {
    setCurrentPageLicenses(page);
  };

  const handleLicensesRowPerPageChange = (page: string) => {
    setCurrentPerPageLicenses(page);
  };

  let additionalHeader: TableHeading;
  if (useCase === 'expired') {
    additionalHeader = {
      title: 'Days Passed',
      className: 'text-right',
    };
  } else {
    additionalHeader = {
      title: 'Expires In ( Days )',
      className: 'text-right',
    };
  }

  let additionallicenseHeader: TableHeading = { title: '', className: '' };

  additionallicenseHeader = {
    title: 'Assigned',
    className: 'text-right',
  };

  const alteredAssetsTableHeader = [...assetTableHeaders, additionalHeader];
  const alteredLicensesTableHeader = [
    ...licensesTableHeader,
    additionallicenseHeader,
  ];

  let expiredEntityType: string;
  if (
    assetsExpiry &&
    assetsExpiry.length > 0 &&
    licensesExpiry &&
    licensesExpiry.length > 0
  ) {
    expiredEntityType = 'Assets and Licenses';
  } else if (licensesExpiry && licensesExpiry.length > 0) {
    expiredEntityType = 'Licenses';
  } else {
    expiredEntityType = 'Assets';
  }

  return (
    <div className="flex flex-col gap-y-6">
      {useCase === 'expired' ? (
        <p className="text-2xl font-bold">{`Expired ${expiredEntityType}`}</p>
      ) : (
        <p className="text-2xl font-bold">{`Upcoming Expiries on ${expiredEntityType}`}</p>
      )}
      {assetsExpiry &&
      assetsExpiry.length > 0 &&
      permissions.assets.includes('read') ? (
        <>
          <div className="w-full">
            <p className="mb-2 text-lg">Assets</p>
            <Table className={tableStyle}>
              <TableHeader className="asset-management-table-heading">
                {alteredAssetsTableHeader.map((heading) => {
                  return (
                    <TableColumn
                      className={heading.className}
                      key={heading.title}
                    >
                      {heading.title}
                    </TableColumn>
                  );
                })}
              </TableHeader>
              <TableBody>
                {paginatedAssets.map((asset) => (
                  <TableRow key={asset.id}>
                    <TableCell className="first-letter:uppercase">
                      <Link
                        className="text-primary-600 hover:underline"
                        href={`${assetsRoutes.MAIN}/${asset.id}`}
                      >
                        {asset.assetName}
                      </Link>
                    </TableCell>
                    <TableCell
                      className={cn(
                        !asset.assignedUser?.name ? 'opacity-50' : '',
                      )}
                    >
                      {asset.assignedUser?.name ?? 'Not Assigned'}
                    </TableCell>
                    <TableCell>{asset.assetTag}</TableCell>
                    <TableCell className="max-w-[40px]">
                      <HoverCard>
                        <div className=" flex flex-wrap">
                          <HoverCardTrigger className=" truncate">
                            {asset.serialNumber}
                          </HoverCardTrigger>
                        </div>
                        {asset.serialNumber ? (
                          <HoverCardContent>
                            {' '}
                            {asset.serialNumber}
                          </HoverCardContent>
                        ) : null}
                      </HoverCard>
                    </TableCell>
                    <TableCell>
                      {displayDataOrDefault(asset.assetStatus?.name)}
                    </TableCell>
                    <TableCell>
                      {displayDataOrDefault(asset.assetModel?.modelName)}
                    </TableCell>
                    <TableCell className="start-case">
                      {asset.location.name}
                    </TableCell>
                    <TableCell>
                      {asset.endOfLife
                        ? parseDate('MMM dd, yyyy')(asset.endOfLife)
                        : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      {useCase === 'expired'
                        ? differenceInDays(
                            new Date(),
                            new Date(asset.endOfLife ? asset.endOfLife : ''),
                          )
                        : differenceInDays(
                            new Date(asset.endOfLife ? asset.endOfLife : ''),
                            new Date(),
                          ) + 1}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="pagination relative">
            <PaginationClient
              dataCount={assetsExpiry.length || 0}
              onPageChange={handleAssetsPageChange}
              onRowPerPageChange={handleAssetsRowPerPageChange}
              totalPages={Math.round(
                getNumberOfPages(assetsExpiry.length, currentPerPageAssets),
              )}
            />
          </div>
        </>
      ) : null}
      {licensesExpiry &&
      licensesExpiry.length > 0 &&
      permissions.licenses.includes('read') ? (
        <>
          <div className="w-full">
            <p className="mb-2 text-lg">Licenses</p>
            <Table className={tableStyle}>
              <TableHeader className="asset-management-table-heading">
                {alteredLicensesTableHeader.map((heading) => {
                  return (
                    <TableColumn
                      className={heading.className}
                      key={heading.title}
                    >
                      {heading.title}
                    </TableColumn>
                  );
                })}
              </TableHeader>
              <TableBody>
                {paginatedLicenses.map((license) => (
                  <TableRow key={license.id}>
                    <TableCell className="first-letter:uppercase">
                      <Link
                        className="text-primary-600 hover:underline"
                        href={`${licensesRoutes.MAIN}/${license.id}`}
                      >
                        {license.name}
                      </Link>
                    </TableCell>
                    <TableCell className="max-w-[200px] truncate">
                      <span title={license.productKey}>
                        {license.productKey}
                      </span>
                    </TableCell>
                    <TableCell>
                      {displayDataOrDefault(license.licenseHolderName)}
                    </TableCell>
                    <TableCell>
                      {displayDataOrDefault(license.manufacturer?.name)}
                    </TableCell>
                    <TableCell className="text-right">
                      {license.totalQuantity}
                    </TableCell>
                    <TableCell className="text-right">
                      {license.availableQuantity}
                    </TableCell>
                    <TableCell>
                      {license.expiryDate
                        ? parseDate('MMM dd, yyyy')(license.expiryDate)
                        : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      {/* {useCase === 'expired'
                      ? differenceInDays(
                          new Date(),
                          new Date(
                            license.expiryDate ? license.expiryDate : '',
                          ),
                        )
                      : differenceInDays(
                          new Date(
                            license.expiryDate ? license.expiryDate : '',
                          ),
                          new Date(),
                        ) + 1} */}
                      {license.totalQuantity - license.availableQuantity}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          <div className="pagination relative">
            <PaginationClient
              dataCount={licensesExpiry.length || 0}
              onPageChange={handleLicensesPageChange}
              onRowPerPageChange={handleLicensesRowPerPageChange}
              totalPages={Math.round(
                getNumberOfPages(licensesExpiry.length, currentPerPageLicenses),
              )}
            />
          </div>
        </>
      ) : null}
    </div>
  );
}
