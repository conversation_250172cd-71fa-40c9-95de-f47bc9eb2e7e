import {
  Selector,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import Link from 'next/link';
import {
  AssignUserButton,
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { getAllAccessories } from '@/services/accessories';
import { accessoriesRoutes } from '@/constants/routes';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import {
  EntityType,
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { DownloadDocument } from '@/components/DownloadDocument/download-document';
import { getAllLocations } from '@/services/locations';

interface AccessoriesSearchParams extends SearchParams {
  location?: string;
  category?: string[];
}
export default async function AccessoriesPage({
  searchParams,
}: {
  searchParams?: AccessoriesSearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Category' },
    { title: 'Model No.' },
    { title: 'Location' },
    { title: 'Min Quantity', className: 'text-right' },
    { title: 'Total', className: 'text-right' },
    { title: 'Available', className: 'text-right' },
  ];
  const renderAction = await getActionColumnByRole('accessories', [
    'create',
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    location: searchParams?.location ?? '',
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const accessories = await getAllAccessories(queryParams);
  const locations = await getAllLocations();

  if (accessories.type === 'error') {
    return <>Something Went wrong</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations, please try again!</>;
  }
  const totalPages = getNumberOfPages(accessories.count, searchParams?.limit);
  const locationList = locations.data.map((location) => location.name);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Accessories</h1>
        <div className="flex gap-3">
          <Selector
            options={locationList}
            placeholder="location"
            queryParam="location"
          />
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, model no, category"
          />
          <CreateButton
            href={accessoriesRoutes.CREATE}
            label="Create Accessory"
          />
          <DownloadDocument entityType={EntityType.Accessory} />
        </div>
      </div>

      <Table className="asset-management-table">
        <TableHeader>
          {tableHeaders.map((heading) => {
            return (
              <TableColumn
                className={`last:text-center ${heading.className}`}
                key={heading.title}
              >
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {accessories.data && accessories.data.length > 0 ? (
            accessories.data.map((accessory) => (
              <TableRow key={accessory.name}>
                <TableCell>
                  <Link
                    className="text-primary-600 hover:underline"
                    href={`${accessoriesRoutes.BASE}/${accessory.id}`}
                  >
                    {accessory.name}
                  </Link>
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(accessory.category?.name)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(accessory.modelNumber)}
                </TableCell>
                <TableCell className="start-case">
                  {accessory.location.name}
                </TableCell>
                <TableCell className="text-right">
                  {accessory.minQuantity}
                </TableCell>
                <TableCell className="text-right">
                  {accessory.totalQuantity}
                </TableCell>
                <TableCell className="text-right">
                  {accessory.availableQuantity}
                </TableCell>
                {renderAction ? (
                  <TableCell>
                    <div className="flex justify-center gap-2">
                      <AssignUserButton
                        href={{
                          pathname: `${accessoriesRoutes.ASSIGN}/${accessory.id}`,
                          query: {
                            name: accessory.name,
                            category: accessory.category?.name,
                          },
                        }}
                        isDisabled={accessory.availableQuantity === 0}
                      />
                      <EditButton
                        href={`${accessoriesRoutes.EDIT}/${accessory.id}`}
                      />
                      <DeleteEntity
                        entityId={accessory.id}
                        entityName={accessory.name}
                        entityType={Routes.ACCESSORY}
                      >
                        <DeleteButton
                          isDisabled={
                            accessory.availableQuantity !==
                            accessory.totalQuantity
                          }
                        />
                      </DeleteEntity>
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={accessories.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
