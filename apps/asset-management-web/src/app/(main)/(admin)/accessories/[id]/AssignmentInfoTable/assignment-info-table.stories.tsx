import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AssignmentInfoTable } from './assignment-info-table';

const meta: Meta<typeof AssignmentInfoTable> = {
  title: 'components/Accessories/AssignmentInfoTable',
  component: AssignmentInfoTable,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AssignmentInfoTable>;

export const DefaultAssignmentInfoTable: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
