import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import { getAccessoryFinanceInfo } from '@/services/accessories';
import { TableHeading } from '@/types';
import {
  displayDataOrDefault,
  generateDynamicRoute,
} from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { EditButton } from '@/components/ActionButtons/EditButton/edit-button';
import { accessoriesRoutes } from '@/constants/routes';

interface FinanceTableProps {
  id: string;
}

/**
 * Component representing a table displaying finance information for an accessory.
 * FinanceTableProps represents the props for the FinanceTable component.
 * returns Promise resolving to the JSX element representing the finance table.
 */
export async function FinanceTable({
  id,
}: FinanceTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Order No' },
    { title: 'Supplier' },
    { title: 'Quantity' },
    { title: 'Purchase Cost' },
    { title: 'Purchase Date' },
    { title: 'Purchased By' },
  ];

  const renderAction = await getActionColumnByRole('accessories', [
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;

  const accessoryFinanceInfo = await getAccessoryFinanceInfo(id);
  if (accessoryFinanceInfo.type === 'error') {
    return <>Could not load finance details, please try again.</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {accessoryFinanceInfo.data.map((finance) => (
          <TableRow key={finance.id}>
            <TableCell>{displayDataOrDefault(finance.orderNumber)}</TableCell>
            <TableCell>
              {displayDataOrDefault(finance.supplier?.name)}
            </TableCell>
            <TableCell>{finance.quantity}</TableCell>
            <TableCell>{finance.purchaseCost}</TableCell>
            <TableCell>
              {finance.purchaseDate
                ? parseDate('MMM dd, yyyy')(finance.purchaseDate)
                : '-'}
            </TableCell>
            <TableCell>
              {displayDataOrDefault(finance.purchasedBy?.name)}
            </TableCell>
            {renderAction ? (
              <TableCell>
                <div className=" flex items-center gap-4">
                  <EditButton
                    href={generateDynamicRoute(
                      accessoriesRoutes.EDIT_PURCHASE,
                      { purchaseId: finance.id },
                    )}
                  />
                </div>
              </TableCell>
            ) : null}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
