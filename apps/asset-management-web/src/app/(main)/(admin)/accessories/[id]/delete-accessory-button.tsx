'use client';

import { useRouter } from 'next/navigation';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { OutlinedDeleteButton } from '@/components/client';
import { Routes } from '@/types';

interface DeleteAccessoryButtonProps {
  id: string;
  name: string;
  disabled?: boolean;
}

export function DeleteAccessoryButton({
  id,
  name,
  disabled,
}: DeleteAccessoryButtonProps): React.JSX.Element {
  const router = useRouter();
  return (
    <DeleteEntity
      entityId={id}
      entityName={name}
      entityType={Routes.ACCESSORY}
      onDelete={() => {
        router.push('/accessories');
      }}
    >
      <OutlinedDeleteButton isDisabled={disabled} />
    </DeleteEntity>
  );
}
