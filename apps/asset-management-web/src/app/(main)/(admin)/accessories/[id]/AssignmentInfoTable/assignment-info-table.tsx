import {
  Button,
  Hover<PERSON>ard,
  HoverCard<PERSON>ontent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getAccessoryAssignmentInfo } from '@/services/accessories';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { UnassignAccessory } from '../../UnassignAccessory';

const tableHeaders = ['User', 'Assigned Date', 'Note', 'Actions'];

interface AssignmentInfoTableProps {
  id: string;
  name: string;
}

export async function AssignmentInfoTable({
  id,
  name,
}: AssignmentInfoTableProps): Promise<React.JSX.Element> {
  const accessoryAssignmentInfo = await getAccessoryAssignmentInfo(id);
  if (accessoryAssignmentInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {accessoryAssignmentInfo.data.length > 0 ? (
          accessoryAssignmentInfo.data.map((assignment) => (
            <TableRow key={assignment.id}>
              <TableCell>{assignment.user?.name}</TableCell>
              <TableCell>{assignment.date}</TableCell>
              <TableCell className="max-w-[200px]">
                <HoverCard>
                  <HoverCardTrigger className="line-clamp-2">
                    {displayDataOrDefault(assignment.note)}
                  </HoverCardTrigger>
                  {assignment.note ? (
                    <HoverCardContent>{assignment.note}</HoverCardContent>
                  ) : null}
                </HoverCard>
              </TableCell>

              <TableCell>
                <UnassignAccessory
                  accessoryName={name}
                  assignment={assignment}
                  assignmentId={assignment.id}
                  userName={assignment.user?.name ?? ''}
                  users={usersResponse.data}
                >
                  <Button className="pressed:bg-pink-700 bg-pink-600 px-4 py-1 text-xs hover:bg-pink-500  focus-visible:bg-pink-500 focus-visible:ring-pink-500 active:bg-pink-700 disabled:bg-pink-400">
                    Unassign
                  </Button>
                </UnassignAccessory>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
