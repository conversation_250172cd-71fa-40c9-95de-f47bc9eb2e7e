import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from 'ui';
import { getAccessory } from '@/services/accessories';
import { accessoriesRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { AssignmentInfoTable } from './AssignmentInfoTable';
import { DeleteAccessoryButton } from './delete-accessory-button';
import { FinanceTable } from './FinanceTable';
import { AccessoryHistory } from './AccessoryHistory';
import { AccessoryInfoTable } from './AccessoryInfoTable/accessory-info-table';

interface AccessoryDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function AccessoryDetailsPage({
  params: { id },
}: AccessoryDetailsPageProps): Promise<React.JSX.Element> {
  const accessoryInfoResponse = await getAccessory(id);
  if (accessoryInfoResponse.type === 'error') {
    return <>Something went wrong</>;
  }

  const accessoryInfo = accessoryInfoResponse.data;
  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {accessoryInfo.name}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={['purchase', 'assign', 'document']}
            entityId={id}
            entityName={accessoryInfo.name}
            routes={accessoriesRoutes}
          />
          <DeleteAccessoryButton
            disabled={
              accessoryInfo.availableQuantity !== accessoryInfo.totalQuantity
            }
            id={id}
            name={accessoryInfo.name}
          />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="assignment">Assignments</TabsTrigger>
            <TabsTrigger value="finance">Finance</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="document">Documents</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <AccessoryInfoTable accessoryData={accessoryInfo} />
          </TabsContent>
          <TabsContent value="assignment">
            <AssignmentInfoTable id={id} name={accessoryInfo.name} />
          </TabsContent>
          <TabsContent value="finance">
            {' '}
            <FinanceTable id={id} />
          </TabsContent>
          <TabsContent value="history">
            {' '}
            <AccessoryHistory id={id} name={accessoryInfo.name} />
          </TabsContent>
          <TabsContent value="document">
            <DocumentTable
              categoryType={CategoryType.ACCESSORY}
              entityType={EntityType.Accessory}
              id={id}
            />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
