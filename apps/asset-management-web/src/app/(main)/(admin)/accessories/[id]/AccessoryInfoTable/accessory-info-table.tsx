import { InfoItem } from '@/components/InfoItems/info-item';
import { AccessoryData } from '@/types/accessories';
import {
  CustomFieldMap,
  mapCustomFieldNamesToValues,
} from '@/utils/custom-field';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { toStartCase } from '@/utils/string-parser';

interface AccessoryInfoTableProps {
  accessoryData: AccessoryData;
}

interface AccessoryInfoType {
  displayName: string;
  value: string | string[] | null;
}

export async function AccessoryInfoTable({
  accessoryData,
}: AccessoryInfoTableProps): Promise<React.JSX.Element> {
  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    accessoryData.customFields?.data,
  );
  const accessoryInfo: AccessoryInfoType[] = [
    {
      displayName: 'Name',
      value: accessoryData.name,
    },
    {
      displayName: 'Category',
      value: accessoryData.category?.name ?? '',
    },
    {
      displayName: 'Manufacturer',
      value: accessoryData.manufacturer?.name ?? '',
    },
    {
      displayName: 'Location',
      value: toStartCase(accessoryData.location.name),
    },
    {
      displayName: 'Total Quantity',
      value: accessoryData.totalQuantity.toString(),
    },
    {
      displayName: 'Min Quantity',
      value: accessoryData.minQuantity.toString(),
    },
    {
      displayName: 'Available Quantity',
      value: accessoryData.availableQuantity?.toString() ?? '',
    },
    {
      displayName: 'Model Number',
      value: accessoryData.modelNumber,
    },
    {
      displayName: 'Notes',
      value: accessoryData.note,
    },
  ];

  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        {accessoryInfo.map((accessory) => (
          <InfoItem
            key={accessory.displayName}
            name={accessory.displayName}
            value={
              Array.isArray(accessory.value)
                ? displayDataOrDefault(accessory.value.join())
                : displayDataOrDefault(accessory.value)
            }
          />
        ))}
        {Object.entries(customFieldMap).map(([fieldName, value]) => (
          <InfoItem key={fieldName} name={fieldName} value={value} />
        ))}
      </div>

      {accessoryData.accessoryImageUrl ? (
        <div className="flex w-1/4 flex-shrink-0 justify-end">
          <img
            alt="accessory-name"
            className="h-[200px] w-auto rounded object-cover shadow"
            src={accessoryData.accessoryImageUrl}
            width={400}
            height={400}
          />
        </div>
      ) : null}
    </div>
  );
}
