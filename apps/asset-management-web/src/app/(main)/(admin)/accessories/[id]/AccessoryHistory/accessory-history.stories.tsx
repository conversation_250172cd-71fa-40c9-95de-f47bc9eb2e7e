import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AccessoryHistory } from './accessory-history';

const meta: Meta<typeof AccessoryHistory> = {
  title: 'components/Accessories/AccessoryHistory',
  component: AccessoryHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AccessoryHistory>;

export const DefaultAccessoryHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
    name: 'A4 Sheets',
  },
};
