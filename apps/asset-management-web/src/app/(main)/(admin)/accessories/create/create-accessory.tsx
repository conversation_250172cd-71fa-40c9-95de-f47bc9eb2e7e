'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { AccessoryFormSchema } from '@/schemas/accessories';
import { createAccessory } from '@/services/accessories';
import type { AccessoryFormData } from '@/types/accessories';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { EntityType, type Option, type PurchaseInfo } from '@/types';
import { PurchaseInfoSchema } from '@/schemas';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import type { FieldGroupDataType } from '@/types/custom-field';
import type { SelectedData } from '@/utils/custom-field';
import {
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { AccessoryForm } from '../AccessoriesForm';

interface CreateAccessoryProps extends BaseFormData {
  users: Option[];
  suppliers: Option[];
  locations: Option[];
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}

export function CreateAccessory({
  categories,
  manufacturers,
  users,
  suppliers,
  locations,
  fieldGroups,
  fieldGroupDatas,
}: CreateAccessoryProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const {
    fieldErrors: accessoryFieldErrors,
    setFieldErrors: setAccessoryFieldErrors,
    resetFieldErrors: resetAccessoryFieldErrors,
  } = useFieldErrors<AccessoryFormData>();

  const {
    fieldErrors: purchaseInfoFieldErrors,
    setFieldErrors: setPurchaseInfoFieldErrors,
    resetFieldErrors: resetPurchaseInfoFieldErrors,
  } = useFieldErrors<PurchaseInfo>();

  const addAccessory = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );
    const {
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      purchaseDate,
      purchasedById,
      accessoryImageUrl,
      ...accessoryFields
    } = Object.fromEntries(formData);

    const parsedAccesoryResult = AccessoryFormSchema.safeParse({
      ...accessoryFields,
      accessoryImageUrl: '',
      customFields: customFieldData,
    });
    const parsedPurchaseInfoResult = PurchaseInfoSchema.safeParse({
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      purchaseDate,
      purchasedById,
    });

    if (!parsedAccesoryResult.success) {
      setAccessoryFieldErrors(parsedAccesoryResult.error.flatten().fieldErrors);
    } else {
      resetAccessoryFieldErrors();
    }

    if (!parsedPurchaseInfoResult.success) {
      setPurchaseInfoFieldErrors(
        parsedPurchaseInfoResult.error.flatten().fieldErrors,
      );
    } else {
      resetPurchaseInfoFieldErrors();
    }

    if (!parsedAccesoryResult.success || !parsedPurchaseInfoResult.success) {
      return;
    }

    let uploadedAccessoryImageUrl = '';
    if (
      isImage &&
      accessoryImageUrl instanceof File &&
      accessoryImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', accessoryImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Accessory,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedAccessoryImageUrl = generateCloudFrontUrl(
        EntityType.Accessory,
        imageUploadResponse.data.fileName,
      );
    }

    const createAccessoryData = {
      ...parsedAccesoryResult.data,
      accessoryImageUrl: uploadedAccessoryImageUrl,
    };

    const accessoryResponse = await createAccessory(
      createAccessoryData,
      parsedPurchaseInfoResult.data,
    );

    if (accessoryResponse.type === 'error') {
      if (accessoryResponse.errors.errorMessages) {
        accessoryResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('something went wrong!');
      return;
    }

    toast.success('Accessory created successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Accessory</h1>
      <AccessoryForm
        accessoryFieldErrors={accessoryFieldErrors}
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        manufacturers={manufacturers}
        mode="create"
        onSubmit={addAccessory}
        purchaseInfoFieldErrors={purchaseInfoFieldErrors}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        users={users}
        locations={locations}
      />
    </div>
  );
}
