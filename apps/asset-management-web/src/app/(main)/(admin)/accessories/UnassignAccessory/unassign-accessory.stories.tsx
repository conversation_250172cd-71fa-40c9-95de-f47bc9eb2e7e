import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from 'ui';
import { UnassignAccessory } from './unassign-accessory';

const meta: Meta<typeof UnassignAccessory> = {
  title: 'components/Accessories/UnassignAccessory',
  component: UnassignAccessory,
};

export default meta;

type Story = StoryObj<typeof UnassignAccessory>;

export const DefaultUnassignAccessoryForm: Story = {
  render: () => {
    return (
      <UnassignAccessory
        accessoryName="Dell Laptop"
        assignment={{
          id: 'abc123',
          typeOfAssignment: 'Accessory',
          date: '2024-05-07',
          user: {
            id: 'xyz789',
            name: '<PERSON>',
          },
          entityId: 'def456',
          isPending: false,
          note: 'Accessory unassiged.',
        }}
        assignmentId="123"
        userName="User-1"
        users={[
          {
            displayName: 'Josh',
            value: '14526',
          },
          {
            displayName: 'Ariana',
            value: '56359',
          },
          {
            displayName: '<PERSON>',
            value: '1459',
          },
        ]}
      >
        <Button className="pressed:bg-pink-700 bg-pink-600 px-4 py-1 text-xs hover:bg-pink-500  focus-visible:bg-pink-500 focus-visible:ring-pink-500 active:bg-pink-700 disabled:bg-pink-400">
          Unassign
        </Button>
      </UnassignAccessory>
    );
  },
};
