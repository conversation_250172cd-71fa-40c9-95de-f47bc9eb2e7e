'use client';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import { unassignAccessory } from '@/services/accessories';
import { unAssignmentFormSchema } from '@/schemas';
import { UnAssignEntityForm } from '@/components/UnAssignEntityForm/un-assign-entity-form';
import type { AssignmentDataType } from '@/types/assets';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, INPUT_VALUES_ERROR_MESSAGE } from '@/constants';

interface UnassignAccessoryProps {
  assignmentId: string;
  accessoryName: string;
  userName: string;
  children: React.ReactNode;
  onDelete?: () => void;
  assignment: AssignmentDataType;
  users: Option[];
}

export function UnassignAccessory({
  accessoryName,
  userName,
  assignmentId,
  assignment,
  users,
}: UnassignAccessoryProps): React.JSX.Element {
  /**
   * Handles the unassignment of the accessory.
   */
  const handleUnassignAccessory = async (formData: FormData): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const unAssignDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };
    const parsedResult = unAssignmentFormSchema.safeParse(unAssignDetails);

    if (!parsedResult.success) {
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    const unassignResponse = await unassignAccessory(
      assignmentId,
      parsedResult.data,
    );

    if (unassignResponse.type === 'error') {
      if (unassignResponse.errors.errorMessages) {
        unassignResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }
    toast.success(`${accessoryName} unassigned successfully from ${userName}`);
  };

  return (
    <UnAssignEntityForm
      assignment={assignment}
      entityName={accessoryName}
      handleSubmit={handleUnassignAccessory}
      userName={userName}
      users={users}
    />
  );
}
