import { getAllUsersOptions } from '@/services/users';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { UpdatePurchaseAccessory } from './edit-purchase';
import { PurchaseInfo } from '@/types';
import { format, parseISO } from 'date-fns';
import { getPurchaseInfoById } from '@/services/purchase';

interface PurchaseAccessoryProps {
  params: {
    id: string;
  };
}

export default async function Purchase({
  params: { id },
}: PurchaseAccessoryProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  const suppliers = await getAllSuppliersOptions();
  const purchaseInfoResponse = await getPurchaseInfoById(id);

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (purchaseInfoResponse.type === 'error') {
    return <>Id not found</>;
  }

  const purchaseInfo = purchaseInfoResponse.data;

  const { supplier, purchasedBy, purchaseDate, ...rest } = purchaseInfo;

  const initialValues: Partial<PurchaseInfo> = {
    ...rest,
    supplierId: supplier?.id ?? undefined,
    purchasedById: purchasedBy?.id ?? undefined,
    purchaseDate: purchaseDate
      ? format(parseISO(purchaseDate), 'yyyy-MM-dd')
      : null,
  };

  return (
    <UpdatePurchaseAccessory
      initialValues={initialValues}
      id={purchaseInfo.id}
      suppliers={suppliers.data}
      users={usersResponse.data}
      accessoryId={purchaseInfo.entityId}
    />
  );
}
