'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchema } from '@/schemas';
import { createPurchaseAccessory } from '@/services/accessories';
import type { Option, PurchaseInfo } from '@/types';
import { PurchaseAccessoryForm } from '../PurchaseAccessoryForm';

interface PurchaseAccessoryProps {
  /** The unique identifier of the accessory. */
  id: string;
  /** Name of the accessory */
  name: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
}

/**
 * Component for purchasing a accessory.
 * PurchaseAccessoryProps represents the properties for the PurchaseAccessory component.
 * returns the rendered PurchaseAccessory component.
 */
export function PurchaseAccessory({
  id,
  name,
  users,
  suppliers,
}: PurchaseAccessoryProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<PurchaseInfo>();
  const purchaseAccessory = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchema.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await createPurchaseAccessory(
      id,
      parsedResult.data,
    );

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Failed to create Purchase Accessory. Please try again');
      return;
    }

    toast.success('Purchase Accessory created successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Purchase {name}</h1>
      <PurchaseAccessoryForm
        errors={fieldErrors}
        onSubmit={purchaseAccessory}
        suppliers={suppliers}
        users={users}
        mode='create'
      />
    </div>
  );
}
