import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { AssignAccessory } from './assign-accessory';

interface AssignAccessoryProps {
  params: {
    id: string;
  };
  searchParams: {
    name: string;
    category: string;
  };
}

export default async function Assign({
  params: { id },
  searchParams: { name, category },
}: AssignAccessoryProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return (
    <AssignAccessory
      category={category}
      id={id}
      name={name}
      users={usersResponse.data}
    />
  );
}
