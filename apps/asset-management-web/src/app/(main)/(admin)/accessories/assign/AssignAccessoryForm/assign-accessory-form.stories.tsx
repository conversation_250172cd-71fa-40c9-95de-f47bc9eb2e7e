import type { Meta, StoryObj } from '@storybook/react';
import { AssignAccessoryForm } from './assign-accessory-form';

const meta: Meta<typeof AssignAccessoryForm> = {
  title: 'components/Accessories/AssignAccessoryForm',
  component: AssignAccessoryForm,
};

export default meta;

type Story = StoryObj<typeof AssignAccessoryForm>;

export const DefaultAssignAccessoryForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-16 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Assign Accessory</h1>
        <AssignAccessoryForm
          category="Laptop"
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};
