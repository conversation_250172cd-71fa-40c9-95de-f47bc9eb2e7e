'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import type { AssignmentFormData, Option } from '@/types';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { AssignmentFormSchema } from '@/schemas';
import { assignAccessory } from '@/services/accessories';
import { AssignAccessoryForm } from '../AssignAccessoryForm';
import { GENERIC_ERROR_MESSAGE } from '@/constants';

interface AssignAccessoryProps {
  id: string;
  name: string;
  category: string;
  users: Option[];
}

export function AssignAccessory({
  category,
  name,
  users,
  id,
}: AssignAccessoryProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssignmentFormData>();
  const assign = async (formData: FormData): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const assignmentDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };

    const parsedResult = AssignmentFormSchema.safeParse(assignmentDetails);

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const assignmentResponse = await assignAccessory(id, parsedResult.data);

    if (assignmentResponse.type === 'error') {
      if (assignmentResponse.errors.errorMessages) {
        assignmentResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Accessory Assigned successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto mb-12 h-fit w-2/5 min-w-max">
      <h1 className="asset-management-form-heading">Assign {name}</h1>
      <AssignAccessoryForm
        category={category}
        errors={fieldErrors}
        onSubmit={assign}
        users={users}
      />
    </div>
  );
}
