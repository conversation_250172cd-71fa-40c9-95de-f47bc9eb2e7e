import type { Meta, StoryObj } from '@storybook/react';
import { AccessoryForm } from './accessories-form';

const categories = [
  {
    displayName: 'Laptop',
    value: 'laptop',
  },
  {
    displayName: 'Tablet',
    value: 'tablet',
  },
  {
    displayName: 'Phone',
    value: 'phone',
  },
  {
    displayName: 'Keyboard',
    value: 'keyboard',
  },
  {
    displayName: 'Mouse',
    value: 'mouse',
  },
];

const manufacturers = [
  {
    displayName: 'Apple',
    value: 'apple',
  },
  {
    displayName: 'Dell',
    value: 'dell',
  },
  {
    displayName: 'Samsung',
    value: 'samsung',
  },
  {
    displayName: 'HP',
    value: 'hp',
  },
  {
    displayName: 'Google',
    value: 'google',
  },
];

const suppliers = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'JSF',
  },
  {
    displayName: 'Kunze, Predovic and Ziemann',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Ke<PERSON>-<PERSON>',
    value: 'KR',
  },
];

const locations = [
  { displayName: 'Mangalore', value: '1' },
  { displayName: 'Bangalore', value: '2' },
];

const meta: Meta<typeof AccessoryForm> = {
  title: 'components/Accessories/AccessoryForm',
  component: AccessoryForm,
};

export default meta;

type Story = StoryObj<typeof AccessoryForm>;

export const AccessoriesFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="mb-6 text-center text-2xl font-bold">Add Accessory</h1>
        <AccessoryForm
          categories={categories}
          manufacturers={manufacturers}
          locations={locations}
          mode="create"
          suppliers={suppliers}
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};

export const AccessoriesFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="mb-6 text-center text-2xl font-bold">Edit Accessory</h1>
        <AccessoryForm
          categories={categories}
          initialAccessoryInfo={{
            accessoryImageUrl: 'http://images.com/image-1',
            categoryId: '1',
            location: 'mangalore',
            manufacturerId: '1',
            minQuantity: 10,
            modelNumber: '1222321',
            name: 'Dell Super Cool Laptop',
            note: 'This is super cool laptop',
          }}
          locations={locations}
          manufacturers={manufacturers}
          mode="edit"
        />
      </div>
    );
  },
};
