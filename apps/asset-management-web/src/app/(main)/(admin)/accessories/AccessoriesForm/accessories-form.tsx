import {
  Label,
  Input,
  SubmitButton,
  Textarea,
  ImageInputWithPreview,
} from 'ui';
import type { AccessoryFormData } from '@/types/accessories';
import type { FieldErrors, Option, PurchaseInfo } from '@/types';
import { OptionsComboBox } from '@/components/client';
import { allowedImageType } from '@/constants';
import type { CustomField } from '@/utils/custom-field';
import { renderCustomFieldGroups } from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { PurchaseInfoFields } from '../purchase-info-fields';

interface CreateAccessoryFormProps extends BaseFormData {
  mode: 'create';
  initialAccessoryInfo?: Partial<AccessoryFormData>;
  initialPurchaseInfo?: Partial<PurchaseInfo>;
  accessoryFieldErrors?: FieldErrors<AccessoryFormData>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfo>;
  onSubmit?: (formData: FormData) => Promise<void>;
  users: Option[];
  suppliers: Option[];
  locations: Option[];
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditAccessoryFormProps extends BaseFormData {
  mode: 'edit';
  initialAccessoryInfo: AccessoryFormData;
  accessoryFieldErrors?: FieldErrors<AccessoryFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
  locations: Option[];
}

export function AccessoryForm(
  props: CreateAccessoryFormProps | EditAccessoryFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    setSelectedData,
    manufacturers,
    categories,
    locations,
    mode,
    setIsImage,
    accessoryFieldErrors,
    initialAccessoryInfo,
    fieldGroups,
    customFields,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialAccessoryInfo,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="name" required>
                Name
              </Label>
              <Input
                defaultValue={initialAccessoryInfo?.name ?? ''}
                id="name"
                isInvalidInput={Boolean(accessoryFieldErrors?.name)}
                name="name"
                placeholder="e.g. Bluetooth Keyboard"
                type="text"
              />
              {accessoryFieldErrors?.name ? (
                <p>{accessoryFieldErrors.name[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="categoryId">Category</Label>
              <OptionsComboBox
                id="categoryId"
                initialValue={initialAccessoryInfo?.categoryId}
                name="categoryId"
                options={categories}
                placeholder="Choose Category"
              />
            </fieldset>
          </div>
          <fieldset>
            <Label htmlFor="manufacturerId">Manufacturer</Label>
            <OptionsComboBox
              id="manufacturerId"
              initialValue={initialAccessoryInfo?.manufacturerId}
              name="manufacturerId"
              options={manufacturers}
              placeholder="Choose manufacturer"
            />
          </fieldset>
          <fieldset>
            <Label htmlFor="location" required>
              Location
            </Label>
            <OptionsComboBox
              id="location"
              initialValue={initialAccessoryInfo?.location}
              name="location"
              placeholder="Choose location"
              options={locations}
            />
            {accessoryFieldErrors?.location ? (
              <p>{accessoryFieldErrors.location[0]}</p>
            ) : null}
          </fieldset>
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="modelNumber">Model Number</Label>
              <Input
                defaultValue={initialAccessoryInfo?.modelNumber ?? ''}
                id="modelNumber"
                isInvalidInput={Boolean(accessoryFieldErrors?.modelNumber)}
                name="modelNumber"
                placeholder="e.g. 1233221"
                type="text"
              />
              {accessoryFieldErrors?.modelNumber ? (
                <p>{accessoryFieldErrors.modelNumber[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="minQuantity" required>
                Minimum Quantity
              </Label>
              <Input
                defaultValue={initialAccessoryInfo?.minQuantity ?? ''}
                id="minQuantity"
                isInvalidInput={Boolean(accessoryFieldErrors?.minQuantity)}
                name="minQuantity"
                placeholder="e.g. 10"
                type="number"
              />
              {accessoryFieldErrors?.minQuantity ? (
                <p>{accessoryFieldErrors.minQuantity[0]}</p>
              ) : null}
            </fieldset>
          </div>
        </div>
      </div>

      {mode === 'create' ? (
        <div>
          <h2>Purchase Information</h2>
          <PurchaseInfoFields
            initialPurchaseInfo={props.initialPurchaseInfo}
            purchaseInfoFieldErrors={props.purchaseInfoFieldErrors}
            suppliers={props.suppliers}
            users={props.users}
          />
        </div>
      ) : null}

      <div>
        <h2>Additional Information</h2>
        {displayCustomFieldGroups}
        <div className="space-y-3">
          <fieldset>
            <Label htmlFor="note">Notes</Label>
            <Textarea
              defaultValue={initialAccessoryInfo?.note ?? ''}
              id="note"
              name="note"
              placeholder="Include any additional information in this note."
              rows={4}
            />
            {accessoryFieldErrors?.note ? (
              <p>{accessoryFieldErrors.note[0]}</p>
            ) : null}
          </fieldset>
          <fieldset>
            <Label htmlFor="accessoryImageUrl">Select Image</Label>
            <ImageInputWithPreview
              accept={allowedImageType.join(',')}
              className="mt-2"
              defaultValue={initialAccessoryInfo?.accessoryImageUrl ?? ''}
              id="accessoryImageUrl"
              name="accessoryImageUrl"
              setIsImage={setIsImage}
            />
            {accessoryFieldErrors?.accessoryImageUrl ? (
              <p>{accessoryFieldErrors.accessoryImageUrl[0]}</p>
            ) : null}
          </fieldset>
        </div>
      </div>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
