import { getAllCategoriesOptions } from '@/services/categories';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { getAccessory } from '@/services/accessories';
import type { AccessoryFormData } from '@/types/accessories';
import { EditAccessory } from './edit-accessory';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getAllLocationsOptions } from '@/services/locations';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditAccessoryPage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('ACCESSORY');
  const manufacturers = await getAllManufacturersOptions();
  const accessoryInfo = await getAccessory(id);
  const locations = await getAllLocationsOptions();

  if (accessoryInfo.type === 'error') {
    return <>Id not found</>;
  }

  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }
  const { manufacturer, category, location, ...rest } = accessoryInfo.data;

  const intitialValues: AccessoryFormData = {
    ...rest,
    categoryId: category?.id ?? undefined,
    manufacturerId: manufacturer?.id ?? undefined,
    location: location.id,
    customFields: rest.customFields ? rest.customFields : {},
  };

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }
  return (
    <EditAccessory
      categories={categories.data}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      initialValues={intitialValues}
      manufacturers={manufacturers.data}
      locations={locations.data}
    />
  );
}
