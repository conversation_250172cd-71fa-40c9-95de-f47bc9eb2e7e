'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useState } from 'react';
import type { AccessoryFormData } from '@/types/accessories';
import { AccessoryFormSchema } from '@/schemas/accessories';
import { updateAccessory } from '@/services/accessories';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType, Option } from '@/types';
import { AccessoryForm } from '../../AccessoriesForm';
import type { BaseFormData } from '../../types';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface EditAccessoryProps extends BaseFormData {
  id: string;
  initialValues: AccessoryFormData;
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
  locations: Option[];
}
export function EditAccessory({
  id,
  categories,
  manufacturers,
  initialValues,
  fieldGroups,
  fieldGroupDatas,
  locations,
}: EditAccessoryProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AccessoryFormData>();

  const editAccessory = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );
    const accessoryFields = Object.fromEntries(formData);
    const { accessoryImageUrl, ...accessoryDetails } = accessoryFields;

    const parsedResult = AccessoryFormSchema.safeParse({
      ...accessoryDetails,
      accessoryImageUrl: '',
      customFields: customFieldData,
    });
    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    let uploadedAccessoryImageUrl = initialValues.accessoryImageUrl;
    if (
      isImage &&
      accessoryImageUrl instanceof File &&
      accessoryImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', accessoryImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Accessory,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedAccessoryImageUrl = generateCloudFrontUrl(
        EntityType.Accessory,
        imageUploadResponse.data.fileName,
      );
    }

    const updateAccessoryData = {
      ...parsedResult.data,
      accessoryImageUrl: isImage ? uploadedAccessoryImageUrl : '',
    };

    const accessoryResponse = await updateAccessory(id, updateAccessoryData);

    if (accessoryResponse.type === 'error') {
      if (accessoryResponse.errors.errorMessages) {
        accessoryResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('something went wrong');
      return;
    }

    toast.success('Accessory updated successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Accessory</h1>
      <AccessoryForm
        accessoryFieldErrors={fieldErrors}
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        initialAccessoryInfo={initialValues}
        manufacturers={manufacturers}
        mode="edit"
        onSubmit={editAccessory}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        locations={locations}
      />
    </div>
  );
}
