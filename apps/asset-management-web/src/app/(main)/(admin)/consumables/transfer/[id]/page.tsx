import { getTransferTargetConsumables } from '@/services/consumables';
import { ConsumableTransfer } from './consumable-transfer';
import { CategoryType } from '@/types';

interface ConsumableTransferPageProps {
  params: {
    id: string;
  };
}

export default async function ConsumableTransferPage({
  params: { id },
}: ConsumableTransferPageProps) {
  const response = await getTransferTargetConsumables(
    id,
    CategoryType.CONSUMABLE,
  );
  if (response.type === 'error') {
    return <p>Unable to Get Target Consumables. Try again</p>;
  }
  return <ConsumableTransfer targetConsumables={response.data} sourceId={id} />;
}
