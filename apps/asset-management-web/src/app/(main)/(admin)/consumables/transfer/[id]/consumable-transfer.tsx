'use client';
import { TransferForm } from '@/components/TransferForm/transfer-form';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { TransferFormSchema } from '@/schemas';
import { transferConsumable } from '@/services/consumables';
import { CategoryType, Option, TransferFormData } from '@/types';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'sonner';

interface ConsumableTransferProps {
  targetConsumables: Option[];
  sourceId: string;
}

export function ConsumableTransfer({
  targetConsumables,
  sourceId,
}: ConsumableTransferProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<TransferFormData>();
  const handleSubmit = async (formData: FormData) => {
    const parsedResult = TransferFormSchema.safeParse(
      Object.fromEntries(formData),
    );
    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const transferResponse = await transferConsumable(
      parsedResult.data,
      sourceId,
      CategoryType.CONSUMABLE,
    );
    if (transferResponse.type === 'error') {
      if (transferResponse.errors.errorMessages) {
        transferResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('something went wrong!');
      return;
    }

    toast.success('Consumable transferred successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Transfer Consumable</h1>
      <TransferForm
        targetEntities={targetConsumables}
        onSubmit={handleSubmit}
        fieldErrors={fieldErrors}
      />
    </div>
  );
}
