'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { createConsumable } from '@/services/consumables';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { ConsumableRequestData } from '@/types/consumables';
import { consumableRequestSchema } from '@/schemas/consumables';
import { EntityType, type Option, type PurchaseInfo } from '@/types';
import { PurchaseInfoSchema } from '@/schemas';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { ConsumablesForm } from '../ConsumablesForm';
import type { BaseFormData } from '../types';
import { formatISO, parseISO } from 'date-fns';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface CreateConsumableProps extends BaseFormData {
  users: Option[];
  locations: Option[];
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}
export function CreateConsumable({
  categories,
  suppliers,
  manufacturers,
  users,
  locations,
  fieldGroups,
  fieldGroupDatas,
}: CreateConsumableProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const {
    fieldErrors: consumableFieldErrors,
    setFieldErrors: setConsumableFieldErrors,
    resetFieldErrors: resetConsumableFieldErrors,
  } = useFieldErrors<ConsumableRequestData>();

  const {
    fieldErrors: purchaseInfoFieldErrors,
    setFieldErrors: setPurchaseInfoFieldErrors,
    resetFieldErrors: resetPurchaseInfoFieldErrors,
  } = useFieldErrors<PurchaseInfo>();
  async function addConsumable(formData: FormData): Promise<void> {
    const customFieldData = formattedCustomFields(formData);

    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );
    const {
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      purchaseDate,
      purchasedById,
      supplierId,
      consumableImageUrl,
      ...consumableFields
    } = Object.fromEntries(formData);

    const parsedConsumableResult = consumableRequestSchema.safeParse({
      ...consumableFields,
      consumableImageUrl: '',
      customFields: customFieldData,
    });
    const parsedPurchaseInfoResult = PurchaseInfoSchema.safeParse({
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
      purchasedById,
      supplierId,
    });

    if (!parsedConsumableResult.success) {
      setConsumableFieldErrors(
        parsedConsumableResult.error.flatten().fieldErrors,
      );
    } else {
      resetConsumableFieldErrors();
    }

    if (!parsedPurchaseInfoResult.success) {
      setPurchaseInfoFieldErrors(
        parsedPurchaseInfoResult.error.flatten().fieldErrors,
      );
    } else {
      resetPurchaseInfoFieldErrors();
    }

    if (!parsedConsumableResult.success || !parsedPurchaseInfoResult.success) {
      return;
    }

    let uploadedConsumableImageUrl = '';
    if (
      isImage &&
      consumableImageUrl instanceof File &&
      consumableImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', consumableImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Consumable,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedConsumableImageUrl = generateCloudFrontUrl(
        EntityType.Consumable,
        imageUploadResponse.data.fileName,
      );
    }

    const createConsumableData = {
      ...parsedConsumableResult.data,
      consumableImageUrl: uploadedConsumableImageUrl,
    };
    const consumableResponse = await createConsumable(
      createConsumableData,
      parsedPurchaseInfoResult.data,
    );

    if (consumableResponse.type === 'error') {
      if (consumableResponse.errors.errorMessages) {
        consumableResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('something went wrong!');
      return;
    }

    toast.success('Consumable created successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Consumable</h1>
      <ConsumablesForm
        categories={categories}
        consumableFieldErrors={consumableFieldErrors}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        manufacturers={manufacturers}
        mode="create"
        onSubmit={addConsumable}
        purchaseInfoFieldErrors={purchaseInfoFieldErrors}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        users={users}
        locations={locations}
      />
    </div>
  );
}
