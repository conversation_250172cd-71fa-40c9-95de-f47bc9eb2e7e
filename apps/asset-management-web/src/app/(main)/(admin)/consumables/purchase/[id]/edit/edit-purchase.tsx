'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchema } from '@/schemas';
import type { Option, PurchaseInfo } from '@/types';
import { PurchaseConsumableForm } from '../../PurchaseConsumableForm';
import { updateConsumablePurchaseDetails } from '@/services/consumables';

interface PurchaseConsumablesProps {
  /** The unique identifier of the accessory. */
  id: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
  /** Initial values for purchase information. */
  initialValues: Partial<PurchaseInfo>;

  consumableId: string;
}

/**
 * Component for purchasing a accessory.
 * PurchaseAccessoryProps represents the properties for the PurchaseAccessory component.
 * returns the rendered PurchaseAccessory component.
 */
export function UpdatePurchaseConsumable({
  id,
  users,
  suppliers,
  initialValues,
  consumableId,
}: PurchaseConsumablesProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<PurchaseInfo>();
  const purchaseConsumable = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchema.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await updateConsumablePurchaseDetails(
      id,
      consumableId,
      parsedResult.data,
    );

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Failed to update Purchase Details. Please try again');
      return;
    }

    toast.success('Purchase Consumable updated successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Edit Purchase</h1>
      <PurchaseConsumableForm
        errors={fieldErrors}
        onSubmit={purchaseConsumable}
        suppliers={suppliers}
        users={users}
        purchaseInfo={initialValues}
        mode="edit"
      />
    </div>
  );
}
