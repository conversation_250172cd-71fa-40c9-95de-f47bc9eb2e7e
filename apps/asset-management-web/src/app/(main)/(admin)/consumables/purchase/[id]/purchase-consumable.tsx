'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchema } from '@/schemas';
import { createPurchaseConsumable } from '@/services/consumables';
import type { Option, PurchaseInfo } from '@/types';
import { PurchaseConsumableForm } from '../PurchaseConsumableForm';

interface PurchaseConsumableProps {
  /** The unique identifier of the consumable. */
  id: string;
  /** Name of the consumable */
  name: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
}

/**
 * Component for purchasing a consumable.
 * PurchaseConsumableProps represents the properties for the PurchaseConsumable component.
 * returns the rendered PurchaseConsumable component.
 */
export function PurchaseConsumable({
  id,
  name,
  users,
  suppliers,
}: PurchaseConsumableProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<PurchaseInfo>();
  const purchaseConsumable = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchema.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const assignmentResponse = await createPurchaseConsumable(
      id,
      parsedResult.data,
    );

    if (assignmentResponse.type === 'error') {
      if (assignmentResponse.errors.errorMessages) {
        assignmentResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Purchase Consumable created successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Purchase {name}</h1>
      <PurchaseConsumableForm
        errors={fieldErrors}
        onSubmit={purchaseConsumable}
        suppliers={suppliers}
        users={users}
        mode="create"
      />
    </div>
  );
}
