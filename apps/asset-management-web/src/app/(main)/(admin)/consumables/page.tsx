import React from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Selector,
} from 'ui';
import Link from 'next/link';
import {
  EntityType,
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { Pagination } from '@/components/Pagination/pagination';
import {
  AssignUserButton,
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { consumableRoutes } from '@/constants/routes';
import { getAllConsumables } from '@/services/consumables';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { DownloadDocument } from '@/components/DownloadDocument/download-document';
import { getAllLocations } from '@/services/locations';

interface ConsumablesSearchParams extends SearchParams {
  location?: string;
  category?: string[];
}

export default async function Consumables({
  searchParams,
}: {
  searchParams?: ConsumablesSearchParams;
}): Promise<React.JSX.Element> {
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    location: searchParams?.location ?? '',
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const consumablesData = await getAllConsumables(queryParams);
  const locations = await getAllLocations();

  if (consumablesData.type === 'error') {
    return <>Something Went wrong</>;
  }

  if (!consumablesData.data) {
    return <>Unable to fetch data, try again!</>;
  }

  if (locations.type === 'error') {
    return <>Something Went wrong</>;
  }
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Model No' },
    { title: 'Manufacturer' },
    { title: 'Category' },
    { title: 'Location' },
    { title: 'Min Qty', className: 'text-right' },
    { title: 'Total', className: 'text-right' },
    { title: 'Available', className: 'text-right' },
  ];
  const renderAction = await getActionColumnByRole('consumables', [
    'create',
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;

  const totalPages = getNumberOfPages(
    consumablesData.count,
    searchParams?.limit,
  );
  const locationList = locations.data.map((location) => location.name);
  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-700">Consumables</h1>
        <div className="flex gap-3">
          <Selector
            options={locationList}
            placeholder="location"
            queryParam="location"
          />
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, model no, category"
          />
          <CreateButton
            href={consumableRoutes.CREATE}
            label="Create Consumable"
          />
          <DownloadDocument entityType={EntityType.Consumable} />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader>
          {tableHeaders.map((heading) => {
            return (
              <TableColumn
                className={`last:text-center ${heading.className} `}
                key={heading.title}
              >
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>

        <TableBody>
          {consumablesData.data.length > 0 ? (
            consumablesData.data.map((consumable) => (
              <TableRow key={consumable.id}>
                <TableCell className="first-letter:uppercase">
                  <Link
                    className="text-primary-600 hover:underline"
                    href={`${consumableRoutes.CONSUMABLES}/${consumable.id}`}
                  >
                    {consumable.name}
                  </Link>
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(consumable.modelNumber)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(consumable.manufacturer?.name)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(consumable.category?.name)}
                </TableCell>
                <TableCell className="start-case">
                  {consumable.location.name}
                </TableCell>
                <TableCell className="text-right">
                  {consumable.minQuantity}
                </TableCell>
                <TableCell className="text-right">
                  {consumable.totalQuantity}
                </TableCell>
                <TableCell className="text-right">
                  {consumable.availableQuantity}
                </TableCell>
                {renderAction ? (
                  <TableCell>
                    <div className="flex items-center justify-center gap-2">
                      <AssignUserButton
                        href={{
                          pathname: `${consumableRoutes.ASSIGN}/${consumable.id}`,
                          query: { name: consumable.name },
                        }}
                        isDisabled={consumable.availableQuantity <= 0}
                      />
                      <EditButton
                        href={`${consumableRoutes.EDIT}/${consumable.id}`}
                      />
                      <DeleteEntity
                        entityId={consumable.id}
                        entityName={consumable.name}
                        entityType={Routes.CONSUMABLE}
                      >
                        <DeleteButton
                          isDisabled={
                            consumable.availableQuantity !==
                            consumable.totalQuantity
                          }
                        />
                      </DeleteEntity>
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={consumablesData.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
