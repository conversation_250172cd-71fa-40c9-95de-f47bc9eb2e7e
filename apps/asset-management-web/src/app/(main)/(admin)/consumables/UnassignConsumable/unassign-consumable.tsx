'use client';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import { unAssignmentFormSchema } from '@/schemas';
import { UnAssignEntityForm } from '@/components/UnAssignEntityForm/un-assign-entity-form';
import type { AssignmentDataType } from '@/types/assets';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { unassignConsumable } from '@/services/consumables';

interface UnassignConsumableProps {
  assignmentId: string;
  consumableName: string;
  userName: string;
  children: React.ReactNode;
  onDelete?: () => void;
  assignment: AssignmentDataType;
  users: Option[];
}

export function UnassignConsumable({
  consumableName,
  userName,
  assignmentId,
  assignment,
  users,
}: UnassignConsumableProps): React.JSX.Element {
  /**
   * Handles the unassignment of the accessory.
   */
  const handleUnassignConsumable = async (
    formData: FormData,
  ): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const unAssignDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };
    const parsedResult = unAssignmentFormSchema.safeParse(unAssignDetails);

    if (!parsedResult.success) {
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    const unassignResponse = await unassignConsumable(
      assignmentId,
      parsedResult.data,
    );

    if (unassignResponse.type === 'error') {
      if (unassignResponse.errors.errorMessages) {
        unassignResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }
    toast.success(`${consumableName} unassigned successfully from ${userName}`);
  };

  return (
    <UnAssignEntityForm
      assignment={assignment}
      entityName={consumableName}
      handleSubmit={handleUnassignConsumable}
      userName={userName}
      users={users}
    />
  );
}
