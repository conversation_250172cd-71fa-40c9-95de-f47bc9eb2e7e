import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from 'ui';
import { UnassignConsumable } from './unassign-consumable';

const meta: Meta<typeof UnassignConsumable> = {
  title: 'components/Accessories/UnassignConsumable',
  component: UnassignConsumable,
};

export default meta;

type Story = StoryObj<typeof UnassignConsumable>;

export const DefaultUnassignConsumableForm: Story = {
  render: () => {
    return (
      <UnassignConsumable
        consumableName="A4 Sheet"
        assignment={{
          id: 'abc123',
          typeOfAssignment: 'Accessory',
          date: '2024-05-07',
          user: {
            id: 'xyz789',
            name: '<PERSON>',
          },
          entityId: 'def456',
          isPending: false,
          note: 'Accessory unassiged.',
        }}
        assignmentId="123"
        userName="User-1"
        users={[
          {
            displayName: 'Josh',
            value: '14526',
          },
          {
            displayName: 'Ariana',
            value: '56359',
          },
          {
            displayName: 'Kylie',
            value: '1459',
          },
        ]}
      >
        <Button className="pressed:bg-pink-700 bg-pink-600 px-4 py-1 text-xs hover:bg-pink-500  focus-visible:bg-pink-500 focus-visible:ring-pink-500 active:bg-pink-700 disabled:bg-pink-400">
          Unassign
        </Button>
      </UnassignConsumable>
    );
  },
};
