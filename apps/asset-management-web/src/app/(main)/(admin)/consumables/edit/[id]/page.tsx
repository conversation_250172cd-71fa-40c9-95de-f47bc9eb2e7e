import { getAllCategoriesOptions } from '@/services/categories';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { getConsumable } from '@/services/consumables';
import { getAllUsersOptions } from '@/services/users';
import type { ConsumableRequestData } from '@/types/consumables';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { EditConsumable } from './edit-consumable';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getAllLocationsOptions } from '@/services/locations';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function Edit({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('CONSUMABLE');
  const suppliers = await getAllSuppliersOptions();
  const manufacturers = await getAllManufacturersOptions();
  const consumableInfo = await getConsumable(id);
  const usersResponse = await getAllUsersOptions();
  const locations = await getAllLocationsOptions();

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }
  if (consumableInfo.type === 'error') {
    return <>Id not found</>;
  }
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }

  const { manufacturer, category, location, ...rest } = consumableInfo.data;

  const intitialValues: ConsumableRequestData = {
    ...rest,
    categoryId: category?.id ?? undefined,
    manufacturerId: manufacturer?.id ?? undefined,
    location: location.id,
    customFields: rest.customFields ? rest.customFields : {},
  };

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  return (
    <EditConsumable
      categories={categories.data}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      initialValues={intitialValues}
      manufacturers={manufacturers.data}
      suppliers={suppliers.data}
      locations={locations.data}
    />
  );
}
