'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useState } from 'react';
import type { ConsumableRequestData } from '@/types/consumables';
import { editConsumable } from '@/services/consumables';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { consumableRequestSchema } from '@/schemas/consumables';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { uploadFile } from '@/services/file-upload';
import { EntityType, Option } from '@/types';
import type { BaseFormData } from '../../types';
import { ConsumablesForm } from '../../ConsumablesForm';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface EditConsumableProps extends BaseFormData {
  /**
   * Unique identifier for the status
   */
  id: string;
  initialValues: ConsumableRequestData;
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
  locations: Option[];
}
export function EditConsumable({
  id,
  categories,
  suppliers,
  locations,
  manufacturers,
  initialValues,
  fieldGroups,
  fieldGroupDatas,
}: EditConsumableProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<ConsumableRequestData>();
  async function editConsumableData(formData: FormData): Promise<void> {
    const customFieldData = formattedCustomFields(formData);
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );

    const consumableFields = Object.fromEntries(formData);
    const { consumableImageUrl, ...consumableDetails } = consumableFields;

    const parsedResult = consumableRequestSchema.safeParse({
      ...consumableDetails,
      consumableImageUrl: '',
      customFields: customFieldData,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    let uploadedConsumableImageUrl = initialValues.consumableImageUrl;
    if (
      isImage &&
      consumableImageUrl instanceof File &&
      consumableImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', consumableImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Consumable,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedConsumableImageUrl = generateCloudFrontUrl(
        EntityType.Consumable,
        imageUploadResponse.data.fileName,
      );
    }
    const updateConsumbaleData = {
      ...parsedResult.data,
      consumableImageUrl: isImage ? uploadedConsumableImageUrl : '',
    };

    const consumableResponse = await editConsumable(id, updateConsumbaleData);

    if (consumableResponse.type === 'error') {
      if (consumableResponse.errors.errorMessages) {
        consumableResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Consumable edited successfully');
    router.back();
  }
  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Consumable</h1>
      <ConsumablesForm
        categories={categories}
        consumableFieldErrors={fieldErrors}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        initialValues={initialValues}
        manufacturers={manufacturers}
        mode="edit"
        onSubmit={editConsumableData}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        locations={locations}
      />
    </div>
  );
}
