import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { AssignConsumablesForm } from './assign-consumables-form';

const meta: Meta<typeof AssignConsumablesForm> = {
  title: 'components/Consumables/AssignConsumablesForm',
  component: AssignConsumablesForm,
};

export default meta;

type Story = StoryObj<typeof AssignConsumablesForm>;
const users = [
  {
    displayName: 'Nazal',
    value: 'Nazal',
  },
  {
    displayName: 'Nikhil',
    value: '<PERSON><PERSON>',
  },
  {
    displayName: '<PERSON><PERSON><PERSON>',
    value: '<PERSON><PERSON>n',
  },
  {
    displayName: '<PERSON>z<PERSON>',
    value: '<PERSON><PERSON><PERSON>',
  },
  {
    displayName: 'Sushmitha',
    value: '<PERSON><PERSON>mith<PERSON>',
  },
];

export const DefaultAssignConsumablesForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-16 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Assign Accessory</h1>
        <AssignConsumablesForm users={users} />
      </div>
    );
  },
};
