import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { AssignConsumable } from './assign-consumable';

interface AssignConsumableProps {
  params: {
    id: string;
  };
  searchParams: {
    name: string;
  };
}

export default async function Assign({
  params: { id },
  searchParams: { name },
}: AssignConsumableProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return <AssignConsumable id={id} name={name} users={usersResponse.data} />;
}
