import { Label, Textarea, SubmitButton, Input } from 'ui';
import type { AssignmentFormData, FieldErrors, Option } from '@/types';
import { OptionsComboBox } from '@/components/client';
import { requiredFieldErrorMessage } from '@/constants';
import { getCurrentDate } from '@/utils/date-utils';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

interface AssignConsumablesFormProps {
  users: Option[];
  onSubmit?: (formData: FormData) => Promise<void>;
  errors?: FieldErrors<AssignmentFormData>;
}

/**
 * AssignConsumablesForm component: A React form for assigning consumable items to users.
 * Allows selection of a user from the provided options and adding optional notes.
 */
export function AssignConsumablesForm({
  onSubmit,
  users,
  errors,
}: AssignConsumablesFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-assign-form">
      <fieldset>
        <Label htmlFor="userId" required>
          Select a user
        </Label>
        <OptionsComboBox
          id="userId"
          isInvalidInput={Boolean(errors?.userId)}
          name="userId"
          options={users}
          placeholder="Choose User"
        />
        {errors?.userId ? <p>{requiredFieldErrorMessage}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="date">Date of Assignment</Label>
        <Input
          className="mt-2"
          defaultValue={getCurrentDate()}
          id="date"
          isInvalidInput={Boolean(errors?.date)}
          name="date"
          type="date"
        />
        {errors?.date ? <p>{errors.date[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="notifyUser">Notify</Label>
        <MultiSelectComboBox
          id="notifyUser"
          name="notifyUser"
          options={users}
          placeholder="Choose users to be notified"
        />
      </fieldset>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          className="mt-2"
          id="note"
          name="note"
          placeholder="Include any additional information in this note."
          rows={4}
        />
        {errors?.note ? <p>{errors.note[0]}</p> : null}
      </fieldset>
      <SubmitButton>Assign</SubmitButton>
    </form>
  );
}
