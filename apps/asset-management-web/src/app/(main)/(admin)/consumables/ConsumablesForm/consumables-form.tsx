import {
  Label,
  Input,
  SubmitButton,
  Textarea,
  ImageInputWithPreview,
} from 'ui';
import { allowedImageType } from '@/constants';
import type { ConsumableRequestData } from '@/types/consumables';
import type { FieldErrors, Option, PurchaseInfo } from '@/types';
import { OptionsComboBox } from '@/components/client';
import { CustomField, renderCustomFieldGroups } from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { PurchaseInfoFields } from '../purchase-info-fields';

interface CreateConsumableFormProps extends BaseFormData {
  mode: 'create';
  initialValues?: Partial<ConsumableRequestData>;
  initialPurchaseInfo?: Partial<PurchaseInfo>;
  consumableFieldErrors?: FieldErrors<ConsumableRequestData>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfo>;
  onSubmit?: (formData: FormData) => Promise<void>;
  users: Option[];
  locations: Option[];
  setIsImage?: (isImage: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditConsumableFormProps extends BaseFormData {
  mode: 'edit';
  initialValues: ConsumableRequestData;
  consumableFieldErrors?: FieldErrors<ConsumableRequestData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setIsImage?: (isImage: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
  locations: Option[];
}

/**
 * ConsumablesForm component: A React form for creating and editing consumable items.
 * Captures information such as name, category, purchase details, and additional notes.
 */
export function ConsumablesForm(
  props: CreateConsumableFormProps | EditConsumableFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    setSelectedData,
    manufacturers,
    categories,
    suppliers,
    locations,
    mode,
    setIsImage,
    consumableFieldErrors,
    initialValues,
    fieldGroups,
    customFields,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialValues,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="name" required>
                Name
              </Label>
              <Input
                defaultValue={initialValues?.name ?? ''}
                id="name"
                isInvalidInput={Boolean(consumableFieldErrors?.name)}
                name="name"
                placeholder="e.g. A4 Sheets"
                type="text"
              />
              {consumableFieldErrors?.name ? (
                <p>{consumableFieldErrors.name[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="categoryId">Category</Label>
              <OptionsComboBox
                id="categoryId"
                initialValue={initialValues?.categoryId}
                name="categoryId"
                options={categories}
                placeholder="Choose category"
              />
            </fieldset>
          </div>
          <fieldset>
            <Label htmlFor="manufacturerId">Manufacturer</Label>
            <OptionsComboBox
              id="manufacturerId"
              initialValue={initialValues?.manufacturerId}
              name="manufacturerId"
              options={manufacturers}
              placeholder="Choose manufacturer"
            />
          </fieldset>
          <fieldset>
            <Label htmlFor="location" required>
              Location
            </Label>
            <OptionsComboBox
              id="location"
              initialValue={initialValues?.location}
              name="location"
              placeholder="Choose location"
              options={locations}
            />
            {consumableFieldErrors?.location ? (
              <p>{consumableFieldErrors.location[0]}</p>
            ) : null}
          </fieldset>
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="modelNumber">Model Number</Label>
              <Input
                defaultValue={initialValues?.modelNumber ?? ''}
                id="modelNumber"
                name="modelNumber"
                placeholder="e.g. 1233221"
                type="text"
              />
              {consumableFieldErrors?.modelNumber ? (
                <p>{consumableFieldErrors.modelNumber[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="minQuantity" required>
                Minimum Quantity
              </Label>
              <Input
                defaultValue={initialValues?.minQuantity ?? ''}
                id="minQuantity"
                isInvalidInput={Boolean(consumableFieldErrors?.minQuantity)}
                name="minQuantity"
                placeholder="e.g. 10"
                type="number"
              />
              {consumableFieldErrors?.minQuantity ? (
                <p>{consumableFieldErrors.minQuantity[0]}</p>
              ) : null}
            </fieldset>
          </div>
        </div>
      </div>

      {mode === 'create' ? (
        <div>
          <h2>Purchase Information</h2>
          <PurchaseInfoFields
            initialPurchaseInfo={props.initialPurchaseInfo}
            purchaseInfoFieldErrors={props.purchaseInfoFieldErrors}
            suppliers={suppliers}
            users={props.users}
          />
        </div>
      ) : null}

      <div>
        <h2>Additional Information</h2>
        {displayCustomFieldGroups}
        <div className="space-y-3">
          <fieldset>
            <Label htmlFor="note">Notes</Label>
            <Textarea
              defaultValue={initialValues?.note ?? ''}
              id="note"
              name="note"
              placeholder="Include any additional information in this note."
              rows={4}
            />
            {consumableFieldErrors?.note ? (
              <p>{consumableFieldErrors.note[0]}</p>
            ) : null}
          </fieldset>
          <fieldset>
            <Label htmlFor="consumableImageUrl">Select Image</Label>
            <ImageInputWithPreview
              accept={allowedImageType.join(',')}
              className="mt-2"
              defaultValue={initialValues?.consumableImageUrl ?? ''}
              id="consumableImageUrl"
              name="consumableImageUrl"
              setIsImage={setIsImage}
            />
            {consumableFieldErrors?.consumableImageUrl ? (
              <p>{consumableFieldErrors.consumableImageUrl[0]}</p>
            ) : null}
          </fieldset>
        </div>
      </div>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
