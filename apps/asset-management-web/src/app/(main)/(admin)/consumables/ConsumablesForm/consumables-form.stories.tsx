import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { ConsumablesForm } from './consumables-form';

const meta: Meta<typeof ConsumablesForm> = {
  title: 'components/Consumables/ConsumablesForm',
  component: ConsumablesForm,
};

export default meta;

type Story = StoryObj<typeof ConsumablesForm>;

const categories = [
  {
    displayName: 'Laptop',
    value: 'laptop',
  },
  {
    displayName: 'Tablet',
    value: 'tablet',
  },
  {
    displayName: 'Phone',
    value: 'phone',
  },
  {
    displayName: 'Keyboard',
    value: 'keyboard',
  },
  {
    displayName: 'Mouse',
    value: 'mouse',
  },
];

const manufacturers = [
  {
    displayName: 'Apple',
    value: 'apple',
  },
  {
    displayName: 'Dell',
    value: 'dell',
  },
  {
    displayName: 'Samsung',
    value: 'samsung',
  },
  {
    displayName: 'HP',
    value: 'hp',
  },
  {
    displayName: 'Google',
    value: 'google',
  },
];

const suppliers = [
  {
    displayName: '<PERSON>, St<PERSON><PERSON> and Funk',
    value: '<PERSON><PERSON>',
  },
  {
    displayName: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];
const users = [
  {
    displayName: 'Nazal',
    value: 'b42d0724-512d-42e1-9ab1-2df1138a52df',
  },
  {
    displayName: 'Reevan',
    value: 'b42d0724-512d-42e1-9ab1-2df1138a52de',
  },
  {
    displayName: 'Pavithra',
    value: 'b42d0724-512d-42e1-9ab1-2df1138a52dc',
  },
];
const locations = [
  { displayName: 'Mangalore', value: '1' },
  { displayName: 'Bangalore', value: '2' },
];

export const ConsumablesFormWithoutInitialValues: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="asset-management-form-heading">Create Consumable</h1>
        <ConsumablesForm
          categories={categories}
          locations={locations}
          manufacturers={manufacturers}
          mode="create"
          suppliers={suppliers}
          users={users}
        />
      </div>
    );
  },
};

export const ConsumablesFormWithInitialValues: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="asset-management-form-heading">Edit Consumable</h1>
        <ConsumablesForm
          categories={categories}
          initialValues={{
            consumableImageUrl: 'http://images.com/image-1',
            categoryId: '1',
            location: 'mangalore',
            manufacturerId: '1',
            minQuantity: 10,
            modelNumber: '1222321',
            name: 'Ink',
            note: 'Ink from HP',
          }}
          locations={locations}
          manufacturers={manufacturers}
          mode="edit"
          suppliers={suppliers}
        />
      </div>
    );
  },
};
