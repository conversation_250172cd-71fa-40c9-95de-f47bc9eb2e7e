import { InfoItem } from '@/components/InfoItems/info-item';
import { ConsumableResponse } from '@/types/consumables';
import {
  CustomFieldMap,
  mapCustomFieldNamesToValues,
} from '@/utils/custom-field';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { toStartCase } from '@/utils/string-parser';

interface ConsumbaleInfoTableProps {
  consumbaleData: ConsumableResponse;
}

interface ConsumbaleInfoType {
  displayName: string;
  value: string | string[] | null;
}

export async function ConsumbalesInfoTable({
  consumbaleData,
}: ConsumbaleInfoTableProps): Promise<React.JSX.Element> {
  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    consumbaleData.customFields?.data,
  );
  const consumbaleInfo: ConsumbaleInfoType[] = [
    {
      displayName: 'Name',
      value: consumbaleData.name,
    },
    {
      displayName: 'Category',
      value: consumbaleData.category?.name ?? '',
    },

    {
      displayName: 'Manufacturer',
      value: consumbaleData.manufacturer?.name ?? '',
    },
    {
      displayName: 'Location',
      value: toStartCase(consumbaleData.location.name),
    },
    {
      displayName: 'Total Quantity',
      value: consumbaleData.totalQuantity.toString(),
    },
    {
      displayName: 'Min Quantity',
      value: consumbaleData.minQuantity.toString(),
    },
    {
      displayName: 'Available Quantity',
      value: consumbaleData.availableQuantity.toString(),
    },
    {
      displayName: 'Model Number',
      value: consumbaleData.modelNumber,
    },
    {
      displayName: 'Notes',
      value: consumbaleData.note,
    },
  ];
  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        {consumbaleInfo.map((consumbale) => (
          <InfoItem
            key={consumbale.displayName}
            name={consumbale.displayName}
            value={
              Array.isArray(consumbale.value)
                ? displayDataOrDefault(consumbale.value.join())
                : displayDataOrDefault(consumbale.value)
            }
          />
        ))}
        {Object.entries(customFieldMap).map(([fieldName, value]) => (
          <InfoItem key={fieldName} name={fieldName} value={value} />
        ))}
      </div>
      {consumbaleData.consumableImageUrl ? (
        <div className="flex w-1/4 flex-shrink-0 justify-end">
          <img
            alt="consumable-name"
            className="h-[200px] w-auto rounded object-cover shadow"
            src={consumbaleData.consumableImageUrl}
            width={400}
            height={400}
          />
        </div>
      ) : null}
    </div>
  );
}
