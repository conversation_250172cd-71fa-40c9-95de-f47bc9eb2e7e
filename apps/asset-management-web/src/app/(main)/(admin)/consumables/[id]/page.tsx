import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from 'ui';
import { getConsumable } from '@/services/consumables';
import { consumableRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { DeleteConsumableButton } from './delete-consumable-button';
import { AssignmentInfoTable } from './AssignmentInfoTable';
import { FinanceTable } from './FinanceTable';
import { ConsumableHistory } from './ConsumableHistory';
import { ConsumbalesInfoTable } from './ConsumablesInfo/consumables-info';
import { Suspense } from 'react';
import { ConsumableTransfer } from './ConsumableTransfer';
import Loading from '@/components/loader';

interface ConsumableDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function ConsumableDetailsPage({
  params: { id },
}: ConsumableDetailsPageProps): Promise<React.JSX.Element> {
  const consumableData = await getConsumable(id);
  if (consumableData.type === 'error') {
    return <>Something went wrong</>;
  }
  const consumableInfo = consumableData.data;

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {consumableInfo.name}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={['purchase', 'assign', 'document', 'transfer']}
            entityId={id}
            entityName={consumableInfo.name}
            routes={consumableRoutes}
          />
          <DeleteConsumableButton
            disabled={
              consumableInfo.availableQuantity !== consumableInfo.totalQuantity
            }
            id={id}
            name={consumableInfo.name}
          />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="assignemnt">Assignments</TabsTrigger>
            <TabsTrigger value="finance">Finance</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="document">Document</TabsTrigger>
            <TabsTrigger value="transfer">Transfer</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <Suspense fallback={<Loading />}>
              <ConsumbalesInfoTable consumbaleData={consumableInfo} />
            </Suspense>
          </TabsContent>
          <TabsContent value="assignemnt">
            <Suspense>
              <AssignmentInfoTable id={id} name={consumableInfo.name} />
            </Suspense>
          </TabsContent>
          <TabsContent value="finance">
            <Suspense fallback={<Loading />}>
              <FinanceTable id={id} />
            </Suspense>
          </TabsContent>
          <TabsContent value="history">
            <Suspense fallback={<Loading />}>
              <ConsumableHistory id={id} name={consumableInfo.name} />
            </Suspense>
          </TabsContent>
          <TabsContent value="document">
            <Suspense fallback={<Loading />}>
              <DocumentTable
                id={id}
                entityType={EntityType.Consumable}
                categoryType={CategoryType.CONSUMABLE}
              />
            </Suspense>
          </TabsContent>
          <TabsContent value="transfer">
            <Suspense fallback={<Loading />}>
              <ConsumableTransfer id={id} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
