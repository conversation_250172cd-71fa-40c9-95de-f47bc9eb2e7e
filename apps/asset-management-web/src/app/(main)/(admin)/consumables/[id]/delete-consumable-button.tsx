'use client';

import { useRouter } from 'next/navigation';
import { consumableRoutes } from '@/constants/routes';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { OutlinedDeleteButton } from '@/components/client';
import { Routes } from '@/types';

export function DeleteConsumableButton({
  id,
  name,
  disabled,
}: {
  id: string;
  name: string;
  disabled?: boolean;
}): React.JSX.Element {
  const router = useRouter();
  return (
    <DeleteEntity
      entityId={id}
      entityName={name}
      entityType={Routes.CONSUMABLE}
      onDelete={() => {
        router.push(consumableRoutes.CONSUMABLES);
      }}
    >
      <OutlinedDeleteButton isDisabled={disabled} />
    </DeleteEntity>
  );
}
