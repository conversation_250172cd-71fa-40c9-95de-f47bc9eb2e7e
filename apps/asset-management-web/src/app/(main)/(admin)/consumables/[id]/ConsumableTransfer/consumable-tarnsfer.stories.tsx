import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { ConsumableTransfer } from './consumable-transfer';

const meta: Meta<typeof ConsumableTransfer> = {
  title: 'components/Consumables/ConsumableTransfer',
  component: ConsumableTransfer,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ConsumableTransfer>;

export const DefaultConsumableHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
