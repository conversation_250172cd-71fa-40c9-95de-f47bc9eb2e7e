import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { ConsumableHistory } from './consumable-history';

const meta: Meta<typeof ConsumableHistory> = {
  title: 'components/Consumables/ConsumableHistory',
  component: ConsumableHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ConsumableHistory>;

export const DefaultConsumableHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
    name: 'A4 Sheets',
  },
};
