import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getConsumableFinanceInfo } from '@/services/consumables';
import { parseDate } from '@/utils/date-utils';
import { Routes, TableHeading } from '@/types';
import {
  displayDataOrDefault,
  generateDynamicRoute,
} from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { EditButton } from '@/components/ActionButtons/EditButton/edit-button';
import { consumableRoutes } from '@/constants/routes';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { DeleteButton } from '@/components/ActionButtons/DeleteButton/delete-button';

interface FinanceTableProps {
  id: string;
}

export async function FinanceTable({
  id,
}: FinanceTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Order No' },
    { title: 'Supplier' },
    { title: 'Quantity', className: 'text-right' },
    { title: 'Purchase Cost', className: 'text-right' },
    { title: 'Purchase Date' },
    { title: 'Purchased By' },
  ];
  const renderAction = await getActionColumnByRole('consumables', [
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;

  const consumableFinanceInfo = await getConsumableFinanceInfo(id);
  if (consumableFinanceInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {consumableFinanceInfo.data.map((finance) => (
          <TableRow key={finance.id}>
            <TableCell>{displayDataOrDefault(finance.orderNumber)}</TableCell>
            <TableCell>
              {displayDataOrDefault(finance.supplier?.name)}
            </TableCell>
            <TableCell className="text-right">{finance.quantity}</TableCell>
            <TableCell className="text-right">{finance.purchaseCost}</TableCell>
            <TableCell>
              {finance.purchaseDate
                ? parseDate('MMM dd, yyyy')(finance.purchaseDate)
                : null}
            </TableCell>
            <TableCell>
              {displayDataOrDefault(finance.purchasedBy?.name)}
            </TableCell>
            {renderAction ? (
              <TableCell>
                <div className=" flex items-center gap-4">
                  <EditButton
                    href={generateDynamicRoute(consumableRoutes.EDIT_PURCHASE, {
                      purchaseId: finance.id,
                    })}
                  />
                  <DeleteEntity
                    entityId={finance.id}
                    entityType={Routes.PURCHASE}
                  >
                    <DeleteButton />
                  </DeleteEntity>
                </div>
              </TableCell>
            ) : null}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
