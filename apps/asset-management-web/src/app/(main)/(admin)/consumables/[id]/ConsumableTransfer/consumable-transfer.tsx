import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getTransferredConsumables } from '@/services/consumables';
import { parseDate } from '@/utils/date-utils';

const tableHeaders = ['Actions Performed', 'Date', 'Action By'];
interface ConsumableTransferProps {
  id: string;
}

export async function ConsumableTransfer({
  id,
}: ConsumableTransferProps): Promise<React.JSX.Element> {
  const consumableTransferInfo = await getTransferredConsumables(id);

  if (consumableTransferInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {consumableTransferInfo.data.map((transfer) => (
          <TableRow key={transfer.id}>
            <TableCell>
              {(() => {
                if (id === transfer.fromEntity.id) {
                  return (
                    <p>
                      Transferred <b>{transfer.quantity}</b> items to{' '}
                      <b>
                        {transfer.toEntity.name} (
                        {transfer.toEntity.location.name})
                      </b>
                    </p>
                  );
                } else {
                  return (
                    <p>
                      Received <b>{transfer.quantity}</b> items from{' '}
                      <b>
                        {transfer.fromEntity.name} (
                        {transfer.fromEntity.location.name})
                      </b>
                    </p>
                  );
                }
              })()}
            </TableCell>
            <TableCell>
              {parseDate('MMM dd, yyyy')(transfer.createdAt)}
            </TableCell>
            <TableCell>{transfer.user.name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
