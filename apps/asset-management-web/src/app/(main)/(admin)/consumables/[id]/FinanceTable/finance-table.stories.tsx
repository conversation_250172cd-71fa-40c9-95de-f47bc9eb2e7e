import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { FinanceTable } from './finance-table';

const meta: Meta<typeof FinanceTable> = {
  title: 'components/Consumables/FinanceTable',
  component: FinanceTable,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof FinanceTable>;

export const DefaultFinanceTable: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
