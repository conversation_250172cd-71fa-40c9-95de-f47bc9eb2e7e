import {
  Hover<PERSON>ard<PERSON>ontent,
  HoverCardTrigger,
  HoverCard,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Selector,
} from 'ui';
import Link from 'next/link';
import { Codesandbox as BaseIcon } from 'lucide-react';
import { getAllUnassignedAssets } from '@/services/assets';
import { assetsRoutes } from '@/constants/routes';
import { parseDate } from '@/utils/date-utils';
import {
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { Search } from '@/components/client';
import { getAllLocations } from '@/services/locations';

interface LocationSearchParams extends SearchParams {
  location?: string;
}
export default async function UnassignedAssetPage({
  searchParams,
}: {
  searchParams?: LocationSearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Image' },
    { title: 'Name' },
    { title: 'Tag' },
    { title: 'Serial No' },
    { title: 'Status' },
    { title: 'Model' },
    { title: 'Location' },
    { title: 'EOW' },
  ];

  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    location: searchParams?.location,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const unassignedAssets = await getAllUnassignedAssets(queryParams);
  const locations = await getAllLocations();

  if (unassignedAssets.type === 'error') {
    return <>Not able to load/get asset, please try again!</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations, please try again!</>;
  }
  const totalPages = getNumberOfPages(
    unassignedAssets.count,
    searchParams?.limit,
  );

  const locationList = locations.data.map((item) => item.name);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          Unassigned Assets
        </h1>
        <div className="flex gap-3">
          <Selector
            options={locationList}
            placeholder="location"
            queryParam="location"
          />
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, model, serial number"
          />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {unassignedAssets.data && unassignedAssets.data.length > 0 ? (
            unassignedAssets.data.map((asset) => (
              <TableRow key={asset.id}>
                <TableCell className="text-center ">
                  {asset.assetImageUrl ? (
                    <img
                      alt={asset.assetName}
                      className="img-cell-style"
                      height={10}
                      src={asset.assetImageUrl}
                      width={30}
                    />
                  ) : (
                    <BaseIcon className="h-7 w-7" />
                  )}
                </TableCell>
                <TableCell className="first-letter:uppercase">
                  <Link
                    className="text-primary-600 hover:underline "
                    href={`${assetsRoutes.MAIN}/${asset.id}`}
                  >
                    {asset.assetName}
                  </Link>
                </TableCell>
                <TableCell>{asset.assetTag}</TableCell>
                <TableCell className=" max-w-[40px]">
                  {' '}
                  <HoverCard>
                    <div className=" flex flex-wrap">
                      <HoverCardTrigger className=" truncate">
                        {asset.serialNumber}
                      </HoverCardTrigger>
                    </div>
                    {asset.serialNumber ? (
                      <HoverCardContent> {asset.serialNumber}</HoverCardContent>
                    ) : null}
                  </HoverCard>
                </TableCell>

                <TableCell>
                  {displayDataOrDefault(asset.assetStatus?.name)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(asset.assetModel?.modelName)}
                </TableCell>
                <TableCell className="start-case">
                  {asset.location.name}
                </TableCell>
                <TableCell>
                  {asset.endOfLife
                    ? parseDate('MMM dd, yyyy')(asset.endOfLife)
                    : '-'}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={unassignedAssets.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
