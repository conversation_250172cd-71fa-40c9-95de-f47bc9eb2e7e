import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  ToolTip,
  ToolTipContent,
  ToolTipTrigger,
} from 'ui';
import { GetAllQueryParams, SearchParams, type TableHeading } from '@/types';
import { getNumberOfPages } from '@/utils/helper-functions';
import { Pagination } from '@/components/Pagination/pagination';
import { Search } from '@/components/client';
import { getSupplierEvaluations } from '@/services/evaluation';
import { initialRowsPerPage } from '@/constants';
import { parseDate } from '@/utils/date-utils';
import { InfoButton } from '@/components/ActionButtons/InfoButton/info-button';
import { supplierRoutes } from '@/constants/routes';
import { toStartCase } from '@/utils/string-parser';
import Link from 'next/link';

interface EvaluationSearchParams extends SearchParams {
  serviceType?: string;
}

export default async function SupplierEvaluationPage({
  searchParams,
}: {
  searchParams?: EvaluationSearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Supplier' },
    { title: 'Evaluation 1' },
    { title: 'Evaluation 2' },
    { title: 'Evaluation 3' },
    { title: 'Evaluation 4' },
  ];

  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    serviceType: searchParams?.serviceType,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const supplierEvaluations = await getSupplierEvaluations(queryParams);

  if (supplierEvaluations.type === 'error') {
    return <>Not able to load/get data, please try again!</>;
  }

  const totalPages = getNumberOfPages(
    supplierEvaluations.count,
    searchParams?.limit,
  );

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          Supplier Evaluations
        </h1>
        <div className="flex gap-3">
          <Search className="w-[18.5rem]" placeholder="Search supplier name" />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader>
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {supplierEvaluations.data && supplierEvaluations.data.length > 0 ? (
            supplierEvaluations.data.map((item) => (
              <TableRow key={item.supplier.id}>
                <TableCell>
                  <div>
                    <Link
                      className="text-primary-600 first-letter:uppercase"
                      href={`${supplierRoutes.BASE}/${item.supplier.id}`}
                    >
                      {item.supplier.name}
                    </Link>
                    <span className="ml-2">
                      ({toStartCase(item.supplier.evaluationFrequency)})
                    </span>
                  </div>
                  <div>{item.supplier.evaluatorDepartment?.name || ''}</div>
                </TableCell>

                {[0, 1, 2, 3].map((index) => {
                  const evaluation = item.evaluations[index] as
                    | (typeof item.evaluations)[number]
                    | undefined;

                  return (
                    <TableCell key={index}>
                      {evaluation?.date ? (
                        <>
                          <span>Date: </span>

                          {parseDate('MMM dd, yyyy')(evaluation.date)}
                        </>
                      ) : (
                        ''
                      )}
                      {evaluation?.totalScore !== undefined ? (
                        <div className="mt-2 flex items-center gap-2">
                          <span className="font-medium">
                            Score: {evaluation.totalScore}
                          </span>
                          <ToolTip>
                            <ToolTipTrigger>
                              <InfoButton
                                className="text-yellow-500"
                                href={`${supplierRoutes.BASE}/${evaluation.id}${supplierRoutes.DISPLAY_EVALUATION}?evaluationId=${evaluation.id}`}
                              />
                            </ToolTipTrigger>
                            <ToolTipContent className="z-10">
                              View
                            </ToolTipContent>
                          </ToolTip>
                        </div>
                      ) : (
                        '-'
                      )}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination mt-4">
        <Pagination
          dataCount={supplierEvaluations.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
