'use client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import type { ApplianceFormData } from '@/types/appliance';
import { ApplianceFormSchema } from '@/schemas/appliance';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { updateAppliances } from '@/services/appliance';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType, Option } from '@/types';
import type { BaseFormData } from '../../types';
import { ApplianceForm } from '../../ApplianceForm/index';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface EditapplianceProps extends BaseFormData {
  id: string;
  initialValues: ApplianceFormData;
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
  locations: Option[];
}
export function EditAppliance({
  id,
  categories,
  manufacturers,
  locations,
  initialValues,
  fieldGroups,
  fieldGroupDatas,
}: EditapplianceProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<ApplianceFormData>();
  const editAppliance = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );
    const applianceFields = Object.fromEntries(formData);
    const { applianceImageUrl, ...applianceDetails } = applianceFields;

    const parsedResult = ApplianceFormSchema.safeParse({
      ...applianceDetails,
      applianceImageUrl: '',
      customFields: customFieldData,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    let uploadedApplianceImageUrl = initialValues.applianceImageUrl;
    if (
      isImage &&
      applianceImageUrl instanceof File &&
      applianceImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', applianceImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Appliance,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedApplianceImageUrl = generateCloudFrontUrl(
        EntityType.Appliance,
        imageUploadResponse.data.fileName,
      );
    }

    const updateAccessoryData = {
      ...parsedResult.data,
      applianceImageUrl: isImage ? uploadedApplianceImageUrl : '',
    };

    const applianceResponse = await updateAppliances(id, updateAccessoryData);

    if (applianceResponse.type === 'error') {
      if (applianceResponse.errors.errorMessages) {
        applianceResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('something went wrong');
      return;
    }

    toast.success('appliance edited successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Appliance</h1>
      <ApplianceForm
        applianceFieldErrors={fieldErrors}
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        initialApplianceInfo={initialValues}
        manufacturers={manufacturers}
        locations={locations}
        mode="edit"
        onSubmit={editAppliance}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
      />
    </div>
  );
}
