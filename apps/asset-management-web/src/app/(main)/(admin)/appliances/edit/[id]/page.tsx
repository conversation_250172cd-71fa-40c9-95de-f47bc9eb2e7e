import { getAllCategoriesOptions } from '@/services/categories';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { getAppliance } from '@/services/appliance';
import { ApplianceFormData } from '@/types/appliance';
import { EditAppliance } from './edit-appliance';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getAllLocationsOptions } from '@/services/locations';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditappliancePage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('APPLIANCE');
  const manufacturers = await getAllManufacturersOptions();
  const applianceInfo = await getAppliance(id);
  const locations = await getAllLocationsOptions();

  if (applianceInfo.type === 'error') {
    return <>Id not found</>;
  }

  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load Location. Please try again!</>;
  }

  const { manufacturer, category, location, ...rest } = applianceInfo.data;

  const intitialValues: ApplianceFormData = {
    ...rest,
    categoryId: category?.id ?? undefined,
    manufacturerId: manufacturer?.id ?? undefined,
    location: location.id,
    customFields: rest.customFields ? rest.customFields : {},
  };

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  return (
    <EditAppliance
      categories={categories.data}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      initialValues={intitialValues}
      manufacturers={manufacturers.data}
      locations={locations.data}
    />
  );
}
