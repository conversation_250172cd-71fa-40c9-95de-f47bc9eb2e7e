import {
  Label,
  Textarea,
  Input,
  SubmitButton,
  MultipleImageInputWithPreview,
} from 'ui';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { cn } from 'utils';
import type { FieldErrors, Option } from '@/types';
import { ServiceRating } from '@/types';
import { OptionsComboBox } from '@/components/client';
import { allowedImageType } from '@/constants';
import { getCurrentDate } from '@/utils/date-utils';
import { CustomField, renderCustomFieldGroups } from '@/utils/custom-field';
import { ServicesFormData } from '@/types/services';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

const defaultRatingStyle = 'cursor-pointer text-neutral-500 hover:scale-110';

interface ApplianceFormProps {
  /**
   * Initial value for the form fields.
   */
  initialValues?: Partial<ServicesFormData>;
  /**
   * Callback function triggered on form submission.
   */
  onSubmit?: (formData: FormData) => Promise<void>;
  /**
   * Errors object containing field-specific error messages.
   */
  errors?: FieldErrors<ServicesFormData>;
  /**
   * The unique identifier of the appliance.
   */
  id: string;
  /**
   * Callback function triggered when a rating is selected.
   */
  handleRating: (value: ServiceRating) => void;
  /**
   * The selected rating value
   */
  rating: string | undefined;
  /**
   * An array of options representing the suppliers.
   */
  suppliers: Option[];
  /**
   * Optional callback function to update the status of the defualt image is provided or not.
   */
  mode: 'create' | 'edit';
  setIsImage: (isImage: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
  setImageUrls: (isData: File[]) => void;
  imageUrls: File[];
}

export function ApplianceServiceForm({
  initialValues,
  errors,
  onSubmit,
  id,
  handleRating,
  rating,
  setIsImage,
  suppliers,
  setSelectedData,
  fieldGroups,
  customFields,
  setImageUrls,
  imageUrls,
  mode,
}: ApplianceFormProps): React.JSX.Element {
  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialValues,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form pt-0">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="name" required>
                Service Name
              </Label>
              <Input
                defaultValue={initialValues?.name ?? ''}
                id="name"
                name="name"
                placeholder="Service name"
                type="text"
              />
              {errors?.name ? <p>{errors.name[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="date">Service Date</Label>
              <Input
                defaultValue={initialValues?.date ?? getCurrentDate()}
                id="date"
                isInvalidInput={Boolean(errors?.date)}
                name="date"
                type="date"
              />
              {errors?.date ? <p>{errors.date[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="nextServiceDate">Next Service Date</Label>
              <Input
                defaultValue={initialValues?.nextServiceDate}
                id="nextServiceDate"
                isInvalidInput={Boolean(errors?.nextServiceDate)}
                name="nextServiceDate"
                type="date"
              />
              {errors?.nextServiceDate ? (
                <p>{errors.nextServiceDate[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="contactName">Contact Name</Label>
              <Input
                defaultValue={initialValues?.contactName}
                id="contactName"
                name="contactName"
                placeholder="Enter Contact Name"
                type="text"
              />
              {errors?.contactName ? <p>{errors.contactName[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="contactNumber">Phone Number</Label>
              <Input
                defaultValue={initialValues?.contactNumber}
                id="contactNumber"
                name="contactNumber"
                placeholder="Enter Contact Number"
                type="number"
              />
              {errors?.contactNumber ? <p>{errors.contactNumber[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="cost">Cost</Label>
              <Input
                defaultValue={initialValues?.cost}
                id="cost"
                name="cost"
                placeholder="E.g 1000"
                type="number"
              />
              {errors?.cost ? <p>{errors.cost[0]}</p> : null}
            </fieldset>
            <fieldset className="hidden">
              <Label htmlFor="applianceIds" required>
                Appliance Id
              </Label>
              <MultiSelectComboBox
                id="applianceIds"
                name="applianceIds"
                options={[]}
                placeholder="Select"
                selectedValues={[id]}
              />
            </fieldset>
            <fieldset>
              <Label htmlFor="supplierId">Supplier</Label>
              <OptionsComboBox
                id="supplierId"
                initialValue={initialValues?.supplierId}
                name="supplierId"
                options={suppliers}
                placeholder="Choose Suppiler"
              />
              {errors?.supplierId ? <p>{errors.supplierId[0]}</p> : null}
            </fieldset>
          </div>
        </div>
        <div className="mt-5">
          <h2>Additional Information</h2>
          {displayCustomFieldGroups}
          <div className="space-y-3">
            <fieldset className="mt-2">
              <Label htmlFor="note">Notes</Label>
              <Textarea
                defaultValue={initialValues?.note ?? ''}
                id="note"
                name="note"
                placeholder="Include any additional information in this note."
                rows={4}
              />
              {errors?.note ? <p>{errors.note[0]}</p> : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="serviceImageUrl">Select Image</Label>
              <MultipleImageInputWithPreview
                accept={allowedImageType.join(',')}
                className="mt-2"
                defaultValue={initialValues?.serviceImageUrl ?? []}
                id="serviceImageUrl"
                imageUrls={imageUrls}
                name="serviceImageUrl"
                setImageUrls={setImageUrls}
                setIsImage={setIsImage}
              />
              {errors?.serviceImageUrl ? (
                <p>{errors.serviceImageUrl[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label>Rate Service</Label>
              <div className="flex gap-5 pt-1">
                <ThumbsUp
                  className={cn(defaultRatingStyle, {
                    'fill-blue-200': rating === ServiceRating.GOOD,
                  })}
                  onClick={() => {
                    handleRating(ServiceRating.GOOD);
                  }}
                />
                <ThumbsDown
                  className={cn(defaultRatingStyle, {
                    'fill-blue-200': rating === ServiceRating.BAD,
                  })}
                  onClick={() => {
                    handleRating(ServiceRating.BAD);
                  }}
                />
              </div>
            </fieldset>
          </div>
        </div>

        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
