import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import type { ServiceRating } from '@/types';
import { ApplianceServiceForm } from './appliance-service-form';

const meta: Meta<typeof ApplianceServiceForm> = {
  title: 'components/Appliance/ApplianceServiceForm',
  component: ApplianceServiceForm,
};

export default meta;

type Story = StoryObj<typeof ApplianceServiceForm>;

const suppliers = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'JSF',
  },
  {
    displayName: 'Kunze, Predovic and Ziemann',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];

export const ApplianceServiceFormWithoutInitialData: Story = {
  render: () => {
    function ApplianceFormWithoutInitialDataWrapper(): React.JSX.Element {
      const [rating, setRating] = useState<ServiceRating | undefined>(
        undefined,
      );
      const [imageUrls, setImageUrls] = useState<File[]>([]);
      const [isImage, setIsImage] = useState<boolean>(false);

      const handleSetImageUrls = (files: File[]) => {
        setImageUrls(files);
        setIsImage(files.length > 0);
      };

      const handleSetIsImage = () => {
        setIsImage(isImage);
      };

      return (
        <div className="shadow-container m-auto my-8 h-fit w-1/2">
          <h1 className="asset-management-form-heading">
            Add Appliance service
          </h1>
          <ApplianceServiceForm
            handleRating={(value: ServiceRating): void => {
              setRating((prevRating) =>
                prevRating === value ? undefined : value,
              );
            }}
            id="1"
            imageUrls={imageUrls}
            rating={rating}
            setImageUrls={handleSetImageUrls}
            setIsImage={handleSetIsImage}
            suppliers={suppliers}
            mode="edit"
          />
        </div>
      );
    }
    return <ApplianceFormWithoutInitialDataWrapper />;
  },
};
