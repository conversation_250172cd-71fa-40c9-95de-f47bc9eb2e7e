import {
  Label,
  Input,
  SubmitButton,
  Textarea,
  ImageInputWithPreview,
} from 'ui';
import { OptionsComboBox } from '@/components/client';
import { allowedImageType } from '@/constants';
import type { ApplianceFormData } from '@/types/appliance';
import type { FieldErrors, Option, PurchaseInfo } from '@/types';
import { CustomField, renderCustomFieldGroups } from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { PurchaseInfoFields } from '../purchase-info-fields';

interface CreateApplianceFormProps extends BaseFormData {
  mode: 'create';
  initialApplianceInfo?: Partial<ApplianceFormData>;
  initialPurchaseInfo?: Partial<PurchaseInfo>;
  applianceFieldErrors?: FieldErrors<ApplianceFormData>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfo>;
  onSubmit?: (formData: FormData) => Promise<void>;
  users: Option[];
  suppliers: Option[];
  locations: Option[];
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditApplianceFormProps extends BaseFormData {
  mode: 'edit';
  initialApplianceInfo: ApplianceFormData;
  applianceFieldErrors?: FieldErrors<ApplianceFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
  locations: Option[];
}

/**
 * ApplianceForm component: A React form for creating and editing appliance items.
 * Captures information such as name, company name, category, purchasedate , purchasecost, and additional notes.
 */
export function ApplianceForm(
  props: CreateApplianceFormProps | EditApplianceFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    setSelectedData,
    manufacturers,
    categories,
    locations,
    mode,
    setIsImage,
    applianceFieldErrors,
    initialApplianceInfo,
    fieldGroups,
    customFields,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialApplianceInfo,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-5">
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="name" required>
                Appliance Name
              </Label>
              <Input
                defaultValue={initialApplianceInfo?.name ?? ''}
                id="name"
                isInvalidInput={Boolean(applianceFieldErrors?.name)}
                name="name"
                placeholder="e.g. CardStock(White)"
                type="text"
              />
              {applianceFieldErrors?.name ? (
                <p>{applianceFieldErrors.name[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="categoryId">Category</Label>
              <OptionsComboBox
                id="categoryId"
                initialValue={initialApplianceInfo?.categoryId}
                name="categoryId"
                options={categories}
                placeholder="Choose category"
              />
              {applianceFieldErrors?.categoryId ? (
                <p>{applianceFieldErrors.categoryId[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="manufacturerId">Manufacturer</Label>
              <OptionsComboBox
                id="manufacturerId"
                initialValue={initialApplianceInfo?.manufacturerId}
                name="manufacturerId"
                options={manufacturers}
                placeholder="Choose manufacturer"
              />
              {applianceFieldErrors?.manufacturerId ? (
                <p>{applianceFieldErrors.manufacturerId[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="location" required>
                Location
              </Label>
              <OptionsComboBox
                id="location"
                initialValue={initialApplianceInfo?.location}
                name="location"
                placeholder="Choose location"
                options={locations}
              />
              {applianceFieldErrors?.location ? (
                <p>{applianceFieldErrors.location[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="modelNumber">Model</Label>
              <Input
                defaultValue={initialApplianceInfo?.modelNumber ?? ''}
                id="modelNumber"
                isInvalidInput={Boolean(applianceFieldErrors?.modelNumber)}
                name="modelNumber"
                placeholder="Enter model"
                type="text"
              />
              {applianceFieldErrors?.modelNumber ? (
                <p>{applianceFieldErrors.modelNumber[0]}</p>
              ) : null}
            </fieldset>
          </div>
        </div>
      </div>

      {mode === 'create' ? (
        <div>
          <h2>Purchase Information</h2>
          <PurchaseInfoFields
            initialPurchaseInfo={props.initialPurchaseInfo}
            purchaseInfoFieldErrors={props.purchaseInfoFieldErrors}
            suppliers={props.suppliers}
            users={props.users}
          />
        </div>
      ) : null}

      <div>
        <h2 className="mb-4 text-sm font-semibold uppercase text-slate-500">
          Additional Information
        </h2>
        {displayCustomFieldGroups}
        <div className="space-y-4">
          <fieldset>
            <Label htmlFor="note">Notes</Label>
            <Textarea
              defaultValue={initialApplianceInfo?.note ?? ''}
              id="note"
              name="note"
              placeholder="Include any additional information in this note."
              rows={4}
            />
            {applianceFieldErrors?.note ? (
              <p>{applianceFieldErrors.note[0]}</p>
            ) : null}
          </fieldset>
          <fieldset>
            <Label htmlFor="applianceImageUrl">Select Image</Label>
            <ImageInputWithPreview
              accept={allowedImageType.join(',')}
              className="mt-2"
              defaultValue={initialApplianceInfo?.applianceImageUrl ?? ''}
              id="applianceImageUrl"
              name="applianceImageUrl"
              setIsImage={setIsImage}
            />
            {applianceFieldErrors?.applianceImageUrl ? (
              <p>{applianceFieldErrors.applianceImageUrl[0]}</p>
            ) : null}
          </fieldset>
        </div>
      </div>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
