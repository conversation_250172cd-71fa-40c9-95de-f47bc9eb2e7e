import type { Meta, StoryObj } from '@storybook/react';
import { ApplianceForm } from './appliance-form';

const categories = [
  {
    displayName: 'Printer Link',
    value: 'pl',
  },
  {
    displayName: 'Printer Paper',
    value: 'pp',
  },
];

const manufacturers = [
  {
    displayName: 'Apple',
    value: 'apple',
  },
  {
    displayName: 'Asus',
    value: 'asus',
  },
  {
    displayName: 'Avery',
    value: 'avery',
  },
  {
    displayName: 'Dell',
    value: 'dell',
  },
  {
    displayName: 'Samsung',
    value: 'samsung',
  },
  {
    displayName: 'HP',
    value: 'hp',
  },
  {
    displayName: 'Google',
    value: 'google',
  },
];

const suppliers = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'JSF',
  },
  {
    displayName: 'Kunze, Predovic and Ziemann',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: '<PERSON><PERSON>-<PERSON>',
    value: 'KR',
  },
];

const locations = [
  { displayName: 'Mangalore', value: '1' },
  { displayName: 'Bangalore', value: '2' },
];

const meta: Meta<typeof ApplianceForm> = {
  title: 'components/Appliance/ApplianceForm',
  component: ApplianceForm,
};

export default meta;

type Story = StoryObj<typeof ApplianceForm>;

export const ApplianceFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="asset-management-form-heading">Add Appliance</h1>
        <ApplianceForm
          categories={categories}
          manufacturers={manufacturers}
          mode="create"
          suppliers={suppliers}
          locations={locations}
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};

export const ApplianceFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="asset-management-form-heading">Edit Appliance</h1>
        <ApplianceForm
          categories={categories}
          initialApplianceInfo={{
            name: 'Printer Ink',
            categoryId: '1',
            manufacturerId: '1',
            modelNumber: 'M-456',
            location: 'BANGALORE',
            note: 'High-demand item',
            applianceImageUrl: 'https://example.com/ink_image.jpg',
          }}
          locations={locations}
          manufacturers={manufacturers}
          mode="edit"
        />
      </div>
    );
  },
};
