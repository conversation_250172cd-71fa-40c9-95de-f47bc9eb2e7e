import { format, parseISO } from 'date-fns';
import { getAllUsersOptions } from '@/services/users';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { getApplianceFinanceInfo } from '@/services/appliance';
import type { PurchaseInfo } from '@/types';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { PurchaseAppliance } from '../purchase-appliance';

interface PurhcaseApplianceProps {
  params: {
    id: string;
  };
}

export default async function Purchase({
  params: { id },
}: PurhcaseApplianceProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  const suppliers = await getAllSuppliersOptions();
  const purchaseInfoResponse = await getApplianceFinanceInfo(id);

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (purchaseInfoResponse.type === 'error') {
    return <>Id not found</>;
  }

  const purchaseInfoArray = purchaseInfoResponse.data;

  const purchaseInfo = purchaseInfoArray[0];

  const { supplier, purchasedBy, purchaseDate, ...rest } = purchaseInfo;

  const initialValues: Partial<PurchaseInfo> = {
    ...rest,
    supplierId: supplier?.id ?? undefined,
    purchasedById: purchasedBy?.id ?? undefined,
    purchaseDate: purchaseDate
      ? format(parseISO(purchaseDate), 'yyyy-MM-dd')
      : null,
  };

  return (
    <PurchaseAppliance
      id={purchaseInfo.id}
      initialValues={initialValues}
      suppliers={suppliers.data}
      users={usersResponse.data}
      applianceId={id}
    />
  );
}
