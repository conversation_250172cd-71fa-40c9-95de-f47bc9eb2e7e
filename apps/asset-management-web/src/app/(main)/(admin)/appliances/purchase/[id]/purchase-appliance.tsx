'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchemaPartial } from '@/schemas';
import type { Option, PurchaseInfo } from '@/types';
import { PurchaseApplianceForm } from '../PurchaseApplianceForm/purchase-appliance-form';
import { editPurchaseAppliance } from '@/services/appliance';

interface PurchaseApplianceProps {
  /** The unique identifier of the appliance. */
  id: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
  /** Initial values for purchase information. */
  initialValues: Partial<PurchaseInfo>;

  applianceId: string;
}

/**
 * Component for purchasing a appliance.
 * PurchaseApplianceProps represents the properties for the PurchaseLicense component.
 * returns the rendered PurchaseLicense component.
 */
export function PurchaseAppliance({
  id,
  users,
  suppliers,
  initialValues,
  applianceId,
}: PurchaseApplianceProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<Partial<PurchaseInfo>>();
  const purchaseAppliance = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchemaPartial.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await editPurchaseAppliance(id, applianceId, {
      ...parsedResult.data,
      quantity: 1,
    });

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Purchase Appliance updated successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Edit Purchase</h1>
      <PurchaseApplianceForm
        errors={fieldErrors}
        onSubmit={purchaseAppliance}
        purchaseInfo={initialValues}
        suppliers={suppliers}
        users={users}
      />
    </div>
  );
}
