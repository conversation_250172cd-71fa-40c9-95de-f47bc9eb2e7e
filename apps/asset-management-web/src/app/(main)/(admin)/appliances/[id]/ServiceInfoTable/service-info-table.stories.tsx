import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { ServiceInfoTable } from './service-info-table';

const meta: Meta<typeof ServiceInfoTable> = {
  title: 'components/Appliance/ServiceInfoTable',
  component: ServiceInfoTable,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ServiceInfoTable>;

export const DefaultAssignmentInfoTable: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
