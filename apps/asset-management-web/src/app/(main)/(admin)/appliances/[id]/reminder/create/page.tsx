import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { getAllUsersOptions } from '@/services/users';
import { CreateReminder } from '@/components/CreateReminder/create-reminder';
import { CategoryType } from '@/types';

interface ApplianceDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function page({
  params: { id },
}: ApplianceDetailsPageProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return (
    <CreateReminder
      categoryType={CategoryType.APPLIANCE}
      id={id}
      users={usersResponse.data}
    />
  );
}
