import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getApplianceFinanceInfo } from '@/services/appliance';
import { parseDate } from '@/utils/date-utils';
import { TableHeading } from '@/types';
import {
  displayDataOrDefault,
  generateDynamicRoute,
} from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { EditButton } from '@/components/ActionButtons/EditButton/edit-button';
import { applianceRoutes } from '@/constants/routes';

interface FinanceTableProps {
  id: string;
}

export async function FinanceTable({
  id,
}: FinanceTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Order No' },
    { title: 'Supplier' },
    { title: 'Currency' },
    { title: 'Purchase Cost' },
    { title: 'Purchase Date' },
    { title: 'Purchased By' },
  ];

  const renderAction = await getActionColumnByRole('appliances', [
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;

  const applianceFinanceInfo = await getApplianceFinanceInfo(id);
  if (applianceFinanceInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {applianceFinanceInfo.data.map((finance) => (
          <TableRow key={finance.id}>
            <TableCell>{finance.orderNumber ?? '-'}</TableCell>
            <TableCell>{finance.supplier?.name ?? '-'}</TableCell>
            <TableCell>{displayDataOrDefault(finance.currency)}</TableCell>
            <TableCell>{finance.purchaseCost}</TableCell>
            <TableCell>
              {finance.purchaseDate
                ? parseDate('MMM dd, yyyy')(finance.purchaseDate)
                : '-'}
            </TableCell>
            <TableCell>{finance.purchasedBy?.name ?? '-'}</TableCell>
            {renderAction ? (
              <TableCell>
                <div className=" flex items-center gap-4">
                  <EditButton
                    href={generateDynamicRoute(applianceRoutes.EDIT_PURCHASE, {
                      purchaseId: id,
                    })}
                  />
                </div>
              </TableCell>
            ) : null}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
