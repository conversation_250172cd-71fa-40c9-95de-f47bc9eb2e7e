import React from 'react';
import { ApplianceData } from '@/types/appliance';
import { InfoItem } from '@/components/InfoItems/info-item';
import {
  CustomFieldMap,
  mapCustomFieldNamesToValues,
} from '@/utils/custom-field';

interface ApplianceInfoProps {
  /** Details about a particular appliance. */
  data: ApplianceData;
}

/**
 * This is a React component that displays detailed information about an appliance, including name, model number, location, total quantity, category, supplier, manufacturer, quantity, and notes.
 */
export async function ApplianceInfo({
  data,
}: ApplianceInfoProps): Promise<React.JSX.Element> {
  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    data.customFields?.data,
  );

  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        <InfoItem name="Appliance Name" value={data.name} />
        <InfoItem name="Model Number" value={data.modelNumber ?? '-'} />
        <InfoItem name="Location" value={data.location.name} />
        <InfoItem name="Manufacturer" value={data.manufacturer?.name ?? '-'} />
        <InfoItem name="Category" value={data.category?.name ?? '-'} />
        <InfoItem name="Quantity" value={data.totalQuantity} />
        <InfoItem name="Notes" value={data.note ?? '-'} />
        {Object.entries(customFieldMap).map(([fieldName, value]) => (
          <InfoItem key={fieldName} name={fieldName} value={value} />
        ))}
      </div>
      {data.applianceImageUrl ? (
        <div className="flex w-1/4 flex-shrink-0 justify-end">
          <img
            alt="asset-name"
            className="h-[200px] w-auto rounded object-cover shadow"
            src={data.applianceImageUrl}
            width={400}
            height={400}
          />
        </div>
      ) : null}
    </div>
  );
}
