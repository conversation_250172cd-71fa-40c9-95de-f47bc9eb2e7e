import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from 'ui';
import { getAppliance } from '@/services/appliance';
import { applianceRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { ServiceInfoTable } from './ServiceInfoTable';
import { ApplianceInfo } from './ApplianceInfo';
import { FinanceTable } from './FinanceTable/index';
import { ApplianceHistory } from './ApplianceHistory/index';
import { DeleteApplianceButton } from './delete-appliance-button';
import { ReminderTable } from '@/components/ReminderTable/reminder-table';

interface ApplianceDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function applianceDetailsPage({
  params: { id },
}: ApplianceDetailsPageProps): Promise<React.JSX.Element> {
  const applianceInfoResponse = await getAppliance(id);

  if (applianceInfoResponse.type === 'error') {
    return <>Fail to load appliance service Info</>;
  }

  const applianceInfo = applianceInfoResponse.data;

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {applianceInfo.name}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={['document', 'service', 'reminder']}
            entityId={id}
            entityName={applianceInfo.name}
            routes={applianceRoutes}
          />
          <DeleteApplianceButton id={id} name={applianceInfo.name} />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="service">Service</TabsTrigger>
            <TabsTrigger value="finance">Finance</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="document">Document</TabsTrigger>
            <TabsTrigger value="reminder">Reminder</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <ApplianceInfo data={applianceInfo} />
          </TabsContent>
          <TabsContent value="service">
            <ServiceInfoTable id={id} />
          </TabsContent>
          <TabsContent value="finance">
            <FinanceTable id={id} />
          </TabsContent>
          <TabsContent value="history">
            <ApplianceHistory id={id} />
          </TabsContent>
          <TabsContent value="document">
            <DocumentTable
              categoryType={CategoryType.APPLIANCE}
              entityType={EntityType.Appliance}
              id={id}
            />
          </TabsContent>
          <TabsContent value="reminder">
            <ReminderTable
              entityId={id}
              categoryType={CategoryType.APPLIANCE}
              entityType={EntityType.Appliance}
            />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
