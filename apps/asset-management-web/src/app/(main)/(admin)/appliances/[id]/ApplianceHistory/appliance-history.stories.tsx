import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { ApplianceHistory } from './appliance-history';

const meta: Meta<typeof ApplianceHistory> = {
  title: 'components/Appliance/ApplianceHistory',
  component: ApplianceHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ApplianceHistory>;

export const DefaultApplianceHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
