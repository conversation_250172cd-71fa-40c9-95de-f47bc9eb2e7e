import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import Link from 'next/link';
import { applianceRoutes, servicesRoutes } from '@/constants/routes';
import {
  displayDataOrDefault,
  generateDynamicRoute,
} from '@/utils/helper-functions';
import { parseDate } from '@/utils/date-utils';
import { Routes, type TableHeading } from '@/types';
import { getServiceByEntityId } from '@/services/services';
import { getActionColumnByRole } from '@/services/roles';
import { DeleteButton, EditButton } from '@/components/client';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';

interface ApplianceInfoTableProps {
  id: string;
}
export async function ServiceInfoTable({
  id,
}: ApplianceInfoTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Service Name' },
    { title: 'Service Date' },
    { title: 'Next Service Date' },
    { title: 'Contact Name' },
    { title: 'Phone Number' },
    { title: 'Cost' },
    { title: 'Supplier' },
  ];

  const renderAction = await getActionColumnByRole('appliances', [
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;

  const applianceServiceInfo = await getServiceByEntityId('APPLIANCE', id);

  if (applianceServiceInfo.type === 'error') {
    return <>Not able to load appliance serviceinfo</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading.title}>{heading.title}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {applianceServiceInfo.data.length > 0 ? (
          applianceServiceInfo.data.map((service) => (
            <TableRow key={service.id}>
              <TableCell>
                <Link
                  className="text-primary-600 hover:underline"
                  href={`${servicesRoutes.MAIN}/${service.id}`}
                >
                  {service.name}
                </Link>
              </TableCell>
              <TableCell>
                {service.date ? parseDate('MMM dd, yyyy')(service.date) : '-'}
              </TableCell>
              <TableCell>
                {service.nextServiceDate
                  ? parseDate('MMM dd, yyyy')(service.nextServiceDate)
                  : '-'}
              </TableCell>
              <TableCell className="first-letter:uppercase">
                {displayDataOrDefault(service.contactName)}
              </TableCell>
              <TableCell>
                {displayDataOrDefault(service.contactNumber)}
              </TableCell>
              <TableCell>{displayDataOrDefault(service.cost)}</TableCell>
              <TableCell>
                {displayDataOrDefault(service.supplier?.name)}
              </TableCell>
              {renderAction ? (
                <TableCell>
                  <div className=" flex items-center gap-4">
                    <EditButton
                      href={generateDynamicRoute(applianceRoutes.EDIT_SERVICE, {
                        serviceId: service.id,
                      })}
                    />
                    <DeleteEntity
                      entityId={service.id}
                      entityName={service.name}
                      entityType={Routes.SERVICES}
                    >
                      <DeleteButton isDisabled={false} />
                    </DeleteEntity>
                  </div>
                </TableCell>
              ) : null}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
