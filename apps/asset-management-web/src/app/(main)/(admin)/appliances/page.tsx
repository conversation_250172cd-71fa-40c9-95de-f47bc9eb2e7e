import {
  Selector,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import Link from 'next/link';
import { getAllAppliances } from '@/services/appliance';
import {
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { applianceRoutes } from '@/constants/routes';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import {
  EntityType,
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { getAllCategories } from '@/services/categories';
import { MultiSelectOptions } from '@/components/MultiSelectOptions/multi-select-options';
import { DownloadDocument } from '@/components/DownloadDocument/download-document';
import { getAllLocations } from '@/services/locations';

interface AppliancesSearchParams extends SearchParams {
  location?: string;
  category?: string[];
}
export default async function AppliancePage({
  searchParams,
}: {
  searchParams?: AppliancesSearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Category' },
    { title: 'Model' },
    { title: 'Location' },
  ];

  const renderAction = await getActionColumnByRole('appliances', [
    'update',
    'delete',
  ]);

  renderAction ? tableHeaders.push(renderAction) : null;
  const query = searchParams?.query || '';

  let category: string[] = [];
  if (searchParams && Array.isArray(searchParams.category)) {
    category = searchParams.category;
  } else if (searchParams && typeof searchParams.category === 'string') {
    category = [searchParams.category];
  }
  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
    category,
    location: searchParams?.location ?? '',
  };
  const appliances = await getAllAppliances(queryParams);
  const categories = await getAllCategories();
  const locations = await getAllLocations();

  if (appliances.type === 'error') {
    return <>Something Went wrong</>;
  }
  if (categories.type === 'error') {
    return <>Not able to load/get asset, please try again!</>;
  }
  if (locations.type === 'error') {
    return <>Not able to Location, please try again!</>;
  }
  const categoryList = categories.data
    ?.filter((data) => data.typeOfCategory === 'APPLIANCE')
    .map((item) => item.name);

  const totalPages = getNumberOfPages(appliances.count, searchParams?.limit);

  const locationList = locations.data.map((location) => location.name);
  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-700">Appliances</h1>
        <div className=" flex gap-3">
          <Selector
            options={locationList}
            placeholder="location"
            queryParam="location"
          />
          <div className="relative z-50">
            {categoryList && categoryList.length > 0 ? (
              <MultiSelectOptions
                options={categoryList}
                placeholder="All Category"
                id="category-selector"
                name="category"
                queryParam="category"
                selectedValues={category}
              />
            ) : null}
          </div>
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, model no, category"
          />
          <CreateButton
            href={applianceRoutes.CREATE}
            label="Create Appliance"
          />
          <DownloadDocument entityType={EntityType.Appliance} />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader className="asset-management-table-heading">
          {tableHeaders.map((heading) => {
            return (
              <TableColumn className={heading.className} key={heading.title}>
                {heading.title}
              </TableColumn>
            );
          })}
        </TableHeader>
        <TableBody>
          {appliances.data.length > 0 ? (
            appliances.data.map((appliance) => (
              <TableRow key={appliance.name}>
                <TableCell>
                  <Link
                    className="text-primary-600 hover:underline"
                    href={`${applianceRoutes.MAIN}/${appliance.id}`}
                  >
                    {appliance.name}
                  </Link>
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(appliance.category?.name)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(appliance.modelNumber)}
                </TableCell>
                <TableCell className="start-case">
                  {appliance.location.name}
                </TableCell>
                {renderAction ? (
                  <TableCell>
                    <div className="flex gap-2">
                      <EditButton
                        href={`${applianceRoutes.EDIT}/${appliance.id}`}
                      />
                      <DeleteEntity
                        entityId={appliance.id}
                        entityName={appliance.name}
                        entityType={Routes.APPLIANCE}
                      >
                        <DeleteButton isDisabled={false} />
                      </DeleteEntity>
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={appliances.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
