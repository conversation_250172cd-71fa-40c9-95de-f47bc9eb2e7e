import { getAllCategoriesOptions } from '@/services/categories';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { CreateAppliance } from './create-appliance';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getAllLocationsOptions } from '@/services/locations';

export default async function CreateappliancePage(): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('APPLIANCE');
  const suppliers = await getAllSuppliersOptions();
  const manufacturers = await getAllManufacturersOptions();
  const usersResponse = await getAllUsersOptions();
  const locations = await getAllLocationsOptions();

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }
  return (
    <CreateAppliance
      categories={categories.data}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      manufacturers={manufacturers.data}
      suppliers={suppliers.data}
      users={usersResponse.data}
      locations={locations.data}
    />
  );
}
