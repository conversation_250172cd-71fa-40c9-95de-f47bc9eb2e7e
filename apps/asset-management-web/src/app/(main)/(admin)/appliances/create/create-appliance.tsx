'use client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { ApplianceFormSchema } from '@/schemas/appliance';
import { createAppliance } from '@/services/appliance';
import type { ApplianceFormData } from '@/types/appliance';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { EntityType, type Option, type PurchaseInfo } from '@/types';
import { PurchaseInfoSchemaPartial } from '@/schemas';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { uploadFile } from '@/services/file-upload';
import { ApplianceForm } from '../ApplianceForm/index';
import type { BaseFormData } from '../types';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface CreateApplianceProps extends BaseFormData {
  users: Option[];
  suppliers: Option[];
  locations: Option[];
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}
export function CreateAppliance({
  categories,
  suppliers,
  manufacturers,
  users,
  locations,
  fieldGroups,
  fieldGroupDatas,
}: CreateApplianceProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const {
    fieldErrors: applianceFieldErrors,
    setFieldErrors: setApplianceFieldErrors,
    resetFieldErrors: resetApplianceFieldErrors,
  } = useFieldErrors<ApplianceFormData>();

  const {
    fieldErrors: purchaseInfoFieldErrors,
    setFieldErrors: setPurchaseInfoFieldErrors,
    resetFieldErrors: resetPurchaseInfoFieldErrors,
  } = useFieldErrors<PurchaseInfo>();

  const addAppliance = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );
    const {
      orderNumber,
      purchaseCost,
      currency,
      purchaseDate,
      purchasedById,
      supplierId,
      applianceImageUrl,
      ...applianceFields
    } = Object.fromEntries(formData);

    const parsedApplianceResult = ApplianceFormSchema.safeParse({
      ...applianceFields,
      applianceImageUrl: '',
      customFields: customFieldData,
    });

    const parsedPurchaseInfoResult = PurchaseInfoSchemaPartial.safeParse({
      orderNumber,
      purchaseCost,
      currency,
      quantity: '1',
      supplierId,
      purchaseDate,
      purchasedById,
    });

    if (!parsedApplianceResult.success) {
      setApplianceFieldErrors(
        parsedApplianceResult.error.flatten().fieldErrors,
      );
    } else {
      resetApplianceFieldErrors();
    }

    if (!parsedPurchaseInfoResult.success) {
      setPurchaseInfoFieldErrors(
        parsedPurchaseInfoResult.error.flatten().fieldErrors,
      );
    } else {
      resetPurchaseInfoFieldErrors();
    }

    if (!parsedApplianceResult.success || !parsedPurchaseInfoResult.success) {
      return;
    }

    let uploadedApplianceImageUrl = '';
    if (
      isImage &&
      applianceImageUrl instanceof File &&
      applianceImageUrl.size > 0
    ) {
      const newFormData = new FormData();
      newFormData.append('file', applianceImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Appliance,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      uploadedApplianceImageUrl = generateCloudFrontUrl(
        EntityType.Appliance,
        imageUploadResponse.data.fileName,
      );
    }

    const createAccessoryData = {
      ...parsedApplianceResult.data,
      applianceImageUrl: uploadedApplianceImageUrl,
    };
    const applianceResponse = await createAppliance(
      createAccessoryData,
      parsedPurchaseInfoResult.data,
    );

    if (applianceResponse.type === 'error') {
      if (applianceResponse.errors.errorMessages) {
        applianceResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong, not able to create appliance.');
      return;
    }

    toast.success('Appliance created successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Appliance</h1>
      <ApplianceForm
        applianceFieldErrors={applianceFieldErrors}
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        manufacturers={manufacturers}
        mode="create"
        onSubmit={addAppliance}
        purchaseInfoFieldErrors={purchaseInfoFieldErrors}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        users={users}
        locations={locations}
      />
    </div>
  );
}
