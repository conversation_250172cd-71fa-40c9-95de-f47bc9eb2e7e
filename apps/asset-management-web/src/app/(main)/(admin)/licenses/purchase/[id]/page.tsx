import { getAllUsersOptions } from '@/services/users';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { PurchaseLicense } from './purchase-license';

interface PurchaseLicenseProps {
  params: {
    id: string;
  };
  searchParams: {
    name: string;
  };
}

export default async function Purchase({
  params: { id },
  searchParams: { name },
}: PurchaseLicenseProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  const suppliers = await getAllSuppliersOptions();
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  return (
    <PurchaseLicense
      id={id}
      name={name}
      suppliers={suppliers.data}
      users={usersResponse.data}
    />
  );
}
