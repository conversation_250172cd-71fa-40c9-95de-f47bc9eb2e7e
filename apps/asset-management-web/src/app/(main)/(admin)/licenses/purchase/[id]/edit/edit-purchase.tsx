'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchema } from '@/schemas';
import type { Option, PurchaseInfo } from '@/types';
import { PurchaseLicenseForm } from '../../PurchaseLicenseForm';
import { updateLicensePurchaseDetails } from '@/services/licenses';

interface PurchaseLicenseProps {
  /** The unique identifier of the License. */
  id: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
  /** Initial values for purchase information. */
  initialValues: Partial<PurchaseInfo>;

  licenseId: string;
}

/**
 * Component for purchasing a license.
 * PurchaseLicenseProps represents the properties for the PurchaseAccessory component.
 * returns the rendered PurchaseLicense component.
 */
export function UpdatePurchaseLicense({
  id,
  users,
  suppliers,
  initialValues,
  licenseId,
}: PurchaseLicenseProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<PurchaseInfo>();
  const purchaseLicense = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchema.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await updateLicensePurchaseDetails(
      id,
      licenseId,
      parsedResult.data,
    );

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Failed to update Purchase License. Please try again');
      return;
    }

    toast.success('Purchase License updated successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Edit Purchase</h1>
      <PurchaseLicenseForm
        errors={fieldErrors}
        onSubmit={purchaseLicense}
        suppliers={suppliers}
        users={users}
        purchaseInfo={initialValues}
        mode="edit"
      />
    </div>
  );
}
