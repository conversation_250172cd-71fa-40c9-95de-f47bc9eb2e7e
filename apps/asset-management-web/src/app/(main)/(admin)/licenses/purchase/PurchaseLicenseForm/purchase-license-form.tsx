import { Label, Input, SubmitButton } from 'ui';
import { OptionsComboBox, PrimitiveComboBox } from '@/components/client';
import { currencies } from '@/constants';
import type { FieldErrors, Option, PurchaseInfo } from '@/types';

interface PurchaseInfoFieldsProps {
  /** Partial purchase information. */
  purchaseInfo?: Partial<PurchaseInfo>;
  /** An array of options representing users. */
  users: Option[];
  /** Callback function triggered on form submission. */
  onSubmit?: (formData: FormData) => Promise<void>;
  /** Errors associated form fields */
  errors?: FieldErrors<PurchaseInfo>;
  /** An array of options representing suppliers. */
  suppliers: Option[];
  /** Mode of the form, either 'create' for creating a new purchase or 'edit' for editing an existing purchase. */
  mode: 'create' | 'edit';
}

/**
 * PurchaseLicenseForm component: A React form for purchasing license items to users.
 * Allows selection of a user and suppliers from the provided options.
 */
export function PurchaseLicenseForm({
  purchaseInfo,
  users,
  onSubmit,
  errors,
  suppliers,
  mode,
}: PurchaseInfoFieldsProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-form space-y-7">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="orderNumber">Order Number</Label>
          <Input
            defaultValue={purchaseInfo?.orderNumber ?? ''}
            id="orderNumber"
            name="orderNumber"
            placeholder="e.g. 11440"
            type="text"
          />
          {errors?.orderNumber ? <p>{errors.orderNumber[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="quantity" required>
            Quantity
          </Label>
          <Input
            defaultValue={purchaseInfo?.quantity ?? ''}
            id="quantity"
            name="quantity"
            placeholder="e.g. 20"
            type="number"
          />
          {errors?.quantity ? <p>{errors.quantity[0]}</p> : null}
        </fieldset>
      </div>
      <fieldset>
        <Label htmlFor="supplierId">Supplier</Label>
        <OptionsComboBox
          id="supplierId"
          name="supplierId"
          options={suppliers}
          placeholder="Choose Supplier"
        />
      </fieldset>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="purchaseDate">Purchase Date</Label>
          <Input
            defaultValue={purchaseInfo?.purchaseDate ?? ''}
            id="purchaseDate"
            name="purchaseDate"
            type="date"
          />
          {errors?.purchaseDate ? <p>{errors.purchaseDate[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="purchasedById">Purchased By:</Label>
          <OptionsComboBox
            id="purchasedById"
            initialValue={purchaseInfo?.purchasedById}
            name="purchasedById"
            options={users}
            placeholder="Choose User"
          />
          {errors?.purchasedById ? <p>{errors.purchasedById[0]}</p> : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="purchaseCost">Purchase Cost</Label>
          <Input
            defaultValue={purchaseInfo?.purchaseCost ?? ''}
            id="purchaseCost"
            name="purchaseCost"
            placeholder="e.g. 2999"
            type="number"
          />
          {errors?.purchaseCost ? <p>{errors.purchaseCost[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="currency">Currency</Label>
          <PrimitiveComboBox
            id="currency"
            initialValue={purchaseInfo?.currency}
            name="currency"
            placeholder="Choose currency"
            values={currencies}
          />
          {errors?.currency ? <p>{errors.currency[0]}</p> : null}
        </fieldset>
      </div>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
