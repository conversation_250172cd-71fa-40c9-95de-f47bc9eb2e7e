'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchema } from '@/schemas';
import type { Option, PurchaseInfo } from '@/types';
import { createPurchaseLicense } from '@/services/licenses';
import { PurchaseLicenseForm } from '../PurchaseLicenseForm';

interface PurchaseLicenseProps {
  /** The unique identifier of the license. */
  id: string;
  /** Name of the License */
  name: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
}

/**
 * Component for purchasing a license.
 * PurchaseLicenseProps represents the properties for the PurchaseLicense component.
 * returns the rendered PurchaseLicense component.
 */
export function PurchaseLicense({
  id,
  name,
  users,
  suppliers,
}: PurchaseLicenseProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<PurchaseInfo>();
  const purchaseLicense = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);

    const parsedResult = PurchaseInfoSchema.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await createPurchaseLicense(id, parsedResult.data);

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong');
      return;
    }

    toast.success('Purchase License created successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Purchase {name}</h1>
      <PurchaseLicenseForm
        errors={fieldErrors}
        onSubmit={purchaseLicense}
        suppliers={suppliers}
        users={users}
        mode="create"
      />
    </div>
  );
}
