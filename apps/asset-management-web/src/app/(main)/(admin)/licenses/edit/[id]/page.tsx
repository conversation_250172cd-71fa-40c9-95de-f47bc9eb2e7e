import { format, parseISO } from 'date-fns';
import { getAllCategoriesOptions } from '@/services/categories';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { getAllManufacturersOptions } from '@/services/manufacturers';
import { getLicense } from '@/services/licenses';
import type { LicenseFormData } from '@/types/licenses';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { EditLicense } from './edit-license';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditLicensePage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const categories = await getAllCategoriesOptions('SOFTWARE_LICENSE');
  const suppliers = await getAllSuppliersOptions();
  const manufacturers = await getAllManufacturersOptions();
  const licenseInfo = await getLicense(id);

  if (licenseInfo.type === 'error') {
    return <>Id not found</>;
  }

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (manufacturers.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  const { manufacturer, category, expiryDate, termination, ...rest } =
    licenseInfo.data;

  const intitialValues: LicenseFormData = {
    ...rest,
    categoryId: category?.id ?? undefined,
    manufacturerId: manufacturer?.id ?? undefined,
    expiryDate: format(parseISO(expiryDate), 'yyyy-MM-dd'),
    termination: termination
      ? format(parseISO(termination), 'yyyy-MM-dd')
      : null,
    customFields: rest.customFields ? rest.customFields : {},
  };

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  if (categories.type === 'error') {
    return <p>Something went wrong while fetching categories</p>;
  }

  return (
    <EditLicense
      categories={categories.data}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      initialValues={intitialValues}
      manufacturers={manufacturers.data}
      mode="edit"
    />
  );
}
