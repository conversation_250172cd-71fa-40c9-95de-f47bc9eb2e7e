'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatISO, parseISO } from 'date-fns';
import { toast } from 'sonner';
import type { LicenseFormData } from '@/types/licenses';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { updateLicense } from '@/services/licenses';
import { LicenseFormSchema } from '@/schemas/licenses';
import type { Option } from '@/types';
import type { FieldGroupDataType } from '@/types/custom-field';
import type { SelectedData } from '@/utils/custom-field';
import {
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';
import { LicenseForm } from '../../LicensesForm';
import type { BaseFormData } from '../../types';

interface EditLicenseProps extends BaseFormData {
  /** The unique identifier of the license to be updated. */
  id: string;
  /** Initial value for the form fields. */
  initialValues: LicenseFormData;
  /**
   * An array of options representing field groups.
   */
  fieldGroups: Option[] | undefined;

  /**
   * An array of field group data.
   */
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
  mode: 'edit' | 'renew';
}

/**
 * A React component for updating an existing software license.
 * The formData containing the license details to be updated.
 * returns a promise that resolves after the license is successfully updated.
 */
export function EditLicense({
  categories,
  manufacturers,
  initialValues,
  id,
  fieldGroups,
  fieldGroupDatas,
  mode,
}: EditLicenseProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<LicenseFormData>();

  const editLicense = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    const licenseData = Object.fromEntries(formData);
    const {
      licenseHolderEmail,
      expiryDate,
      termination,
      maintenanceRequired,
      ...licenseDetails
    } = licenseData;

    const parsedResult = LicenseFormSchema.safeParse({
      ...licenseDetails,
      maintenanceRequired: maintenanceRequired === 'on',
      ...(licenseHolderEmail ? { licenseHolderEmail } : {}),
      ...(expiryDate && typeof expiryDate === 'string'
        ? {
            expiryDate: formatISO(parseISO(expiryDate)),
          }
        : {}),
      ...(termination && typeof termination === 'string'
        ? {
            termination: formatISO(parseISO(termination)),
          }
        : {}),
      customFields: customFieldData,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const licenseResponse = await updateLicense(id, parsedResult.data, mode);

    if (licenseResponse.type === 'error') {
      if (licenseResponse.errors.errorMessages) {
        licenseResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('something went wrong');
      return;
    }

    if (mode === 'renew') {
      toast.success('License renewed successfully');
    } else {
      toast.success('License updated successfully');
    }
    router.back();
  };

  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">
        {mode === 'renew' ? 'Renew' : 'Edit'} License
      </h1>
      <LicenseForm
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        initialLicenseInfo={initialValues}
        licenseFieldErrors={fieldErrors}
        manufacturers={manufacturers}
        mode={mode}
        onSubmit={editLicense}
        setSelectedData={setSelectedData}
      />
    </div>
  );
}
