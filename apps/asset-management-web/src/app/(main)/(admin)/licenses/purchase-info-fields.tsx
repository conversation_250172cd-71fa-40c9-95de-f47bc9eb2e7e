import { Label, Input } from 'ui';
import { OptionsComboBox, PrimitiveComboBox } from '@/components/client';
import { currencies } from '@/constants';
import type { FieldErrors, Option, PurchaseInfo } from '@/types';

interface PurchaseInfoFieldsProps {
  initialPurchaseInfo?: Partial<PurchaseInfo>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfo>;
  suppliers: Option[];
  users: Option[];
}

export function PurchaseInfoFields({
  suppliers,
  users,
  initialPurchaseInfo,
  purchaseInfoFieldErrors,
}: PurchaseInfoFieldsProps): React.JSX.Element {
  return (
    <div className="space-y-3">
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="orderNumber">Order Number</Label>
          <Input
            defaultValue={initialPurchaseInfo?.orderNumber ?? ''}
            id="orderNumber"
            name="orderNumber"
            placeholder="e.g. 11440"
            type="text"
          />
          {purchaseInfoFieldErrors?.orderNumber ? (
            <p>{purchaseInfoFieldErrors.orderNumber[0]}</p>
          ) : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="quantity" required>
            Quantity
          </Label>
          <Input
            defaultValue={initialPurchaseInfo?.quantity ?? ''}
            id="quantity"
            isInvalidInput={Boolean(purchaseInfoFieldErrors?.quantity)}
            name="quantity"
            placeholder="e.g. 20"
            type="number"
          />
          {purchaseInfoFieldErrors?.quantity ? (
            <p>{purchaseInfoFieldErrors.quantity[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <fieldset>
        <Label htmlFor="supplierId">Supplier</Label>
        <OptionsComboBox
          id="supplierId"
          initialValue={initialPurchaseInfo?.supplierId}
          name="supplierId"
          options={suppliers}
          placeholder="Choose supplier"
        />
      </fieldset>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="purchaseDate">
            Purchase Date
          </Label>
          <Input
            defaultValue={initialPurchaseInfo?.purchaseDate ?? ''}
            id="purchaseDate"
            name="purchaseDate"
            type="date"
          />
          {purchaseInfoFieldErrors?.purchaseDate ? (
            <p>{purchaseInfoFieldErrors.purchaseDate[0]}</p>
          ) : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="purchasedById">
            Purchased By
          </Label>
          <OptionsComboBox
            id="purchasedById"
            initialValue={initialPurchaseInfo?.purchasedById}
            name="purchasedById"
            options={users}
            placeholder="Choose user"
          />
          {purchaseInfoFieldErrors?.purchasedById ? (
            <p>{purchaseInfoFieldErrors.purchasedById[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <fieldset>
          <Label htmlFor="purchaseCost">Purchase Cost</Label>
          <Input
            defaultValue={initialPurchaseInfo?.purchaseCost ?? ''}
            id="purchaseCost"
            name="purchaseCost"
            placeholder="e.g. 2999"
            type="number"
          />
          {purchaseInfoFieldErrors?.purchaseCost ? (
            <p>{purchaseInfoFieldErrors.purchaseCost[0]}</p>
          ) : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="currency">
            Currency
          </Label>
          <PrimitiveComboBox
            id="currency"
            initialValue={initialPurchaseInfo?.currency}
            name="currency"
            placeholder="Choose currency"
            values={currencies}
          />
          {purchaseInfoFieldErrors?.currency ? (
            <p>{purchaseInfoFieldErrors.currency[0]}</p>
          ) : null}
        </fieldset>
      </div>
    </div>
  );
}
