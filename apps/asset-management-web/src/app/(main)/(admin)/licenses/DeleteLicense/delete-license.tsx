'use client';
import React from 'react';
import { toast } from 'sonner';
import {
  <PERSON><PERSON>T<PERSON>ger,
  <PERSON>dal,
  Dialog,
  ModalHeading,
  ModalOverlay,
  Button,
} from 'ui';
import { deleteLicense } from '@/services/licenses';

interface DeleteLicenseProps {
  /** The unique identifier of the license to be deleted. */
  licenseId: string;

  /** The name of the license to be deleted. */
  licenseName: string;

  /** The element that triggers the Delete Modal. */
  children: React.ReactNode;

  /** A callback function triggered after the license is successfully deleted. */
  onConfirmDelete?: () => void;
}

type CloseHandler = () => void;

/**
 * This is a React component that displays a modal triggered by the delete button, providing a confirmation prompt for deleting a license.
 */
export function DeleteLicense({
  licenseId,
  licenseName,
  children,
  onConfirmDelete,
}: DeleteLicenseProps): React.JSX.Element {
  const handleDelete = async (close: CloseHandler): Promise<void> => {
    const licenseDeleteResponse = await deleteLicense(licenseId);

    if (licenseDeleteResponse.type === 'error') {
      if (licenseDeleteResponse.errors.errorMessages) {
        licenseDeleteResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(
        <>
          Failed to delete <span className="font-medium">{licenseName}</span>.
          Please try again.
        </>,
      );
      return;
    }

    toast.success(
      <>
        Deleted <span className="font-medium">{licenseName}</span> successfully
      </>,
    );

    if (onConfirmDelete) onConfirmDelete();
    close();
  };

  return (
    <DialogTrigger>
      {children}
      <ModalOverlay>
        <Modal className="delete-modal w-1/3">
          <Dialog>
            {({ close }: { close: CloseHandler }) => (
              <>
                <ModalHeading slot="title">Delete License</ModalHeading>
                <p>
                  Are you sure you want to delete{' '}
                  <span className="font-medium">{licenseName}</span>?
                </p>
                <div className="delete-modal-footer">
                  <Button onPress={close} variant="outlined">
                    Close
                  </Button>
                  <Button
                    intent="danger"
                    onPress={() => {
                      void handleDelete(close);
                    }}
                  >
                    Delete
                  </Button>
                </div>
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
}
