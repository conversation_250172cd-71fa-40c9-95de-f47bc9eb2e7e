import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { DeleteButton } from '@/components/client';
import { DeleteLicense } from './delete-license';

const meta: Meta<typeof DeleteLicense> = {
  title: 'components/Licenses/DeleteLicense',
  component: DeleteLicense,
};

export default meta;

type Story = StoryObj<typeof DeleteLicense>;

export const DeleteDialogTrigger: Story = {
  render: () => (
    <DeleteLicense licenseId="1" licenseName="Figma">
      <DeleteButton />
    </DeleteLicense>
  ),
};
