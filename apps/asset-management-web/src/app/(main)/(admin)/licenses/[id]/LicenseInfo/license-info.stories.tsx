import type { Meta, StoryObj } from '@storybook/react';
import { LicenseInfo } from './license-info';

const meta: Meta<typeof LicenseInfo> = {
  title: 'components/Licenses/LicenseInfo',
  component: LicenseInfo,
};

export default meta;

type Story = StoryObj<typeof LicenseInfo>;

export const DefaultLicenseInfo: Story = {
  args: {
    data: {
      id: '6ea45197-c3cd-4434-b853-db3de8b86322',
      name: '<PERSON><PERSON><PERSON>tor',
      productKey: '114cf6f5-bb22-3081-b25b-e2ddb9db5b03',
      licenseHolderEmail: '<EMAIL>',
      licenseHolderName: '<PERSON>',
      manufacturer: {
        id: 'hcd23f25-d451-4f98-8bf0-d23c7eaaaf7b',
        name: 'Adobe',
      },
      category: {
        id: 'abcd3f25-d771-48j8-8bf0-d22c7dfg8f7b',
        name: 'Design Software',
      },
      expiryDate: '2025-01-13T14:32:02.773Z',
      termination: '2026-01-13T14:32:02.773Z',
      maintenanceRequired: false,
      reassignable: true,
      totalQuantity: 25,
      availableQuantity: 20,
      note: 'This license is for version 2.0 and includes support for one year.',
    },
  },
};
