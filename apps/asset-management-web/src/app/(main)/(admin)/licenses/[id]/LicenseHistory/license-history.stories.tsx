import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { LicenseHistory } from './license-history';

const meta: Meta<typeof LicenseHistory> = {
  title: 'components/Licenses/LicenseHistory',
  component: LicenseHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof LicenseHistory>;

export const DefaultLicenseHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
    name: 'A4 Sheets',
  },
};
