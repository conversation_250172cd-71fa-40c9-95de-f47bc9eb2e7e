import React from 'react';
import { parseDate } from '@/utils/date-utils';
import type { LicenseDetails } from '@/types/licenses';
import { InfoItem } from '@/components/InfoItems/info-item';
import {
  CustomFieldMap,
  mapCustomFieldNamesToValues,
} from '@/utils/custom-field';

interface LicenseInfoProps {
  /** Details about a particular license. */
  data: LicenseDetails;
}

/**
 * This is a React component that displays detailed information about a software license, including product key, licensed to name and email, manufacturer, supplier, category, expiration and termination dates, maintainability, reassignability, quantity, and notes.
 */
export async function LicenseInfo({
  data,
}: LicenseInfoProps): Promise<React.JSX.Element> {
  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    data.customFields?.data,
  );

  return (
    <div className="space-y-5 px-6 pb-6 pt-4 text-xs text-slate-600 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
      <InfoItem name="Product Key" value={data.productKey} />
      <InfoItem name="Licensee" value={data.licenseHolderName} />
      <InfoItem name="Licensee Email" value={data.licenseHolderEmail} />
      <InfoItem name="Manufacturer" value={data.manufacturer?.name} />
      <InfoItem name="Category" value={data.category?.name} />
      <InfoItem
        name="Expiration"
        value={
          data.expiryDate ? parseDate('MMM dd, yyyy')(data.expiryDate) : null
        }
      />
      <InfoItem
        name="Termination"
        value={
          data.termination ? parseDate('MMM dd, yyyy')(data.termination) : null
        }
      />
      <InfoItem name="Renewable" value={data.maintenanceRequired} />
      <InfoItem name="Quantity" value={data.totalQuantity} />
      <InfoItem name="Notes" value={data.note} />
      {Object.entries(customFieldMap).map(([fieldName, value]) => (
        <InfoItem key={fieldName} name={fieldName} value={value} />
      ))}
    </div>
  );
}
