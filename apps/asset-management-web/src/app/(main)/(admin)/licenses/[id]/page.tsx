import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from 'ui';
import { getLicense } from '@/services/licenses';
import { licensesRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { ReminderTable } from '@/components/ReminderTable/reminder-table';
import { LicenseInfo } from './LicenseInfo';
import { AssignmentsTable } from './AssignmentsTable';
import { DeleteLicenseButton } from './DeleteLicenseButton';
import { FinanceTable } from './FinanceTable';
import { LicenseHistory } from './LicenseHistory';
import { RenewHistory } from './RenewHistory/renew-history';

interface LicenseDetailsPageProps {
  /** The unique identifier of the license. */
  params: { id: string };
}

/**
 * This page displays detailed information about a specific license, including tabs for sections like Info, Assignments, Finance, and History.
 * It also provides buttons for assigning, unassigning, editing, and deleting the license.
 */
export default async function LicenseDetailsPage({
  params: { id },
}: LicenseDetailsPageProps): Promise<React.JSX.Element> {
  const licenseResponse = await getLicense(id);
  if (licenseResponse.type === 'error') {
    return <>Something went wrong</>;
  }

  const licenseInfo = licenseResponse.data;

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {licenseInfo.name}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={['purchase', 'assign', 'document', 'reminder', 'renew']}
            entityId={id}
            entityName={licenseInfo.name}
            routes={licensesRoutes}
          />
          <DeleteLicenseButton
            isDisabled={
              licenseInfo.availableQuantity !== licenseInfo.totalQuantity
            }
            licenseId={id}
            licenseName={licenseInfo.name}
          />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="assignments">Assignments</TabsTrigger>
            <TabsTrigger value="finance">Finance</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="document">Document</TabsTrigger>
            <TabsTrigger value="reminder">Reminder</TabsTrigger>
            <TabsTrigger value="renewHistory">Renew History</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <LicenseInfo data={licenseInfo} />
          </TabsContent>
          <TabsContent value="assignments">
            <AssignmentsTable id={id} name={licenseInfo.name} />
          </TabsContent>
          <TabsContent value="finance">
            <FinanceTable id={id} />
          </TabsContent>
          <TabsContent value="history">
            <LicenseHistory id={id} name={licenseInfo.name} />
          </TabsContent>
          <TabsContent value="document">
            <DocumentTable
              categoryType={CategoryType.SOFTWARE}
              entityType={EntityType.Liscence}
              id={id}
            />
          </TabsContent>
          <TabsContent value="reminder">
            <ReminderTable
              entityId={id}
              entityType={EntityType.Liscence}
              categoryType={CategoryType.SOFTWARE}
            />
          </TabsContent>
          <TabsContent value="renewHistory">
            {/* TODO: to be changed */}
            <RenewHistory licenseId={id} />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
