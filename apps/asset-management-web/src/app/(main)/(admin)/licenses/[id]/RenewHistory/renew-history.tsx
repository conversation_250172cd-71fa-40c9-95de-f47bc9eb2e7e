import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import type { TableHeading } from '@/types';
import { getLicense, getLicenseHistoryInfo } from '@/services/licenses';
import type { LicenseDetails } from '@/types/licenses';
import { displayDataOrDefault } from '@/utils/helper-functions';

export async function RenewHistory({
  licenseId,
}: {
  licenseId: string;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Product Key' },
    { title: 'Expiration' },
    { title: 'Licensee' },
    { title: 'Manufacturer' },
    { title: 'Total', className: 'text-right' },
    { title: 'Available', className: 'text-right' },
  ];

  const licenseHistoryInfo = await getLicenseHistoryInfo(licenseId);
  if (licenseHistoryInfo.type === 'error') {
    return <>Something went wrong!</>;
  }

  const licenseInfo = await getLicense(licenseId);
  if (licenseInfo.type === 'error') {
    return <>Something went wrong!</>;
  }

  const licenseCreateData: LicenseDetails = licenseInfo.data;

  const renewedFieldsList = licenseHistoryInfo.data
    .filter((item) => {
      return item.action === 'RENEWED';
    })
    .map((item) => item.log.updatedFields);

  const renewedHistoryList: LicenseDetails[] = [];
  const renewedData = licenseCreateData;

  renewedFieldsList.forEach((renewedEntries) => {
    renewedEntries?.forEach((entry) => {
      renewedData[entry.fieldUpdated] = entry.from;
    });
    renewedHistoryList.push(structuredClone(renewedData));
  });

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader>
        {tableHeaders.map((header) => (
          <TableColumn
            className={`last:text-center ${header.className}`}
            key={header.title}
          >
            {header.title}
          </TableColumn>
        ))}
      </TableHeader>
      <TableBody>
        {renewedHistoryList.length > 0 ? (
          renewedHistoryList.map((license) => (
            <TableRow key={license.id}>
              <TableCell className="first-letter:uppercase">
                {license.name}
              </TableCell>
              <TableCell className="max-w-[200px] truncate">
                <span title={license.productKey}>{license.productKey}</span>
              </TableCell>
              <TableCell>
                {license.expiryDate
                  ? parseDate('MMM dd, yyyy')(license.expiryDate)
                  : '-'}
              </TableCell>
              <TableCell>
                {displayDataOrDefault(license.licenseHolderName)}
              </TableCell>
              <TableCell>
                {displayDataOrDefault(license.manufacturer?.name)}
              </TableCell>
              <TableCell className="text-right">
                {license.totalQuantity}
              </TableCell>
              <TableCell className="text-right">
                {license.availableQuantity}
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
