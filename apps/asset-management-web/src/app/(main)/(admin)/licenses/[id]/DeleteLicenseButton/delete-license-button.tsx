'use client';

import { useRouter } from 'next/navigation';
import { licensesRoutes } from '@/constants/routes';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { OutlinedDeleteButton } from '@/components/client';
import { Routes } from '@/types';

interface DeleteLicenseButtonProps {
  /** The unique identifier of the license to be deleted. */
  licenseId: string;

  /** The name of the license to be deleted. */
  licenseName: string;

  /** Indicates whether the button is disabled. */
  isDisabled?: boolean;
}

/**
 * This is a React component that represents a button for deleting a license. It triggers a confirmation modal, leading to deletion and navigation to the main licenses route upon confirmation.
 */
export function DeleteLicenseButton({
  licenseId,
  licenseName,
  isDisabled,
}: DeleteLicenseButtonProps): React.JSX.Element {
  const router = useRouter();

  return (
    <DeleteEntity
      entityId={licenseId}
      entityName={licenseName}
      entityType={Routes.SOFTWARE}
      onDelete={() => {
        router.push(licensesRoutes.MAIN);
      }}
    >
      <OutlinedDeleteButton isDisabled={isDisabled} />
    </DeleteEntity>
  );
}
