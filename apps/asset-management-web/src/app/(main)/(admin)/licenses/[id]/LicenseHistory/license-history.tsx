import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionTrigger,
  AccordionItem,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getLicenseHistoryInfo } from '@/services/licenses';
import { parseDate } from '@/utils/date-utils';
import { generateUpdateText } from '@/utils/helper-functions';

const tableHeaders = ['Actions Performed', 'Date', 'Action By'];
interface LicenseHistoryProps {
  id: string;
  name: string;
}

export async function LicenseHistory({
  id,
  name,
}: LicenseHistoryProps): Promise<React.JSX.Element> {
  const licenseHistoryInfo = await getLicenseHistoryInfo(id);

  if (licenseHistoryInfo.type === 'error') {
    return <>Something went wrong</>;
  }

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {licenseHistoryInfo.data.map((history) => (
          <TableRow key={history.log.userId}>
            <TableCell>
              {(() => {
                switch (history.action) {
                  case 'CREATED':
                    return (
                      <p>
                        {history.changeInTable === 'SOFTWARE_LICENSE' ? (
                          <>
                            License
                            <span className="font-semibold"> created</span>
                          </>
                        ) : (
                          <>
                            Purchase record
                            <span className="font-semibold"> created</span>
                          </>
                        )}
                      </p>
                    );
                  case 'UPDATED':
                    return history.log.updatedFields &&
                      history.log.updatedFields.length > 0 ? (
                      <Accordion className="w-72">
                        <AccordionItem
                          className="text-xs"
                          value={`item-${history.log.userId}`}
                        >
                          <AccordionTrigger>
                            <p>
                              <span className="font-semibold">Updated</span>(
                              {history.log.updatedFields.length} fields)
                            </p>
                          </AccordionTrigger>
                          <AccordionContent className="space-y-1">
                            {history.log.updatedFields.map(generateUpdateText)}
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <p>No fields updated</p>
                    );
                  case 'ASSIGNED':
                    return (
                      <p>
                        {name} <span className="font-semibold">assigned</span>{' '}
                        to{' '}
                        <span>
                          {history.log.assignmentDetails?.assignedTo ?? 'Asset'}
                        </span>
                      </p>
                    );
                  case 'UNASSIGNED':
                    return (
                      <p>
                        {name} <span className="font-semibold">unassigned</span>{' '}
                        from{' '}
                        <span>
                          {history.log.assignmentDetails?.assignedTo ?? 'Asset'}
                        </span>
                      </p>
                    );
                  case 'RENEWED':
                    return (
                      <p>
                        License <span className="font-semibold">renewed</span>
                      </p>
                    );
                  default:
                    return null;
                }
              })()}
            </TableCell>
            <TableCell>{parseDate('MMM dd, yyyy')(history.date)}</TableCell>
            <TableCell>{history.log.name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
