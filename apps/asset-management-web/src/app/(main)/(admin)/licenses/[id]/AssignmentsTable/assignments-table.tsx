import React from 'react';
import {
  <PERSON><PERSON>,
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { getLicenseAssignments } from '@/services/licenses';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { UnassignLicense } from '../../UnassignLicense';

const tableHeaders = ['User', 'Asset', 'Notes', 'Assigned Date', 'Actions'];

interface AssignmentsTableProps {
  /** The unique identifier of the license. */
  id: string;

  /** The name of the license. */
  name: string;
}

/**
 * This is a React component that displays a table of software license assignments for each user.
 */
export async function AssignmentsTable({
  id,
  name,
}: AssignmentsTableProps): Promise<React.JSX.Element> {
  const licenseAssignments = await getLicenseAssignments(id);

  if (licenseAssignments.type === 'error') {
    return <>Something went wrong</>;
  }

  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  const assignments = licenseAssignments.data;

  return (
    <Table className="asset-management-table-md rounded-none px-5 pb-4 pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => (
          <TableColumn key={heading}>{heading}</TableColumn>
        ))}
      </TableHeader>
      <TableBody>
        {assignments.length > 0 ? (
          assignments.map((assignment) => (
            <TableRow key={assignment.id}>
              <TableCell>
                {displayDataOrDefault(assignment.user?.name)}
              </TableCell>
              <TableCell>
                {displayDataOrDefault(assignment.asset?.assetName)}
              </TableCell>
              <TableCell className="max-w-[200px]">
                <HoverCard>
                  <HoverCardTrigger className="line-clamp-2">
                    {displayDataOrDefault(assignment.note)}
                  </HoverCardTrigger>
                  {assignment.note ? (
                    <HoverCardContent>{assignment.note}</HoverCardContent>
                  ) : null}
                </HoverCard>
              </TableCell>
              <TableCell>{assignment.date}</TableCell>
              <TableCell>
                <UnassignLicense
                  assignment={assignment}
                  licenseId={assignment.id}
                  licenseName={name}
                  userName={assignment.user?.name ?? ''}
                  users={usersResponse.data}
                >
                  <Button className="pressed:bg-pink-700 bg-pink-600 px-4 py-1 text-xs hover:bg-pink-500  focus-visible:bg-pink-500 focus-visible:ring-pink-500 active:bg-pink-700">
                    Unassign
                  </Button>
                </UnassignLicense>
              </TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No assignments yet
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
