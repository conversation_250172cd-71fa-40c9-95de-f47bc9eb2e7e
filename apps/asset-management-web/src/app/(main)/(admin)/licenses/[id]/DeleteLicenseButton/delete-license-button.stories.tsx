import type { <PERSON>a, StoryObj } from '@storybook/react';
import { DeleteLicenseButton } from './delete-license-button';

const meta: Meta<typeof DeleteLicenseButton> = {
  title: 'components/Licenses/DeleteLicenseButton',
  component: DeleteLicenseButton,
};

export default meta;

type Story = StoryObj<typeof DeleteLicenseButton>;

export const DeleteDialogTrigger: Story = {
  args: { licenseId: '1', licenseName: 'Figma' },
};
