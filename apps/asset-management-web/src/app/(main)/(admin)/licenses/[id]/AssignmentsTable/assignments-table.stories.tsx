import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AssignmentsTable } from './assignments-table';

const meta: Meta<typeof AssignmentsTable> = {
  title: 'components/Licenses/AssignmentsTable',
  component: AssignmentsTable,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AssignmentsTable>;

export const DefaultAssignmentsTable: Story = {
  args: { id: '1', name: 'Figma' },
};
