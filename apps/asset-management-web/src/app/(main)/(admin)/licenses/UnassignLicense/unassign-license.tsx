'use client';
import React from 'react';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import { unassignLicense } from '@/services/licenses';
import { unAssignmentFormSchema } from '@/schemas';
import { UnAssignEntityForm } from '@/components/UnAssignEntityForm/un-assign-entity-form';
import type { AssignmentDataType } from '@/types/assets';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, INPUT_VALUES_ERROR_MESSAGE } from '@/constants';

interface UnassignLicenseProps {
  /** The unique identifier of the license to be unassigned. */
  licenseId: string;

  /** The name of the license to be unassigned. */
  licenseName: string;

  /** The user from whom the license will be unassigned. */
  userName: string;

  /** The element that triggers the Unassign Modal. */
  children: React.ReactNode;

  /** A callback function triggered after the license is successfully unassigned. */
  onConfirm?: () => void;

  /** The data type representing the assignment details */
  assignment: AssignmentDataType;

  /** An array of options representing the possible users */
  users: Option[];
}

/**
 * This is a React component that displays a modal triggered by the unassign button, providing a confirmation prompt for unassigning a license.
 */
export function UnassignLicense({
  licenseId,
  licenseName,
  userName,
  users,
  assignment,
}: UnassignLicenseProps): React.JSX.Element {
  /**
   * Handles the unassignment of the accessory.
   */
  const handleUnassignLicense = async (formData: FormData): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const unAssignDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };
    const parsedResult = unAssignmentFormSchema.safeParse(unAssignDetails);

    if (!parsedResult.success) {
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    const unassignResponse = await unassignLicense(
      licenseId,
      parsedResult.data,
    );

    if (unassignResponse.type === 'error') {
      if (unassignResponse.errors.errorMessages) {
        unassignResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }
    toast.success(`${licenseName} unassigned successfully from ${userName}`);
  };

  return (
    <UnAssignEntityForm
      assignment={assignment}
      entityName={licenseName}
      entityType="License"
      handleSubmit={handleUnassignLicense}
      userName={userName}
      users={users}
    />
  );
}
