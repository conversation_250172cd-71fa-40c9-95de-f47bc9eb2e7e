import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from 'ui';
import { UnassignLicense } from './unassign-license';

const meta: Meta<typeof UnassignLicense> = {
  title: 'components/Licenses/UnassignLicense',
  component: UnassignLicense,
};

export default meta;

type Story = StoryObj<typeof UnassignLicense>;

export const UnassignDialogTrigger: Story = {
  render: () => (
    <UnassignLicense
      assignment={{
        id: 'abc123',
        typeOfAssignment: 'Accessory',
        date: '2024-05-07',
        user: {
          id: 'xyz789',
          name: '<PERSON>',
        },
        entityId: 'def456',
        isPending: false,
        note: 'Accessory unassiged.',
      }}
      licenseId="1"
      licenseName="Figma"
      userName="User-1"
      users={[
        {
          displayName: 'Josh',
          value: '14526',
        },
        {
          displayName: 'Ariana',
          value: '56359',
        },
        {
          displayName: 'Kylie',
          value: '1459',
        },
      ]}
    >
      <Button className="pressed:bg-pink-700 bg-pink-600 px-4 py-1 text-xs hover:bg-pink-500  focus-visible:bg-pink-500 focus-visible:ring-pink-500 active:bg-pink-700 disabled:bg-pink-400">
        Unassign
      </Button>
    </UnassignLicense>
  ),
};
