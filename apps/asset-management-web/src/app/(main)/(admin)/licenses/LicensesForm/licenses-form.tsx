import React from 'react';
import { Label, Textarea, Input, Checkbox, SubmitButton } from 'ui';
import type { LicenseFormData } from '@/types/licenses';
import type { FieldErrors, PurchaseInfo, Option } from '@/types';
import { requiredFieldErrorMessage } from '@/constants';
import { OptionsComboBox } from '@/components/client';
import type { CustomField } from '@/utils/custom-field';
import { renderCustomFieldGroups } from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { PurchaseInfoFields } from '../purchase-info-fields';

interface CreateLicenseFormProps extends BaseFormData {
  mode: 'create';
  initialLicenseInfo?: Partial<LicenseFormData>;
  initialPurchaseInfo?: Partial<PurchaseInfo>;
  licenseFieldErrors?: FieldErrors<LicenseFormData>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfo>;
  onSubmit?: (formData: FormData) => Promise<void>;
  users: Option[];
  suppliers: Option[];
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditLicenseFormProps extends BaseFormData {
  mode: 'edit' | 'renew';
  initialLicenseInfo: LicenseFormData;
  licenseFieldErrors?: FieldErrors<LicenseFormData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}
/**
 * LicensesForm component: A React form for creating and editing license items.
 * Captures information such as name, category, purchase details, and additional notes.
 */
export function LicenseForm(
  props: CreateLicenseFormProps | EditLicenseFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    setSelectedData,
    manufacturers,
    categories,
    mode,
    licenseFieldErrors,
    initialLicenseInfo,
    fieldGroups,
    customFields,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialLicenseInfo,
    setSelectedData,
    customFields,
  );

  return (
    <form action={onSubmit} className="asset-management-form">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="name" required>
                Name
              </Label>
              <Input
                defaultValue={initialLicenseInfo?.name ?? ''}
                id="name"
                isInvalidInput={Boolean(licenseFieldErrors?.name)}
                name="name"
                placeholder="e.g. Adobe Illustrator"
                type="text"
              />
              {licenseFieldErrors?.name ? (
                <p>{licenseFieldErrors.name[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="categoryId">Category</Label>
              <OptionsComboBox
                id="categoryId"
                initialValue={initialLicenseInfo?.categoryId}
                name="categoryId"
                options={categories}
                placeholder="Choose category"
              />
            </fieldset>
          </div>
          <fieldset>
            <Label htmlFor="manufacturerId">Manufacturer</Label>
            <OptionsComboBox
              id="manufacturerId"
              initialValue={initialLicenseInfo?.manufacturerId}
              name="manufacturerId"
              options={manufacturers}
              placeholder="Choose manufacturer"
            />
          </fieldset>
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label className="w-1/3" htmlFor="licenseHolderName">
                Licensee
              </Label>
              <Input
                defaultValue={initialLicenseInfo?.licenseHolderName ?? ''}
                id="licenseHolderName"
                name="licenseHolderName"
                placeholder="e.g. Jane Doe"
              />
              {licenseFieldErrors?.licenseHolderName ? (
                <p>{licenseFieldErrors.licenseHolderName}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label className="w-1/3" htmlFor="licenseHolderEmail">
                Licensee Email
              </Label>
              <Input
                defaultValue={initialLicenseInfo?.licenseHolderEmail ?? ''}
                id="licenseHolderEmail"
                name="licenseHolderEmail"
                placeholder="e.g. <EMAIL>"
              />
              {licenseFieldErrors?.licenseHolderEmail ? (
                <p>{licenseFieldErrors.licenseHolderEmail}</p>
              ) : null}
            </fieldset>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <fieldset>
              <Label htmlFor="expiryDate" required>
                Expiration
              </Label>
              <Input
                defaultValue={initialLicenseInfo?.expiryDate ?? ''}
                id="expiryDate"
                isInvalidInput={Boolean(licenseFieldErrors?.expiryDate)}
                name="expiryDate"
                type="date"
              />
              {licenseFieldErrors?.expiryDate ? (
                <p>{requiredFieldErrorMessage}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="termination">Termination</Label>
              <Input
                defaultValue={initialLicenseInfo?.termination ?? ''}
                id="termination"
                name="termination"
                type="date"
              />
            </fieldset>
          </div>
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label className="w-1/3" htmlFor="productKey" required>
                Product Key
              </Label>
              <Input
                defaultValue={initialLicenseInfo?.productKey ?? ''}
                id="productKey"
                isInvalidInput={Boolean(licenseFieldErrors?.productKey)}
                name="productKey"
                placeholder="e.g. f83bafe8-d62a-1b0ca602c"
              />
              {licenseFieldErrors?.productKey ? (
                <p>{licenseFieldErrors.productKey}</p>
              ) : null}
            </fieldset>
            <div className="mt-8 flex gap-4">
              <div className="flex flex-row items-center gap-2">
                <fieldset className="mb-2">
                  <Checkbox
                    defaultChecked={
                      initialLicenseInfo?.maintenanceRequired ?? false
                    }
                    id="maintenanceRequired"
                    name="maintenanceRequired"
                  >
                    <Label
                      className="checkbox-label"
                      htmlFor="maintenanceRequired"
                    >
                      Renewable
                    </Label>
                  </Checkbox>
                </fieldset>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        {mode === 'create' ? (
          <div>
            <h2>Purchase Information</h2>
            <PurchaseInfoFields
              initialPurchaseInfo={props.initialPurchaseInfo}
              purchaseInfoFieldErrors={props.purchaseInfoFieldErrors}
              suppliers={props.suppliers}
              users={props.users}
            />
          </div>
        ) : null}
      </div>
      <div>
        <h2 className="mb-4 text-sm font-semibold uppercase text-slate-500">
          Additional Information
        </h2>
        {displayCustomFieldGroups}
        <div className="space-y-3">
          <fieldset>
            <Label htmlFor="note">Notes</Label>
            <Textarea
              defaultValue={initialLicenseInfo?.note ?? ''}
              id="note"
              name="note"
              placeholder="Include any additional information in this note."
              rows={4}
            />
            {licenseFieldErrors?.note ? (
              <p>{licenseFieldErrors.note[0]}</p>
            ) : null}
          </fieldset>
        </div>
      </div>

      <div>
        {mode === 'edit' ? <SubmitButton>Save</SubmitButton> : null}
        {mode === 'renew' ? <SubmitButton>Renew</SubmitButton> : null}
        {mode === 'create' ? <SubmitButton>Create</SubmitButton> : null}
      </div>
    </form>
  );
}
