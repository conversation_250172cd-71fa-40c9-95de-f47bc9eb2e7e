import type { Meta, StoryObj } from '@storybook/react';
import { LicenseForm } from './licenses-form';

const categories = [
  {
    displayName: 'Laptop',
    value: 'laptop',
  },
  {
    displayName: 'Tablet',
    value: 'tablet',
  },
  {
    displayName: 'Phone',
    value: 'phone',
  },
  {
    displayName: 'Keyboard',
    value: 'keyboard',
  },
  {
    displayName: 'Mouse',
    value: 'mouse',
  },
];

const manufacturers = [
  {
    displayName: 'Apple',
    value: 'apple',
  },
  {
    displayName: 'Dell',
    value: 'dell',
  },
  {
    displayName: 'Samsung',
    value: 'samsung',
  },
  {
    displayName: 'HP',
    value: 'hp',
  },
  {
    displayName: 'Google',
    value: 'google',
  },
];

const suppliers = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'JSF',
  },
  {
    displayName: 'Kunze, Predovic and Ziemann',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];

const meta: Meta<typeof LicenseForm> = {
  title: 'components/Licenses/LicenseForm',
  component: LicenseForm,
};

export default meta;

type Story = StoryObj<typeof LicenseForm>;

export const LicenseFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="asset-management-form-heading">Create License</h1>
        <LicenseForm
          categories={categories}
          manufacturers={manufacturers}
          mode="create"
          suppliers={suppliers}
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};

export const LicenseFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto h-fit w-full">
        <h1 className="asset-management-form-heading">Edit License</h1>
        <LicenseForm
          categories={categories}
          initialLicenseInfo={{
            name: 'Adobe Illustrator',
            categoryId: '254cf6f5-bb22-3081-b25b-e2ddb9db5b03',
            manufacturerId: '165cf6f5-bb22-3081-b25b-e2ddb9db5b03',
            productKey: '114cf6f5-bb22-3081-b25b-e2ddb9db5b03',
            licenseHolderEmail: '<EMAIL>',
            expiryDate: '2025-12-10',
            termination: '2025-12-10',
            reassignable: true,
            maintenanceRequired: false,
            licenseHolderName: 'jane doe',
            note: 'sample description',
          }}
          manufacturers={manufacturers}
          mode="edit"
        />
      </div>
    );
  },
};
