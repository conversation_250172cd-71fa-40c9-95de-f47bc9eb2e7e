import Link from 'next/link';
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import {
  AssignUserButton,
  CreateButton,
  DeleteButton,
  EditButton,
  Search,
} from '@/components/client';
import { getAllLicenses } from '@/services/licenses';
import { licensesRoutes } from '@/constants/routes';
import { parseDate } from '@/utils/date-utils';
import {
  EntityType,
  Routes,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import {
  displayDataOrDefault,
  getNumberOfPages,
} from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { getActionColumnByRole } from '@/services/roles';
import { DownloadDocument } from '@/components/DownloadDocument/download-document';

/**
 * This page displays a table of software licenses, including various details for each license.
 * It fetches the licenses using the `getAllLicenses` function and renders them in a table format.
 */
export default async function LicensesPage({
  searchParams,
}: {
  searchParams?: SearchParams;
}): Promise<React.JSX.Element> {
  /**
   * This is an array defining the column names of the Software Licenses table.
   */
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Product Key' },
    { title: 'Expiration' },
    { title: 'Licensee' },
    { title: 'Manufacturer' },
    { title: 'Total', className: 'text-right' },
    { title: 'Available', className: 'text-right' },
  ];
  const query = searchParams?.query || '';

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };
  const renderAction = await getActionColumnByRole('licenses', [
    'create',
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;
  const licensesResponse = await getAllLicenses(queryParams);

  if (licensesResponse.type === 'error') {
    return <>Something went wrong</>;
  }

  const totalPages = getNumberOfPages(
    licensesResponse.count,
    searchParams?.limit,
  );

  const licenses = licensesResponse.data;

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          Software Licenses
        </h1>
        <div className="flex gap-3">
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, product key, licensee"
          />
          <CreateButton href={licensesRoutes.CREATE} label="Create License" />
          <DownloadDocument entityType={EntityType.Liscence} />
        </div>
      </div>
      <Table className="asset-management-table">
        <TableHeader>
          {tableHeaders.map((header) => (
            <TableColumn
              className={`last:text-center ${header.className}`}
              key={header.title}
            >
              {header.title}
            </TableColumn>
          ))}
        </TableHeader>
        <TableBody>
          {licenses && licenses.length > 0 ? (
            licenses.map((license) => (
              <TableRow key={license.id}>
                <TableCell className="first-letter:uppercase">
                  <Link
                    className="text-primary-600 hover:underline"
                    href={`${licensesRoutes.MAIN}/${license.id}`}
                  >
                    {license.name}
                  </Link>
                </TableCell>
                <TableCell className="max-w-[200px] truncate">
                  <span title={license.productKey}>{license.productKey}</span>
                </TableCell>
                <TableCell>
                  {license.expiryDate
                    ? parseDate('MMM dd, yyyy')(license.expiryDate)
                    : '-'}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(license.licenseHolderName)}
                </TableCell>
                <TableCell>
                  {displayDataOrDefault(license.manufacturer?.name)}
                </TableCell>
                <TableCell className="text-right">
                  {license.totalQuantity}
                </TableCell>
                <TableCell className="text-right">
                  {license.availableQuantity}
                </TableCell>
                {renderAction ? (
                  <TableCell>
                    <div className="flex justify-center gap-2">
                      <AssignUserButton
                        href={{
                          pathname: `${licensesRoutes.ASSIGN}/${license.id}`,
                          query: {
                            name: license.name,
                            category: license.category?.name,
                            productKey: license.productKey,
                          },
                        }}
                        isDisabled={license.availableQuantity === 0}
                      />
                      <EditButton
                        href={`${licensesRoutes.EDIT}/${license.id}`}
                      />
                      <DeleteEntity
                        entityId={license.id}
                        entityName={license.name}
                        entityType={Routes.SOFTWARE}
                      >
                        <DeleteButton
                          isDisabled={
                            license.availableQuantity !== license.totalQuantity
                          }
                        />
                      </DeleteEntity>
                    </div>
                  </TableCell>
                ) : null}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell className="text-lg font-bold text-slate-600">
                No Data Found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      <div className="pagination">
        <Pagination
          dataCount={licensesResponse.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
