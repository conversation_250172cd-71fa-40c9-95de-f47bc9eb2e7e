import type { Meta, StoryObj } from '@storybook/react';
import { AssignLicensesForm } from './assign-licenses-form';

const meta: Meta<typeof AssignLicensesForm> = {
  title: 'components/Licenses/AssignLicensesForm',
  component: AssignLicensesForm,
};

export default meta;

type Story = StoryObj<typeof AssignLicensesForm>;
const users = [
  {
    displayName: 'User 1',
    value: 'user1',
  },
  {
    displayName: 'User 2',
    value: 'user2',
  },
  {
    displayName: 'User 3',
    value: 'user3',
  },
  {
    displayName: 'User 4',
    value: 'user4',
  },
  {
    displayName: 'User 5',
    value: 'user5',
  },
];

export const DefaultAssignLicensesForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-1/2">
        <h1 className="asset-management-form-heading">Assign License</h1>
        <AssignLicensesForm
          category="Laptop"
          productKey="3996a98f-84f1-392f-9b8f-72d85143fec1"
          users={users}
          assets={[]}
        />
      </div>
    );
  },
};
