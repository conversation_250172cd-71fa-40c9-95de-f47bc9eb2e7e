import { useState } from 'react';
import { Label, Textarea, Input, SubmitButton } from 'ui';
import type { FieldErrors, LicenseAssignmentFormData, Option } from '@/types';
import { OptionsComboBox, PrimitiveComboBox } from '@/components/client';
import { getCurrentDate } from '@/utils/date-utils';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';

interface AssignLicensesFormProps {
  /**
   * The category to which the license item belongs.
   */
  category: string;
  /**
   * The product key associated with the license item.
   */
  productKey: string;

  /**
   * Callback function triggered on form submission.
   */
  onSubmit?: (formData: FormData) => Promise<void>;

  errors?: FieldErrors<LicenseAssignmentFormData>;
  /**
   * An array of options representing users to whom licenses can be assigned.
   */
  users: Option[];
  /**
   * An array of options representing assets that licenses can be assigned to.
   */
  assets: Option[];
}

/**
 * AssignLicensesForm component: A React form for assigning license items to users or assets.
 * Allows selection of a user or asset from the provided options and adding optional notes.
 */
export function AssignLicensesForm({
  category,
  productKey,
  onSubmit,
  users,
  assets,
  errors,
}: AssignLicensesFormProps): React.JSX.Element {
  const [selectedType, setSelectedType] = useState<'User' | 'Asset'>('User');
  const [formErrors, setFormErrors] = useState<{
    userId?: string;
    assetId?: string;
  }>({});

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const formData = new FormData(event.currentTarget);
    const assignmentType = formData.get('assignmentType') as 'User' | 'Asset';
    const userId = formData.get('userId') as string | null;
    const assetId = formData.get('assetId') as string | null;

    const fieldErrors: { userId?: string; assetId?: string } = {};
    if (assignmentType === 'User' && !userId) {
      fieldErrors.userId = 'This field is required';
    }
    if (assignmentType === 'Asset' && !assetId) {
      fieldErrors.assetId = 'This field is required';
    }

    setFormErrors(fieldErrors);

    if (Object.keys(fieldErrors).length > 0) {
      return;
    }

    if (onSubmit) {
      await onSubmit(formData);
    }
  };

  const assignmentTypeOptions = ['User', 'Asset'];

  return (
    <form
      className="asset-management-assign-form"
      onSubmit={(event) => {
        void handleSubmit(event);
      }}
    >
      {category ? (
        <fieldset className="flex space-x-2">
          <Label>Category:</Label>
          <Label>{category}</Label>
        </fieldset>
      ) : null}
      {productKey ? (
        <fieldset className="flex space-x-2">
          <Label>Product Key:</Label>
          <Label>{productKey}</Label>
        </fieldset>
      ) : null}
      <fieldset>
        <Label htmlFor="assignmentType">Assign To</Label>
        <PrimitiveComboBox
          id="assignmentType"
          initialValue={selectedType}
          name="assignmentType"
          onChange={(newValue) => {
            setSelectedType(newValue as 'User' | 'Asset');
          }}
          placeholder="Choose assignment type"
          values={assignmentTypeOptions}
        />
      </fieldset>
      {selectedType === 'User' && (
        <fieldset>
          <Label htmlFor="userId">Select User</Label>
          <OptionsComboBox
            id="userId"
            isInvalidInput={Boolean(errors?.userId)}
            name="userId"
            options={users}
            placeholder="Choose User"
          />
          {formErrors.userId ? <p className="text-red-500">{formErrors.userId}</p> : null}
        </fieldset>
      )}
      {selectedType === 'Asset' && (
        <fieldset>
          <Label htmlFor="assetId">Select Asset</Label>
          <OptionsComboBox
            id="assetId"
            isInvalidInput={Boolean(errors?.assetId)}
            name="assetId"
            options={assets}
            placeholder="Choose Asset"
          />
          {formErrors.assetId ? <p className="text-red-500">{formErrors.assetId}</p> : null}
        </fieldset>
      )}
      <fieldset>
        <Label htmlFor="date">Date of Assignment</Label>
        <Input
          className="mt-2"
          defaultValue={getCurrentDate()}
          id="date"
          isInvalidInput={Boolean(errors?.date)}
          name="date"
          type="date"
        />
        {errors?.date ? <p>{errors.date[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="notifyUser">Notify</Label>
        <MultiSelectComboBox
          id="notifyUser"
          name="notifyUser"
          options={users}
          placeholder="Choose users to be notified"
        />
      </fieldset>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          className="mt-2"
          id="note"
          name="note"
          placeholder="Include any additional information in this note."
          rows={4}
        />
        {errors?.note ? <p>{errors.note[0]}</p> : null}
      </fieldset>
      <SubmitButton>Assign</SubmitButton>
    </form>
  );
}
