import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { AssignLicense } from './assign-license';
import { getAllAssetOptions } from '@/services/assets';

interface AssignLicenseProps {
  params: {
    id: string;
  };

  searchParams: {
    name: string;
    category: string;
    productKey: string;
  };
}

export default async function AssignLicensePage({
  params: { id },
  searchParams: { name, category, productKey },
}: AssignLicenseProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  const assetResponse = await getAllAssetOptions();
  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  if (assetResponse.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  return (
    <AssignLicense
      assets={assetResponse.data}
      category={category}
      id={id}
      name={name}
      productKey={productKey}
      users={usersResponse.data}
    />
  );
}
