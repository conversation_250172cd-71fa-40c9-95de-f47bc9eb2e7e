'use client';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { reduceFormData } from 'utils';
import type { AssignmentFormData, Option } from '@/types';
import { assignLicense } from '@/services/licenses';
import { LicenseAssignmentFormSchema } from '@/schemas';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { GENERIC_ERROR_MESSAGE } from '@/constants';
import { AssignLicensesForm } from './AssignLicenseForm';

interface AssignLicenseProps {
  /** The unique identifier of the license. */
  id: string;
  /** category of the license */
  category: string;
  /** name of the license */
  name: string;
  /** product key of the license */
  productKey: string;
  /** An array of options representing users. */
  users: Option[];
  assets: Option[];
}

/**
 * Component for assigning a license.
 * AssignLicenseProps represents the properties for the AssignLicense component.
 * returns the rendered AssignLicense component.
 */
export function AssignLicense({
  category,
  name,
  productKey,
  users,
  id,
  assets,
}: AssignLicenseProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssignmentFormData>();
  const assign = async (formData: FormData): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const assignmentDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };
    const parsedResult =
      LicenseAssignmentFormSchema.safeParse(assignmentDetails);

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();
    const assignmentResponse = await assignLicense(id, parsedResult.data);

    if (assignmentResponse.type === 'error') {
      if (assignmentResponse.errors.errorMessages) {
        assignmentResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('License Assigned successfully');
    router.back();
  };

  return (
    <div className="shadow-container m-auto w-2/5 min-w-max">
      <h1 className="asset-management-form-heading">Assign {name}</h1>
      <AssignLicensesForm
        category={category}
        errors={fieldErrors}
        onSubmit={assign}
        productKey={productKey}
        users={users}
        assets={assets}
      />
    </div>
  );
}
