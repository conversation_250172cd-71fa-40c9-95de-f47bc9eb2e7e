'use client';
import { formatISO, parseISO } from 'date-fns';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import type { Option, PurchaseInfo } from '@/types';
import { createLicense } from '@/services/licenses';
import { LicenseFormSchema } from '@/schemas/licenses';
import type { LicenseFormData } from '@/types/licenses';
import { PurchaseInfoSchema } from '@/schemas';
import type { BaseFormData } from '../types';
import { LicenseForm } from '../LicensesForm';
import { FieldGroupDataType } from '@/types/custom-field';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

interface CreateLicenseProps extends BaseFormData {
  suppliers: Option[];
  users: Option[];
  fieldGroups: Option[] | undefined;
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}
/**
 * A React page for creating software license.
 * Renders a form (LicenseForm component) for capturing license details such as supplier, category, manufacturer.
 * Upon form submission, the entered values are included in the form data.
 */

export function CreateLicense({
  categories,
  suppliers,
  manufacturers,
  users,
  fieldGroups,
  fieldGroupDatas,
}: CreateLicenseProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const {
    fieldErrors: licenseFieldErrors,
    setFieldErrors: setLicenseFieldErrors,
    resetFieldErrors: resetLicenseFieldErrors,
  } = useFieldErrors<LicenseFormData>();

  const {
    fieldErrors: purchaseInfoFieldErrors,
    setFieldErrors: setPurchaseInfoFieldErrors,
    resetFieldErrors: resetPurchaseInfoFieldErrors,
  } = useFieldErrors<PurchaseInfo>();

  const addLicense = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    const {
      licenseHolderEmail,
      expiryDate,
      termination,
      maintenanceRequired,
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      purchaseDate,
      purchasedById,
      supplierId,
      ...licenseDetails
    } = Object.fromEntries(formData);

    const parsedLicenseResult = LicenseFormSchema.safeParse({
      ...licenseDetails,
      maintenanceRequired: maintenanceRequired === 'on',
      ...(licenseHolderEmail ? { licenseHolderEmail } : {}),
      ...(expiryDate && typeof expiryDate === 'string'
        ? {
            expiryDate: formatISO(parseISO(expiryDate)),
          }
        : {}),
      ...(termination && typeof termination === 'string'
        ? {
            termination: formatISO(parseISO(termination)),
          }
        : {}),
      customFields: customFieldData,
    });

    const parsedPurchaseInfoResult = PurchaseInfoSchema.safeParse({
      orderNumber,
      purchaseCost,
      currency,
      quantity,
      purchaseDate,
      purchasedById,
      supplierId,
    });

    if (!parsedLicenseResult.success) {
      setLicenseFieldErrors(parsedLicenseResult.error.flatten().fieldErrors);
    } else {
      resetLicenseFieldErrors();
    }

    if (!parsedPurchaseInfoResult.success) {
      setPurchaseInfoFieldErrors(
        parsedPurchaseInfoResult.error.flatten().fieldErrors,
      );
    } else {
      resetPurchaseInfoFieldErrors();
    }

    if (!parsedLicenseResult.success || !parsedPurchaseInfoResult.success) {
      return;
    }

    const licenseResponse = await createLicense(
      parsedLicenseResult.data,
      parsedPurchaseInfoResult.data,
    );

    if (licenseResponse.type === 'error') {
      if (licenseResponse.errors.errorMessages) {
        licenseResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('something went wrong!');
      return;
    }

    toast.success('License created successfully');
    router.back();
  };

  return (
    <div className="shadow-container m-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create License</h1>
      <LicenseForm
        categories={categories}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        licenseFieldErrors={licenseFieldErrors}
        manufacturers={manufacturers}
        mode="create"
        onSubmit={addLicense}
        purchaseInfoFieldErrors={purchaseInfoFieldErrors}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        users={users}
      />
    </div>
  );
}
