'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { PurchaseInfoSchemaPartial } from '@/schemas';
import type { Option, PurchaseInfo } from '@/types';
import { editPurchaseAssets } from '@/services/assets';
import { PurchaseAssetForm } from '../../PurchaseAssetForm';

interface PurchaseAssetProps {
  /** The unique identifier of the Asset. */
  id: string;
  /** An array of options representing users. */
  users: Option[];
  /** An array of options representing suppliers. */
  suppliers: Option[];
  /** Initial values for purchase information. */
  initialValues?: Partial<PurchaseInfo>;

  assetId: string;
}

/**
 * Component for purchasing a Asset.
 * PurchaseAssetProps represents the properties for the PurchaseAsset component.
 * returns the rendered PurchaseAsset component.
 */
export function PurchaseAsset({
  id,
  users,
  suppliers,
  initialValues,
  assetId,
}: PurchaseAssetProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<Partial<PurchaseInfo>>();
  const purchaseAsset = async (formData: FormData): Promise<void> => {
    const { purchaseDate, ...rest } = Object.fromEntries(formData);
    const parsedResult = PurchaseInfoSchemaPartial.safeParse({
      ...rest,
      ...(purchaseDate && typeof purchaseDate === 'string'
        ? {
            purchaseDate: formatISO(parseISO(purchaseDate)),
          }
        : {}),
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const purchaseResponse = await editPurchaseAssets(id, assetId, {
      ...parsedResult.data,
      quantity: 1,
    });

    if (purchaseResponse.type === 'error') {
      if (purchaseResponse.errors.errorMessages) {
        purchaseResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Failed to update Purchase Asset. Please try again');
      return;
    }

    toast.success('Purchase Asset updated successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto h-fit w-2/5">
      <h1 className="asset-management-form-heading">Edit Purchase</h1>
      <PurchaseAssetForm
        errors={fieldErrors}
        onSubmit={purchaseAsset}
        purchaseInfo={initialValues}
        suppliers={suppliers}
        users={users}
      />
    </div>
  );
}
