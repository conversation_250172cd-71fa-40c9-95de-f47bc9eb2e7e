'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { createAsset } from '@/services/assets';
import { assetRequestSchema } from '@/schemas/asset';
import type { AssetRequestData } from '@/types/assets';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { EntityType, type Option, type PurchaseInfoPartial } from '@/types';
import { PurchaseInfoSchemaPartial } from '@/schemas';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import type { FieldGroupDataType } from '@/types/custom-field';
import { GENERIC_ERROR_MESSAGE } from '@/constants';
import { AssetForm } from '../AssetForm';
import type { BaseFormData } from '../types';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

/**
 * Props for the CreateAsset component.
 */
interface CreateAssetProps extends BaseFormData {
  /**
   * An array of options representing users.
   */
  users: Option[];

  /**
   * An array of options representing suppliers.
   */
  suppliers: Option[];
  /**
   * An array of options representing locations.
   */
  locations: Option[];

  /**
   * An array of options representing field groups.
   */
  fieldGroups: Option[] | undefined;

  /**
   * An array of field group data.
   */
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}

export function CreateAsset({
  suppliers,
  assetModel,
  assetStatus,
  users,
  locations,
  fieldGroups,
  fieldGroupDatas,
}: CreateAssetProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );

  const [isImage, setIsImage] = useState(true);
  const {
    fieldErrors: assetFieldErrors,
    setFieldErrors: setAssetFieldErrors,
    resetFieldErrors: resetAssetFieldErrors,
  } = useFieldErrors<AssetRequestData>();

  const {
    fieldErrors: purchaseInfoFieldErrors,
    setFieldErrors: setPurchaseInfoFieldErrors,
    resetFieldErrors: resetPurchaseInfoFieldErrors,
  } = useFieldErrors<PurchaseInfoPartial>();

  /**
   * Function to handle the creation of a new asset.
   */
  async function addAsset(formData: FormData): Promise<void> {
    const customFieldData = formattedCustomFields(formData);

    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );

    const {
      endOfLife,
      orderNumber,
      purchaseCost,
      currency,
      purchaseDate,
      purchasedById,
      supplierId,
      warranty,
      assetImageUrl,
      ...assetFields
    } = Object.fromEntries(formData);

    const formDataObject = {
      ...assetFields,
      warranty: warranty ? warranty : null,
      ...(endOfLife && typeof endOfLife === 'string'
        ? {
            endOfLife: formatISO(parseISO(endOfLife)),
          }
        : {}),
      assetImageUrl: '',
      customFields: customFieldData,
    };

    const parsedResult = assetRequestSchema.safeParse(formDataObject);
    const parsedPurchaseInfoResult = PurchaseInfoSchemaPartial.safeParse({
      orderNumber,
      purchaseCost,
      currency,
      quantity: '1',
      purchaseDate,
      purchasedById,
      supplierId,
    });

    if (!parsedResult.success) {
      setAssetFieldErrors(parsedResult.error.flatten().fieldErrors);
    } else {
      resetAssetFieldErrors();
    }

    if (!parsedPurchaseInfoResult.success) {
      setPurchaseInfoFieldErrors(
        parsedPurchaseInfoResult.error.flatten().fieldErrors,
      );
    } else {
      resetPurchaseInfoFieldErrors();
    }

    if (!parsedResult.success || !parsedPurchaseInfoResult.success) {
      return;
    }

    let uploadedAssetImageUrl = '';
    if (isImage && assetImageUrl instanceof File && assetImageUrl.size > 0) {
      const newFormData = new FormData();
      newFormData.append('file', assetImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Asset,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error(GENERIC_ERROR_MESSAGE);
        return;
      }
      uploadedAssetImageUrl = generateCloudFrontUrl(
        EntityType.Asset,
        imageUploadResponse.data.fileName,
      );
    }

    const createAssetData = {
      ...parsedResult.data,
      assetImageUrl: uploadedAssetImageUrl,
    };
    const assetResponse = await createAsset(
      createAssetData,
      parsedPurchaseInfoResult.data,
    );

    if (assetResponse.type === 'error') {
      if (assetResponse.errors.errorMessages) {
        assetResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Asset created successfully');
    router.back();
  }
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Create Asset</h1>
      <AssetForm
        assetFieldErrors={assetFieldErrors}
        assetModel={assetModel}
        assetStatus={assetStatus}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        mode="create"
        onSubmit={addAsset}
        purchaseInfoFieldErrors={purchaseInfoFieldErrors}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        users={users}
        locations={locations}
      />
    </div>
  );
}
