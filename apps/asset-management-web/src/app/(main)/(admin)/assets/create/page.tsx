import { getAllSuppliersOptions } from '@/services/suppliers';
import { getAllAssetModelOptions } from '@/services/asset-models';
import { getAllStatusOptions } from '@/services/status';
import { getAllUsersOptions } from '@/services/users';
import {
  getAllFieldGroupsOptions,
  getAllFieldGroups,
} from '@/services/custom-field';
import {
  USERS_NOT_FOUND_MESSAGE,
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { CreateAsset } from './create-asset';
import { getAllLocationsOptions } from '@/services/locations';

export default async function CreateassetPage(): Promise<React.JSX.Element> {
  const suppliers = await getAllSuppliersOptions();
  const assetModel = await getAllAssetModelOptions();
  const assetStatus = await getAllStatusOptions();
  const usersResponse = await getAllUsersOptions();
  const locations = await getAllLocationsOptions();

  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }
  return (
    <CreateAsset
      assetModel={assetModel}
      assetStatus={assetStatus}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      suppliers={suppliers.data}
      users={usersResponse.data}
      locations={locations.data}
    />
  );
}
