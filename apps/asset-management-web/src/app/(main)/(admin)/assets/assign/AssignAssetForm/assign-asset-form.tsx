import { Label, Textarea, Input, SubmitButton } from 'ui';
import type { FieldErrors, Option } from '@/types';
import { OptionsComboBox } from '@/components/client';
import { requiredFieldErrorMessage } from '@/constants';
import { getCurrentDate } from '@/utils/date-utils';
import { MultiSelectComboBox } from '@/components/MultiselectComboBox/multiselect-combo-box';
import { AssetAssignFormType } from '@/types/assets';

interface AssignAssetFormProps {
  assetModel: string;
  users: Option[];
  assetStatus: Option[];
  onSubmit?: (formData: FormData) => Promise<void>;
  errors?: FieldErrors<AssetAssignFormType>;
}

/**
 * AssignAssetForm component: A React form for assigning Asset items to users.
 * Allows selection of a user from the provided options and adding optional notes.
 */
export function AssignAssetForm({
  onSubmit,
  users,
  assetModel,
  errors,
  assetStatus,
}: AssignAssetFormProps): React.JSX.Element {
  return (
    <form action={onSubmit} className="asset-management-assign-form">
      {assetModel ? (
        <fieldset className="flex space-x-2">
          <Label>Asset Model: </Label>
          <Label>{assetModel}</Label>
        </fieldset>
      ) : null}
      <fieldset>
        <Label htmlFor="userId" required className="">
          Select a User
        </Label>
        <OptionsComboBox
          id="userId"
          isInvalidInput={Boolean(errors?.userId)}
          name="userId"
          options={users}
          placeholder="Choose User"
        />
        {errors?.userId ? (
          <p className=" text-xs text-red-600">{requiredFieldErrorMessage}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="date">Date of Assignment</Label>
        <Input
          className=" mt-2"
          defaultValue={getCurrentDate()}
          id="date"
          isInvalidInput={Boolean(errors?.date)}
          name="date"
          type="date"
        />
        {errors?.date ? <p>{errors.date[0]}</p> : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="assetStausId" required>
          Asset Status
        </Label>
        <OptionsComboBox
          id="assetStausId"
          name="assetStausId"
          options={assetStatus}
          placeholder="Choose asset status"
        />
        {errors?.assetStausId ? (
          <p className=" text-xs text-red-600">{requiredFieldErrorMessage}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="notifyUser">Notify</Label>
        <MultiSelectComboBox
          id="notifyUser"
          name="notifyUser"
          options={users}
          placeholder="Choose users to be notified"
        />
      </fieldset>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          className="mt-2"
          id="note"
          name="note"
          placeholder="Include any additional information in this note."
          rows={4}
        />
        {errors?.note ? <p>{errors.note[0]}</p> : null}
      </fieldset>
      <SubmitButton>Assign</SubmitButton>
    </form>
  );
}
