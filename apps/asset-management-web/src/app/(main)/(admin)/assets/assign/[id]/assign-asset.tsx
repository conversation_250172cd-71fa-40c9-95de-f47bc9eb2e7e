'use client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { assignAsset } from '@/services/assets';
import type { AssignmentFormData, Option } from '@/types';
import { AssetAssignmentFormSchema } from '@/schemas/asset';
import { GENERIC_ERROR_MESSAGE } from '@/constants';
import { AssignAssetForm } from '../AssignAssetForm';

interface AssignAssetProps {
  assetModel: string;
  id: string;
  name: string;
  users: Option[];
  assetStatus: Option[];
  initialAssetStatus?: string | null;
}

export function AssignAsset({
  assetModel,
  id,
  name,
  users,
  assetStatus,
}: AssignAssetProps): React.JSX.Element {
  const router = useRouter();
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssignmentFormData>();
  const assign = async (formData: FormData): Promise<void> => {
    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));
    const assignmentDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };
    const parsedResult = AssetAssignmentFormSchema.safeParse(assignmentDetails);

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }

    resetFieldErrors();
    const assignmentResponse = await assignAsset(id, parsedResult.data);

    if (assignmentResponse.type === 'error') {
      if (assignmentResponse.errors.errorMessages) {
        assignmentResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success('Acknowledge mail sent successfully');
    router.back();
  };
  return (
    <div className="shadow-container m-auto my-8  w-2/5 min-w-max">
      <h1 className="asset-management-form-heading">Assign {name}</h1>
      <AssignAssetForm
        assetModel={assetModel}
        assetStatus={assetStatus}
        errors={fieldErrors}
        onSubmit={assign}
        users={users}
      />
    </div>
  );
}
