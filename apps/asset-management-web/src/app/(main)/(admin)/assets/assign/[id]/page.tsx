import { getAllUsersOptions } from '@/services/users';
import { getAllStatusOptions } from '@/services/status';
import { getAsset } from '@/services/assets';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { AssignAsset } from './assign-asset';

interface AssignAssetProps {
  params: {
    id: string;
  };
  searchParams: {
    name: string;
    assetModel: string;
  };
}

export default async function Assign({
  params: { id },
  searchParams: { name, assetModel },
}: AssignAssetProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();
  const assetStatus = await getAllStatusOptions();
  const assetInfo = await getAsset(id);
  if (assetInfo.type === 'error') {
    return <>Asset not found</>;
  }

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return (
    <AssignAsset
      assetModel={assetModel}
      assetStatus={assetStatus}
      id={id}
      initialAssetStatus={assetInfo.data.assetStatus?.id}
      name={name}
      users={usersResponse.data}
    />
  );
}
