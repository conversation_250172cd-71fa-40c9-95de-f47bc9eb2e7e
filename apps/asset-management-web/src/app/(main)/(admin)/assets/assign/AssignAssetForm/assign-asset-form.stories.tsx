import type { Meta, StoryObj } from '@storybook/react';
import { AssignAssetForm } from './assign-asset-form';

const meta: Meta<typeof AssignAssetForm> = {
  title: 'components/Asset/AssignAssetForm',
  component: AssignAssetForm,
};

export default meta;

type Story = StoryObj<typeof AssignAssetForm>;

export const DefaultAssignAssetForm: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-16 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Assign Asset</h1>
        <AssignAssetForm
          assetModel="assetmodel"
          assetStatus={[
            {
              displayName: 'Created',
              value: 'created',
            },
            {
              displayName: 'Assigned',
              value: 'assigned',
            },
            {
              displayName: 'UnAssigned',
              value: 'unassigned',
            },
          ]}
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};
