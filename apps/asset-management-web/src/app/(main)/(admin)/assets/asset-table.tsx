'use client';

import {
  Ho<PERSON><PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import Link from 'next/link';
// import { useState } from 'react';
// import { ArrowUpDown } from 'lucide-react';
import { capitalizeFirstLetter } from 'utils';
import {
  EditButton,
  DeleteButton,
  AssignUserButton,
} from '@/components/client';
import { Routes, type TableHeading } from '@/types';
import type { GetAllAssetsResponse } from '@/types/assets';
import { assetsRoutes, userRoutes } from '@/constants/routes';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';

interface AssetTableProps {
  key: string;
  tableHeaders: TableHeading[];
  assets: GetAllAssetsResponse;
  renderAction: TableHeading | null;
  assignedAssets: Record<string, string>;
}

interface Asset {
  id: string;
  location: string;
  assetTag: string;
  assetName: string;
  serialNumber: string;
  assetModel: string | null;
  categoryId: string | null;
  assetStatus: string | null;
  assignedUser: string | null;
  assetModelId: string | null;
  categoryName: string | null;
  assignedUserId: string | null;
  acknowledgedOn: string | null;
}

// const columnKeysPair: Record<string, keyof Asset> = {
//   Tag: 'assetTag',
//   Name: 'assetName',
//   Model: 'assetModel',
//   Location: 'location',
//   Assignee: 'assignedUser',
//   Category: 'categoryName',
//   'Serial No': 'serialNumber',
//   'Acknowledged on': 'acknowledgedOn',
// };

// const sortVisibleFor = [
//   'Name',
//   'Location',
//   'Assignee',
//   'Serial No',
//   'Acknowledged on',
// ];

function AssetTable({
  key,
  assets,
  tableHeaders,
  renderAction,
  assignedAssets,
}: AssetTableProps): React.JSX.Element {
  const allAssets: Asset[] = assets.map((asset) => {
    return {
      id: asset.id,
      assetTag: asset.assetTag,
      location: asset.location.name,
      assetName: asset.assetName,
      serialNumber: asset.serialNumber,
      acknowledgedOn: assignedAssets[asset.id],
      assetModelId: asset.assetModel?.id || null,
      assetStatus: asset.assetStatus?.name || null,
      assignedUser: asset.assignedUser?.name || null,
      assignedUserId: asset.assignedUser?.id || null,
      assetModel: asset.assetModel?.modelName || null,
      categoryId: asset.assetModel?.category?.id || null,
      categoryName: asset.assetModel?.category?.name || null,
    };
  });

  // const [allAssets, setAllAssets] = useState(modifiedAssets);
  // const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // const sortAssets = (columnName: string): void => {
  //   const sortedAssets = [...allAssets].sort((a, b) => {
  //     const aValue: string | null = a[columnKeysPair[columnName]];
  //     const bValue: string | null = b[columnKeysPair[columnName]];

  //     if (typeof aValue === 'string' && typeof bValue === 'string') {
  //       return sortOrder === 'asc'
  //         ? aValue.localeCompare(bValue)
  //         : bValue.localeCompare(aValue);
  //     } else if (typeof aValue === 'number' && typeof bValue === 'number') {
  //       return sortOrder === 'asc' ? bValue - aValue : aValue - bValue;
  //     }

  //     return 0;
  //   });

  //   setAllAssets(sortedAssets);
  //   // Toggle the sort order
  //   setSortOrder((prevOrder) => (prevOrder === 'asc' ? 'desc' : 'asc'));
  // };

  return (
    <Table className="asset-management-table" key={key}>
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              <div className="flex gap-1">
                {heading.title}{' '}
                {/* {sortVisibleFor.includes(heading.title) && (
                  <ArrowUpDown
                    className="text-primary-500 h-4 w-4 hover:cursor-pointer"
                    onClick={() => {
                      sortAssets(heading.title);
                    }}
                  />
                )} */}
              </div>
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {allAssets.length > 0 ? (
          allAssets.map((asset) => (
            <TableRow key={asset.id}>
              <TableCell className="first-letter:uppercase">
                <Link
                  className="text-primary-600 hover:underline "
                  href={`${assetsRoutes.MAIN}/${asset.id}`}
                >
                  {asset.assetName}
                </Link>
              </TableCell>
              <TableCell>
                {asset.assignedUser ? (
                  <Link
                    className="text-primary-600 hover:underline"
                    href={`${userRoutes.MAIN}/${asset.assignedUserId}`}
                  >
                    {asset.assignedUser}
                  </Link>
                ) : (
                  'Not Assigned'
                )}
              </TableCell>
              <TableCell>{asset.assetTag}</TableCell>

              <TableCell className="max-w-[40px]">
                <HoverCard>
                  <div className=" flex flex-wrap">
                    <HoverCardTrigger className=" truncate">
                      {asset.serialNumber}
                    </HoverCardTrigger>
                  </div>
                  {asset.serialNumber ? (
                    <HoverCardContent> {asset.serialNumber}</HoverCardContent>
                  ) : null}
                </HoverCard>
              </TableCell>

              <TableCell>
                {displayDataOrDefault(
                  asset.assetStatus ? asset.assetStatus : null,
                )}
              </TableCell>
              <TableCell>{displayDataOrDefault(asset.assetModel)}</TableCell>
              <TableCell className="start-case">{asset.location}</TableCell>
              <TableCell className="start-case">
                {asset.acknowledgedOn ?? '-'}
              </TableCell>
              <TableCell>
                {asset.categoryName
                  ? capitalizeFirstLetter(asset.categoryName)
                  : displayDataOrDefault(asset.categoryName)}
              </TableCell>

              {renderAction ? (
                <TableCell className="m-auto">
                  <div className="flex items-center justify-center gap-2">
                    <AssignUserButton
                      href={{
                        pathname: `${assetsRoutes.ASSIGN}/${asset.id}`,
                        query: {
                          name: asset.assetName,
                          assetModel: asset.assetModel,
                        },
                      }}
                      isDisabled={Boolean(asset.assignedUser)}
                    />
                    <EditButton href={`${assetsRoutes.EDIT}/${asset.id}`} />
                    <DeleteEntity
                      entityId={asset.id}
                      entityName={asset.assetName}
                      entityType={Routes.ASSET}
                    >
                      <DeleteButton isDisabled={Boolean(asset.assignedUser)} />
                    </DeleteEntity>
                  </div>
                </TableCell>
              ) : null}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}

export default AssetTable;
