import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { AssetForm } from './asset-form';

const suppliers = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'J<PERSON>',
  },
  {
    displayName: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];
const assetModel = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: 'JSF',
  },
  {
    displayName: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>',
    value: 'KPZ',
  },
  {
    displayName: 'Pfannerstill and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];

const assetStatus = [
  {
    displayName: 'Jones, Stoltenberg and Funk',
    value: '<PERSON><PERSON>',
  },
  {
    displayName: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>',
    value: 'KP<PERSON>',
  },
  {
    displayName: 'Pfanners<PERSON><PERSON> and Sons',
    value: 'PS',
  },
  {
    displayName: 'Hickle-Hand',
    value: 'HH',
  },
  {
    displayName: 'Kemmer-Rodriguez',
    value: 'KR',
  },
];

const locations = [
  { displayName: 'Mangalore', value: '1' },
  { displayName: 'Bangalore', value: '2' },
];

const meta: Meta<typeof AssetForm> = {
  title: 'components/Asset/AssetForm',
  component: AssetForm,
};

export default meta;

type Story = StoryObj<typeof AssetForm>;

export const AssetFormWithoutInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Add Asset</h1>
        <AssetForm
          assetModel={assetModel}
          assetStatus={assetStatus}
          locations={locations}
          mode="create"
          suppliers={suppliers}
          users={[
            {
              displayName: 'User 1',
              value: 'user1',
            },
            {
              displayName: 'User 2',
              value: 'user2',
            },
            {
              displayName: 'User 3',
              value: 'user3',
            },
            {
              displayName: 'User 4',
              value: 'user4',
            },
            {
              displayName: 'User 5',
              value: 'user5',
            },
          ]}
        />
      </div>
    );
  },
};

export const AssetFormWithInitialData: Story = {
  render: () => {
    return (
      <div className="shadow-container m-auto my-8 h-fit w-2/5">
        <h1 className="asset-management-form-heading">Edit Asset</h1>
        <AssetForm
          assetModel={assetModel}
          assetStatus={assetStatus}
          initialAssetInfo={{
            assetModelId: '1',
            assetStausId: '1',
            assetName: 'Laptop',
            assetTag: 'LT001',
            assetImageUrl: 'https://example.com/laptop-image.jpg',
            endOfLife: '2024-12-31',
            location: 'Mangalore',
            note: 'High-performance laptop for development',
            requestedById: '4',
            serialNumber: 'SN123456',
            warranty: 2,
          }}
          locations={locations}
          mode="edit"
        />
      </div>
    );
  },
};
