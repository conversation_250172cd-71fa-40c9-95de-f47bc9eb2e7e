'use client';

import {
  Label,
  Input,
  Textarea,
  ImageInputWithPreview,
  SubmitButton,
} from 'ui';
import { allowedImageType } from '@/constants';
import { OptionsComboBox } from '@/components/client';
import type { AssetRequestData } from '@/types/assets';
import type { FieldErrors, Option, PurchaseInfoPartial } from '@/types';
import { CustomField, renderCustomFieldGroups } from '@/utils/custom-field';
import type { BaseFormData } from '../types';
import { PurchaseInfoFields } from '../purchase-info-fields';

interface CreateAssetFormProps extends BaseFormData {
  mode: 'create';
  initialAssetInfo?: Partial<AssetRequestData>;
  initialPurchaseInfo?: Partial<PurchaseInfoPartial>;
  assetFieldErrors?: FieldErrors<AssetRequestData>;
  purchaseInfoFieldErrors?: FieldErrors<PurchaseInfoPartial>;
  onSubmit?: (formData: FormData) => Promise<void>;
  users: Option[];
  suppliers: Option[];
  locations: Option[];
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
}

interface EditAssetFormProps extends BaseFormData {
  mode: 'edit';
  initialAssetInfo: AssetRequestData;
  assetFieldErrors?: FieldErrors<AssetRequestData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setIsImage?: (photo: boolean) => void;
  setSelectedData?: (selectedData: object | undefined) => void;
  fieldGroups?: Option[] | undefined;
  customFields?: (CustomField | undefined)[];
  locations: Option[];
}

/**
 * AssetForm component: A React form for creating and editing Asset items.
 * Captures information such as asset Image, asset Name, assettag ,  and additional notes.
 */
export function AssetForm(
  props: CreateAssetFormProps | EditAssetFormProps,
): React.JSX.Element {
  const {
    onSubmit,
    setSelectedData,
    assetModel,
    assetStatus,
    mode,
    setIsImage,
    assetFieldErrors,
    initialAssetInfo,
    fieldGroups,
    customFields,
    locations,
  } = props;

  const displayCustomFieldGroups = renderCustomFieldGroups(
    fieldGroups,
    initialAssetInfo,
    setSelectedData,
    customFields,
  );

  /**
   * Renders the custom fields based on the provided definitions.
   */
  return (
    <form action={onSubmit} className="asset-management-form pt-0">
      <div>
        <h2>Basic Information</h2>
        <div className="space-y-7">
          <div className="grid grid-cols-2 gap-6">
            <fieldset>
              <Label htmlFor="assetName" required>
                Asset Name
              </Label>
              <Input
                defaultValue={initialAssetInfo?.assetName ?? ''}
                id="assetName"
                isInvalidInput={Boolean(assetFieldErrors?.assetName)}
                name="assetName"
                placeholder="Enter asset name"
                type="text"
              />
              {assetFieldErrors?.assetName ? (
                <p>{assetFieldErrors.assetName[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="warranty">warranty</Label>
              <Input
                defaultValue={initialAssetInfo?.warranty ?? ''}
                id="warranty"
                name="warranty"
                placeholder="E.g. 10"
                type="number"
              />
              {assetFieldErrors?.warranty ? (
                <p>{assetFieldErrors.warranty[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="endOfLife">EOW</Label>
              <Input
                defaultValue={initialAssetInfo?.endOfLife ?? ''}
                id="endOfLife"
                name="endOfLife"
                type="date"
              />
              {assetFieldErrors?.endOfLife ? (
                <p>{assetFieldErrors.endOfLife}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="assetTag" required>
                Asset Tag
              </Label>
              <Input
                defaultValue={initialAssetInfo?.assetTag ?? ''}
                id="assetTag"
                isInvalidInput={Boolean(assetFieldErrors?.assetTag)}
                name="assetTag"
                placeholder="E.g. CardStock(White)"
                type="text"
              />
              {assetFieldErrors?.assetTag ? (
                <p>{assetFieldErrors.assetTag[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="serialNumber" required>
                Serial Number
              </Label>
              <Input
                defaultValue={initialAssetInfo?.serialNumber ?? ''}
                id="serialNumber"
                isInvalidInput={Boolean(assetFieldErrors?.serialNumber)}
                name="serialNumber"
                placeholder="Enter asset serial number"
                type="text"
              />
              {assetFieldErrors?.serialNumber ? (
                <p>{assetFieldErrors.serialNumber[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="location" required>
                Location
              </Label>
              <OptionsComboBox
                id="location"
                initialValue={initialAssetInfo?.location}
                name="location"
                placeholder="Choose location"
                options={locations}
              />
              {assetFieldErrors?.location ? (
                <p>{assetFieldErrors.location[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="assetModelId">Asset Model</Label>
              <OptionsComboBox
                id="assetModelId"
                initialValue={initialAssetInfo?.assetModelId ?? ''}
                name="assetModelId"
                options={assetModel}
                placeholder="Choose asset model"
              />
              {assetFieldErrors?.assetModelId ? (
                <p>{assetFieldErrors.assetModelId[0]}</p>
              ) : null}
            </fieldset>
            <fieldset>
              <Label htmlFor="assetStausId">Asset Status</Label>
              <OptionsComboBox
                id="assetStausId"
                initialValue={initialAssetInfo?.assetStausId ?? ''}
                name="assetStausId"
                options={assetStatus}
                placeholder="Choose asset status"
              />
              {assetFieldErrors?.assetStausId ? (
                <p>{assetFieldErrors.assetStausId[0]}</p>
              ) : null}
            </fieldset>
          </div>
        </div>
      </div>

      {mode === 'create' ? (
        <div>
          <h2>Purchase Information</h2>
          <PurchaseInfoFields
            initialPurchaseInfo={props.initialPurchaseInfo}
            purchaseInfoFieldErrors={props.purchaseInfoFieldErrors}
            suppliers={props.suppliers}
            users={props.users}
          />
        </div>
      ) : null}

      <div>
        <h2 className="mb-4 text-sm font-semibold uppercase text-slate-500">
          Additonal Information
        </h2>

        {displayCustomFieldGroups}
        {mode === 'create' ? (
          <fieldset>
            <Label htmlFor="requestedById">Requested By:</Label>
            <OptionsComboBox
              id="requestedById"
              initialValue={initialAssetInfo?.requestedById ?? ''}
              name="requestedById"
              options={props.users}
              placeholder="Choose user"
            />
            {assetFieldErrors?.requestedById ? (
              <p>{assetFieldErrors.requestedById[0]}</p>
            ) : null}
          </fieldset>
        ) : null}

        <fieldset className=" mt-3">
          <Label htmlFor="note">Notes</Label>
          <Textarea
            defaultValue={initialAssetInfo?.note ?? ''}
            id="note"
            name="note"
            placeholder="Include any additional information in this note."
            rows={4}
          />
          {assetFieldErrors?.note ? <p>{assetFieldErrors.note[0]}</p> : null}
        </fieldset>
        <fieldset>
          <Label htmlFor="assetImageUrl">Select Image</Label>
          <ImageInputWithPreview
            accept={allowedImageType.join(',')}
            className="mt-2"
            defaultValue={initialAssetInfo?.assetImageUrl}
            id="assetImageUrl"
            name="assetImageUrl"
            setIsImage={setIsImage}
          />
          {assetFieldErrors?.assetImageUrl ? (
            <p>{assetFieldErrors.assetImageUrl[0]}</p>
          ) : null}
        </fieldset>
      </div>
      <div>
        <SubmitButton>{mode === 'edit' ? 'Save' : 'Create'}</SubmitButton>
      </div>
    </form>
  );
}
