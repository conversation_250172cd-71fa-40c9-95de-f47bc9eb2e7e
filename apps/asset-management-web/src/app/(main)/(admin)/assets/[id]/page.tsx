import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from 'ui';
import { getAsset } from '@/services/assets';
import { assetsRoutes } from '@/constants/routes';
import { ActionPanel } from '@/components/ActionPanel/action-panel';
import { DocumentTable } from '@/components/DocumentTable/document-table';
import { CategoryType, EntityType } from '@/types';
import { ReminderTable } from '@/components/ReminderTable/reminder-table';
import { AssignmentInfoTable } from './AssignmentInfoTable';
import { AssetInfo } from './AssetInfo';
import { FinanceTable } from './FinanceTable';
import { AssetHistory } from './AssetHistory';
import { AuditInfoTable } from './AuditInfoTable/audit-info-table';
import { DeleteAssetButton } from './delete-asset-button';
import { ServiceInfo } from './ServiceInfo';

interface AssetDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function AssetDetailsPage({
  params: { id },
}: AssetDetailsPageProps): Promise<React.JSX.Element> {
  const assetInfoResponse = await getAsset(id);

  if (assetInfoResponse.type === 'error') {
    return <>Not able to load/get asset, please try again!</>;
  }

  const assetInfo = assetInfoResponse.data;

  return (
    <section className="entity-detail-page">
      <div className="flex w-full justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">
          {assetInfo.assetName}
        </h1>
        <div className="flex gap-x-2">
          <ActionPanel
            actions={['assign', 'audit', 'document', 'reminder', 'service']}
            entityId={id}
            entityName={assetInfo.assetName}
            isAssigned={Boolean(assetInfo.assignedUser)}
            routes={assetsRoutes}
          />
          <DeleteAssetButton
            id={id}
            isDisabled={Boolean(assetInfo.assignedUser)}
            name={assetInfo.assetName}
          />
        </div>
      </div>
      <div className="flex w-full gap-8">
        <Tabs
          className="h-max w-full rounded-md bg-white shadow-md"
          defaultValue="info"
        >
          <TabsList className="[&>*]:py-4">
            <TabsTrigger value="info">Info</TabsTrigger>
            <TabsTrigger value="assignment">Assignments</TabsTrigger>
            <TabsTrigger value="finance">Finance</TabsTrigger>
            <TabsTrigger value="audit">Audit</TabsTrigger>
            <TabsTrigger value="service">Service</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="document">Documents</TabsTrigger>
            <TabsTrigger value="reminder">Reminder</TabsTrigger>
          </TabsList>
          <TabsContent value="info">
            <AssetInfo data={assetInfo} />
          </TabsContent>
          <TabsContent value="assignment">
            <AssignmentInfoTable id={id} name={assetInfo.assetName} />
          </TabsContent>
          <TabsContent value="finance">
            <FinanceTable id={id} />
          </TabsContent>
          <TabsContent value="audit">
            <AuditInfoTable id={id} />
          </TabsContent>
          <TabsContent value="service">
            <ServiceInfo id={id} />
          </TabsContent>
          <TabsContent value="history">
            <AssetHistory id={id} />
          </TabsContent>
          <TabsContent value="document">
            <DocumentTable
              categoryType={CategoryType.ASSET}
              entityType={EntityType.Asset}
              id={id}
            />
          </TabsContent>
          <TabsContent value="reminder">
            <ReminderTable
              categoryType={CategoryType.ASSET}
              entityId={id}
              entityType={EntityType.Asset}
            />
          </TabsContent>
        </Tabs>
      </div>
    </section>
  );
}
