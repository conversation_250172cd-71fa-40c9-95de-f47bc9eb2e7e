import {
  Ho<PERSON><PERSON><PERSON>,
  HoverCardContent,
  HoverCardTrigger,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { Eye } from 'lucide-react';
import {
  getAsset,
  getAssetAssignmentInfo,
  unAssignedDetails,
} from '@/services/assets';
import { getAllStatusOptions } from '@/services/status';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { parseDate } from '@/utils/date-utils';
import { getAllUsersOptions } from '@/services/users';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { UnAssignAsset } from '../../UnAssignAsset/unassign-asset';

const tableHeaders = ['User', 'Assigned Date', 'Unassigned Date', 'Actions'];

interface AssetInfoTableProps {
  id: string;
  name: string;
}

export async function AssignmentInfoTable({
  id,
  name,
}: AssetInfoTableProps): Promise<React.JSX.Element> {
  const assetAssignmentInfo = await getAssetAssignmentInfo(id);

  if (assetAssignmentInfo.type === 'error') {
    return <>Something went wrong, not able to load the asset assigment info</>;
  }

  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }

  const assetStatus = await getAllStatusOptions();
  const assetInfo = await getAsset(id);
  if (assetInfo.type === 'error') {
    return <>Asset not found</>;
  }

  const unAssignedAssetDetails = await unAssignedDetails(id);

  if (unAssignedAssetDetails.type === 'error') {
    return (
      <>Unable to fetch Unassigned details for now, Please try again later</>
    );
  }

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {assetAssignmentInfo.data.length > 0
          ? assetAssignmentInfo.data.map((assignment) => (
              <TableRow key={assignment.id}>
                <TableCell>{assignment.user?.name}</TableCell>
                <TableCell>
                  <div className="flex gap-2 ">
                    {assignment.date}{' '}
                    <HoverCard>
                      <HoverCardTrigger>
                        {assignment.note ? <Eye className=" h-4 w-4" /> : null}
                      </HoverCardTrigger>
                      {assignment.note ? (
                        <HoverCardContent>{assignment.note}</HoverCardContent>
                      ) : null}
                    </HoverCard>
                  </div>
                </TableCell>
                <TableCell>-</TableCell>
                <TableCell>
                  <UnAssignAsset
                    assetName={name}
                    assetStatus={assetStatus}
                    assignment={assignment}
                    assignmentId={assignment.id}
                    userName={assignment.user?.name ?? ''}
                    users={usersResponse.data}
                  />
                </TableCell>
              </TableRow>
            ))
          : null}
        {unAssignedAssetDetails.data && unAssignedAssetDetails.data.length > 0
          ? unAssignedAssetDetails.data.map((unAssignedAsset) => {
              return (
                <TableRow key={unAssignedAsset.id}>
                  <TableCell>
                    {displayDataOrDefault(unAssignedAsset.assignedUser)}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2 ">
                      {unAssignedAsset.assignedDate
                        ? parseDate('MMM dd, yyyy')(
                            unAssignedAsset.assignedDate,
                          )
                        : '-'}{' '}
                      <HoverCard>
                        <HoverCardTrigger>
                          {unAssignedAsset.assignedNote ? (
                            <Eye className=" h-4 w-4" />
                          ) : null}
                        </HoverCardTrigger>
                        {unAssignedAsset.assignedNote ? (
                          <HoverCardContent>
                            {unAssignedAsset.assignedNote}
                          </HoverCardContent>
                        ) : null}
                      </HoverCard>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2 ">
                      {unAssignedAsset.unAssignedDate
                        ? parseDate('MMM dd, yyyy')(
                            unAssignedAsset.unAssignedDate,
                          )
                        : '-'}{' '}
                      <HoverCard>
                        <HoverCardTrigger>
                          {unAssignedAsset.unAssignedNote ? (
                            <Eye className=" h-4 w-4" />
                          ) : null}
                        </HoverCardTrigger>
                        {unAssignedAsset.unAssignedNote ? (
                          <HoverCardContent>
                            {unAssignedAsset.unAssignedNote}
                          </HoverCardContent>
                        ) : null}
                      </HoverCard>
                    </div>
                  </TableCell>
                  <TableCell>-</TableCell>
                </TableRow>
              );
            })
          : null}
      </TableBody>
    </Table>
  );
}
