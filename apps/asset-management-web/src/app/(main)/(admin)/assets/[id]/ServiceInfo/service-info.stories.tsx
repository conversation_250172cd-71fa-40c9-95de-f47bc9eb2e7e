import type { Meta, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { ServiceInfo } from './service-info';

const meta: Meta<typeof ServiceInfo> = {
  title: 'components/Asset/ServiceInfo',
  component: ServiceInfo,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof ServiceInfo>;

export const DefaultServiceInfoTable: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
