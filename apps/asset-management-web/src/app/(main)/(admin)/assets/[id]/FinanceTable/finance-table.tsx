import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import { getAssetFinanceInfo } from '@/services/assets';
import { TableHeading } from '@/types';
import {
  displayDataOrDefault,
  generateDynamicRoute,
} from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { EditButton } from '@/components/ActionButtons/EditButton/edit-button';
import { assetsRoutes } from '@/constants/routes';

interface FinanceTableProps {
  id: string;
}

export async function FinanceTable({
  id,
}: FinanceTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Order No' },
    { title: 'Supplier' },
    { title: 'Currency' },
    { title: 'Purchase Cost' },
    { title: 'Purchase Date' },
    { title: 'Purchased By' },
  ];

  const renderAction = await getActionColumnByRole('assets', [
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;
  const assetFinanceInfo = await getAssetFinanceInfo(id);
  if (assetFinanceInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {assetFinanceInfo.data.map((finance) => (
          <TableRow key={finance.id}>
            <TableCell>{finance.orderNumber ?? '-'}</TableCell>
            <TableCell>{finance.supplier?.name ?? '-'}</TableCell>
            <TableCell>{displayDataOrDefault(finance.currency)}</TableCell>
            <TableCell>{finance.purchaseCost}</TableCell>
            <TableCell>
              {finance.purchaseDate
                ? parseDate('MMM dd, yyyy')(finance.purchaseDate)
                : '-'}
            </TableCell>
            <TableCell>{finance.purchasedBy?.name ?? '-'}</TableCell>
            {renderAction ? (
              <TableCell>
                <div className=" flex items-center gap-4">
                  <EditButton
                    href={generateDynamicRoute(assetsRoutes.EDIT_PURCHASE, {
                      purchaseId: id,
                    })}
                  />
                </div>
              </TableCell>
            ) : null}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
