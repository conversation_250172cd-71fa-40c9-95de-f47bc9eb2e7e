import type { GetAssetResponse } from '@/types/assets';
import { parseDate } from '@/utils/date-utils';
import { displayDataOrDefault } from '@/utils/helper-functions';
import type { CustomFieldMap } from '@/utils/custom-field';
import { mapCustomFieldNamesToValues } from '@/utils/custom-field';
import { InfoItem } from '@/components/InfoItems/info-item';
import { QRCodeGenerator } from 'ui';

interface AssetInfoProps {
  data: GetAssetResponse;
}

/**
 * This is a React component that displays detailed information about an asset, including assetName, assetTag, serialNumber, assetModel, assetStatus, supplier, warranty, endOfLife, and notes.
 */
export async function AssetInfo({
  data,
}: AssetInfoProps): Promise<React.JSX.Element> {
  const latestAudit =
    data.Audit && data.Audit.length > 0
      ? data.Audit[data.Audit.length - 1]
      : null;

  const customFieldMap: CustomFieldMap = await mapCustomFieldNamesToValues(
    data.customFields?.data,
  );

  return (
    <div className="flex justify-between px-6 pb-6 pt-4 text-xs text-slate-600">
      <div className="w-3/4 space-y-5 [&>div]:grid [&>div]:grid-cols-[25%,auto]">
        <InfoItem
          name="Requested By"
          value={displayDataOrDefault(data.requestedBy?.name)}
        />
        <InfoItem name="Asset Name" value={data.assetName} />
        {latestAudit ? (
          <>
            <InfoItem
              name="Last Audit Date"
              value={
                latestAudit.lastAuditDate
                  ? parseDate('MMM dd, yyyy')(latestAudit.lastAuditDate)
                  : '-'
              }
            />
            <InfoItem
              name="Next Audit Date"
              value={
                latestAudit.nextAuditDate
                  ? parseDate('MMM dd, yyyy')(latestAudit.nextAuditDate)
                  : '-'
              }
            />
          </>
        ) : null}
        <InfoItem name="Asset Tag" value={data.assetTag} />
        <InfoItem name="Serial Number" value={data.serialNumber} />
        <InfoItem name="Asset Model" value={data.assetModel?.modelName} />
        <InfoItem name="Asset Status" value={data.assetStatus?.name} />
        <InfoItem
          name="End Of Life"
          value={
            data.endOfLife ? parseDate('MMM dd, yyyy')(data.endOfLife) : null
          }
        />
        <InfoItem name="Warranty" value={data.warranty} />
        <InfoItem name="Notes" value={data.note} />
        {Object.entries(customFieldMap).map(([fieldName, value]) => (
          <InfoItem key={fieldName} name={fieldName} value={value} />
        ))}
      </div>
      <div className="flex flex-col space-y-10">
        {data.assetImageUrl ? (
          <div className="flex flex-shrink-0 justify-end">
            <img
              alt="asset-name"
              className="h-[200px] w-auto rounded object-cover shadow"
              src={data.assetImageUrl}
              width={400}
              height={400}
            />
          </div>
        ) : null}
        <div className="flex items-center justify-center">
          <QRCodeGenerator
            assetId={data.id}
            assetTag={data.assetTag}
            assetName={data.assetName}
          />
        </div>
      </div>
    </div>
  );
}
