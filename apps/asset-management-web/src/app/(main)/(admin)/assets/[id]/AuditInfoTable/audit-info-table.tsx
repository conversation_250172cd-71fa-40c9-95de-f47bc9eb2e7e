import {
  Hover<PERSON>ard,
  HoverCardContent,
  HoverCardTrigger,
  ImagePile,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { Codesandbox as BaseIcon } from 'lucide-react';
import { getAuditInfo } from '@/services/assets';
import { parseDate } from '@/utils/date-utils';
import { displayDataOrDefault } from '@/utils/helper-functions';
import { getActionColumnByRole } from '@/services/roles';
import { CarouselModal } from '@/components/CarouselModal/carousel-modal';
import { Routes, TableHeading } from '@/types';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { DeleteButton } from '@/components/ActionButtons/DeleteButton/delete-button';

interface AuditInfoTableProps {
  id: string;
}

export async function AuditInfoTable({
  id,
}: AuditInfoTableProps): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Audit Image' },
    { title: 'Audit Created By' },
    { title: 'Last Audit Date' },
    { title: 'Next Audit Date' },
    { title: 'Location' },
    { title: 'Note' },
  ];

  const renderAction = await getActionColumnByRole('assets', ['delete']);
  renderAction ? tableHeaders.push(renderAction) : null;

  const auditInfo = await getAuditInfo(id);

  if (auditInfo.type === 'error') {
    return <>Not found audit info</>;
  }

  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return (
            <TableColumn className={heading.className} key={heading.title}>
              {heading.title}
            </TableColumn>
          );
        })}
      </TableHeader>
      <TableBody>
        {auditInfo.data && auditInfo.data.length > 0 ? (
          auditInfo.data.map((audit) => (
            <TableRow key={audit.id}>
              <TableCell>
                {audit.auditImageUrls.length > 0 ? (
                  <CarouselModal images={audit.auditImageUrls}>
                    <ImagePile images={audit.auditImageUrls} />
                  </CarouselModal>
                ) : (
                  <BaseIcon className="h-7 w-7" />
                )}
              </TableCell>
              <TableCell>
                {audit.createdBy}
              </TableCell>
              <TableCell>
                {audit.lastAuditDate
                  ? parseDate('MMM dd, yyyy')(audit.lastAuditDate)
                  : '-'}
              </TableCell>
              <TableCell>
                {audit.nextAuditDate
                  ? parseDate('MMM dd, yyyy')(audit.nextAuditDate)
                  : '-'}
              </TableCell>
              <TableCell className="start-case">
                {audit.location.name}
              </TableCell>
              <TableCell className="max-w-xs">
                <HoverCard>
                  <div className=" flex flex-wrap">
                    <HoverCardTrigger className=" truncate">
                      {displayDataOrDefault(audit.note)}
                    </HoverCardTrigger>
                  </div>
                  {audit.note ? (
                    <HoverCardContent align="start">
                      {audit.note}
                    </HoverCardContent>
                  ) : null}
                </HoverCard>
              </TableCell>
              {renderAction ? (
                <TableCell>
                  <div className=" flex items-center gap-4">
                    <DeleteEntity entityId={audit.id} entityType={Routes.AUDIT}>
                      <DeleteButton />
                    </DeleteEntity>
                  </div>
                </TableCell>
              ) : null}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="text-lg font-bold text-slate-600">
              No Data Found
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
