import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionTrigger,
  AccordionItem,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from 'ui';
import { parseDate } from '@/utils/date-utils';
import { getAssetHistoryInfo } from '@/services/assets';
import { generateUpdateText } from '@/utils/helper-functions';

const tableHeaders = ['Actions Performed', 'Date', 'Action By'];
interface AssetHistoryProps {
  id: string;
}

export async function AssetHistory({
  id,
}: AssetHistoryProps): Promise<React.JSX.Element> {
  const consumableHistoryInfo = await getAssetHistoryInfo(id);

  if (consumableHistoryInfo.type === 'error') {
    return <>Something went wrong</>;
  }
  return (
    <Table className="asset-management-table-md rounded-none pt-0 shadow-none">
      <TableHeader className="asset-management-table-heading">
        {tableHeaders.map((heading) => {
          return <TableColumn key={heading}>{heading}</TableColumn>;
        })}
      </TableHeader>
      <TableBody>
        {consumableHistoryInfo.data.map((history) => (
          <TableRow key={history.log.userId}>
            <TableCell>
              {(() => {
                switch (history.action) {
                  case 'CREATED':
                    return (
                      <p>
                        {history.changeInTable === 'ASSET' ? (
                          <>
                            Asset
                            <span className="font-semibold"> created</span>
                          </>
                        ) : (
                          <>
                            Purchase record
                            <span className="font-semibold"> created</span>
                          </>
                        )}
                      </p>
                    );
                  case 'UPDATED':
                    return history.log.updatedFields &&
                      history.log.updatedFields.length > 0 ? (
                      <Accordion className="w-72">
                        <AccordionItem
                          className="text-xs"
                          value={`item-${history.log.userId}`}
                        >
                          <AccordionTrigger>
                            <p>
                              <span className="font-semibold">Updated</span>(
                              {history.log.updatedFields.length} fields)
                            </p>
                          </AccordionTrigger>
                          <AccordionContent className="space-y-1">
                            {history.log.updatedFields.map(generateUpdateText)}
                          </AccordionContent>
                        </AccordionItem>
                      </Accordion>
                    ) : (
                      <p>No fields updated</p>
                    );
                  case 'ASSIGNED':
                    return (
                      <p>
                        Asset{' '}
                        <span className="font-semibold">acknowledged</span> and{' '}
                        <span className="font-semibold">
                          successfully assigned
                        </span>{' '}
                        to{' '}
                        <span className="font-semibold">
                          {history.log.assignmentDetails?.assignedTo}
                        </span>
                      </p>
                    );
                  case 'PENDING':
                    return (
                      <p>
                        Acknowledgment is currently{' '}
                        <span className="font-semibold">
                          awaiting confirmation
                        </span>{' '}
                        from{' '}
                        <span className="font-semibold">
                          {history.log.assignmentDetails?.assignedTo}
                        </span>
                      </p>
                    );
                  case 'UNASSIGNED':
                    return (
                      <p>
                        This asset is currently{' '}
                        <span className="font-semibold">unassigned</span>.
                      </p>
                    );
                  default:
                    return null;
                }
              })()}
            </TableCell>
            <TableCell>{parseDate('MMM dd, yyyy')(history.date)}</TableCell>
            <TableCell>{history.log.name}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
