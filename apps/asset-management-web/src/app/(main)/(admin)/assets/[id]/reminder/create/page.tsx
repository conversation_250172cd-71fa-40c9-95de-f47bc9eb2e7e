import { CreateReminder } from '@/components/CreateReminder/create-reminder';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { getAllUsersOptions } from '@/services/users';
import { CategoryType } from '@/types';

interface PolicyDetailsPageProps {
  params: {
    id: string;
  };
}

export default async function Page({
  params: { id },
}: PolicyDetailsPageProps): Promise<React.JSX.Element> {
  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  return (
    <CreateReminder
      categoryType={CategoryType.ASSET}
      id={id}
      users={usersResponse.data}
    />
  );
}
