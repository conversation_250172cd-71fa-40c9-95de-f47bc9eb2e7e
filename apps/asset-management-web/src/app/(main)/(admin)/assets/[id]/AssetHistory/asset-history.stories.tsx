import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { Suspense } from 'react';
import { AssetHistory } from './asset-history';

const meta: Meta<typeof AssetHistory> = {
  title: 'components/Asset/AssetHistory',
  component: AssetHistory,
  decorators: [
    (Story) => (
      <Suspense>
        <Story />
      </Suspense>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof AssetHistory>;

export const DefaultAssetHistory: Story = {
  args: {
    id: 'aaa-bbb-ccc',
  },
};
