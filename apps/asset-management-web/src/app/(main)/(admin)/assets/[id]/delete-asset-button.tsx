'use client';

import { useRouter } from 'next/navigation';
import { DeleteEntity } from '@/components/DeleteEntity/delete-entity';
import { OutlinedDeleteButton } from '@/components/client';
import { Routes } from '@/types';

export function DeleteAssetButton({
  id,
  name,
  isDisabled,
}: {
  id: string;
  name: string;
  isDisabled: boolean;
}): React.JSX.Element {
  const router = useRouter();
  return (
    <DeleteEntity
      entityId={id}
      entityName={name}
      entityType={Routes.ASSET}
      onDelete={() => {
        router.push('/assets');
      }}
    >
      <OutlinedDeleteButton isDisabled={isDisabled} />
    </DeleteEntity>
  );
}
