import { format, parseISO } from 'date-fns';
import { getReminder } from '@/services/reminder';
import type { ReminderFormData } from '@/types/reminder';
import EditReminder from '@/components/EditReminder/edit-reminder';
import { getAsset } from '@/services/assets';
import { CategoryType } from '@/types';
import calculateInitialTimeAndUnit from '@/utils/date-utils';
import { USERS_NOT_FOUND_MESSAGE } from '@/constants';
import { getAllUsersOptions } from '@/services/users';

interface EditReminderPageProps {
  params: {
    id: string;
    reminderId: string;
  };
}

export default async function EditReminderPage({
  params: { id, reminderId },
}: EditReminderPageProps): Promise<React.JSX.Element | undefined> {
  const assetInfo = await getAsset(id);
  const reminderInfo = await getReminder(reminderId, CategoryType.ASSET);
  if (assetInfo.type === 'error') {
    return <>Not able to load data. Please try again!</>;
  }

  if (reminderInfo.type === 'error') {
    return <>Reminder not found</>;
  }

  const usersResponse = await getAllUsersOptions();

  if (usersResponse.type === 'error') {
    return <>{USERS_NOT_FOUND_MESSAGE}</>;
  }
  const { endDate } = reminderInfo.data;

  const { time, unit } = calculateInitialTimeAndUnit(
    endDate,
    reminderInfo.data.startDate,
  );

  const editFormData: ReminderFormData = {
    ...reminderInfo.data,
    endDate: format(parseISO(endDate), 'yyyy-MM-dd'),
    time: time.toString(),
    unit,
  };

  return (
    <EditReminder
      categoryType={CategoryType.ASSET}
      initialValues={editFormData}
      reminderId={reminderId}
      users={usersResponse.data}
    />
  );
}
