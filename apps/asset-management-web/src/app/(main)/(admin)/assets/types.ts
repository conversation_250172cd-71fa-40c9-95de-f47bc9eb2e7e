import type { Option } from '@/types';

export interface BaseFormData {
  assetModel: Option[];
  assetStatus: Option[];
}

/**
 *  Interface representing the props for the UnassignAsset component.
 */
export interface UnAssignEntityBaseProps {
  /**
   * The name of the entity to be unassigned.
   */
  entityName: string;

  /**
   * The name of the user to whom the entity is currently assigned.
   */
  userName: string;

  /**
   * The initial status of the entity.
   */
  initialEntityStatus?: string | null;

  /**
   * An array of options representing the possible statuses for the asset.
   */
  entityStatus: Option[];

  users: Option[];

  /**
   * Optional callback function triggered when the asset is deleted.
   */
  onDelete?: () => void;
}
