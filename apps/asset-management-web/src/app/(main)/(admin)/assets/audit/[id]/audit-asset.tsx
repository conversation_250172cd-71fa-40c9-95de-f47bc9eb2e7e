'use client';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { formatISO, parseISO } from 'date-fns';
import { useState } from 'react';
import { AuditFormSchema } from '@/schemas/asset';
import type { AuditRequestData } from '@/types/assets';
import { createAuditAsset } from '@/services/assets';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { uploadFiles } from '@/services/file-upload';
import { EntityType, Option } from '@/types';
import { AuditForm } from '../AuditForm/audit-form';

interface AuditAssetProps {
  assetModel: string;
  id: string;
  name: string;
  locations: Option[];
}

export function AuditAsset({
  assetModel,
  id,
  name,
  locations,
}: AuditAssetProps): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const [imageUrls, setImageUrls] = useState<File[]>([]);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AuditRequestData>();
  const audit = async (formData: FormData): Promise<void> => {
    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );

    const auditData = Object.fromEntries(formData);
    const { nextAuditDate, assetId, ...auditDetails } = auditData;

    const parsedResult = AuditFormSchema.safeParse({
      ...auditDetails,
      ...(assetId
        ? {
            assetId: id,
          }
        : null),
      ...(nextAuditDate && typeof nextAuditDate === 'string'
        ? {
            nextAuditDate: formatISO(parseISO(nextAuditDate)),
          }
        : {}),
      auditImageUrls: [],
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const uploadedAuditImageUrls: string[] = [];
    if (isImage && imageUrls.length > 0) {
      const newFormData = new FormData();
      imageUrls.forEach((file) => {
        newFormData.append('file', file);
      });
      const imageUploadResponse = await uploadFiles(
        newFormData,
        EntityType.Asset,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      for (const image of imageUploadResponse.data.fileName) {
        const ImageUrl = generateCloudFrontUrl(EntityType.Asset, image);
        uploadedAuditImageUrls.push(ImageUrl);
      }
    }

    const createAuditData = {
      ...parsedResult.data,
      auditImageUrls: uploadedAuditImageUrls,
    };
    const auditResponse = await createAuditAsset(id, createAuditData);

    if (auditResponse.type === 'error') {
      if (auditResponse.errors.errorMessages) {
        auditResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('something went wrong');
      return;
    }

    toast.success('Audit created successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto h-fit w-2/5 min-w-max">
      <h1 className="asset-management-form-heading">Audit {name}</h1>
      <AuditForm
        assetModel={assetModel}
        auditFieldErrors={fieldErrors}
        id={id}
        imageUrls={imageUrls}
        onSubmit={audit}
        setImageUrls={setImageUrls}
        setIsImage={setIsImage}
        locations={locations}
      />
    </div>
  );
}
