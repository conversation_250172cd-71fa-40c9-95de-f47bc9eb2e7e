import { getAllLocationsOptions } from '@/services/locations';
import { AuditAsset } from './audit-asset';

interface AuditProps {
  params: {
    id: string;
  };

  searchParams: {
    name: string;
    assetModel: string;
  };
}

export default async function Audit({
  params: { id },
  searchParams: { name, assetModel },
}: AuditProps): Promise<React.JSX.Element> {
  const locations = await getAllLocationsOptions();
  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }
  return <AuditAsset assetModel={assetModel} id={id} name={name} locations={locations.data}/>;
}
