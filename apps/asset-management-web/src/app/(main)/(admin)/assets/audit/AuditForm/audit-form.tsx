import React, { useState } from 'react';
import {
  Button,
  CameraImage,
  Input,
  Label,
  MultipleImageInputWithPreview,
  SubmitButton,
  Textarea,
} from 'ui';
import { OptionsComboBox } from '@/components/client';
import { allowedImageType, requiredFieldErrorMessage } from '@/constants';
import type { FieldErrors, Option } from '@/types';
import type { AuditRequestData } from '@/types/assets';
import { Focus } from 'lucide-react';

interface AuditFormProps {
  id: string;
  assetModel: string;
  auditFieldErrors?: FieldErrors<AuditRequestData>;
  initialAuditInfo?: Partial<AuditRequestData>;
  onSubmit?: (formData: FormData) => Promise<void>;
  setIsImage: (isImage: boolean) => void;
  imageUrls: File[];
  setImageUrls: (isData: File[]) => void;
  locations: Option[];
}

export function AuditForm({
  auditFieldErrors,
  onSubmit,
  assetModel,
  initialAuditInfo,
  id,
  setIsImage,
  imageUrls,
  setImageUrls,
  locations,
}: AuditFormProps): React.JSX.Element {
  const [useWebcam, setUseWebcam] = useState(false);

  return (
    <form action={onSubmit} className="asset-management-assign-form">
      {assetModel ? (
        <fieldset className="flex space-x-2">
          <Label>Asset Model:</Label>
          <Label>{assetModel}</Label>
        </fieldset>
      ) : null}

      <fieldset>
        <Label htmlFor="nextAuditDate" required>
          Next Audit Date
        </Label>
        <Input
          className="mt-2"
          defaultValue={initialAuditInfo?.nextAuditDate ?? ''}
          id="nextAuditDate"
          isInvalidInput={Boolean(auditFieldErrors?.nextAuditDate)}
          name="nextAuditDate"
          type="date"
        />
        {auditFieldErrors?.nextAuditDate ? (
          <p>{requiredFieldErrorMessage}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="location" required>
          Location
        </Label>
        <OptionsComboBox
          id="location"
          initialValue={initialAuditInfo?.location}
          name="location"
          placeholder="Choose location"
          options={locations}
        />
        {auditFieldErrors?.location ? (
          <p>{auditFieldErrors.location[0]}</p>
        ) : null}
      </fieldset>
      <fieldset className="hidden">
        <Label htmlFor="assetId" required>
          Asset Id
        </Label>
        <Input
          defaultValue={initialAuditInfo?.assetId ?? id}
          id="assetId"
          name="assetId"
          placeholder="eb58010d-0c68-4f36-ac27-b7c1a13b0491"
          type="text"
        />
        {auditFieldErrors?.assetId ? (
          <p>{auditFieldErrors.assetId[0]}</p>
        ) : null}
      </fieldset>
      <fieldset>
        <Label htmlFor="note">Notes</Label>
        <Textarea
          className="mt-2"
          defaultValue={initialAuditInfo?.note ?? ''}
          id="note"
          name="note"
          placeholder="Include any additional information in this note."
          rows={4}
        />
        {auditFieldErrors?.note ? <p>{auditFieldErrors.note[0]}</p> : null}
      </fieldset>

      <fieldset>
        <Label htmlFor="auditImageUrls">Select Image</Label>

        {!useWebcam && (
          <>
            <MultipleImageInputWithPreview
              accept={allowedImageType.join(',')}
              className="mt-2"
              defaultValue={initialAuditInfo?.auditImageUrls ?? []}
              id="auditImageUrls"
              imageUrls={imageUrls}
              name="auditImageUrls"
              setImageUrls={setImageUrls}
              setIsImage={setIsImage}
            />
            <div className="flex items-center justify-center">
              <Label>OR</Label>
            </div>
          </>
        )}
        <div className="mt-4 flex flex-col items-start items-center justify-center gap-4">
          <Button
            className="pressed:bg-primary-100 hover:bg-primary-50 h-26 flex w-1/2 items-center justify-center gap-x-1 border-dashed border-neutral-300 text-lg font-semibold shadow-sm"
            onPress={() => {
              setUseWebcam((prev) => !prev);
            }}
            variant="outlined"
          >
            <Focus /> Capture Image
          </Button>
        </div>
        {useWebcam && (
          <CameraImage
            setImageUrls={setImageUrls}
            setIsImage={setIsImage}
            imageUrls={imageUrls}
          />
        )}
        {auditFieldErrors?.auditImageUrls ? (
          <p>{auditFieldErrors.auditImageUrls[0]}</p>
        ) : null}
      </fieldset>

      <SubmitButton>Audit</SubmitButton>
    </form>
  );
}
