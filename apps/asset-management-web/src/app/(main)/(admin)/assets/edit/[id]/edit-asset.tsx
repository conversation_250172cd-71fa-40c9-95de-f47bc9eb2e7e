'use client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';
import { formatISO, parseISO } from 'date-fns';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { updateAsset } from '@/services/assets';
import { assetRequestSchema } from '@/schemas/asset';
import type { AssetRequestData } from '@/types/assets';
import { uploadFile } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import { EntityType } from '@/types';
import type { Option } from '@/types';
import type { FieldGroupDataType } from '@/types/custom-field';
import { GENERIC_ERROR_MESSAGE } from '@/constants';
import { AssetForm } from '../../AssetForm';
import type { BaseFormData } from '../../types';
import {
  SelectedData,
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';

/**
 * Props for the EditAsset component.
 */
interface EditAssetProps extends BaseFormData {
  /**
   * Unique identifier for the asset
   */
  id: string;

  /**
   * Initial values for the asset.
   */
  initialValues: AssetRequestData;

  /**
   * An array of options representing field groups.
   */
  fieldGroups: Option[] | undefined;

  /**
   * An array of field group data.
   */
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;

  /**
   * An array of locations
   */
  locations: Option[];
}

export function EditAsset({
  id,
  assetModel,
  assetStatus,
  initialValues,
  fieldGroups,
  fieldGroupDatas,
  locations,
}: EditAssetProps): React.JSX.Element {
  const router = useRouter();
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );
  const [isImage, setIsImage] = useState(true);
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssetRequestData>();

  /**
   * Handles the edit operation for the asset.
   */
  async function handleEdit(formData: FormData): Promise<void> {
    const customFieldData = formattedCustomFields(formData);

    formData.set(
      'location',
      formData.get('location')?.toString().toUpperCase() || '',
    );

    const assetData = Object.fromEntries(formData);
    const { endOfLife, warranty, assetImageUrl, ...assetDetails } = assetData;

    const parsedResult = assetRequestSchema.safeParse({
      ...assetDetails,
      warranty: warranty ? warranty : null,
      ...(endOfLife && typeof endOfLife === 'string'
        ? {
            endOfLife: formatISO(parseISO(endOfLife)),
          }
        : {}),
      assetImageUrl: '',
      customFields: customFieldData,
    });

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }

    resetFieldErrors();
    let uploadedAssetImageUrl = initialValues.assetImageUrl;
    if (isImage && assetImageUrl instanceof File && assetImageUrl.size > 0) {
      const newFormData = new FormData();
      newFormData.append('file', assetImageUrl);
      const imageUploadResponse = await uploadFile(
        newFormData,
        EntityType.Asset,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error(GENERIC_ERROR_MESSAGE);
        return;
      }
      uploadedAssetImageUrl = generateCloudFrontUrl(
        EntityType.Asset,
        imageUploadResponse.data.fileName,
      );
    }

    const updateAssetData = {
      ...parsedResult.data,
      assetImageUrl: isImage ? uploadedAssetImageUrl : '',
    };
    const assetResponse = await updateAsset(id, updateAssetData);

    if (assetResponse.type === 'error') {
      if (assetResponse.errors.errorMessages) {
        assetResponse.errors.errorMessages.forEach((err) => toast.error(err));
        return;
      }
      toast.error('Unable to update the asset');
      return;
    }

    toast.success('Asset updated successfully');
    router.back();
  }
  return (
    <div className="shadow-container mx-auto mb-8 h-fit w-1/2">
      <h1 className="asset-management-form-heading">Edit Asset</h1>
      <AssetForm
        assetFieldErrors={fieldErrors}
        assetModel={assetModel}
        assetStatus={assetStatus}
        customFields={uniqueCustomFields}
        fieldGroups={fieldGroups}
        initialAssetInfo={initialValues}
        mode="edit"
        onSubmit={handleEdit}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        locations={locations}
      />
    </div>
  );
}
