import { format, parseISO } from 'date-fns';
import { getAsset } from '@/services/assets';
import { getAllSuppliersOptions } from '@/services/suppliers';
import { getAllStatusOptions } from '@/services/status';
import { getAllAssetModelOptions } from '@/services/asset-models';
import type { AssetRequestData } from '@/types/assets';
import {
  getAllFieldGroupsOptions,
  getAllFieldGroups,
} from '@/services/custom-field';
import { EditAsset } from './edit-asset';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getAllLocationsOptions } from '@/services/locations';

interface EditProps {
  params: {
    id: string;
  };
}

export default async function EditappliancePage({
  params: { id },
}: EditProps): Promise<React.JSX.Element> {
  const suppliers = await getAllSuppliersOptions();
  const assetStatuss = await getAllStatusOptions();
  const assetModels = await getAllAssetModelOptions();
  const assetInfoResponse = await getAsset(id);
  const locations = await getAllLocationsOptions();

  if (assetInfoResponse.type === 'error') {
    return <>Asset Id not found</>;
  }
  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }
  if (locations.type === 'error') {
    return <>Not able to load locations. Please try again!</>;
  }

  const { assetStatus, assetModel, endOfLife, requestedBy, location, ...rest } =
    assetInfoResponse.data;

  const intitialValues: AssetRequestData = {
    ...rest,
    assetStausId: assetStatus?.id ?? undefined,
    assetModelId: assetModel?.id ?? undefined,
    requestedById: requestedBy?.id ?? undefined,
    location: location.id,
    endOfLife: endOfLife ? format(parseISO(endOfLife), 'yyyy-MM-dd') : null,
    customFields: rest.customFields ? rest.customFields : {},
  };

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  return (
    <EditAsset
      assetModel={assetModels}
      assetStatus={assetStatuss}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      initialValues={intitialValues}
      locations={locations.data}
    />
  );
}
