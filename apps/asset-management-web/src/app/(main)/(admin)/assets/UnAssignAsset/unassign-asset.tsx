'use client';

import { toast } from 'sonner';
import { reduceFormData } from 'utils';
import type {
  AssetUnAssignmentFormData,
  AssignmentDataType,
} from '@/types/assets';
import { unassignAsset } from '@/services/assets';
import type { UnAssignEntityProps } from '@/components/UnAssignEntityForm/un-assign-entity-form';
import { UnAssignEntityForm } from '@/components/UnAssignEntityForm/un-assign-entity-form';
import type { Option } from '@/types';
import { GENERIC_ERROR_MESSAGE, INPUT_VALUES_ERROR_MESSAGE } from '@/constants';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { assetUnAssignmentFormSchema } from '@/schemas/asset';

/**
 *  Interface representing the props for the UnassignAsset component.
 */
interface UnassignAssetProps extends UnAssignEntityProps {
  /**
   * The ID of the assignment related to the asset.
   */
  assignmentId: string;

  /**
   * The data type representing the assignment details.
   */
  assignment: AssignmentDataType;

  /**
   * Name of the asset
   */
  assetName: string;

  /**
   * An array of options representing the possible statuses for the asset.
   */
  assetStatus: Option[];
}

/**
 * component for unassigning an asset.
 */
export function UnAssignAsset({
  assetName,
  assignmentId,
  assetStatus,
  assignment,
  userName,
  users,
}: UnassignAssetProps): React.JSX.Element {
  /**
   * Handles the unassignment of the asset.
   */
  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<AssetUnAssignmentFormData>();
  const handleUnassignAsset = async (formData: FormData): Promise<void> => {
    formData.append('unAssignedDate', new Date().toISOString());

    const notifiedUsers = reduceFormData('notifyUser', formData);
    const notifyUser = notifiedUsers.map((userId) => ({ id: userId }));

    const unAssignDetails = {
      ...Object.fromEntries(formData),
      notifyUser,
    };

    const parsedResult = assetUnAssignmentFormSchema.safeParse(unAssignDetails);

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      toast.error(INPUT_VALUES_ERROR_MESSAGE);
      return;
    }
    resetFieldErrors();
    const unassignResponse = await unassignAsset(
      assignmentId,
      parsedResult.data,
    );

    if (unassignResponse.type === 'error') {
      if (unassignResponse.errors.errorMessages) {
        unassignResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error(GENERIC_ERROR_MESSAGE);
      return;
    }

    toast.success(`${assetName} unassigned successfully`);
  };
  return (
    <UnAssignEntityForm
      assignment={assignment}
      entityName={assetName}
      entityStatus={assetStatus}
      entityType="Asset"
      handleSubmit={handleUnassignAsset}
      userName={userName}
      users={users}
      errors={fieldErrors}
      resetFieldErrors={resetFieldErrors}
    />
  );
}
