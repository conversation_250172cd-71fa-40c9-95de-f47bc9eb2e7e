'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatISO, parseISO } from 'date-fns';
import { toast } from 'sonner';
import { useFieldErrors } from '@/hooks/use-field-errors';
import { createService } from '@/services/services';
import { EntityType, type Option, type ServiceRating } from '@/types';
import { uploadFiles } from '@/services/file-upload';
import { generateCloudFrontUrl } from '@/utils/helper-functions';
import type { FieldGroupDataType } from '@/types/custom-field';
import type { ServicesFormData } from '@/types/services';
import type { SelectedData } from '@/utils/custom-field';
import {
  formattedCustomFields,
  getUniqueCustomFields,
} from '@/utils/custom-field';
import { ServicesRequestSchema } from '@/schemas/services';
import { AssetServiceForm } from '../AssetServiceForm/asset-service-form';

interface ApplianceServiceProps {
  /**
   * The unique identifier for the asset service.
   */
  id: string;
  /**
   * An array of options representing the suppliers.
   */
  suppliers: Option[];
  /**
   * Array of options representing field groups which is used to categorize or group fields in the form.
   */
  fieldGroups: Option[] | undefined;
  /**
   * Array of data related to field groups, used to dynamically generate or prepopulate form fields.
   */
  fieldGroupDatas: FieldGroupDataType[] | null | undefined;
}

export function AssetService({
  id,
  suppliers,
  fieldGroups,
  fieldGroupDatas,
}: ApplianceServiceProps): React.JSX.Element {
  const router = useRouter();
  const [isImage, setIsImage] = useState(true);
  const [imageUrls, setImageUrls] = useState<File[]>([]);
  const [rating, setRating] = useState<ServiceRating | undefined>(undefined);
  const [selectedData, setSelectedData] = useState<SelectedData | undefined>();

  const uniqueCustomFields = getUniqueCustomFields(
    fieldGroupDatas,
    selectedData,
  );
  const toggleRating = (currentRating: ServiceRating): void => {
    setRating((prevRating) =>
      prevRating === currentRating ? undefined : currentRating,
    );
  };

  const { fieldErrors, setFieldErrors, resetFieldErrors } =
    useFieldErrors<ServicesFormData>();
  const addService = async (formData: FormData): Promise<void> => {
    const customFieldData = formattedCustomFields(formData);
    formData.append('rating', rating ?? '');
    const { date, nextServiceDate, ...rest } = Object.fromEntries(
      formData.entries(),
    );

    const formDataObject = {
      ...rest,
      date: date && typeof date === 'string' ? formatISO(parseISO(date)) : '',
      nextServiceDate:
        nextServiceDate && typeof nextServiceDate === 'string'
          ? formatISO(parseISO(nextServiceDate))
          : '',
      rating: rating ?? null,
      type: 'ASSET',
      applianceIds: [],
      serviceImageUrl: [],
      assetIds: [id],
      customFields: customFieldData,
    };

    const parsedResult = ServicesRequestSchema.safeParse(formDataObject);

    if (!parsedResult.success) {
      setFieldErrors(parsedResult.error.flatten().fieldErrors);
      return;
    }
    resetFieldErrors();

    const uploadedServiceImageUrl: string[] = [];
    if (isImage && imageUrls.length > 0) {
      const newFormData = new FormData();
      imageUrls.forEach((file) => {
        newFormData.append('file', file);
      });
      const imageUploadResponse = await uploadFiles(
        newFormData,
        EntityType.Services,
      );

      if (imageUploadResponse.type === 'error') {
        if (imageUploadResponse.errors.errorMessages) {
          imageUploadResponse.errors.errorMessages.forEach((err) =>
            toast.error(err),
          );
          return;
        }
        toast.error('something went wrong');
        return;
      }
      for (const image of imageUploadResponse.data.fileName) {
        const ImageUrl = generateCloudFrontUrl(EntityType.Services, image);
        uploadedServiceImageUrl.push(ImageUrl);
      }
    }
    const createAsseteData = {
      ...parsedResult.data,
      serviceImageUrl: uploadedServiceImageUrl,
    };

    const assetServiceResponse = await createService(createAsseteData);

    if (assetServiceResponse.type === 'error') {
      if (assetServiceResponse.errors.errorMessages) {
        assetServiceResponse.errors.errorMessages.forEach((err) =>
          toast.error(err),
        );
        return;
      }
      toast.error('Something went wrong!');
      return;
    }

    toast.success('Service created successfully');
    router.back();
  };
  return (
    <div className="shadow-container mx-auto h-fit w-1/2">
      <h1 className="asset-management-form-heading">
        Create Service
      </h1>
      <AssetServiceForm
        customFields={uniqueCustomFields}
        errors={fieldErrors}
        fieldGroups={fieldGroups}
        handleRating={toggleRating}
        id={id}
        imageUrls={imageUrls}
        onSubmit={addService}
        rating={rating}
        setImageUrls={setImageUrls}
        setIsImage={setIsImage}
        setSelectedData={setSelectedData}
        suppliers={suppliers}
        mode="create"
      />
    </div>
  );
}
