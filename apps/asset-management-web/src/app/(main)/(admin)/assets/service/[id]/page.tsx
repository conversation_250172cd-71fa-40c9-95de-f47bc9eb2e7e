import { getAllSuppliersOptions } from '@/services/suppliers';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { AssetService } from './asset-service';

interface AssetServiceProps {
  params: {
    id: string;
  };
}
export default async function CreateAssetServicePage({
  params: { id },
}: AssetServiceProps): Promise<React.JSX.Element> {
  const suppliers = await getAllSuppliersOptions();
  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  const fieldGroups = await getAllFieldGroupsOptions();

  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }

  return (
    <AssetService
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      id={id}
      suppliers={suppliers.data}
    />
  );
}
