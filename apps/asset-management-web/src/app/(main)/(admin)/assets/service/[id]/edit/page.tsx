import { format, parseISO } from 'date-fns';
import { getAllSuppliersOptions } from '@/services/suppliers';
import {
  getAllFieldGroups,
  getAllFieldGroupsOptions,
} from '@/services/custom-field';
import {
  ERROR_MESSAGE_FIELD_GROUP,
  ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED,
} from '@/constants';
import { getServiceById } from '@/services/services';
import { ServicesFormData } from '@/types/services';
import { capitalizeFirstLetter } from 'utils';
import EditAssetService from './edit-asset-service';

interface AssetServiceProps {
  params: {
    id: string;
  };
}

export default async function CreateAssetServicePage({
  params: { id },
}: AssetServiceProps): Promise<React.JSX.Element> {
  const suppliers = await getAllSuppliersOptions();
  const serviceInfo = await getServiceById(id);
  const fieldGroups = await getAllFieldGroupsOptions();
  const fieldGroupDatas = await getAllFieldGroups();
  if (fieldGroups.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP_LOAD_FAILED}</>;
  }

  if (fieldGroupDatas.type === 'error') {
    return <>{ERROR_MESSAGE_FIELD_GROUP}</>;
  }
  if (serviceInfo.type === 'error') {
    return <>Not able to load services. Please try again!</>;
  }
  if (suppliers.type === 'error') {
    return <>Not able to load suppliers. Please try again!</>;
  }

  const serviceData = serviceInfo.data;

  const {
    date,
    nextServiceDate,
    cost,
    contactName,
    contactNumber,
    rating,
    serviceImageUrl,
    supplier,
    appliances,
    assets,
    type,
    ...rest
  } = serviceData;

  const initialValues: ServicesFormData = {
    ...rest,
    date: format(parseISO(date), 'yyyy-MM-dd'),
    nextServiceDate: nextServiceDate
      ? format(parseISO(nextServiceDate), 'yyyy-MM-dd')
      : undefined,
    cost: cost ?? undefined,
    contactName: contactName ?? undefined,
    contactNumber: contactNumber ?? undefined,
    rating: rating ?? undefined,
    serviceImageUrl: serviceImageUrl ?? undefined,
    supplierId: supplier?.id ?? undefined,
    applianceIds: appliances.map((appliance) => appliance?.id ?? ''),
    assetIds: assets.map((asset) => asset?.id ?? ''),
    customFields: rest.customFields ? rest.customFields : {},
    type: capitalizeFirstLetter(type),
  };

  return (
    <EditAssetService
      id={serviceData.id}
      fieldGroupDatas={fieldGroupDatas.data}
      fieldGroups={fieldGroups.data}
      suppliers={suppliers.data}
      initialValues={initialValues}
      mode="edit"
    />
  );
}
