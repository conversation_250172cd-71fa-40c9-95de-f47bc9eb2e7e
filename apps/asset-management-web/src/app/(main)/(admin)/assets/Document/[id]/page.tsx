import { UploadDocument } from '@/components/UploadDocument/upload-document';
import { CategoryType, EntityType } from '@/types';

interface DocumentProps {
  params: {
    id: string;
  };
}

export default function Document({
  params: { id },
}: DocumentProps): JSX.Element {
  return (
    <UploadDocument
      categoryType={CategoryType.ASSET}
      entityType={EntityType.Asset}
      id={id}
    />
  );
}
