import { Selector } from 'ui';
import { CreateButton, Search } from '@/components/client';
import { getAllAssets } from '@/services/assets';
import { assetsRoutes } from '@/constants/routes';
import {
  EntityType,
  type GetAllQueryParams,
  type SearchParams,
  type TableHeading,
} from '@/types';
import { getNumberOfPages } from '@/utils/helper-functions';
import { initialRowsPerPage } from '@/constants';
import { Pagination } from '@/components/Pagination/pagination';
import { getActionColumnByRole } from '@/services/roles';
import { getAllStatus } from '@/services/status';
import { getAllAssignments } from '@/services/assignments';
import { getAllCategories } from '@/services/categories';
import { MultiSelectOptions } from '@/components/MultiSelectOptions/multi-select-options';
import { DownloadDocument } from '@/components/DownloadDocument/download-document';
import AssetTable from './asset-table';
import { getAllLocations } from '@/services/locations';

interface AssetsSearchParams extends SearchParams {
  status?: string;
  location?: string;
  category?: string[];
}

type AssignedAssets = Record<string, string>;

export default async function AssetPage({
  searchParams,
}: {
  searchParams?: AssetsSearchParams;
}): Promise<React.JSX.Element> {
  const tableHeaders: TableHeading[] = [
    { title: 'Name' },
    { title: 'Assignee' },
    { title: 'Tag' },
    { title: 'Serial No' },
    { title: 'Status' },
    { title: 'Model' },
    { title: 'Location' },
    { title: 'Acknowledged on' },
    { title: 'Category' },
  ];
  const renderAction = await getActionColumnByRole('assets', [
    'create',
    'update',
    'delete',
  ]);
  renderAction ? tableHeaders.push(renderAction) : null;
  const query = searchParams?.query || '';

  let category: string[] = [];
  if (searchParams && Array.isArray(searchParams.category)) {
    category = searchParams.category;
  } else if (searchParams && typeof searchParams.category === 'string') {
    category = [searchParams.category];
  }

  const queryParams: GetAllQueryParams = {
    searchInput: query,
    location: searchParams?.location ?? '',
    status: searchParams?.status ?? '',
    category,
    page: Number(searchParams?.page ?? '1'),
    limit: Number(searchParams?.limit ?? initialRowsPerPage.toString()),
  };

  const assets = await getAllAssets(queryParams);
  const statusResponse = await getAllStatus();
  const assignments = await getAllAssignments({ typeOfEntity: 'ASSET' });
  const categories = await getAllCategories();
  const locations = await getAllLocations();
  // An object which contains the acknowledged asset id as the key and the acknowledged date as its value.
  const assignedAssets: AssignedAssets =
    assignments.type === 'success'
      ? assignments.data?.reduce((acc, asset) => {
          if (!asset.isPending && asset.entityId && asset.updatedAt) {
            acc[asset.entityId] = asset.updatedAt;
          }
          return acc;
        }, {} as AssignedAssets) || {}
      : {};

  if (assets.type === 'error') {
    return <>Not able to load/get asset, please try again!</>;
  }

  if (statusResponse.type === 'error') {
    return <>Not able to load/get status, please try again!</>;
  }
  if (categories.type === 'error') {
    return <>Not able to load/get asset, please try again!</>;
  }
  if (locations.type === 'error') {
    return <>Not able to load locations, please try again!</>;
  }
  const categoryList = categories.data
    ?.filter((data) => data.typeOfCategory === 'ASSET')
    .map((item) => item.name);

  const statusList = statusResponse.data?.map((status) => status.name);
  const locationList = locations.data.map((location) => location.name);
  const totalPages = getNumberOfPages(assets.count, searchParams?.limit);

  return (
    <section className="page">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-semibold text-slate-600">Assets</h1>
        <div className="flex gap-3">
          <Selector
            options={locationList}
            placeholder="location"
            queryParam="location"
          />
          {statusList && statusList.length > 0 ? (
            <Selector
              options={statusList}
              placeholder="status"
              queryParam="status"
            />
          ) : null}
          {categoryList && categoryList.length > 0 ? (
            <MultiSelectOptions
              id="category-selector"
              name="category"
              options={categoryList}
              placeholder="All Category"
              queryParam="category"
              selectedValues={category}
            />
          ) : null}
          <Search
            className="w-[18.5rem]"
            placeholder="Search name, tag, model, serial no"
          />
          <CreateButton href={assetsRoutes.CREATE} label="Create Asset" />
          <DownloadDocument entityType={EntityType.Asset} />
        </div>
      </div>
      <AssetTable
          key={JSON.stringify(assets.data)}
          tableHeaders={tableHeaders}
          assets={assets.data}
          assignedAssets={assignedAssets}
          renderAction={renderAction}
        />
        <div className="pagination">
        <Pagination
          dataCount={assets.count ?? 0}
          totalPages={Math.round(totalPages)}
        />
      </div>
    </section>
  );
}
