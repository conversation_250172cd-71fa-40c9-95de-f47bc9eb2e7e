# MONOREPO Version

FROM node:18-alpine AS base
RUN npm i -g pnpm@8.9.0


FROM base AS builder
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat openssl 
RUN apk update
RUN npm i -g turbo
WORKDIR /app

COPY . .
RUN turbo prune asset-management-backend --docker


FROM base AS installer
RUN apk add --no-cache libc6-compat openssl
RUN apk update
WORKDIR /app

COPY .gitignore .gitignore
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml

RUN pnpm install
COPY --from=builder /app/out/full/ .
COPY turbo.json turbo.json

ENV NODE_ENV production
RUN pnpm --prefix packages/@prisma-clients/asset-management-backend db:generate
RUN pnpm --prefix packages/@prisma-clients/timesheet-backend db:generate
RUN pnpm build --filter=asset-management-backend
RUN rm -rf apps/asset-management-backend/dist/auth/dev
RUN find /app -type d -name "node_modules" -exec rm -rf {} +



FROM base AS runner
WORKDIR /app
RUN apk add --no-cache libc6-compat openssl

COPY --from=installer --chown=node:node /app .
RUN pnpm install --prod --ignore-scripts
RUN pnpm --prefix packages/@prisma-clients/asset-management-backend db:generate
RUN pnpm --prefix packages/@prisma-clients/timesheet-backend db:generate


CMD [ "node", "apps/asset-management-backend/dist/src/main" ]









# ###################
# # BUILD FOR PRODUCTION
# ###################

# FROM node:18-alpine As builder
# RUN npm i -g pnpm
# WORKDIR /home/<USER>/app

# COPY --chown=node:node package*.json ./
# RUN pnpm install

# COPY --chown=node:node . .

# ENV NODE_ENV production

# RUN pnpm dlx prisma generate
# RUN pnpm build

# # RUN npm ci --only=production && npm cache clean --force

# ###################
# # PRODUCTION
# ###################
# FROM node:18-alpine As production

# WORKDIR /home/<USER>/app

# COPY --chown=node:node --from=builder /home/<USER>/app/node_modules ./node_modules
# COPY --chown=node:node --from=builder /home/<USER>/app/dist ./dist
# COPY --chown=node:node --from=builder /home/<USER>/app/prisma ./prisma

# RUN mkdir /home/<USER>/app/files
# RUN chown node:node /home/<USER>/app/files

# CMD [ "node", "dist/src/main" ]
