# List of stages for jobs, and their order of execution
stages:
  - build
  - test
  - deploy

variables:
  AWS_REGION: ap-south-1
  AWS_ACCOUNT: ************

# # Define the job for building
.AM-docker-build-template:
  image: bentolor/docker-dind-awscli:latest
  stage: build
  variables:
    AWS_ACCESS_KEY_ID: '${AWS_ACCESS_KEY_ID}'
    AWS_SECRET_ACCESS_KEY: '${AWS_SECRET_ACCESS_KEY}'
  services:
    - name: docker:dind
  before_script:
    - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com
  script:
    - aws_registry_image="${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/asset-management-backend"
    - tag=":$CI_COMMIT_REF_NAME"
    - docker build --pull --platform linux/amd64 -t "${aws_registry_image}${tag}" -f apps/asset-management-backend/Dockerfile .
    - docker push "${aws_registry_image}${tag}"

AM-backend-docker-build-dev:
  extends: .AM-docker-build-template
  rules:
    - if: '$CI_COMMIT_REF_PROTECTED == "true" && $CI_COMMIT_TAG == null'
      changes:
        - apps/asset-management-backend/**/*

AM-backend-docker-build-prod:
  extends: .AM-docker-build-template
  rules:
    - if: '$CI_COMMIT_TAG =~ /^asset-management-backend-v[0-9]+\.[0-9]+\.[0-9]+$/ && $CI_COMMIT_REF_PROTECTED == "true"'

AM-backend-pnpm-build:
  stage: build
  before_script:
    - npm i -g pnpm@8.9.0
  script:
    - echo "Building backend app.."
    - pnpm install
    - pnpm --prefix packages/@prisma-clients/asset-management-backend db:generate
    - pnpm --prefix packages/@prisma-clients/timesheet-backend db:generate
    - pnpm build --filter=asset-management-backend
  rules:
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - apps/asset-management-backend/**/*

AM-api-docker-build:
  image: bentolor/docker-dind-awscli:latest
  stage: build
  services:
    - name: docker:dind
  script:
    - docker build -f ./apps/asset-management-backend/Dockerfile .
  rules:
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - apps/asset-management-backend/**/*

# Define the job for running tests
AM-backend-unit-test:
  stage: test
  before_script:
    - npm i -g pnpm@8.11.0
  script:
    - echo "Testing backend unit job"
    - pnpm install
    - pnpm --prefix packages/@prisma-clients/asset-management-backend db:generate
    - pnpm --prefix packages/@prisma-clients/timesheet-backend db:generate
    - pnpm build --filter=asset-management-backend
    - pnpm --prefix apps/asset-management-backend test
  rules:
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - apps/asset-management-backend/**/*

# Define the job for deploy
AM-api-deploy-development:
  image: linuxserver/openssh-server:version-9.3_p2-r0
  stage: deploy
  before_script:
    - chmod 600 $AM_AWS_DEV_PRIVATE_KEY
  script:
    - ssh -o StrictHostKeyChecking=no -i $AM_AWS_DEV_PRIVATE_KEY -p 2222 ec2-user@$AM_AWS_DEV_INSTANCE_IP "cd /home/<USER>/asset-management-docker/api && ./update.sh update"
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
      changes:
        - apps/asset-management-backend/**/*
      when: manual

AM-api-deploy-production:
  image: linuxserver/openssh-server:version-9.3_p2-r0
  stage: deploy
  before_script:
    - chmod 600 $AM_AWS_PROD_PRIVATE_KEY
  script:
    - ssh -o StrictHostKeyChecking=no -i $AM_AWS_PROD_PRIVATE_KEY -p 2222 ec2-user@$AM_AWS_PROD_INSTANCE_IP "echo $CI_COMMIT_REF_NAME>/home/<USER>/asset-management-docker-prod/api/image-tag"
    - ssh -o StrictHostKeyChecking=no -i $AM_AWS_PROD_PRIVATE_KEY -p 2222 ec2-user@$AM_AWS_PROD_INSTANCE_IP "cd /home/<USER>/asset-management-docker-prod/api && ./update.sh update"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^asset-management-backend-v[0-9]+\.[0-9]+\.[0-9]+$/ && $CI_COMMIT_REF_PROTECTED == "true"' # For production
      when: manual
