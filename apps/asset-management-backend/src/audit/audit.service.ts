import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  Audit,
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  getUpdatedFields,
  setDateWithZeroTime,
} from 'src/utility';
import {
  CreateAuditDto,
  GetAllAuditsResponseDto,
  GetAuditResponseDto,
  UpdateAuditDto,
} from './dto/audit.dto';
import { User } from 'types';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import {
  AUDIT_DATE_EXISTS,
  AUDIT_NOT_FOUND,
} from 'src/constants/message-constants';
import { isUUID } from 'class-validator';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class AuditService {
  private logger = new Logger('AuditService');
  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  private selectArgs = {
    id: true,
    lastAuditDate: true,
    nextAuditDate: true,
    asset: {
      select: {
        id: true,
        assetName: true,
      },
    },
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    auditImageUrls: true,
    note: true,
  };

  /**
   * This method creates a audit based on the provided DTO if not already exists.
   * Also creates a record in history table for the created audit.
   *
   * @param {CreateAuditDto} dto The DTO (Data Transfer Object) containing audit details.
   * @returns {Promise<GetAuditResponseDto>} A Promise that resolves to an auditResponsePayload representing the created audit.
   * @throws ConflictException if a audit with the same name already exists.
   * @throws NotFoundException if the supplier, audit, or category with the specified IDs are not found.
   */
  async createAudit(
    entityId: string,
    dto: CreateAuditDto,
    user: User,
  ): Promise<GetAuditResponseDto> {
    try {
      if (dto.lastAuditDate > dto.nextAuditDate) {
        throw new BadRequestException(
          `next Audit date must be greater than last audit date`,
        );
      }
      const audit = await this.prisma.audit.findFirst({
        where: {
          isDeleted: false,
          assetId: dto.assetId,
          nextAuditDate: setDateWithZeroTime(dto.nextAuditDate),
        },
      });
      if (audit) {
        this.logger.error(
          'Found a duplicate audit with the same next audit date',
        );
        throw new BadRequestException(
          'Found a duplicate audit with the same next audit date',
        );
      }
      const AuditTransaction: GetAuditResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const createdAudit: GetAuditResponseDto = await prisma.audit.create({
            data: {
              lastAuditDate: new Date(),
              nextAuditDate: setDateWithZeroTime(dto.nextAuditDate),
              asset: dto.assetId
                ? {
                    connect: {
                      id: entityId,
                    },
                  }
                : undefined,
              location: dto.location
                ? {
                    connect: {
                      id: dto.location,
                    },
                  }
                : undefined,
              auditImageUrls: dto.auditImageUrls,
              note: dto.note,
            },
            select: this.selectArgs,
          });

          this.logger.log(
            `Audit with id: ${createdAudit.id} created successfully`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.AUDIT,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdAudit.id,
              log: {
                userId: user.id,
                name: user.name,
                auditId: createdAudit.id,
              },
            },
          });

          this.logger.log("History for 'create' created successfully");

          return createdAudit;
        });

      return AuditTransaction;
    } catch (error) {
      //If something went wrong, delete new uploaded Image.
      if (dto.auditImageUrls && dto.auditImageUrls.length > 0) {
        for (const image of dto.auditImageUrls)
          this.awsService.deleteFile(image);
      }
      this.logger.log(
        'Asset-Audit image uploaded on s3 bucket deleted successfully',
      );
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create audit: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves a paginated list of audits based on optional query parameters.
   * It applies pagination, sorting, and search functionality if provided in the query parameters.
   *
   * @param {GetAllAuditQueryParamsDto} [dto] (Optional) The DTO containing query parameters for retrieving audits.
   * @returns {Promise<GetAllAuditsResponseDto['data']>} A Promise that resolves to an GetAllauditsResponse['data'] containing a paginated list of audits and the total count.
   */
  async getAllAudits(
    dto?: GetAllQueryParamsDto,
  ): Promise<GetAllAuditsResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | null = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
    };

    const whereOptions: Prisma.AuditWhereInput = {
      assetId: dto?.searchInput
        ? {
            equals: dto?.searchInput,
            mode: 'insensitive',
          }
        : undefined,
      isDeleted: false,
    };

    const [audits, count] = await this.prisma.$transaction([
      this.prisma.audit.findMany({
        where: whereOptions,
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.audit.count({
        where: whereOptions,
      }),
    ]);
    const auditHistories = await this.prisma.history.findMany({
      where: {
        changeInTable: ChangesOcccuredIn.AUDIT,
      },
    });
    const auditsWithUser = audits.map((audit) => {
      const userInfo = auditHistories
        .filter((history) => audit.id === history.entityId)
        .map((history) => history.log['name']);
      return {
        ...audit,
        createdBy: userInfo.length ? userInfo[0] : undefined,
      };
    });
    this.logger.log(`Fetched ${count} audit/s successfully`);

    return {
      audits: auditsWithUser,
      count,
    };
  }

  /**
   * This method retrives audit with the specified ID that is not deleted.
   *
   * @param {AssetIdDto['id']} assetId The ID of the audit to retrieve.
   * @returns {Promise<GetAuditResponseDto>} A Promise that resolves to an auditResponsePayload representing the retrieved audit.
   * @throws NotFoundException if the audit with the specified ID is not found or has been deleted.
   */
  async getAuditById(assetId: string): Promise<GetAuditResponseDto> {
    const audit: GetAuditResponseDto = await this.prisma.audit.findFirst({
      where: {
        assetId: assetId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });

    if (!audit) {
      this.logger.log(`audit with id: ${assetId} not found`);
      throw new NotFoundException(AUDIT_NOT_FOUND);
    }

    return audit;
  }

  /**
   * This method verifies the existence of the audit
   * by its ID and updates the audit with the provided data.
   *
   * @param {AssetIdDto['id']} assetId The ID of the audit to update.
   * @param {UpdateAuditDto} dto The DTO containing updated audit details.
   * @returns {Promise<GetAuditResponseDto>} A Promise that resolves to an AuditResponsePayload representing the updated audit.
   * @throws NotFoundException if the audit with the specified ID is not found or has been deleted.
   * @throws NotFoundException IDs are not found.
   */
  async updateAudit(
    assetId: string,
    dto: UpdateAuditDto,
    user: User,
  ): Promise<GetAuditResponseDto> {
    try {
      const auditTransaction: GetAuditResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const audit = await prisma.audit.findFirst({
            where: {
              assetId: assetId,
              isDeleted: false,
            },
            include: {
              asset: {
                select: {
                  id: true,
                  assetName: true,
                },
              },
              location: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          });

          if (!audit) {
            this.logger.log(`audit with id: ${assetId} not found`);
            throw new NotFoundException(AUDIT_NOT_FOUND);
          }

          const auditCount: number = await prisma.audit.count({
            where: {
              OR: [
                { lastAuditDate: dto.lastAuditDate },
                { nextAuditDate: dto.nextAuditDate },
              ],
              isDeleted: false,
              id: { not: assetId },
            },
          });

          if (auditCount) {
            this.logger.log('Audit Date already exists');
            throw new ConflictException(AUDIT_DATE_EXISTS);
          }

          const { location, ...rest } = dto;
          const updatedAudit = await prisma.audit.update({
            where: {
              id: audit.id,
            },
            data: {
              locationId: location,
              ...rest,
              lastAuditDate: new Date(),
            },
            select: this.selectArgs,
          });

          this.logger.log(`Audit with id: ${audit.id} updated successfully`);

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.AUDIT,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedAudit.id,
              log: {
                name: user.name,
                userId: user.id,
                auditId: updatedAudit.id,
                updatedFields: getUpdatedFields(audit, updatedAudit),
              },
            },
          });

          this.logger.log("History for 'update' created successfully");

          return updatedAudit;
        });

      return auditTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update audit: ${error}`);
      throw error;
    }
  }

  /**
   * This method marks the audit as deleted in the database by setting the 'isDeleted' flag to true,
   * if the audit exists
   *
   * @param {AuditIdDto['id']} auditId The ID of the audit to delete.
   * @returns {Promise<boolean>} A Promise that resolves to an boolean value representing the status of delete operation.
   * @throws NotFoundException if the audit with the specified ID is not found or has already been deleted.
   */
  async deleteAudit(auditId: string, user: User): Promise<boolean> {
    const audit: number = await this.prisma.audit.count({
      where: {
        id: auditId,
        isDeleted: false,
      },
    });

    if (!audit) {
      this.logger.log(`Audit with specified id not found`);
      throw new NotFoundException(AUDIT_NOT_FOUND);
    }

    const deletedAudit: Audit = await this.prisma.audit.update({
      where: {
        id: auditId,
      },
      data: {
        isDeleted: true,
      },
    });

    this.logger.log('Audit isDeleted updated to true successfully');

    await this.prisma.history.create({
      data: {
        changeInTable: ChangesOcccuredIn.AUDIT,
        action: HistoryActions.DELETED,
        date: new Date(),
        entityId: deletedAudit.id,
        log: {
          userId: user.id,
          name: user.name,
          auditId: deletedAudit.id,
        },
      },
    });

    this.logger.log("History for 'delete' created sucessfully");
    return true;
  }

  /**
   * An injectable service method that fetches purchase details for the given entity ID.
   *
   * @param {string} assetId - The ID of the entity to fetch purchase details for.
   * @param {GetAllQueryParamsDto} queryParams - The query parameters for pagination and sorting.
   * @returns {Promise<PurchaseResponseDto[]>} A Promise that resolves to an array of Purchase details.
   * @throws {BadRequestException} Throws a BadRequestException if there's an error parsing the entity ID.
   */
  async getAuditDetails(
    assetId: string,
    queryParams?: GetAllQueryParamsDto,
  ): Promise<GetAllAuditsResponseDto> {
    if (!isUUID(assetId)) {
      this.logger.log(`Invalid UUID format for assetId: ${assetId}`);
      throw new BadRequestException('Invalid UUID format');
    }

    const count = await this.prisma.audit.count({
      where: { assetId, isDeleted: false },
    });

    const page: number = queryParams?.page ?? 1;
    const limit: number = queryParams?.limit ?? 10;
    const skip: number = (page - 1) * limit;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    // TODO: Implement search functionality

    const audits = await this.prisma.audit.findMany({
      where: { assetId, isDeleted: false },
      select: this.selectArgs,
      orderBy,
      take: limit,
      skip,
    });
    const auditHistories = await this.prisma.history.findMany({
      where: {
        changeInTable: ChangesOcccuredIn.AUDIT,
      },
    });
    const auditsWithUser = audits.map((audit) => {
      const userInfo = auditHistories
        .filter((history) => audit.id === history.entityId)
        .map((history) => history.log['name']);
      return {
        ...audit,
        createdBy: userInfo.length ? userInfo[0] : undefined,
      };
    });
    this.logger.log(
      `Successfully fetched audit details for entityId: ${assetId}`,
    );

    return { audits: auditsWithUser, count };
  }
}
