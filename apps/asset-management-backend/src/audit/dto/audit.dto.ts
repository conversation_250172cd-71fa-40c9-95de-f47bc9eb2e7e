import { ApiProperty } from '@nestjs/swagger';
import {
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';

export class CreateAuditDto {
  @ApiProperty({
    description: 'last audit date',
    example: '2023-12-22T06:17:36.215Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  lastAuditDate: Date;

  @ApiProperty({
    description: 'next audit date',
    example: '2023-12-23T06:17:36.215Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  nextAuditDate: Date;

  @ApiProperty({
    description: 'Location of Audit',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  location?: string;

  @ApiProperty({
    description: 'Audit Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  assetId: string;

  @ApiProperty({
    description: 'URL for audit image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
    required: false,
  })
  @IsNotEmpty()
  auditImageUrls: string[];

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'This papers for used only for internal purposes...',
    required: false,
  })
  @IsOptional()
  note: string;
}

export class UpdateAuditDto extends CreateAuditDto {}

export class AuditIdDto {
  @ApiProperty({
    description: 'Audit Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: true,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  id: string;
}

export class GetAuditResponseDto {
  @ApiProperty({
    description: 'Audit Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'last audit date',
    type: 'Date',
    example: '2023-12-22T06:17:36.215Z',
  })
  lastAuditDate: Date;

  @ApiProperty({
    description: 'next audit date',
    type: 'Date',
    example: '2023-12-23T06:17:36.215Z',
  })
  nextAuditDate: Date;

  @ApiProperty({
    description: 'audit details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'asset-name',
    },
  })
  asset: {
    id: string;
    assetName: string;
  };

  @ApiProperty({
    description: 'Location of audit',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Bangalore',
    },
  })
  location: EntityDto;

  @ApiProperty({
    description: 'URL for audit image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
  })
  auditImageUrls: string[];

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'Include any additional information in this note....',
  })
  note: string;

  @ApiProperty({
    description: 'Audit created user',
    type: 'string',
    example: 'Justin B',
  })
  createdBy?: string;
}

export class GetAllAuditsResponseDto {
  @ApiProperty({
    description: 'Fetched audits response',
    isArray: true,
    type: GetAuditResponseDto,
  })
  audits: GetAuditResponseDto[];

  @ApiProperty({
    description: 'Total count of audits',
    type: 'number',
    example: 100,
  })
  count: number;
}
