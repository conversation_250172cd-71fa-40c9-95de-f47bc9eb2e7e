import { Test, TestingModule } from '@nestjs/testing';
import { AuditService } from './audit.service';
import { AppModule } from 'src/app.module';

describe('AuditService', () => {
  let service: AuditService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<AuditService>(AuditService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
