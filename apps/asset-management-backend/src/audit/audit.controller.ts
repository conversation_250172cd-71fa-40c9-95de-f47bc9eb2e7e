import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUI<PERSON>ip<PERSON>,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import {
  AUDIT_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { AuditService } from './audit.service';
import { GetAuditResponseDto, UpdateAuditDto } from './dto/audit.dto';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { <PERSON><PERSON>and<PERSON> } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Audit')
@Controller('audit')
@UseGuards(PermissionGuard)
export class AuditController {
  private logger = new Logger('AuditController');
  constructor(private readonly auditService: AuditService) {}

  @Get(':assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched audit',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetAuditResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'audit with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: AUDIT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch audit with specified Id',
    summary: 'Fetches audit with given Id',
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter assetId Id to fetch audit details',
  })
  @ApiExtraModels(HTTPResponseDto<GetAuditResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getAssignmentDetails(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
  ): Promise<HTTPResponseDto<GetAuditResponseDto>> {
    const auditDetails = await this.auditService.getAuditById(assetId);

    return {
      statusCode: HttpStatus.OK,
      data: auditDetails,
      message: 'Successfully fetched audit details',
    };
  }

  @Put(':auditId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated audit',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetAuditResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'audit with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: AUDIT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the audit details',
    summary: 'Update audit',
  })
  @ApiParam({
    name: 'auditId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter audit Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET))
  async auditAsset(
    @Param('auditId', new ParseUUIDPipe()) id: string,
    @Body() dto: UpdateAuditDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetAuditResponseDto>> {
    this.logger.log('API to update audit details');

    const { user } = request;
    const audit: GetAuditResponseDto = await this.auditService.updateAudit(
      id,
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: audit,
      message: 'Successfully updated audit',
    };
  }

  @Delete(':auditId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted audit Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: AUDIT_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad request response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the audit',
    summary: 'Delete audit',
  })
  @ApiParam({
    name: 'auditId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter audit Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.ASSET))
  async deleteAudit(
    @Param('auditId', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to soft delete audit');

    const { user } = request;
    const auditDeletedStatus: boolean = await this.auditService.deleteAudit(
      id,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: auditDeletedStatus,
      message: 'audit deleted successfully',
    };
  }
}
