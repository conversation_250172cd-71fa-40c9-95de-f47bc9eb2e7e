import { ApiProperty } from '@nestjs/swagger';
import {
  Currency,
  PaymentFrequency,
  Status,
} from '@prisma-clients/asset-management-backend';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class UpdateInsuranceDto {
  @ApiProperty({
    description: 'Name of the insurance',
    type: 'string',
    required: true,
    example: 'Acer nitro 5 insurance',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'The supplier ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsUUID()
  @IsOptional()
  vendorId: string;

  @ApiProperty({
    description: 'The insurance providing company ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsOptional()
  @IsUUID()
  companyId: string;

  @ApiProperty({
    description: 'The insurance start date',
    type: 'date',
    required: true,
  })
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    description: 'The insurance end date',
    type: 'date',
    required: true,
  })
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    description: 'The insurance amount',
    type: 'number',
    required: true,
    example: 2000,
  })
  @IsNumber()
  premiumAmount: number;

  @ApiProperty({
    description: 'Currency in which purchase is done',
    enum: Currency,
    example: Currency.USD,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency: Currency;

  @ApiProperty({
    description: 'The payment plans',
    enum: PaymentFrequency,
    example: PaymentFrequency.ANNUALLY,
  })
  @IsEnum(PaymentFrequency)
  @IsOptional()
  paymentFrequency: PaymentFrequency;

  @ApiProperty({
    description: 'The insurance coverage amount',
    type: 'number',
    required: true,
    example: 20000,
  })
  @IsNumber()
  coverageAmount: number;

  @ApiProperty({
    description: 'The insurance related info',
    type: 'string',
    example: 'Insurance applicable for only internal damage',
  })
  @IsOptional()
  notes: string;

  @ApiProperty({
    description: 'The insurance status',
    enum: Status,
    example: Status.ACTIVE,
  })
  @IsOptional()
  @IsEnum(Status)
  status: Status;
}

export class CreateInsuranceDto extends UpdateInsuranceDto {
  @ApiProperty({
    description: 'The insurance created user',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsOptional()
  @IsUUID()
  createdBy: string;
}

export class GetInsuranceResponseDto {
  @ApiProperty({
    description: 'The insurance ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the insurance',
    type: 'string',
    example: 'Acer nitro 5 insurance',
  })
  name: string;

  @ApiProperty({
    description: 'The supplier ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  vendor: { id: string; name: string };

  @ApiProperty({
    description: 'The company ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  company: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'The insurance start date',
    type: 'date',
  })
  startDate: Date;

  @ApiProperty({
    description: 'The insurance end date',
    type: 'date',
  })
  endDate: Date;

  @ApiProperty({
    description: 'The insurance amount',
    type: 'number',
    example: 2000,
  })
  premiumAmount: number;

  @ApiProperty({
    description: 'The payment plans',
    type: PaymentFrequency,
    example: PaymentFrequency.ANNUALLY,
  })
  paymentFrequency: PaymentFrequency;

  @ApiProperty({
    description: 'The insurance coverage amount',
    type: 'number',
    example: 2000,
  })
  coverageAmount: number;

  @ApiProperty({
    description: 'The insurance related info',
    type: 'string',
    example: 'Insurance applicable for only internal damage',
  })
  notes: string;

  @ApiProperty({
    description: 'The insurance created user',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  createdBy: { id: string; name: string };

  @ApiProperty({
    description: 'The insurance created date',
    type: 'date',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'The insurance status',
    enum: Status,
    example: Status.ACTIVE,
  })
  status: Status;

  @ApiProperty({
    description: 'Currency in which purchase is done',
    enum: Currency,
    example: Currency.INR,
  })
  currency: Currency;
}
export class GetAllInsuranceResponsesDto {
  @ApiProperty({
    description: 'Document response data',
    type: 'array',
    example: [GetInsuranceResponseDto],
    isArray: true,
  })
  data: GetInsuranceResponseDto[];

  @ApiProperty({
    description: 'Total count of insurances',
    type: 'number',
    example: 100,
  })
  count: number;
}
