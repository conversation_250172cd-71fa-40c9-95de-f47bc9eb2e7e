import { Module } from '@nestjs/common';
import { InsuranceController } from './insurance.controller';
import { InsuranceService } from './insurance.service';
import { AbilityModule } from 'src/ability/ability.module';
import { DocumentService } from 'src/document/document.service';

@Module({
  imports: [AbilityModule],
  controllers: [InsuranceController],
  providers: [InsuranceService, DocumentService],
})
export class InsuranceModule {}
