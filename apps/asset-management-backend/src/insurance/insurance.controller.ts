import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { InsuranceService } from './insurance.service';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  CreateInsuranceDto,
  GetAllInsuranceResponsesDto,
  GetInsuranceResponseDto,
  UpdateInsuranceDto,
} from './dtos/insurance.dto';
import { Request } from 'express';
import {
  INSURANCE_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@Controller('insurance')
@ApiTags('Insurance')
@UseGuards(PermissionGuard)
export class InsuranceController {
  constructor(private readonly insuranceService: InsuranceService) {}
  private logger = new Logger('InsuranceController');

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Insurance created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a insurance',
    summary: 'Create an insurance',
  })
  @ApiExtraModels(HTTPResponseDto<GetInsuranceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.INSURANCE))
  async createInsurance(
    @Body() dto: CreateInsuranceDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetInsuranceResponseDto>> {
    const { user } = request;
    this.logger.log('API to create insurance');
    const createdInsurance = await this.insuranceService.createInsurance(
      dto,
      user,
    );
    return {
      statusCode: HttpStatus.CREATED,
      message: 'An insurance created successfully',
      data: createdInsurance,
    };
  }

  @Delete(':insuranceId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted an insurance',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetInsuranceResponseDto>),
    },
  })
  @ApiParam({
    name: 'insuranceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter insurance Id to delete insurance data',
  })
  @ApiNotFoundResponse({
    description: 'Insurance with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: INSURANCE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete an insurance',
    summary: 'Delete an insurance',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.INSURANCE))
  async deleteInsurance(
    @Param('insuranceId', new ParseUUIDPipe()) insuranceId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    this.logger.log('API to delete an insurance');
    const isDeleted = await this.insuranceService.deleteInsurance(
      insuranceId,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      message: 'The insurance deleted successfully',
      data: isDeleted,
    };
  }

  @Get(':insuranceId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiFoundResponse({
    description: 'Successfully fetched an insurance',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetInsuranceResponseDto>),
    },
  })
  @ApiParam({
    name: 'insuranceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter insurance Id to fetch the data',
  })
  @ApiNotFoundResponse({
    description: 'Insurance with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: INSURANCE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch an insurance',
    summary: 'Fetch an insurance',
  })
  @ApiExtraModels(HTTPResponseDto<GetInsuranceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.INSURANCE))
  async getInsuranceById(
    @Param('insuranceId', new ParseUUIDPipe()) insuranceId: string,
  ): Promise<HTTPResponseDto<GetInsuranceResponseDto>> {
    this.logger.log('API to fetch an insurance');
    const insurance = await this.insuranceService.getInsuranceById(insuranceId);
    return {
      statusCode: HttpStatus.FOUND,
      message: 'An insurance fetched successfully',
      data: insurance,
    };
  }

  @Put(':insuranceId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated an insurance',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetInsuranceResponseDto>),
    },
  })
  @ApiParam({
    name: 'insuranceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter an insurance Id to update the data',
  })
  @ApiNotFoundResponse({
    description: 'Insurance with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: INSURANCE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to update an insurance',
    summary: 'Update an insurance',
  })
  @ApiExtraModels(HTTPResponseDto<GetInsuranceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.INSURANCE))
  async updateInsurance(
    @Param('insuranceId', new ParseUUIDPipe()) insuranceId: string,
    @Body() dto: UpdateInsuranceDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetInsuranceResponseDto>> {
    const { user } = request;
    this.logger.log('API to update an insurance');
    const updatedInsurance = await this.insuranceService.updateInsurance(
      insuranceId,
      dto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      message: 'An insurance updated successfully',
      data: updatedInsurance,
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched all insurances',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetAllInsuranceResponsesDto>),
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all insurances',
    summary: 'Fetch all insurances',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.INSURANCE))
  async getAllInsurances(
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetInsuranceResponseDto[]>> {
    this.logger.log('API to fetch all insurances');
    const insurances = await this.insuranceService.getAllInsurances(dto);
    return {
      statusCode: HttpStatus.OK,
      message: `Fetched ${insurances.count} insurances successfully`,
      data: insurances.data,
      count: insurances.count,
    };
  }

  @Get('history/:insuranceId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched all history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetAllInsuranceResponsesDto>),
    },
  })
  @ApiNotFoundResponse({
    description: 'Insurance with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: INSURANCE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all history',
    summary: 'Fetch all history',
  })
  @ApiExtraModels(HTTPResponseDto<GetEntityHistoryResponse>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.INSURANCE))
  async getAllInsuranceHistory(
    @Param('insuranceId', new ParseUUIDPipe()) insuranceId: string,
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<HTTPResponseDto<GetEntityHistoryResponse>> {
    this.logger.log('API to fetch all history');
    const history = await this.insuranceService.getAllInsuranceHistory(
      insuranceId,
      dto,
    );
    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully fetched the all history',
      data: history,
    };
  }
}
