import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateInsuranceDto,
  GetAllInsuranceResponsesDto,
  GetInsuranceResponseDto,
  UpdateInsuranceDto,
} from './dtos/insurance.dto';
import { User } from 'types';
import {
  checkErrorAndThrowNotFoundError,
  getUpdatedFields,
  setDateWithZeroTime,
} from 'src/utility';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { INSURANCE_NOT_FOUND } from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { DocumentService } from 'src/document/document.service';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';

@Injectable()
export class InsuranceService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly documentService: DocumentService,
  ) {}

  private logger = new Logger('InsuranceService');

  private selectArgs = {
    id: true,
    name: true,
    vendor: { select: { id: true, name: true } },
    company: { select: { id: true, name: true } },
    createdBy: { select: { id: true, name: true } },
    startDate: true,
    endDate: true,
    premiumAmount: true,
    coverageAmount: true,
    createdAt: true,
    notes: true,
    paymentFrequency: true,
    currency: true,
    status: true,
  };

  /**
   * Creates an insurance record with the provided data.
   * @param {CreateInsuranceDto} dto - The data transfer object containing insurance details.
   * @param {User} user - The user object representing the creator of the insurance.
   * @returns {Promise<GetInsuranceResponseDto>} A Promise resolving to the created insurance response DTO.
   * @throws {Error} If there is an error during the creation process.
   */
  async createInsurance(
    dto: CreateInsuranceDto,
    user: User,
  ): Promise<GetInsuranceResponseDto> {
    try {
      const insuranceTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const createdInsurance = await prisma.insurance.create({
            data: {
              name: dto.name,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
              company: dto.companyId
                ? {
                    connect: {
                      id: dto.companyId,
                    },
                  }
                : undefined,
              startDate: setDateWithZeroTime(dto.startDate),
              endDate: setDateWithZeroTime(dto.endDate),
              premiumAmount: dto.premiumAmount,
              coverageAmount: dto.coverageAmount,
              createdBy: {
                connect: {
                  id: user.id,
                },
              },
              notes: dto.notes,
              paymentFrequency: dto.paymentFrequency,
              currency: dto.currency,
              status: dto.status,
            },
            select: this.selectArgs,
          });
          this.logger.log(`An insurance created & ID: ${createdInsurance.id}`);
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.INSURANCE,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdInsurance.id,
              log: {
                userId: user.id,
                name: user.name,
                insuranceId: createdInsurance.id,
              },
            },
          });
          this.logger.log(
            `History for 'create insurance' created successfully`,
          );
          return createdInsurance;
        },
      );
      return insuranceTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create an insurance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Deletes an insurance record with the specified ID.
   * @param {string} insuranceId - The ID of the insurance record to delete.
   * @param {User} user - The user object representing the user performing the deletion.
   * @returns {Promise<boolean>} A Promise resolving to true if the deletion is successful.
   * @throws {NotFoundException} If the insurance record with the specified ID is not found.
   * @throws {Error} If there is an error during the deletion process.
   */
  async deleteInsurance(insuranceId: string, user: User): Promise<boolean> {
    const insuranceTransaction = await this.prisma.$transaction(
      async (prisma) => {
        const insurance = await prisma.insurance.findFirst({
          where: {
            id: insuranceId,
            isDeleted: false,
          },
        });
        if (!insurance) {
          this.logger.error(
            `An insurance not found with this ID: ${insuranceId}`,
          );
          throw new NotFoundException(INSURANCE_NOT_FOUND);
        }
        await prisma.insurance.update({
          where: {
            id: insuranceId,
          },
          data: {
            isDeleted: true,
          },
        });
        this.logger.log(
          `An insurance with the ID: ${insuranceId} is deleted successfully`,
        );
        const documentId = await prisma.document.findMany({
          where: {
            entityId: insuranceId,
          },
          select: {
            id: true,
          },
        });
        if (documentId) {
          documentId.map(async (document) => {
            await this.documentService.deleteDocument(document.id, user);
          });
          this.logger.log(
            `The documents related to insurance ID:${insuranceId} deleted successfully`,
          );
        }
        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.INSURANCE,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: insurance.id,
            log: {
              userId: user.id,
              name: user.name,
              insuranceId: insurance.id,
            },
          },
        });
        this.logger.log(`History for 'delete insurance' created successfully`);
        return true;
      },
    );
    return insuranceTransaction;
  }

  /**
   * Retrieves an insurance record by its ID.
   * @param {string} insuranceId - The ID of the insurance record to retrieve.
   * @returns {Promise<GetInsuranceResponseDto>} A Promise resolving to the retrieved insurance response DTO.
   * @throws {NotFoundException} If the insurance record with the specified ID is not found.
   * @throws {Error} If there is an error during the retrieval process.
   */
  async getInsuranceById(
    insuranceId: string,
  ): Promise<GetInsuranceResponseDto> {
    const insurance = await this.prisma.insurance.findFirst({
      where: {
        id: insuranceId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });
    if (!insurance) {
      this.logger.error(
        `An insurance not found with the specified ID: ${insuranceId}`,
      );
      throw new NotFoundException(INSURANCE_NOT_FOUND);
    }
    this.logger.log('An insurance fetched successfully');
    return insurance;
  }

  /**
   * Updates an existing insurance record with the specified ID using the provided data.
   * @param {string} insuranceId - The ID of the insurance record to update.
   * @param {UpdateInsuranceDto} dto - The data transfer object containing updated insurance details.
   * @param {User} user - The user object representing the user performing the update.
   * @returns {Promise<GetInsuranceResponseDto>} A Promise resolving to the updated insurance response DTO.
   * @throws {NotFoundException} If the insurance record with the specified ID is not found.
   * @throws {Error} If there is an error during the update process.
   */
  async updateInsurance(
    insuranceId: string,
    dto: UpdateInsuranceDto,
    user: User,
  ): Promise<GetInsuranceResponseDto> {
    try {
      const insuranceTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const insurance = await prisma.insurance.findFirst({
            where: {
              id: insuranceId,
              isDeleted: false,
            },
            select: this.selectArgs,
          });
          if (!insurance) {
            this.logger.error(
              `An insurance not found with the specified ID: ${insuranceId}`,
            );
            throw new NotFoundException(INSURANCE_NOT_FOUND);
          }
          const updatedInsurance = await prisma.insurance.update({
            where: {
              id: insuranceId,
            },
            data: {
              name: dto.name,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
              company: dto.companyId
                ? {
                    connect: {
                      id: dto.companyId,
                    },
                  }
                : undefined,
              startDate: setDateWithZeroTime(dto.startDate),
              endDate: setDateWithZeroTime(dto.endDate),
              premiumAmount: dto.premiumAmount,
              coverageAmount: dto.coverageAmount,
              notes: dto.notes,
              paymentFrequency: dto.paymentFrequency,
              currency: dto.currency,
              status: dto.status,
            },
            select: this.selectArgs,
          });
          this.logger.log('An insurance updated successfully');
          await prisma.history.create({
            data: {
              action: HistoryActions.UPDATED,
              changeInTable: ChangesOcccuredIn.INSURANCE,
              entityId: updatedInsurance.id,
              date: new Date(),
              log: {
                userId: user.id,
                name: user.name,
                insuranceId: updatedInsurance.id,
                updatedFields: getUpdatedFields(insurance, updatedInsurance),
              },
            },
          });
          this.logger.log(
            "History for 'update insurance' created successfully",
          );
          return updatedInsurance;
        },
      );
      return insuranceTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create an insurance: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves a list of insurances based on the provided query parameters.
   * @param {GetAllInsuranceQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetAllInsuranceResponsesDto>} A Promise resolving to an object containing the list of insurances and the total count.
   * @throws {Error} If there is an error during the retrieval process.
   */

  async getAllInsurances(
    params?: GetAllQueryParamsDto,
  ): Promise<GetAllInsuranceResponsesDto> {
    try {
      // Extract page and limit from the query parameters, setting them to undefined if not provided.
      const page: number | null = params?.page ? params.page : null;
      const limit: number | undefined = params?.limit
        ? params.limit
        : undefined;

      // Calculate the number of records to skip based on pagination parameters.
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Define the orderBy object for sorting based on query parameters.
      const orderBy = {
        [params?.sortBy || SORT_BY.CREATED_AT]:
          params?.sortOrder || SORT_ORDER.DESC,
      };

      const whereOptions: Prisma.InsuranceWhereInput = {
        isDeleted: false,
        ...(params?.searchInput
          ? {
              OR: [
                {
                  name: {
                    contains: params.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  vendor: {
                    name: { contains: params.searchInput, mode: 'insensitive' },
                  },
                },
                {
                  company: {
                    name: { contains: params.searchInput, mode: 'insensitive' },
                  },
                },
              ],
            }
          : {}),
      };

      // Fetching insurance with specified options, selected fields, sorting, and pagination.
      const insurances = await this.prisma.insurance.findMany({
        where: whereOptions,
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      });

      // Total count of insurance that match the specified criteria.
      const count = await this.prisma.insurance.count({
        where: whereOptions,
      });

      this.logger.log(`Fetched ${count} insurance successfully`);

      return { data: insurances, count };
    } catch (error) {
      this.logger.log(`Failed to fetch the insurance: ${error}`);
      throw error;
    }
  }
  /**
   * Retrieves the history of changes for a specific insurance entity based on the provided query parameters.
   * @param {string} insuranceId - The ID of the insurance entity to retrieve history for.
   * @param {GetAllQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetEntityHistoryResponse>} A Promise resolving to an object containing the history of changes and the total count.
   * @throws {NotFoundException} If the insurance entity with the specified ID is not found.
   * @throws {Error} If there is an error during the retrieval process.
   */
  async getAllInsuranceHistory(
    insuranceId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const insurance = await this.prisma.insurance.findFirst({
      where: {
        id: insuranceId,
        isDeleted: false,
      },
    });
    if (!insurance) {
      this.logger.error('An insurance not found for this ID');
      throw new NotFoundException(INSURANCE_NOT_FOUND);
    }
    const page: number | null = dto.page ? dto.page : undefined;
    const limit: number | null = dto.limit ? dto.limit : undefined;
    const skip: number | null = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
    };
    const [count, history] = await this.prisma.$transaction([
      this.prisma.history.count({
        where: {
          entityId: insuranceId,
        },
        take: limit,
        orderBy,
        skip,
      }),
      this.prisma.history.findMany({
        where: {
          entityId: insuranceId,
        },
        take: limit,
        orderBy,
        skip,
        select: {
          action: true,
          date: true,
          log: true,
        },
      }),
    ]);
    return {
      history,
      count,
    };
  }
}
