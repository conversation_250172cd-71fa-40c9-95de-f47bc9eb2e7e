import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { TypeOfCategory } from '@prisma/client/asset-management-backend';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'Name of the category.',
    type: 'string',
    example: 'Laptop',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Type of category',
    required: true,
    enum: TypeOfCategory,
    example: TypeOfCategory.ASSET,
  })
  @IsString()
  @IsEnum(TypeOfCategory)
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'Note for category',
    required: false,
    example: 'A portable computing device with a keyboard and screen.',
    type: 'string',
  })
  @IsString()
  @IsOptional()
  note: string;
}

export class GetCategoryDto {
  @ApiProperty({
    description: 'The unique identifier for the category',
    example: '073c2022-a405-456a-9d33-844e2c39ac58',
    type: 'uuid',
    required: true,
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Name of the category.',
    example: 'Laptop',
    required: true,
  })
  name: string;

  @ApiProperty({
    description: 'Type of category',
    required: true,
    enum: TypeOfCategory,
    example: TypeOfCategory.ASSET,
  })
  @IsEnum(TypeOfCategory)
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'Note for category',
    required: false,
    example: 'A portable computing device with a keyboard and screen.',
  })
  note: string;
}

export class GetCategoryFilterResponseDto {
  @ApiProperty({
    description: 'The data payload of the response.',
  })
  data: GetCategoryDto[];
  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}
export class UpdateCategoryDto extends CreateCategoryDto {}
