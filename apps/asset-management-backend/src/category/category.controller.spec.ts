import { Test, TestingModule } from '@nestjs/testing';
import { CategoryController } from './category.controller';
import { CategoryService } from './category.service';
import { GetCategoryDto } from './dto/category.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { TypeOfCategory } from '@prisma/client/asset-management-backend';
import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateCategoryDto } from './dto/category.dto';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import { AppModule } from 'src/app.module';

describe('CategoryController', () => {
  let controller: CategoryController;
  let service: CategoryService;

  const user = {
    id: 'user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<CategoryController>(CategoryController);
    service = module.get<CategoryService>(CategoryService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const validCategoryId = '073c2022-a405-456a-9d33-844e2c39ac58';
  const invalidCategoryId = 'nonExistentId';
  const categoryData: GetCategoryDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Laptop',
    typeOfCategory: TypeOfCategory.ASSET,
    note: 'A portable device',
  };

  describe('createCategory', () => {
    it('should create category successfully', async () => {
      const createCategoryDto: CreateCategoryDto = {
        name: 'Laptop',
        typeOfCategory: TypeOfCategory.ASSET,
        note: 'A portable device',
      };

      const expectedResponse: HTTPResponseDto<GetCategoryDto> = {
        statusCode: HttpStatus.CREATED,
        data: categoryData,
        message: 'Category created successfully',
      };

      jest
        .spyOn(service, 'createCategory')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.createCategory(
        createCategoryDto,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle conflict when category already exists', async () => {
      const createCategoryDto: CreateCategoryDto = {
        name: 'Laptop',
        typeOfCategory: TypeOfCategory.ASSET,
        note: 'A portable device',
      };

      jest
        .spyOn(service, 'createCategory')
        .mockRejectedValue(
          new ConflictException('Category name already exists'),
        );

      await expect(
        controller.createCategory(
          createCategoryDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(ConflictException);
    });
  });

  describe('findAllCategory', () => {
    const filters: GetAllQueryParamsDto = {
      searchInput: '',
      page: 1,
      limit: 1,
      sortBy: SORT_BY.UPDATED_AT,
      sortOrder: SORT_ORDER.ASC,
    };
    it('it should retrieve all category successfully', async () => {
      const expectedResponse: GetAllResponseDto<GetCategoryDto[]> = {
        statusCode: HttpStatus.OK,
        data: [categoryData],
        count: 1,
        message: 'Category retrieved successfully',
      };

      jest
        .spyOn(service, 'findAllCategory')
        .mockResolvedValue(expectedResponse);

      const result = await controller.findAllCategory(filters);

      expect(result).toEqual(expectedResponse);
    });

    it('it should handle internal server error', async () => {
      const errorResponse: InternalServerErrorException =
        new InternalServerErrorException('Internal Server Error');

      jest.spyOn(service, 'findAllCategory').mockRejectedValue(errorResponse);
      await expect(controller.findAllCategory(filters)).rejects.toThrowError(
        errorResponse,
      );
    });
  });

  describe('findOneCategory', () => {
    it('it should retrieve category by ID successfully', async () => {
      const expectedResponse: HTTPResponseDto<GetCategoryDto> = {
        statusCode: HttpStatus.OK,
        data: categoryData,
        message: 'Category retrieved successfully',
      };

      jest
        .spyOn(service, 'findOneCategory')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.findOneCategory(validCategoryId);

      expect(result).toEqual(expectedResponse);
    });

    it('it should handle not found error when category is not found', async () => {
      jest
        .spyOn(service, 'findOneCategory')
        .mockRejectedValue(
          new NotFoundException(
            `Category with id: ${invalidCategoryId} not found / already deleted`,
          ),
        );
      await expect(
        controller.findOneCategory(invalidCategoryId),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors', async () => {
      const id = invalidCategoryId;

      jest
        .spyOn(service, 'findOneCategory')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(controller.findOneCategory(id)).rejects.toThrowError(
        BadRequestException,
      );
    });
  });

  describe('updateCategory', () => {
    const updateCategoryDto: CreateCategoryDto = {
      name: 'Laptop',
      typeOfCategory: TypeOfCategory.ASSET,
      note: 'A portable device',
    };
    it('should update category by ID successfully', async () => {
      const expectedResponse = {
        statusCode: HttpStatus.OK,
        data: categoryData,
        message: `Category updated successfully`,
      };

      jest
        .spyOn(service, 'updateCategory')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.updateCategory(
        invalidCategoryId,
        updateCategoryDto,
        user as unknown as Request,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('it should handle not found error when requested ID is not found', async () => {
      jest
        .spyOn(service, 'updateCategory')
        .mockRejectedValue(
          new NotFoundException(
            `Category with id: ${invalidCategoryId} not found / already deleted`,
          ),
        );

      await expect(
        controller.updateCategory(
          invalidCategoryId,
          updateCategoryDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bas request with validation errors during update', async () => {
      jest
        .spyOn(service, 'updateCategory')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.updateCategory(
          invalidCategoryId,
          updateCategoryDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });

  describe('deleteCategory', () => {
    it('should delete category successfully', async () => {
      const expectedResponse: HTTPResponseDto<boolean> = {
        statusCode: HttpStatus.OK,
        data: true,
        message: 'Category has been deleted successfully',
      };

      jest.spyOn(service, 'deleteCategory').mockResolvedValue(true);

      const result = await controller.deleteCategory(
        validCategoryId,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when deleting non-existent category', async () => {
      jest
        .spyOn(service, 'deleteCategory')
        .mockRejectedValue(
          new NotFoundException(
            `Category with id: ${invalidCategoryId} not found / already deleted`,
          ),
        );

      await expect(
        controller.deleteCategory(
          invalidCategoryId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors during delete', async () => {
      jest
        .spyOn(service, 'deleteCategory')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.deleteCategory(
          invalidCategoryId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });
});
