import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  GetCategoryDto,
  CreateCategoryDto,
  GetCategoryFilterResponseDto,
  UpdateCategoryDto,
} from './dto/category.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma/client/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { CATEGORY_EXIST } from 'src/constants/message-constants';
import { CATEGORY_NOT_FOUND } from 'src/constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class CategoryService {
  private logger = new Logger('CategoryService');
  private selectArgs = {
    id: true,
    name: true,
    note: true,
    typeOfCategory: true,
  };
  constructor(private readonly prisma: PrismaService) {}

  // Create category api

  /**
   *  This function is responsible for creating a new category based on the provided request body.
   *  If the data in the request body includes an existing category name, it will result in a conflict error.
   * @param createCategoryDto The request body for creating a category.
   * @returns The response for the created category.
   */

  async createCategory(
    createCategoryDto: CreateCategoryDto,
    user: User,
  ): Promise<GetCategoryDto> {
    try {
      const categoryTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const existingCategoryData: GetCategoryDto =
            await prisma.category.findFirst({
              where: {
                name: createCategoryDto.name,
                isDeleted: false,
              },
            });
          if (existingCategoryData) {
            this.logger.log(
              `Category with name: "${existingCategoryData.name}" already exists`,
            );
            throw new ConflictException(CATEGORY_EXIST);
          }

          const createdCategory = await prisma.category.create({
            data: {
              name: createCategoryDto.name,
              typeOfCategory: createCategoryDto.typeOfCategory,
              note: createCategoryDto.note,
            },
            select: this.selectArgs,
          });
          await this.prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CATEGORY,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdCategory.id,
              log: {
                userId: user.id,
                name: user.name,
                categoryId: createdCategory.id,
              },
            },
          });
          return createdCategory;
        },
      );
      this.logger.log(
        `Created history for newly created category with ID: "${categoryTransaction.id}"`,
      );
      this.logger.log(
        `Category created successfully with ID: "${categoryTransaction.id}"`,
      );
      return categoryTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        this.logger.log(error);
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to create category :${error}`);
      throw error;
    }
  }

  // Get all categories

  /**
   *  This function is responsible for fetching all categories.
   * @returns An array of category records that are not deleted.
   */

  async findAllCategory(
    dto?: GetAllQueryParamsDto,
  ): Promise<GetCategoryFilterResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };

    const allowedTypeOfCategory: TypeOfCategory[] = [
      'ACCESSORY',
      'ASSET',
      'CONSUMABLE',
      'APPLIANCE',
      'SOFTWARE_LICENSE',
    ];

    const whereOptions: Prisma.CategoryWhereInput = dto?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: dto.searchInput,
                mode: 'insensitive',
              },
            },
            ...allowedTypeOfCategory
              .filter((category) =>
                category
                  .toLowerCase()
                  .includes(
                    dto.searchInput.trim().toLowerCase().replace(/\s+/g, '_'),
                  ),
              )
              .map((matchedCategory) => ({
                typeOfCategory: {
                  equals: matchedCategory,
                },
              })),
          ],
        }
      : undefined;

    const data: GetCategoryDto[] = await this.prisma.category.findMany({
      where: {
        ...whereOptions,
        isDeleted: false,
      },
      select: this.selectArgs,
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.category.count({
      where: {
        ...whereOptions,
        isDeleted: false,
      },
    });
    this.logger.log('Categories retrieved successfully');

    return { data, count };
  }

  // Get category by id

  /**
   *  This function is responsible for fetching a category by id.
   * @param categoryId  The category ID for which details is to be fetched.
   * @returns Details of the specified category ID record.
   */

  async findOneCategory(categoryId: string): Promise<GetCategoryDto> {
    const retrievedData: GetCategoryDto = await this.prisma.category.findFirst({
      where: { id: categoryId, isDeleted: false },
      select: this.selectArgs,
    });
    if (!retrievedData) {
      this.logger.log(
        `Category with ID: ${categoryId} not found / already deleted`,
      );
      throw new NotFoundException(CATEGORY_NOT_FOUND);
    }
    this.logger.log(`Category with ID: "${categoryId}" retrieved successfully`);
    return retrievedData;
  }

  // Update category by id

  /**
   *  This function is responsible for updating a category by id.
   * @param categoryId  The category ID for which details is to be updated.
   * @returns Updated data of the specified category ID record.
   */

  async updateCategory(
    categoryId: string,
    updateCategoryDto: UpdateCategoryDto,
    user: User,
  ): Promise<GetCategoryDto> {
    try {
      const categoryTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const categoryData = await this.prisma.category.findFirst({
            where: { id: categoryId, isDeleted: false },
          });
          if (!categoryData) {
            this.logger.log(
              `Category with id: "${categoryId}" not found / already deleted`,
            );
            throw new NotFoundException(CATEGORY_NOT_FOUND);
          }
          const existingCategoryData = await this.prisma.category.findMany({
            where: {
              name: updateCategoryDto.name,
              isDeleted: false,
              NOT: {
                id: categoryId,
              },
            },
          });
          if (existingCategoryData && existingCategoryData.length > 0) {
            this.logger.log(
              `Category with name: "${updateCategoryDto.name}" already exists`,
            );
            throw new ConflictException(CATEGORY_EXIST);
          }
          const updatedCategory = await prisma.category.update({
            where: { id: categoryId },
            data: updateCategoryDto,
            select: this.selectArgs,
          });
          await this.prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CATEGORY,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: categoryId,
              log: {
                userId: user.id,
                name: user.name,
                categoryId: categoryId,
                updatedFields: getUpdatedFields(
                  categoryData,
                  updateCategoryDto,
                ),
              },
            },
          });
          this.logger.log(
            `Created history for newly updated category with ID: "${categoryId}"`,
          );
          this.logger.log(
            `Category with ID: "${categoryId} updated successfully"`,
          );
          return updatedCategory;
        },
      );
      return categoryTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to update category: ${error}`);
      throw error;
    }
  }

  // Delete category by id

  /**
   *  This function is responsible for deleting a category by id.
   * @param categoryId  The category ID for which details is to be deleted.
   * @returns Response message including status code, success status and a message.
   */

  async deleteCategory(categoryId: string, user: User) {
    try {
      const categoryTransaction: boolean = await this.prisma.$transaction(
        async (prisma) => {
          const categoryData = await prisma.category.findFirst({
            where: { id: categoryId, isDeleted: false },
            select: {
              typeOfCategory: true,
              assetModels: true,
            },
          });
          if (!categoryData) {
            this.logger.log(
              `Category with id: "${categoryId}" not found / already deleted`,
            );
            throw new NotFoundException(CATEGORY_NOT_FOUND);
          }
          let categoryCount: number;

          // Disconnect asset models first
          const assetModelIds = categoryData.assetModels.map(
            (assetModel) => assetModel.id,
          );

          await prisma.category.update({
            where: {
              id: categoryId,
            },
            data: {
              assetModels: {
                disconnect: assetModelIds
                  ? assetModelIds.map((assetModelId) => {
                      return { id: assetModelId };
                    })
                  : undefined,
              },
            },
          });

          switch (categoryData.typeOfCategory) {
            case TypeOfCategory.ASSET:
              categoryCount = await prisma.assetModel.count({
                where: {
                  categoryId: categoryId,
                  isDeleted: false,
                },
              });
              break;
            case TypeOfCategory.ACCESSORY:
              categoryCount = await prisma.accessory.count({
                where: {
                  categoryId: categoryId,
                  isDeleted: false,
                },
              });
              break;
            case TypeOfCategory.APPLIANCE:
              categoryCount = await prisma.appliance.count({
                where: {
                  categoryId: categoryId,
                  isDeleted: false,
                },
              });
              break;
            case TypeOfCategory.CONSUMABLE:
              categoryCount = await prisma.consumable.count({
                where: {
                  categoryId: categoryId,
                  isDeleted: false,
                },
              });
              break;
            case TypeOfCategory.SOFTWARE_LICENSE:
              categoryCount = await prisma.softwareLicense.count({
                where: {
                  categoryId: categoryId,
                  isDeleted: false,
                },
              });
              break;
          }
          if (categoryCount) {
            throw new BadRequestException(
              `Delete all entities in "${categoryData.typeOfCategory}" to delete this category`,
            );
          }

          await prisma.category.update({
            where: {
              id: categoryId,
            },
            data: {
              isDeleted: true,
            },
          });
          this.logger.log(`Category deleted successfully`);

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CATEGORY,
              action: HistoryActions.DELETED,
              date: new Date(),
              entityId: categoryId,
              log: {
                userId: user.id,
                userName: user.name,
                categoryId: categoryId,
              },
            },
          });
          this.logger.log(
            `Created history for newly deleted category with ID: "${categoryId}"`,
          );

          return true;
        },
      );

      return categoryTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to delete category: ${error}`);
      throw error;
    }
  }
}
