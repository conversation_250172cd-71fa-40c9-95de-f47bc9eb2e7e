import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpStatus,
  Logger,
  ParseUUIDPipe,
  Query,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { CategoryService } from './category.service';
import {
  CreateCategoryDto,
  GetCategoryDto,
  GetCategoryFilterResponseDto,
} from './dto/category.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Category')
@Controller('category')
@UseGuards(PermissionGuard)
export class CategoryController {
  private logger = new Logger('CategoryController');
  constructor(private readonly categoryService: CategoryService) {}

  @ApiOperation({
    summary: 'Create new category',
    description: 'This API creates new category',
  })
  @ApiExtraModels(HTTPResponseDto<CreateCategoryDto>)
  @ApiCreatedResponse({
    description: 'Created category successfully',
    status: HttpStatus.CREATED,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<CreateCategoryDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to create category',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: 'Category name already exists',
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Post()
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CATEGORY))
  async createCategory(
    @Body() createCategoryDto: CreateCategoryDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<GetCategoryDto>> {
    this.logger.log('API for creating a new category');

    const { user } = req;
    const categoryData = await this.categoryService.createCategory(
      createCategoryDto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: categoryData,
      message: 'Category created successfully',
    };
  }

  @ApiOperation({
    description: 'This API retrieves a list of all categories.',
    summary: 'API for retrieving all categories',
  })
  @ApiExtraModels(GetAllResponseDto<GetCategoryDto[]>)
  @ApiOkResponse({
    description: 'Retrieved all category successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetCategoryDto[]>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve category',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Get()
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CATEGORY))
  async findAllCategory(
    @Query() filters: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetCategoryDto[]>> {
    this.logger.log('API for retrieving all categories');

    const categoryData: GetCategoryFilterResponseDto =
      await this.categoryService.findAllCategory(filters);

    return {
      statusCode: HttpStatus.OK,
      data: categoryData.data,
      count: categoryData.count,
      message: 'Category retrieved successfully',
    };
  }

  @ApiOperation({
    description: 'This API retrieves category by id.',
    summary: 'API for retrieving category by id',
  })
  @ApiOkResponse({
    description: 'Retrieved category successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetCategoryDto>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<GetCategoryDto>)
  @ApiBadRequestResponse({
    description: 'Unable to retrieve category by id',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve category by id',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Category with given id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `Category not found / already deleted`,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Get(':categoryId')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CATEGORY))
  async findOneCategory(
    @Param('categoryId', new ParseUUIDPipe()) categoryId: string,
  ): Promise<HTTPResponseDto<GetCategoryDto>> {
    this.logger.log('API for retrieving category by id');

    const categoryData = await this.categoryService.findOneCategory(categoryId);
    return {
      statusCode: HttpStatus.OK,
      data: categoryData,
      message: 'Category retrieved successfully',
    };
  }

  @ApiOperation({
    description: 'This API updates category by id.',
    summary: 'API for updating category by id',
  })
  @ApiOkResponse({
    description: 'Updated category successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<CreateCategoryDto>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<CreateCategoryDto>)
  @ApiBadRequestResponse({
    description: 'Unable to update category',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to update category',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Category with given id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `Category not found / already deleted`,
        error: 'Not Found',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: 'Category name already exists',
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Put(':categoryId')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.CATEGORY))
  async updateCategory(
    @Param('categoryId', new ParseUUIDPipe()) categoryId: string,
    @Body() updateCategoryDto: CreateCategoryDto,
    @Req() req: Request,
  ) {
    const { user } = req;
    const categoryData = await this.categoryService.updateCategory(
      categoryId,
      updateCategoryDto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: categoryData,
      message: 'Category updated successfully',
    };
  }

  @ApiOperation({
    description: 'This API deletes category by id.',
    summary: 'API for deleting category by id',
  })
  @ApiOkResponse({
    description: 'Deleted category successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<boolean>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @ApiBadRequestResponse({
    description: 'Unable to delete category',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to delete category by id',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Category with given id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `Category not found / already deleted`,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API soft deletes a category by id',
    summary: 'API for deleting category by id',
  })
  @ApiBearerAuth('access-token')
  @Delete(':categoryId')
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.CATEGORY))
  async deleteCategory(
    @Param('categoryId', new ParseUUIDPipe()) categoryId: string,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = req;
    this.logger.log('API to delete category');
    const deleteCategory = await this.categoryService.deleteCategory(
      categoryId,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: deleteCategory,
      message: 'Category has been deleted successfully',
    };
  }
}
