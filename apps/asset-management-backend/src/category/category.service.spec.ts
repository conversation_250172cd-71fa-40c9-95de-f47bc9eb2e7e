import { Test, TestingModule } from '@nestjs/testing';
import { CategoryService } from './category.service';
import {
  Category,
  TypeOfCategory,
} from '@prisma/client/asset-management-backend';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto, GetCategoryDto } from './dto/category.dto';
import { CATEGORY_EXIST } from 'src/constants/message-constants';
import { CATEGORY_NOT_FOUND } from 'src/constants';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('CategoryService', () => {
  let service: CategoryService;
  let prismaService: PrismaService;

  const dto = {
    name: 'Laptop',
    typeOfCategory: TypeOfCategory.ASSET,
    note: 'A portable computing device with a keyboard and screen.',
  };
  const category = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: '<PERSON><PERSON><PERSON>',
    typeOfCategory: TypeOfCategory.ASSET,
    note: 'A portable computing device with a keyboard and screen.',
  };

  const user = {
    id: '29c2c660-be0c-4104-80fc-92b9831eed9f',
    email: '<EMAIL>',
    name: 'John Doe',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        category: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<CategoryService>(CategoryService);
    prismaService = service['prisma'];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe(' createCategory ', () => {
    it(' should return all required properties of newly created category ', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(category);

      const result = await service.createCategory(dto, user);

      expect(result).toEqual(category);
    });

    it('it should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(CATEGORY_EXIST));

      expect(
        async () => await service.createCategory(dto, user),
      ).rejects.toThrowError(new ConflictException(CATEGORY_EXIST));
    });
  });

  describe('findAllCategory', () => {
    it('should retrieve category based on query parameters', async () => {
      const mockCategory = [
        {
          name: 'Laptop',
          typeOfCategory: TypeOfCategory.ASSET,
          note: 'A portable device',
        },
        {
          name: 'Phone',
          typeOfCategory: TypeOfCategory.ASSET,
          note: 'Same has laptop',
        },
      ];

      const count = mockCategory.length;

      jest.spyOn(prismaService.category, 'count').mockResolvedValue(count);
      jest
        .spyOn(prismaService.category, 'findMany')
        .mockResolvedValue(mockCategory as Category[]);

      const result = await service.findAllCategory();
      expect(result).toEqual({
        data: mockCategory,
        count,
      });
    });
  });

  describe(' findOneCategory ', () => {
    it(' should retrieve category requested ', async () => {
      const id = '0466888a-3296-4a6c-9620-b1c04ee5c2c9';
      const expectedCategory = {
        id,
        name: 'Laptop',
        typeOfCategory: TypeOfCategory.ASSET,
        note: 'A portable computing device with a keyboard and screen.',
      };
      jest
        .spyOn(prismaService.category, 'findFirst')
        .mockResolvedValue(expectedCategory as Category);
      const result = await service.findOneCategory(id);

      expect(result).toEqual(expectedCategory);
    });
    it('should throw NotFoundException when category is not found', async () => {
      const nonExistentId = 'nonExistentId';

      jest.spyOn(prismaService.category, 'findFirst').mockResolvedValue(null);

      await expect(
        async () => await service.findOneCategory(nonExistentId),
      ).rejects.toThrowError(new NotFoundException(CATEGORY_NOT_FOUND));
    });
  });

  describe('updateCategory', () => {
    const id = '0466888a-3296-4a6c-9620-b1c04ee5c2c9';
    const createCategoryDto: CreateCategoryDto = {
      name: 'Phone',
      typeOfCategory: TypeOfCategory.ASSET,
      note: 'This is a Phone',
    };

    const updateCategory: GetCategoryDto = {
      id,
      name: 'Phone',
      typeOfCategory: TypeOfCategory.ASSET,
      note: 'This is a Phone',
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update category by id successfully', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(updateCategory);

      const result = await service.updateCategory(id, createCategoryDto, user);

      expect(result).toEqual(updateCategory);
    });

    it('should throw NotFoundException when category is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(CATEGORY_NOT_FOUND));

      await expect(
        service.updateCategory(id, createCategoryDto, user),
      ).rejects.toThrowError(new NotFoundException(CATEGORY_NOT_FOUND));
    });

    it('it should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(CATEGORY_EXIST));

      expect(
        async () => await service.updateCategory(id, updateCategory, user),
      ).rejects.toThrowError(new ConflictException(CATEGORY_EXIST));
    });
  });

  describe('deleteCategory', () => {
    const id = '073c2022-a405-456a-9d33-844e2c39ac58';

    it('should delete category successfully', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(true);

      const result = await service.deleteCategory(id, user);

      expect(result).toBe(true);
    });
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should throw NotFoundException when category is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(CATEGORY_NOT_FOUND));

      await expect(service.deleteCategory(id, user)).rejects.toThrowError(
        new NotFoundException(CATEGORY_NOT_FOUND),
      );
    });
  });
});
