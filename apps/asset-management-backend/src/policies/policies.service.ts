import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { User } from 'types';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  setDateWithZeroTime,
  updateFieldGroups,
} from 'src/utility';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import {
  POLICY_NAME_EXIST,
  POLICY_NOT_FOUND,
} from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { DocumentService } from 'src/document/document.service';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import {
  CreatePolicyDto,
  GetAllPolicyResponsesDto,
  GetPolicyResponseDto,
  PoliciesFilterQueryParamsDto,
  UpdatePolicyDto,
} from './dto/policies.dto';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class PolicyService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly documentService: DocumentService,
    private readonly awsService: AwsService,
  ) {}

  private logger = new Logger('PolicyService');

  private selectArgs = {
    id: true,
    name: true,
    vendor: { select: { id: true, name: true } },
    company: { select: { id: true, name: true } },
    createdBy: { select: { id: true, name: true } },
    startDate: true,
    endDate: true,
    createdAt: true,
    notes: true,
    status: true,
    typeOfPolicy: true,
    locations: {
      select: {
        id: true,
        name: true,
      },
    },
    customFields: true,
    fieldGroups: {
      select: {
        id: true,
      },
    },
  };

  /**
   * Creates a policy record with the provided data.
   * @param {CreatePolicyDto} dto - The data transfer object containing policy details.
   * @param {User} user - The user object representing the creator of the policy.
   * @returns {Promise<GetPolicyResponseDto>} A Promise resolving to the created policy response DTO.
   * @throws {Error} If there is an error during the creation process.
   */
  async createPolicy(
    dto: CreatePolicyDto,
    user: User,
  ): Promise<GetPolicyResponseDto> {
    try {
      const policycreateTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const existingPolicy = await this.prisma.policy.findFirst({
            where: {
              name: {
                equals: dto.name,
                mode: 'insensitive',
              },
              isDeleted: false,
            },
          });
          if (existingPolicy) {
            throw new ConflictException(POLICY_NAME_EXIST);
          }

          const fieldGroups: string[] = dto.customFields
            ? dto.customFields['fieldgroups']
            : [];
          const createdPolicy = await prisma.policy.create({
            data: {
              name: dto.name,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
              company: dto.companyId
                ? {
                    connect: {
                      id: dto.companyId,
                    },
                  }
                : undefined,
              startDate: setDateWithZeroTime(dto.startDate),
              endDate: setDateWithZeroTime(dto.endDate),
              locations: dto.locations
                ? {
                    connect: dto.locations.map((locationId) => ({
                      id: locationId,
                    })),
                  }
                : undefined,
              typeOfPolicy: dto.typeOfPolicy,
              customFields: dto.customFields,
              fieldGroups: {
                connect: fieldGroups.map((fieldGroupId) => ({
                  id: fieldGroupId,
                })),
              },
              createdBy: {
                connect: {
                  id: user.id,
                },
              },
              notes: dto.notes,
              status: dto.status,
            },
            select: this.selectArgs,
          });
          this.logger.log(`Policy created with ID: ${createdPolicy.id}`);
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.POLICY,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdPolicy.id,
              log: {
                userId: user.id,
                name: user.name,
                policyId: createdPolicy.id,
              },
            },
          });
          this.logger.log(`History for 'create policy' created successfully`);
          return createdPolicy;
        },
      );
      return policycreateTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create policy: ${error.message}`);
      throw error;
    }
  }

  /**
   * Deletes policy record with the specified ID.
   * @param {string} policyId - The ID of the policy record to delete.
   * @param {User} user - The user object representing the user performing the deletion.
   * @returns {Promise<boolean>} A Promise resolving to true if the deletion is successful.
   * @throws {NotFoundException} If the policy record with the specified ID is not found.
   * @throws {Error} If there is an error during the deletion process.
   */
  async deletePolicy(policyId: string, user: User): Promise<boolean> {
    const policyDeleteTransaction = await this.prisma.$transaction(
      async (prisma) => {
        const policy = await prisma.policy.findFirst({
          where: {
            id: policyId,
            isDeleted: false,
          },
        });
        if (!policy) {
          this.logger.error(`Policy not found with this ID: ${policyId}`);
          throw new NotFoundException(POLICY_NOT_FOUND);
        }
        await prisma.policy.update({
          where: {
            id: policyId,
          },
          data: {
            isDeleted: true,
          },
        });
        this.logger.log(
          `Policy with the ID: ${policyId} is deleted successfully`,
        );

        /**
         * Disconnects existing field groups associated with the specified policy.
         **/
        await disconnectFieldGroups(prisma, 'policy', policyId, policy);
        this.logger.log(`Asset disconnected from all field groups`);

        const documents = await prisma.document.findMany({
          where: {
            entityId: policyId,
          },
          select: {
            id: true,
          },
        });
        if (documents.length > 0) {
          documents.map(async (document) => {
            await this.documentService.deleteDocument(document.id, user);
          });
          this.logger.log(
            `The documents related to policy ID:${policyId} deleted successfully`,
          );
        }
        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.POLICY,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: policy.id,
            log: {
              userId: user.id,
              name: user.name,
              policyId: policy.id,
            },
          },
        });
        this.logger.log(`History for 'delete policy' created successfully`);
        return true;
      },
    );
    return policyDeleteTransaction;
  }

  /**
   * Retrieves policy record by its ID.
   * @param {string} policyId - The ID of the policy record to retrieve.
   * @returns {Promise<GetpolicyResponseDto>} A Promise resolving to the retrieved policy response DTO.
   * @throws {NotFoundException} If the policy record with the specified ID is not found.
   * @throws {Error} If there is an error during the retrieval process.
   */
  async getPolicyById(policyId: string): Promise<GetPolicyResponseDto> {
    try {
      const policy = await this.prisma.policy.findFirst({
        where: {
          id: policyId,
          isDeleted: false,
        },
        select: this.selectArgs,
      });
      if (!policy) {
        this.logger.error(
          `Policy not found with the specified ID: ${policyId}`,
        );
        throw new NotFoundException(POLICY_NOT_FOUND);
      }
      this.logger.log('Policy fetched successfully');
      return policy;
    } catch (error) {
      this.logger.error(`Failed to fetch policy: ${error.message}`);
      throw error;
    }
  }

  /**
   * Updates existing policy record with the specified ID using the provided data.
   * @param {string} policyId - The ID of the policy record to update.
   * @param {UpdatePolicyDto} dto - The data transfer object containing updated policy details.
   * @param {User} user - The user object representing the user performing the update.
   * @returns {Promise<GetPolicyResponseDto>} A Promise resolving to the updated policy response DTO.
   * @throws {NotFoundException} If the policy record with the specified ID is not found.
   * @throws {Error} If there is an error during the update process.
   */
  async updatePolicy(
    policyId: string,
    dto: UpdatePolicyDto,
    user: User,
    isRenew: string,
  ): Promise<GetPolicyResponseDto> {
    try {
      const policyUpdateTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const policy = await prisma.policy.findFirst({
            where: {
              id: policyId,
              isDeleted: false,
            },
            select: this.selectArgs,
          });
          if (!policy) {
            this.logger.error(
              `Policy not found with the specified ID: ${policyId}`,
            );
            throw new NotFoundException(POLICY_NOT_FOUND);
          }

          /**
           * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
           **/
          await updateFieldGroups(prisma, 'policy', policyId, policy, dto);

          const existingLocationIds = policy.locations.map(
            (location) => location.id,
          );

          const idsToDisconnect = existingLocationIds.filter(
            (id) => !dto.locations.includes(id),
          );
          const idsToConnect = dto.locations.filter(
            (id) => !existingLocationIds.includes(id),
          );

          const policyCount = await this.prisma.policy.count({
            where: {
              NOT: [
                {
                  name: {
                    equals: policy.name,
                    mode: 'insensitive',
                  },
                },
              ],
              OR: [
                {
                  name: {
                    equals: dto.name,
                    mode: 'insensitive',
                  },
                },
              ],
              isDeleted: false,
            },
          });
          if (policyCount) {
            throw new ConflictException(POLICY_NAME_EXIST);
          }
          const updatedPolicy = await prisma.policy.update({
            where: {
              id: policyId,
            },
            data: {
              name: dto.name,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
              company: dto.companyId
                ? {
                    connect: {
                      id: dto.companyId,
                    },
                  }
                : undefined,
              startDate: setDateWithZeroTime(dto.startDate),
              endDate: setDateWithZeroTime(dto.endDate),
              locations: dto.locations
                ? {
                    disconnect: idsToDisconnect.map((id) => ({ id })),
                    connect: idsToConnect.map((id) => ({ id })),
                  }
                : undefined,
              notes: dto.notes,
              typeOfPolicy: dto.typeOfPolicy,
              customFields: dto.customFields,
              status: dto.status,
            },
            select: this.selectArgs,
          });
          this.logger.log('Policy updated successfully');
          await prisma.history.create({
            data: {
              action:
                isRenew === 'renew'
                  ? HistoryActions.RENEWED
                  : HistoryActions.UPDATED,
              changeInTable: ChangesOcccuredIn.POLICY,
              entityId: updatedPolicy.id,
              date: new Date(),
              log: {
                userId: user.id,
                name: user.name,
                policyId: updatedPolicy.id,
                updatedFields: getUpdatedFields(policy, updatedPolicy),
              },
            },
          });
          this.logger.log("History for 'update policy' created successfully");
          return updatedPolicy;
        },
      );
      return policyUpdateTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to update policy: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves a list of policies based on the provided query parameters.
   * @param {GetAllQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetAllPolicyResponsesDto>} A Promise resolving to an object containing the list of policies and the total count.
   * @throws {Error} If there is an error during the retrieval process.
   */

  async getAllPolicies(
    params?: PoliciesFilterQueryParamsDto,
  ): Promise<GetAllPolicyResponsesDto> {
    try {
      // Extract page and limit from the query parameters, setting them to undefined if not provided.
      const page: number | null = params?.page ? params.page : null;
      const limit: number | undefined = params?.limit
        ? params.limit
        : undefined;

      // Calculate the number of records to skip based on pagination parameters.
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Define the orderBy object for sorting based on query parameters.
      const orderBy = {
        [params?.sortBy || SORT_BY.CREATED_AT]:
          params?.sortOrder || SORT_ORDER.DESC,
      };
      const policyType = params?.typeOfPolicy;
      const whereOptions: Prisma.PolicyWhereInput = {
        isDeleted: false,
        ...(params?.searchInput
          ? {
              OR: [
                {
                  name: {
                    contains: params.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  vendor: {
                    name: { contains: params.searchInput, mode: 'insensitive' },
                  },
                },
                {
                  company: {
                    name: { contains: params.searchInput, mode: 'insensitive' },
                  },
                },
              ],
            }
          : {}),
      };

      // Fetching policy with specified options, selected fields, sorting, and pagination.
      const policies = await this.prisma.policy.findMany({
        where: {
          ...whereOptions,
          ...(policyType ? { typeOfPolicy: { equals: policyType } } : {}),
          isDeleted: false,
        },
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      });

      // Total count of policy that match the specified criteria.
      const count = await this.prisma.policy.count({
        where: {
          ...whereOptions,
          ...(policyType ? { typeOfPolicy: { equals: policyType } } : {}),
          isDeleted: false,
        },
      });

      this.logger.log(`Fetched ${count} policies successfully`);

      return { data: policies, count };
    } catch (error) {
      this.logger.log(`Failed to fetch the policies: ${error}`);
      throw error;
    }
  }
  /**
   * Retrieves the history of changes for a specific policy entity based on the provided query parameters.
   * @param {string} policyId - The ID of the policy entity to retrieve history for.
   * @param {GetAllQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetEntityHistoryResponse>} A Promise resolving to an object containing the history of changes and the total count.
   * @throws {NotFoundException} If the policy entity with the specified ID is not found.
   * @throws {Error} If there is an error during the retrieval process.
   */
  async getAllPolicyHistory(
    policyId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const policy = await this.prisma.policy.findFirst({
      where: {
        id: policyId,
        isDeleted: false,
      },
    });
    if (!policy) {
      this.logger.error('A policy not found for this ID');
      throw new NotFoundException(POLICY_NOT_FOUND);
    }
    const page: number | null = dto.page ? dto.page : undefined;
    const limit: number | null = dto.limit ? dto.limit : undefined;
    const skip: number | null = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
    };
    const [count, history] = await this.prisma.$transaction([
      this.prisma.history.count({
        where: {
          entityId: policyId,
        },
        take: limit,
        orderBy,
        skip,
      }),
      this.prisma.history.findMany({
        where: {
          entityId: policyId,
        },
        take: limit,
        orderBy,
        skip,
        select: {
          action: true,
          date: true,
          log: true,
        },
      }),
    ]);
    return {
      history,
      count,
    };
  }

  async downloadPolicies(): Promise<string> {
    const softwareLicenses = await this.prisma.policy.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        name: true,
        vendor: { select: { name: true } },
        company: { select: { name: true } },
        createdBy: { select: { name: true } },
        startDate: true,
        endDate: true,
        notes: true,
        status: true,
        typeOfPolicy: true,
        locations: {
          select: {
            id: true,
            name: true,
          },
        },
        customFields: true,
      },
    });
    const parsedDataPromises = softwareLicenses.map(
      async ({
        customFields,
        vendor,
        company,
        createdBy,
        startDate,
        endDate,
        locations,
        ...rest
      }) => {
        const companyName = company?.name;
        const vendorName = vendor?.name;
        const createdUserName = createdBy?.name;
        const commencementDate = new Date(startDate).toLocaleDateString();
        const closingDate = new Date(endDate).toLocaleDateString();
        const location = locations.map((location) => location.name).join(', ');

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          ...customFieldData,
          companyName,
          vendorName,
          createdUserName,
          commencementDate,
          closingDate,
          location,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/policies',
    );
    return fileName;
  }
}
