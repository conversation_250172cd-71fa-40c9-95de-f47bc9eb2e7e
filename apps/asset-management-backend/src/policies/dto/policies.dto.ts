import { ApiProperty } from '@nestjs/swagger';
import { Status, TypeOfPolicy } from '@prisma-clients/asset-management-backend';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { MinMax } from 'src/common/custom-validators';
import { EntityDto } from 'src/common/dto/entity.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { JsonValue } from 'types';

export class UpdatePolicyDto {
  @ApiProperty({
    description: 'Name of the Policy',
    type: 'string',
    required: true,
    example: 'Acer nitro 5 insurance',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The supplier ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsUUID()
  @IsOptional()
  vendorId: string;

  @ApiProperty({
    description: 'The policy providing company ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsOptional()
  @IsUUID()
  companyId: string;

  @ApiProperty({
    description: 'The policy start date',
    type: 'date',
    required: true,
    example: new Date(),
  })
  @IsNotEmpty()
  @IsDateString()
  startDate: Date;

  @ApiProperty({
    description: 'The policy end date',
    type: 'date',
    required: true,
    example: new Date(),
  })
  @IsNotEmpty()
  @IsDateString()
  @MinMax('startDate')
  endDate: Date;

  @ApiProperty({
    description: 'Policy locations',
    isArray: true,
    example: ['2d3afc9f-3b4f-4e2a-b875-8f99add89aae'],
  })
  @IsArray()
  locations: string[];

  @ApiProperty({
    description: 'The policy related info',
    type: 'string',
    example: 'policy applicable for only internal damage',
  })
  @IsOptional()
  @IsString()
  notes: string;

  @ApiProperty({
    description: 'The policy status',
    enum: Status,
    example: Status.ACTIVE,
  })
  @IsOptional()
  @IsEnum(Status)
  status: Status;

  @ApiProperty({
    description: 'Type Of Policy',
    enum: TypeOfPolicy,
    example: TypeOfPolicy.INSURANCE,
    required: true,
  })
  @IsEnum(TypeOfPolicy)
  @IsNotEmpty()
  typeOfPolicy: TypeOfPolicy;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreatePolicyDto extends UpdatePolicyDto {
  @ApiProperty({
    description: 'The policy created user',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsOptional()
  @IsUUID()
  createdBy: string;
}

export class GetPolicyResponseDto {
  @ApiProperty({
    description: 'The policy ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the policy',
    type: 'string',
    example: 'Acer nitro 5 insurance',
  })
  name: string;

  @ApiProperty({
    description: 'The supplier details',
    type: 'object',
    example: {
      id: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
      name: 'dell',
    },
  })
  vendor: { id: string; name: string };

  @ApiProperty({
    description: 'The company details',
    type: 'object',
    example: {
      id: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
      name: 'dell',
    },
  })
  company: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'The policy start date',
    type: 'date',
  })
  startDate: Date;

  @ApiProperty({
    description: 'The policy end date',
    type: 'date',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Policy location',
    isArray: true,
    example: [
      {
        id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
        name: 'Bangalore',
      },
    ],
  })
  locations: EntityDto[];

  @ApiProperty({
    description: 'The policy related info',
    type: 'string',
    example: 'policy applicable for only internal damage',
  })
  notes: string;

  @ApiProperty({
    description: 'The policy created user',
    type: 'object',
    example: {
      id: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
      name: 'john',
    },
  })
  createdBy: { id: string; name: string };

  @ApiProperty({
    description: 'The policy created date',
    type: 'date',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'The policy status',
    enum: Status,
    example: Status.ACTIVE,
  })
  status: Status;

  @ApiProperty({
    description: 'Type Of Policy',
    enum: TypeOfPolicy,
    example: TypeOfPolicy.INSURANCE,
  })
  typeOfPolicy: TypeOfPolicy;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}
export class GetAllPolicyResponsesDto {
  @ApiProperty({
    description: 'Policy response data',
    type: 'array',
    example: [GetPolicyResponseDto],
    isArray: true,
  })
  data: GetPolicyResponseDto[];

  @ApiProperty({
    description: 'Total count of policies',
    type: 'number',
    example: 100,
  })
  count: number;
}

export class PoliciesFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'type of policy',
    enum: TypeOfPolicy,
    example: 'INSURANCE',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsEnum(TypeOfPolicy)
  typeOfPolicy: TypeOfPolicy;
}
