import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  getSchemaPath,
  ApiUnauthorizedResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import {
  POLICY_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { PolicyService } from './policies.service';
import {
  CreatePolicyDto,
  GetAllPolicyResponsesDto,
  GetPolicyResponseDto,
  PoliciesFilterQueryParamsDto,
  UpdatePolicyDto,
} from './dto/policies.dto';

@Controller('policies')
@ApiTags('Policies')
@UseGuards(PermissionGuard)
export class PolicyController {
  constructor(private readonly policyService: PolicyService) {}
  private logger = new Logger('PolicyController');

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Policy created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a policy',
    summary: 'Create a policy',
  })
  @ApiExtraModels(HTTPResponseDto<GetPolicyResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.POLICY))
  async createPolicy(
    @Body() dto: CreatePolicyDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetPolicyResponseDto>> {
    const { user } = request;
    this.logger.log('API to create policy');
    const createdPolicy = await this.policyService.createPolicy(dto, user);
    return {
      statusCode: HttpStatus.CREATED,
      message: 'Policy created successfully',
      data: createdPolicy,
    };
  }

  @Delete(':policyId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted policy',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetPolicyResponseDto>),
    },
  })
  @ApiParam({
    name: 'policyId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter policy Id to delete policy data',
  })
  @ApiNotFoundResponse({
    description: 'Policy with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: POLICY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete policy',
    summary: 'Delete policy',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.POLICY))
  async deletePolicy(
    @Param('policyId', new ParseUUIDPipe()) policyId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    this.logger.log('API to delete a policy');
    const isDeleted = await this.policyService.deletePolicy(policyId, user);
    return {
      statusCode: HttpStatus.OK,
      message: 'The policy deleted successfully',
      data: isDeleted,
    };
  }

  @Get(':policyId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiFoundResponse({
    description: 'Successfully fetched policy',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetPolicyResponseDto>),
    },
  })
  @ApiParam({
    name: 'policyId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter policy Id to fetch the data',
  })
  @ApiNotFoundResponse({
    description: 'Policy with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: POLICY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch policy',
    summary: 'Fetch policy',
  })
  @ApiExtraModels(HTTPResponseDto<GetPolicyResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.POLICY))
  async getPolicyById(
    @Param('policyId', new ParseUUIDPipe()) policyId: string,
  ): Promise<HTTPResponseDto<GetPolicyResponseDto>> {
    this.logger.log('API to fetch policy');
    const policy = await this.policyService.getPolicyById(policyId);
    return {
      statusCode: HttpStatus.FOUND,
      message: 'Policy fetched successfully',
      data: policy,
    };
  }

  @Put(':policyId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated a policy',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetPolicyResponseDto>),
    },
  })
  @ApiParam({
    name: 'policyId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter an policy Id to update the data',
  })
  @ApiQuery({
    name: 'isRenew',
    type: 'string',
    description: 'Indicates if the policy is being renewed',
  })
  @ApiNotFoundResponse({
    description: 'Policy with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: POLICY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to update policy',
    summary: 'Update policy',
  })
  @ApiExtraModels(HTTPResponseDto<GetPolicyResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.POLICY))
  async updatePolicy(
    @Param('policyId', new ParseUUIDPipe()) policyId: string,
    @Body() dto: UpdatePolicyDto,
    @Req() request: Request,
    @Query('isRenew') isRenew: string,
  ): Promise<HTTPResponseDto<GetPolicyResponseDto>> {
    const { user } = request;
    this.logger.log('API to update policy');
    const upadtedPolicy = await this.policyService.updatePolicy(
      policyId,
      dto,
      user,
      isRenew,
    );
    return {
      statusCode: HttpStatus.OK,
      message: 'Policy updated successfully',
      data: upadtedPolicy,
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched all policies',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetAllPolicyResponsesDto>),
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all policies',
    summary: 'Fetch all policies',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.POLICY))
  async getAllPolicies(
    @Query() dto?: PoliciesFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<GetPolicyResponseDto[]>> {
    this.logger.log('API to fetch all policies');
    const policies = await this.policyService.getAllPolicies(dto);
    return {
      statusCode: HttpStatus.OK,
      message: `Fetched ${policies.count} policies successfully`,
      data: policies.data,
      count: policies.count,
    };
  }

  @Get('history/:policyId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched all history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetAllPolicyResponsesDto>),
    },
  })
  @ApiNotFoundResponse({
    description: 'Policy with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: POLICY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all history',
    summary: 'Fetch all history',
  })
  @ApiExtraModels(HTTPResponseDto<GetEntityHistoryResponse>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.POLICY))
  async getAllPolicyHistory(
    @Param('policyId', new ParseUUIDPipe()) policyId: string,
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<HTTPResponseDto<GetEntityHistoryResponse>> {
    this.logger.log('API to fetch all history');
    const history = await this.policyService.getAllPolicyHistory(policyId, dto);
    return {
      statusCode: HttpStatus.OK,
      message: 'Successfully fetched the all history',
      data: history,
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the policies info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the policies info',
  })
  async downloadPolicies(): Promise<HTTPResponseDto<string>> {
    const fileName = await this.policyService.downloadPolicies();
    return {
      data: fileName,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }
}
