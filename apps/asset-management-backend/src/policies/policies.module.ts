import { Module } from '@nestjs/common';
import { PolicyController } from './policies.controller';
import { PolicyService } from './policies.service';
import { AbilityModule } from 'src/ability/ability.module';
import { DocumentService } from 'src/document/document.service';

@Module({
  imports: [AbilityModule],
  controllers: [PolicyController],
  providers: [PolicyService, DocumentService],
})
export class PoliciesModule {}
