import { Test, TestingModule } from '@nestjs/testing';
import { SupplierController } from './supplier.controller';
import { SupplierService } from './supplier.service';
import {
  SupplierDetailsResponseDto,
  SupplierDto,
  SupplierResponseDto,
} from './dto/supplier.dto';
import { HttpStatus, NotFoundException } from '@nestjs/common';
import { SupplierMessageConsts } from 'src/constants/supplier-constants';
import { Request } from 'express';
import {
  EvaluationFrequency,
  Status,
  TransactionFrequency,
  TypeOfPolicy,
} from '@prisma-clients/asset-management-backend';
import { AppModule } from 'src/app.module';

describe('SupplierController', () => {
  let controller: SupplierController;
  let mockSupplierService: SupplierService;

  const mockTestId = '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92';
  const mockSupplierData: SupplierDto = {
    name: 'A4 paper',
    address: '4th Avenue, Manhattan, London, UK',
    location: ['04fa8727-28dc-445d-8298-91b375b5925f'],
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This papers for used only for internal purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };
  const mockSupplierResponseData: SupplierResponseDto = {
    id: mockTestId,
    name: 'A4 paper',
    address: '4th Avenue, Manhattan, London, UK',
    location: [
      {
        id: '04fa8727-28dc-445d-8298-91b375b5925f',
        name: 'Bangalore',
      },
    ],
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This papers for used only for internal purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    evaluatorDepartment: {
      id: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
      name: 'Web Development',
    },
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const mockSupplierDetailsResponseData: SupplierDetailsResponseDto = {
    id: mockTestId,
    name: 'A4 paper',
    address: '4th Avenue, Manhattan, London, UK',
    location: [
      {
        id: '04fa8727-28dc-445d-8298-91b375b5925f',
        name: 'Bangalore',
      },
    ],
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This papers for used only for internal purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    evaluatorDepartment: {
      id: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
      name: 'Web Development',
    },
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
    Policy: [
      {
        name: 'Supplier Policy 1',
        id: 'policy-id-1',
        company: {
          name: 'Company Name',
          id: 'company-id-1',
        },
        startDate: new Date(),
        endDate: new Date(),
        locations: [
          {
            id: '04fa8727-28dc-445d-8298-91b375b5925f',
            name: 'Bangalore',
          },
        ],
        status: Status.ACTIVE,
        typeOfPolicy: TypeOfPolicy.INSURANCE,
      },
    ],
  };

  // Mocking the request object
  const mockRequest: Partial<Request> = {
    user: {
      id: 'bb0bae90-c9c0-48ee-bd62-071b8d42ba0e',
      email: '<EMAIL>',
      name: 'Mock Name',
      role: {
        id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
        name: 'admin',
        permissions: {
          suppliers: ['read', 'create', 'update', 'delete'],
        },
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<SupplierController>(SupplierController);
    mockSupplierService = module.get<SupplierService>(SupplierService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /** TEST: create new supplier  */
  describe('createSupplier', () => {
    /** API should return a valid status code, valid success message and data */
    it('should return a statusCode CREATED, valid success message and data ', async () => {
      jest
        .spyOn(mockSupplierService, 'create')
        .mockImplementation(() => Promise.resolve(mockSupplierResponseData));

      const result = await controller.create(
        mockSupplierData,
        mockRequest as Request,
      );

      expect(result.statusCode).toBe(HttpStatus.CREATED);
      expect(result.message).toBe(SupplierMessageConsts.successCreateSupplier);
      expect(result.data).toEqual(mockSupplierResponseData);
    });
  });

  /** TEST: get supplier as per given id  */
  describe('getSupplier', () => {
    /** API should return a valid status code, success message and valid data */
    it('should return a statusCode of FOUND, success message and valid data ', async () => {
      jest
        .spyOn(mockSupplierService, 'findOne')
        .mockImplementation(() =>
          Promise.resolve(mockSupplierDetailsResponseData),
        );
      const result = await controller.findOne(mockTestId);
      expect(result.statusCode).toBe(HttpStatus.OK);
      expect(result.message).toBe(
        `${SupplierMessageConsts.successGetSupplier} having id:${mockTestId}`,
      );
      expect(result.data).toEqual(mockSupplierDetailsResponseData);
    });

    /** API should return NotFoundException while trying to get details of  invalid ID*/
    it('should return NotFoundException while trying to get details of non-existing supplier', async () => {
      jest
        .spyOn(mockSupplierService, 'findOne')
        /** id is invalid, exception will throw from controller layer */
        .mockImplementation(jest.fn());

      await expect(
        controller.findOne(
          /** invalid Id */
          '111z1111-z111-111z-1z11-111z1z11zz11',
        ),
      ).rejects.toThrow(
        new NotFoundException(SupplierMessageConsts.notFoundSupplier),
      );
    });
  });

  /** TEST: get all supplier  */
  describe('getAllSupplier', () => {
    /** API should return a valid status code, success message and valid data */
    it('should return a statusCode of 200, success message and valid data', async () => {
      jest
        .spyOn(mockSupplierService, 'findAll')
        .mockResolvedValue(
          Promise.resolve({ suppliers: [mockSupplierResponseData], count: 1 }),
        );

      const result = await controller.findAll();

      expect(result.statusCode === HttpStatus.OK);
      expect(result.message).toBe(SupplierMessageConsts.successGetAllSuppliers);
      expect(result.data).toEqual([mockSupplierResponseData]);
    });

    /** API should not return list of existing supplier having values of invalid data types */
    it('should not return list of existing supplier having values of invalid data types', async () => {
      jest
        .spyOn(mockSupplierService, 'findAll')
        .mockResolvedValue(
          Promise.resolve({ suppliers: [mockSupplierResponseData], count: 1 }),
        );

      const result = await controller.findAll();

      expect(result.data).toEqual(
        expect.arrayContaining([
          expect.not.objectContaining({
            id: expect.any(BigInt),
            name: expect.any(Number),
            note: expect.any(Boolean),
            address: expect.any(Object),
            state: expect.any(Number),
            country: expect.any(Boolean),
            zipCode: expect.any(Number),
            contactName: expect.any(Object),
            contactEmail: expect.any(Symbol),
            contactPhoneNumber: expect.any(Boolean),
            supplierImageUrl: expect.any(Object),
          }),
        ]),
      );
    });
  });

  /** TEST: to update existing supplier  */
  describe('updateSupplier', () => {
    /** API should return a valid status code, valid success message and valid data */
    it('should return a statusCode of 200, valid success message and valid data', async () => {
      jest
        .spyOn(mockSupplierService, 'update')
        .mockResolvedValue(Promise.resolve(mockSupplierResponseData));

      const result = await controller.update(
        mockTestId,
        mockSupplierData,
        mockRequest as Request,
      );

      expect(result.statusCode).toBe(HttpStatus.OK);
      expect(result.message).toBe(SupplierMessageConsts.successUpdateSupplier);
      expect(result.data).toEqual(mockSupplierResponseData);
    });

    /** API should return NotFoundException while trying update details of  invalid ID /  non-existing supplier*/
    it('should return NotFoundException while update details of non-existing supplier', async () => {
      jest.spyOn(mockSupplierService, 'update').mockImplementation(jest.fn());

      await expect(
        controller.update(
          /** id is invalid, NotFoundException will throw from controller layer*/
          '111z1111-z111-111z-1z11-111z1z11zz11',
          mockSupplierData,
          mockRequest as Request,
        ),
      ).rejects.toThrow(
        new NotFoundException(SupplierMessageConsts.notFoundSupplier),
      );
    });
  });
});
