import { Test, TestingModule } from '@nestjs/testing';
import { SupplierService } from './supplier.service';
import { SupplierDto, SupplierResponseDto } from './dto/supplier.dto';
import {
  EvaluationFrequency,
  Supplier,
  TransactionFrequency,
} from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('SupplierService', () => {
  let service: SupplierService;
  let prismaService: PrismaService;

  const mockTestId = '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92';
  const mockSupplierData: SupplierDto = {
    name: 'A4 paper',
    address: '4th Avenue, Manhattan, London, UK',
    location: ['04fa8727-28dc-445d-8298-91b375b5925f'],
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This papers for used only for internal purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const mockedSupplierResponse: Supplier = {
    id: mockTestId,
    name: 'Test Supplier',
    address: '4th Avenue, Manhattan, London, UK',
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This is for test purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: 'QUARTERLY',
    transactionFrequency: 'ONE_TIME',
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const mockSupplierResponseData: SupplierResponseDto = {
    id: mockTestId,
    name: 'A4 paper',
    address: '4th Avenue, Manhattan, London, UK',
    location: [
      {
        id: '04fa8727-28dc-445d-8298-91b375b5925f',
        name: 'Bangalore',
      },
    ],
    zipCode: '5256859',
    contactName: '5256859',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '5256859',
    note: 'This papers for used only for internal purposes...',
    supplierImageUrl: 'https://picsum.photos/200/300',
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    evaluatorDepartment: {
      id: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
      name: 'Web Development',
    },
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const mockUser: User = {
    id: 'bb0bae90-c9c0-48ee-bd62-071b8d42ba0e',
    email: '<EMAIL>',
    name: 'Mock Name',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        suppliers: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<SupplierService>(SupplierService);
    prismaService = service['prisma'];
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /** TEST: create new supplier  */
  describe('create', () => {
    it('should return valid message and all required properties of newly created supplier', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(() => Promise.resolve(mockSupplierResponseData));

      const result = await service.create(mockSupplierData, mockUser);
      expect(result).toEqual(mockSupplierResponseData);
    });
  });

  /** TEST: get supplier as per given id  */
  describe('getSupplier', () => {
    /** API should return valid message and all required properties of existing supplier */
    it('should return all required properties of existing supplier', async () => {
      jest
        .spyOn(prismaService.supplier, 'findFirst')
        .mockResolvedValue(mockedSupplierResponse);

      const result = await service.findOne(mockTestId);
      expect(result).toEqual(mockedSupplierResponse);
    });
  });

  /** TEST: get all suppliers  */
  describe('getAllsupplier', () => {
    /** API should return valid success message and list of existing suppliers */
    it('should return valid success message and list of existing suppliers', async () => {
      jest
        .spyOn(prismaService.supplier, 'findMany')
        .mockResolvedValue([mockedSupplierResponse]);
      jest.spyOn(prismaService.supplier, 'count').mockResolvedValue(1);

      const result = await service.findAll();
      expect(result.suppliers).toEqual([mockedSupplierResponse]);
      expect(result.count).toEqual(1);
    });
  });

  /** TEST: to update existing supplier  */
  describe('updateSupplier', () => {
    /** API should return valid success message and required properties of updated supplier */
    it('should return valid success message and required properties of updated supplier', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(() => Promise.resolve(mockSupplierResponseData));

      const result = await service.update(
        mockTestId,
        mockSupplierData,
        mockUser,
      );
      expect(result).toEqual(mockSupplierResponseData);
    });
  });
});
