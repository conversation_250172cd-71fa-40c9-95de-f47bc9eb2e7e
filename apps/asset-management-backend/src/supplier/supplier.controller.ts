import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  HttpStatus,
  BadRequestException,
  NotFoundException,
  Put,
  Delete,
  ParseUUIDPipe,
  Req,
  Query,
  UseGuards,
} from '@nestjs/common';
import { SupplierService } from './supplier.service';
import {
  SupplierDto,
  SupplierGetAllQueryParamsDto,
  SupplierResponseDto,
} from './dto/supplier.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { SupplierMessageConsts } from 'src/constants/supplier-constants';
import {
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { INTERNAL_ERROR } from 'src/constants/message-constants';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Supplier')
@ApiBearerAuth('access-token')
@Controller('supplier')
@UseGuards(PermissionGuard)
export class SupplierController {
  constructor(private readonly supplierService: SupplierService) {}

  @ApiOperation({
    summary: 'Create new supplier',
    description: 'API for creating new supplier.',
  })
  @ApiCreatedResponse({
    description: 'Created supplier successfully',
    status: HttpStatus.CREATED,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SupplierResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Unable to create the supplier',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Post()
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SUPPLIER))
  async create(
    @Body() supplierDto: SupplierDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<SupplierResponseDto>> {
    const createSupplierResp = await this.supplierService.create(
      supplierDto,
      request.user,
    );

    /** check for null response */
    if (!createSupplierResp) {
      throw new BadRequestException(SupplierMessageConsts.notCreatedSupplier);
    }

    return {
      statusCode: HttpStatus.CREATED,
      message: SupplierMessageConsts.successCreateSupplier,
      data: createSupplierResp,
    };
  }

  @ApiOperation({
    summary: 'Get list of all suppliers',
    description: 'API retrieves all suppliers.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Get all suppliers successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SupplierResponseDto[]>),
    },
  })
  @ApiNotFoundResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'No data found for suppliers',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Get('/')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SUPPLIER))
  async findAll(
    @Query() queryFilters?: SupplierGetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<SupplierResponseDto[]>> {
    const { suppliers, count } =
      await this.supplierService.findAll(queryFilters);

    if (!suppliers) {
      throw new NotFoundException(SupplierMessageConsts.failedGetAllSupplier);
    }

    return {
      statusCode: HttpStatus.OK,
      message: SupplierMessageConsts.successGetAllSuppliers,
      data: suppliers,
      count,
    };
  }

  @ApiOperation({
    summary: 'Get supplier',
    description: 'API retrieves supplier which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successfully retrieved supplier',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SupplierResponseDto>),
    },
  })
  @ApiNotFoundResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'No supplier found for the provided ID',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Get(':id')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SUPPLIER))
  async findOne(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<SupplierResponseDto>> {
    const supplier = await this.supplierService.findOne(id);
    if (!supplier) {
      throw new NotFoundException(SupplierMessageConsts.notFoundSupplier);
    }

    return {
      statusCode: HttpStatus.OK,
      message: `${SupplierMessageConsts.successGetSupplier} having id:${id}`,
      data: supplier,
    };
  }

  @ApiOperation({
    summary: 'Update supplier',
    description: 'This API updates supplier which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successfully updated supplier',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SupplierResponseDto>),
    },
  })
  @ApiNotFoundResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier for the given Id is not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Put(':id')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.SUPPLIER))
  async update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() updateSupplierDto: SupplierDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<SupplierResponseDto>> {
    const supplier = await this.supplierService.update(
      id,
      updateSupplierDto,
      request.user,
    );
    if (!supplier) {
      throw new NotFoundException(SupplierMessageConsts.notFoundSupplier);
    }

    return {
      statusCode: HttpStatus.OK,
      message: SupplierMessageConsts.successUpdateSupplier,
      data: supplier,
    };
  }
  @ApiOperation({
    summary: 'Delete supplier',
    description: 'API deletes supplier which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Successfully deleted supplier',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<boolean>),
    },
  })
  @ApiNotFoundResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'No supplier found for the provided ID',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Delete(':id')
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.SUPPLIER))
  async delete(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const status = await this.supplierService.delete(id, request.user);
    if (!status) {
      throw new NotFoundException(SupplierMessageConsts.failedDeleteSupplier);
    }

    return {
      statusCode: HttpStatus.OK,
      message: SupplierMessageConsts.successDeleteSupplier,
      data: status,
    };
  }
}
