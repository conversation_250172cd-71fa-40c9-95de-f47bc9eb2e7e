import { ApiProperty } from '@nestjs/swagger';
import {
  EvaluationFrequency,
  Status,
  TransactionFrequency,
  TypeOfPolicy,
} from '@prisma-clients/asset-management-backend';
import {
  IsEnum,
  IsNotEmpty,
  IsArray,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

export enum SelectionCriteria {
  Capability_Records = 'Quality records of previously demonstrated capabilities',
  Product_Qualification = 'Review of their Product Qualification / Test Results',
  ISO_Certification = 'Current valid registration to an ISO 9001 or ISO 27001 standard by an accredited agency',
  Customer_Approval = 'Customer approved / Recommendation for the supplier',
  Authorized_Dealership = 'Authorized dealers/ Stockiest of customer approved products',
  Branded_Product = 'Branded Product (Reputed / Approved make)',
  Supplier_Assessment = 'Company Questionnaire / Assessment of Supplier’s Credentials as per Supplier Registration Form',
  Top_Management_Recommendation = 'Recommendation from the Top Management',
  Reputation = 'Reputed Supplier / Service Provider in the market on the basis of Product Quality / Quality of Service',
  Price = 'Price',
}

export enum Agreements {
  AMC = 'AMC',
  PO = 'PO',
  NDA = 'NDA',
}

export class SupplierDto {
  @ApiProperty({
    description: 'Supplier name',
    type: 'string',
    example: 'New Enterprises',
    required: false,
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: 'Supplier address',
    type: 'string',
    example: '4th Avenue, Manhattan, London, UK',
    required: false,
  })
  @IsString()
  @IsOptional()
  address: string;

  @ApiProperty({
    description: 'Supplied location',
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  location: string[];

  @ApiProperty({
    description: 'Supplier zipcode',
    type: 'string',
    example: '5256859',
    required: false,
  })
  @IsString()
  @IsOptional()
  zipCode: string;

  @ApiProperty({
    description: 'Supplier contact name',
    type: 'string',
    example: '5256859',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactName: string;

  @ApiProperty({
    description: 'Supplier contact email',
    type: 'string',
    example: '<EMAIL>',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactEmail: string;

  @ApiProperty({
    description: 'Supplier contact phone number',
    type: 'string',
    example: '5256859',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPhoneNumber: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'This papers for used only for internal purposes...',
    required: false,
  })
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'URL for supplier image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
    required: false,
  })
  @IsOptional()
  supplierImageUrl: string;

  @ApiProperty({
    description: 'Service type',
    type: 'string',
    example: 'Internet',
    required: true,
  })
  @IsNotEmpty({ message: 'Service type should provided' })
  @IsString()
  serviceType: string;

  @ApiProperty({
    description: 'Evaluation frequency of supplier',
    enum: EvaluationFrequency,
    example: EvaluationFrequency.QUARTERLY,
    required: false,
  })
  @IsOptional()
  @IsEnum(EvaluationFrequency)
  evaluationFrequency: EvaluationFrequency;

  @ApiProperty({
    description: 'Transaction frequency of supplier',
    enum: TransactionFrequency,
    example: TransactionFrequency.ONE_TIME,
    required: false,
  })
  @IsOptional()
  @IsEnum(TransactionFrequency)
  transactionFrequency?: TransactionFrequency;

  @ApiProperty({
    description: 'Supplier selection criteria',
    enum: SelectionCriteria,
    isArray: true,
    example: [SelectionCriteria.Branded_Product],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SelectionCriteria, { each: true })
  selectionCriteria: string[];

  @ApiProperty({
    description: 'Service agreement type',
    enum: Agreements,
    isArray: true,
    example: [Agreements.AMC],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(Agreements, { each: true })
  agreement: string[];

  @ApiProperty({
    description: "Evaluator's department",
    type: 'uuid',
    example: 'cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  departmentId: string;

  @ApiProperty({
    description: 'List of users to notify on creation of new supplier',
    isArray: true,
    example: ['cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca'],
  })
  @IsOptional()
  notifyTo: string[];
}

export class SupplierResponseDto {
  @ApiProperty({
    description: 'Supplier Id',
    type: 'string',
    example: 'cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca',
  })
  id: string;

  @ApiProperty({
    description: 'Supplier name',
    type: 'string',
    example: 'A4 paper',
  })
  name: string;

  @ApiProperty({
    description: 'Supplier address',
    type: 'string',
    example: '4th Avenue, Manhattan, London, UK',
  })
  address: string;

  @ApiProperty({
    description: 'Supplied location',
    isArray: true,
  })
  location: EntityDto[];

  @ApiProperty({
    description: 'Supplier zipcode',
    type: 'string',
    example: '5256859',
  })
  zipCode: string;

  @ApiProperty({
    description: 'Supplier contact name',
    type: 'string',
    example: '5256859',
  })
  contactName: string;

  @ApiProperty({
    description: 'Supplier contact email',
    type: 'string',
    example: '<EMAIL>',
  })
  contactEmail: string;

  @ApiProperty({
    description: 'Supplier contact phone number',
    type: 'string',
    example: '5256859',
  })
  contactPhoneNumber: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'This papers for used only for internal purposes...',
  })
  note: string;

  @ApiProperty({
    description: 'URL for supplier image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
  })
  supplierImageUrl: string;

  @ApiProperty({
    description: 'Service type',
    type: 'string',
    example: 'Internet',
  })
  serviceType: string;

  @ApiProperty({
    description: 'Evaluation frequency of the supplier',
    enum: EvaluationFrequency,
    example: EvaluationFrequency.QUARTERLY,
  })
  evaluationFrequency: EvaluationFrequency;

  @ApiProperty({
    description: 'Transaction frequency of the supplier',
    enum: TransactionFrequency,
    example: TransactionFrequency.ONE_TIME,
  })
  transactionFrequency: TransactionFrequency;

  @ApiProperty({
    description: 'Selection criteria of supplier',
    isArray: true,
    enum: SelectionCriteria,
    required: false,
  })
  @IsArray()
  @IsEnum(SelectionCriteria, { each: true })
  selectionCriteria: string[];

  @ApiProperty({
    description: 'Service agreement type',
    enum: Agreements,
    isArray: true,
    example: [Agreements.AMC],
  })
  agreement: string[];

  @ApiProperty({
    description: "Evaluator's department details",
    type: 'object',
    example: {
      id: 'cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca',
      name: 'Web Development',
    },
  })
  evaluatorDepartment: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'List of users to notify on creation of new supplier',
    isArray: true,
    example: ['cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca'],
  })
  @IsOptional()
  notifyTo: string[];
}

export class GetAllSuppliersResponseDto {
  @ApiProperty({
    description: 'List of suppliers',
    type: SupplierResponseDto,
    isArray: true,
  })
  suppliers: SupplierResponseDto[];

  @ApiProperty({
    description: 'Total count of suppliers',
    type: 'number',
    example: 10,
  })
  count: number;
}

export class SupplierDetailsResponseDto extends SupplierResponseDto {
  @ApiProperty({
    description: 'List of policies related to the supplier',
    isArray: true,
    type: 'object',
    example: [
      {
        name: 'Policy Name',
        id: 'policy-id',
        company: { name: 'Company Name', id: 'company-id' },
        startDate: '2024-01-01T00:00:00.000Z',
        endDate: '2025-01-01T00:00:00.000Z',
        locations: { name: 'Location Name', id: 'location-id' },
        status: Status.ACTIVE,
        typeOfPolicy: TypeOfPolicy.AGREEMENT,
      },
    ],
  })
  Policy: {
    name: string;
    id: string;
    company: {
      name: string;
      id: string;
    };
    startDate: Date;
    endDate: Date;
    locations: EntityDto[];
    status: Status;
    typeOfPolicy: TypeOfPolicy;
  }[];
}

export class SupplierGetAllQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Filter by transaction frequency',
    enum: TransactionFrequency,
    example: TransactionFrequency.ONE_TIME,
    required: false,
  })
  @IsOptional()
  @IsEnum(TransactionFrequency)
  transactionFrequency: TransactionFrequency;
}
