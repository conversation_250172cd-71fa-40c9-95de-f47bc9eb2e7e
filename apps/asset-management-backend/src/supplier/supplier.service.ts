import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  GetAllSuppliersResponseDto,
  SupplierDetailsResponseDto,
  SupplierDto,
  SupplierGetAllQueryParamsDto,
  SupplierResponseDto,
} from './dto/supplier.dto';
import { SupplierMessageConsts } from 'src/constants/supplier-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { User } from 'types';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class SupplierService {
  private logger = new Logger('SupplierService');

  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  private fieldsToSelect = {
    id: true,
    name: true,
    address: true,
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    zipCode: true,
    contactName: true,
    contactEmail: true,
    contactPhoneNumber: true,
    note: true,
    supplierImageUrl: true,
    serviceType: true,
    evaluationFrequency: true,
    transactionFrequency: true,
    selectionCriteria: true,
    agreement: true,
    evaluatorDepartment: {
      select: {
        id: true,
        name: true,
      },
    },
    notifyTo: true,
  };

  async create(
    createSupplierDto: SupplierDto,
    user: User,
  ): Promise<SupplierResponseDto> {
    try {
      const createSupplierResponse: SupplierResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const { location, ...rest } = createSupplierDto;
          const createdSupplier: SupplierResponseDto =
            await prisma.supplier.create({
              data: {
                ...rest,
                location: location
                  ? {
                      connect: location.map((locationId) => ({
                        id: locationId,
                      })),
                    }
                  : undefined,
              },
              select: this.fieldsToSelect,
            });
          this.logger.log(
            `${SupplierMessageConsts.successCreateSupplier} have id ${createdSupplier.id}`,
          );

          /** history */
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.SUPPLIER,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdSupplier.id,
              log: {
                userId: user.id,
                name: user.name,
                supplierId: createdSupplier.id,
              },
            },
          });
          this.logger.log(
            `Successfully created history for a new supplier have id ${createdSupplier.id}`,
          );

          return createdSupplier;
        });

      return createSupplierResponse;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (createSupplierDto.supplierImageUrl) {
        this.awsService.deleteFile(createSupplierDto.supplierImageUrl);
        this.logger.log(
          'Supplier image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create supplier: ${error}`);
      throw error;
    }
  }

  async findAll(
    queryFilters?: SupplierGetAllQueryParamsDto,
  ): Promise<GetAllSuppliersResponseDto> {
    const page: number | null = queryFilters?.page ? queryFilters.page : null;
    const limit: number | undefined = queryFilters?.limit
      ? queryFilters.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryFilters?.sortBy || 'createdAt']: queryFilters?.sortOrder || 'desc',
    };

    const whereOptions: Prisma.SupplierWhereInput = queryFilters?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
            {
              contactName: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
            {
              serviceType: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
            {
              evaluatorDepartment: {
                name: {
                  contains: queryFilters.searchInput,
                  mode: 'insensitive',
                },
              },
            },
          ],
        }
      : undefined;

    const suppliers: SupplierResponseDto[] =
      await this.prisma.supplier.findMany({
        where: {
          ...whereOptions,
          transactionFrequency: queryFilters?.transactionFrequency
            ? {
                equals: queryFilters.transactionFrequency,
              }
            : undefined,
          isDeleted: false,
        },
        select: this.fieldsToSelect,
        orderBy,
        take: limit,
        skip,
      });

    const count: number = await this.prisma.supplier.count({
      where: {
        ...whereOptions,
        isDeleted: false,
      },
    });

    this.logger.log(SupplierMessageConsts.successGetAllSuppliers);

    return { suppliers, count };
  }

  async findOne(id: string): Promise<SupplierDetailsResponseDto> {
    const supplier: SupplierDetailsResponseDto =
      await this.prisma.supplier.findFirst({
        where: { id, isDeleted: false },
        select: {
          ...this.fieldsToSelect,
          Policy: {
            where: {
              isDeleted: false,
            },
            select: {
              id: true,
              name: true,
              company: {
                select: {
                  id: true,
                  name: true,
                },
              },
              startDate: true,
              endDate: true,
              locations: true,
              status: true,
              typeOfPolicy: true,
            },
          },
        },
      });

    if (!supplier) {
      this.logger.log(`${SupplierMessageConsts.notFoundSupplier} ${id}`);
      throw new NotFoundException(SupplierMessageConsts.notFoundSupplier);
    }

    this.logger.log(
      `${SupplierMessageConsts.successGetSupplier} have id:${id}`,
    );

    return supplier;
  }

  async update(
    id: string,
    createSupplierDto: SupplierDto,
    user: User,
  ): Promise<SupplierResponseDto> {
    let supplier: SupplierResponseDto;
    try {
      const updatedSupplierResponse: SupplierResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          supplier = await prisma.supplier.findFirst({
            where: { id },
            select: this.fieldsToSelect,
          });

          if (!supplier) {
            this.logger.log(
              `${SupplierMessageConsts.failedUpdateSupplier} have id ${id}`,
            );
            throw new NotFoundException(
              SupplierMessageConsts.failedUpdateSupplier,
            );
          }

          const existingLocationIds = supplier.location.map(
            (location) => location.id,
          );

          const idsToDisconnect = existingLocationIds.filter(
            (id) => !createSupplierDto.location.includes(id),
          );
          const idsToConnect = createSupplierDto.location.filter(
            (id) => !existingLocationIds.includes(id),
          );

          const { location, ...rest } = createSupplierDto;
          const updatedSupplier: SupplierResponseDto =
            await prisma.supplier.update({
              where: { id },
              data: {
                ...rest,
                location: location
                  ? {
                      disconnect: idsToDisconnect.map((id) => ({ id })),
                      connect: idsToConnect.map((id) => ({ id })),
                    }
                  : undefined,
                notifyTo: createSupplierDto.notifyTo ?? undefined,
              },
              select: this.fieldsToSelect,
            });

          this.logger.log(
            `${SupplierMessageConsts.successUpdateSupplier} have id ${id}`,
          );

          // If update successfully, delete previous image attached with supplier
          if (
            updatedSupplier &&
            supplier.supplierImageUrl &&
            supplier.supplierImageUrl !== createSupplierDto.supplierImageUrl
          ) {
            await this.awsService.deleteFile(supplier.supplierImageUrl);
            this.logger.log(
              'Supplier image uploaded on s3 bucket deleted successfully',
            );
          }

          /** history */
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.SUPPLIER,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedSupplier.id,
              log: {
                userId: user.id,
                name: user.name,
                supplierId: updatedSupplier.id,
                updatedFields: getUpdatedFields(supplier, updatedSupplier),
              },
            },
          });
          this.logger.log(
            `Successfully created history for a updated supplier have id ${updatedSupplier.id}`,
          );

          return updatedSupplier;
        });
      return updatedSupplierResponse;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (
        createSupplierDto.supplierImageUrl &&
        supplier.supplierImageUrl !== createSupplierDto.supplierImageUrl
      ) {
        this.awsService.deleteFile(createSupplierDto.supplierImageUrl);
        this.logger.log(
          'Supplier image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update supplier: ${error}`);
      throw error;
    }
  }

  async delete(id: string, user: User): Promise<boolean> {
    const deletedSupplierResponse: boolean = await this.prisma.$transaction(
      async (prisma) => {
        const supplier: SupplierResponseDto = await prisma.supplier.findFirst({
          where: { id },
          select: this.fieldsToSelect,
        });

        if (!supplier) {
          this.logger.log(
            `${SupplierMessageConsts.failedDeleteSupplier} have id ${id}`,
          );
          throw new NotFoundException(
            SupplierMessageConsts.failedDeleteSupplier,
          );
        }

        const deletedSupplier: SupplierResponseDto =
          await prisma.supplier.update({
            where: { id },
            data: { isDeleted: true },
            select: this.fieldsToSelect,
          });
        this.logger.log(
          `${SupplierMessageConsts.successDeleteSupplier} have id ${id}`,
        );

        // If deleted successfully, delete image attached with supplier
        if (supplier.supplierImageUrl && deletedSupplier) {
          await this.awsService.deleteFile(supplier.supplierImageUrl);

          this.logger.log(
            'Supplier image uploaded on s3 bucket deleted successfully',
          );
        }

        /** history */
        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.SUPPLIER,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: deletedSupplier.id,
            log: {
              userId: user.id,
              name: user.name,
              supplierId: deletedSupplier.id,
            },
          },
        });
        this.logger.log(
          `Successfully created history for a updated supplier have id ${deletedSupplier.id}`,
        );

        return true;
      },
    );
    return deletedSupplierResponse;
  }
}
