import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  HttpStatus,
  Logger,
  Query,
  ParseUUIDPipe,
  Req,
  UseGuards,
} from '@nestjs/common';
import { VendorEvaluationService } from './vendor-evaluation.service';
import {
  CreateVendorEvaluationDto,
  GetVendorEvaluationDto,
  GetVendorEvaluationFilteredResponseDto,
  GetAllVendorEvaluationQueryParamsDto,
  VendorEvaluationGroupedResponseDto,
  GetSupplierEvaluationFilteredResponseDto,
} from './dto/vendor-evaluation.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Vendor Evaluation')
@Controller('vendor-evaluation')
@UseGuards(PermissionGuard)
export class VendorEvaluationController {
  private logger = new Logger('VendorEvaluationController');
  constructor(
    private readonly vendorEvaluationService: VendorEvaluationService,
  ) {}

  @ApiOperation({
    summary: 'Create new vendor evaluation',
    description: 'This API creates a new vendor evaluation entry',
  })
  @ApiExtraModels(HTTPResponseDto<CreateVendorEvaluationDto>)
  @ApiCreatedResponse({
    description: 'Created vendor evaluation successfully',
    status: HttpStatus.CREATED,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<CreateVendorEvaluationDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to create vendor evaluation',
              error: 'Bad request',
            },
            summary: 'Example: Bad request due to invalid data',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong while creating vendor evaluation',
        error: 'Internal Server Error',
      },
    },
  })
  @Post()
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SUPPLIER))
  async createVendorEvalution(
    @Body() createVendorEvaluationDto: CreateVendorEvaluationDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<GetVendorEvaluationDto>> {
    this.logger.log('API for creating a new vendor evaluation');
    const { user } = req;
    const vendorEvaluationData: GetVendorEvaluationDto =
      await this.vendorEvaluationService.createVendorEvalution(
        createVendorEvaluationDto,
        user,
      );

    return {
      statusCode: HttpStatus.CREATED,
      data: vendorEvaluationData,
      message: 'Created vendor evaluation successfully',
    };
  }

  @Get('evaluations')
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SUPPLIER))
  async findSupplierEvaluations(
    @Query() filters?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<VendorEvaluationGroupedResponseDto[]>> {
    this.logger.log('API for retrieving all vendor evaluations');
    const vendorEvaluationData: GetSupplierEvaluationFilteredResponseDto =
      await this.vendorEvaluationService.findSupplierEvaluations(filters);

    return {
      statusCode: HttpStatus.OK,
      data: vendorEvaluationData.data,
      count: vendorEvaluationData.count,
      message: 'Supplier evaluations retrieved successfully',
    };
  }

  @ApiOperation({
    summary: 'Retrieve a list of all vendor evaluations',
    description: 'This API retrieves a list of all vendor evaluations.',
  })
  @ApiExtraModels(GetAllResponseDto<GetVendorEvaluationDto[]>)
  @ApiOkResponse({
    description: 'Retrieved all vendor evaluations successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetVendorEvaluationDto[]>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve vendor evaluations',
              error: 'Bad request',
            },
            summary: 'Example: Unable to retrieve due to invalid parameters',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong while retrieving vendor evaluations',
        error: 'Internal Server Error',
      },
    },
  })
  @Get()
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SUPPLIER))
  async findAllVendorEvaluations(
    @Query() filters?: GetAllVendorEvaluationQueryParamsDto,
  ): Promise<GetAllResponseDto<GetVendorEvaluationDto[]>> {
    this.logger.log('API for retrieving all vendor evaluations');

    const vendorEvaluationData: GetVendorEvaluationFilteredResponseDto =
      await this.vendorEvaluationService.findAllVendorEvaluations(filters);

    return {
      statusCode: HttpStatus.OK,
      data: vendorEvaluationData.data,
      count: vendorEvaluationData.count,
      message: 'Vendor evaluations retrieved successfully',
    };
  }

  @ApiOperation({
    summary: 'Retrieve a vendor evaluation detail by ID',
    description:
      'This API retrieves a vendor evaluation detail by its unique ID.',
  })
  @ApiOkResponse({
    description: 'Retrieved vendor evaluation detail successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetVendorEvaluationDto>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<GetVendorEvaluationDto>)
  @ApiBadRequestResponse({
    description: 'Unable to retrieve vendor evaluation detail by ID',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve vendor evaluation detail by ID',
              error: 'Bad request',
            },
            summary: 'Example: Unable to retrieve due to invalid parameters',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Vendor evaluation detail with given ID not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `Vendor evaluation detail not found`,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message:
          'Something went wrong while retrieving vendor evaluation detail',
        error: 'Internal Server Error',
      },
    },
  })
  @Get(':vendorEvaluationId')
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SUPPLIER))
  async findOneVendorEvaluation(
    @Param('vendorEvaluationId', new ParseUUIDPipe())
    vendorEvaluationId: string,
  ): Promise<HTTPResponseDto<GetVendorEvaluationDto>> {
    this.logger.log('API for retrieving vendor evaluation detail by ID');

    const vendorEvaluationData =
      await this.vendorEvaluationService.findOneVendorEvaluation(
        vendorEvaluationId,
      );
    return {
      statusCode: HttpStatus.OK,
      data: vendorEvaluationData,
      message: 'Vendor evaluation detail retrieved successfully',
    };
  }

  @ApiOperation({
    summary: 'Update a vendor evaluation detail by ID',
    description:
      'This API updates a vendor evaluation detail by its unique ID.',
  })
  @ApiOkResponse({
    description: 'Updated vendor evaluation detail successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<CreateVendorEvaluationDto>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<CreateVendorEvaluationDto>)
  @ApiBadRequestResponse({
    description: 'Unable to update vendor evaluation detail',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to update vendor evaluation detail',
              error: 'Bad request',
            },
            summary: 'Example: Unable to update due to invalid parameters',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Vendor evaluation detail with given ID not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `Vendor evaluation detail not found`,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong while updating vendor evaluation detail',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Put(':vendorEvaluationId')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.SUPPLIER))
  async updateVendorEvaluation(
    @Param('vendorEvaluationId', new ParseUUIDPipe())
    vendorEvaluationId: string,
    @Body() updateVendorFormDto: CreateVendorEvaluationDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<GetVendorEvaluationDto>> {
    this.logger.log('API for updating vendor evaluation detail by ID');
    const { user } = req;
    const vendorEvaluationData =
      await this.vendorEvaluationService.updateVendorEvaluation(
        vendorEvaluationId,
        updateVendorFormDto,
        user,
      );
    return {
      statusCode: HttpStatus.OK,
      data: vendorEvaluationData,
      message: 'Vendor evaluation detail updated successfully',
    };
  }
}
