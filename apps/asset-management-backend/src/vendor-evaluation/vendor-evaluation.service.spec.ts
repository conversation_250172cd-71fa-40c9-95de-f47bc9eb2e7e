import { Test, TestingModule } from '@nestjs/testing';
import { VendorEvaluationService } from './vendor-evaluation.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { Request } from 'express';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import {
  CreateVendorEvaluationDto,
  GetVendorEvaluationDto,
} from './dto/vendor-evaluation.dto';
import { SUPPLIER_NOT_FOUND } from 'src/constants';
import { VendorEvaluation } from '@prisma-clients/asset-management-backend';
import { EvaluationMessageConsts } from 'src/constants/evaluation-constants';
import { NotFoundException } from '@nestjs/common';

describe('VendorEvaluationService', () => {
  let service: VendorEvaluationService;
  let prismaService: PrismaService;
  const evaluationData: GetVendorEvaluationDto = {
    id: 'b29c334f-233e-4ff0-83f4-91064ca01b79',
    supplier: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'codecraft',
      evaluationFrequency: 'QUARTERLY',
      evaluatorDepartment: {
        id: 'b29c334f-233e-4ff0-83f4-91064ca01b79',
        name: 'Development',
      },
    },
    serviceType: 'PESTCONTROL',
    securityAdherence: 5,
    ndaProactive: 5,
    productQuality: 5,
    responsiveness: 5,
    deadlineAdherence: 4,
    deliveryProactiveness: 4,
    invoiceClarity: 4,
    overallExperience: 4,
    totalScore: 36,
    maxPossibleScore: 40,
    date: new Date('2024-01-31T09:18:16.167Z'),
    note: 'any note',
  };
  const vendorEvaluation: CreateVendorEvaluationDto = {
    supplierId: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    serviceType: 'PESTCONTROL',
    securityAdherence: 2,
    ndaProactive: 2,
    productQuality: 2,
    responsiveness: 2,
    deadlineAdherence: 2,
    deliveryProactiveness: 2,
    invoiceClarity: 4,
    overallExperience: 4,
    date: new Date('10-02-3024'),
    note: 'any note',
  };
  const request = {
    user: { id: '9bdb13ea-dfcc-4157-b678-8e67bee7bb4f', name: 'John' },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [VendorEvaluationService, PrismaService],
    }).compile();
    service = module.get<VendorEvaluationService>(VendorEvaluationService);
    prismaService = service['prisma'];
  });
  it('should be defined', () => {
    expect(service).toBeDefined();
  });
  describe('create evaluation service', () => {
    it('should create a evaluation when not exists', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(evaluationData);
      const result = await service.createVendorEvalution(
        vendorEvaluation,
        request.user,
      );
      expect(result).toEqual(evaluationData);
    });
    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );
      try {
        await service.createVendorEvalution(vendorEvaluation, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });
  describe('get all evaluation details', () => {
    it('should return all the evaluations in an array when no search input provided', async () => {
      jest
        .spyOn(prismaService.vendorEvaluation, 'findMany')
        .mockResolvedValue([evaluationData] as unknown as VendorEvaluation[]);
      jest.spyOn(prismaService.vendorEvaluation, 'count').mockResolvedValue(1);
      const result = await service.findAllVendorEvaluations();
      expect(result).toEqual({
        data: [evaluationData],
        count: 1,
      });
    });
    it('should return an empty array when no evaluations are present', async () => {
      jest
        .spyOn(prismaService.vendorEvaluation, 'findMany')
        .mockResolvedValue([]);
      jest.spyOn(prismaService.vendorEvaluation, 'count').mockResolvedValue(0);
      const result = await service.findAllVendorEvaluations();
      expect(result).toEqual({
        data: [],
        count: 0,
      });
    });
  });
  describe('get evaluation details by id ', () => {
    it('should return the evaluation with the specified id', async () => {
      jest
        .spyOn(prismaService.vendorEvaluation, 'findFirst')
        .mockResolvedValue(evaluationData as unknown as VendorEvaluation);
      const result = await service.findOneVendorEvaluation(evaluationData.id);
      expect(result).toEqual(evaluationData);
    });
    it('should throw a not found exception when evaluation with specified id not found', async () => {
      jest
        .spyOn(prismaService.vendorEvaluation, 'findFirst')
        .mockResolvedValue(null);
      try {
        await service.findOneVendorEvaluation(evaluationData.id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual(
          EvaluationMessageConsts.EVALUATION_NOT_FOUND,
        );
      }
    });
  });
  describe('update evaluation detail', () => {
    it('should update the evaluation with specified id with given data', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(evaluationData);
      const result = await service.updateVendorEvaluation(
        evaluationData.id,
        vendorEvaluation,
        request.user,
      );
      expect(result).toEqual(evaluationData);
    });
    it('should throw not found exception when the evaluation is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new NotFoundException(EvaluationMessageConsts.EVALUATION_NOT_FOUND),
        );
      try {
        await service.updateVendorEvaluation(
          evaluationData.id,
          vendorEvaluation,
          request.user,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual(
          EvaluationMessageConsts.EVALUATION_NOT_FOUND,
        );
      }
    });
    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );
      try {
        await service.updateVendorEvaluation(
          evaluationData.id,
          vendorEvaluation,
          request.user,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });
});
