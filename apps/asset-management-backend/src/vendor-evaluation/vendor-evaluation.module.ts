import { Module } from '@nestjs/common';
import { VendorEvaluationService } from './vendor-evaluation.service';
import { VendorEvaluationController } from './vendor-evaluation.controller';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [VendorEvaluationController],
  providers: [VendorEvaluationService],
})
export class VendorEvaluationModule {}
