import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateVendorEvaluationDto,
  GetVendorEvaluationDto,
  GetVendorEvaluationFilteredResponseDto,
  GetAllVendorEvaluationQueryParamsDto,
  VendorEvaluationGroupedResponseDto,
  GetSupplierEvaluationFilteredResponseDto,
} from './dto/vendor-evaluation.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  VendorEvaluation,
} from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { EvaluationMessageConsts } from 'src/constants/evaluation-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

@Injectable()
export class VendorEvaluationService {
  private logger = new Logger('VendorEvaluationService');
  private selectArgs = {
    id: true,
    supplier: {
      select: {
        id: true,
        name: true,
        evaluationFrequency: true,
        evaluatorDepartment: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    },
    serviceType: true,
    securityAdherence: true,
    ndaProactive: true,
    productQuality: true,
    responsiveness: true,
    deadlineAdherence: true,
    deliveryProactiveness: true,
    invoiceClarity: true,
    overallExperience: true,
    totalScore: true,
    maxPossibleScore: true,
    date: true,
    note: true,
    user: {
      select: {
        id: true,
        name: true,
      },
    },
  };

  /**
   * Validates the input values for creating or updating a vendor evaluation.
   * @param {CreateVendorEvaluationDto} dto - The DTO containing the evaluation input values.
   */
  validateEvaluationInputs = (dto: CreateVendorEvaluationDto) => {
    // Check for invalid combinations of security adherence and NDA proactive inputs
    if (
      (dto.securityAdherence !== 0 && dto.ndaProactive === 0) ||
      (dto.ndaProactive !== 0 && dto.securityAdherence === 0)
    ) {
      this.logger.log(EvaluationMessageConsts.INVALID_EVALUATION_INPUT);
      throw new BadRequestException(
        EvaluationMessageConsts.INVALID_SECURITY_INPUT,
      );
    }
    // Check for invalid combinations of product quality and responsiveness inputs
    if (
      (dto.productQuality !== 0 && dto.responsiveness === 0) ||
      (dto.responsiveness !== 0 && dto.productQuality === 0)
    ) {
      this.logger.log(EvaluationMessageConsts.INVALID_EVALUATION_INPUT);
      throw new BadRequestException(
        EvaluationMessageConsts.INVALID_QUALITY_INPUT,
      );
    }
    // Check for invalid combinations of deadline adherence and delivery proactiveness inputs
    if (
      (dto.deadlineAdherence !== 0 && dto.deliveryProactiveness === 0) ||
      (dto.deliveryProactiveness !== 0 && dto.deadlineAdherence === 0)
    ) {
      this.logger.log(EvaluationMessageConsts.INVALID_EVALUATION_INPUT);
      throw new BadRequestException(
        EvaluationMessageConsts.INVALID_SCHEDULE_INPUT,
      );
    }
    // Check for invalid combinations of invoice clarity and overall experience inputs
    if (
      (dto.invoiceClarity !== 0 && dto.overallExperience === 0) ||
      (dto.overallExperience !== 0 && dto.invoiceClarity === 0)
    ) {
      this.logger.log(EvaluationMessageConsts.INVALID_EVALUATION_INPUT);
      throw new BadRequestException(
        EvaluationMessageConsts.INVALID_GENERAL_INPUT,
      );
    }
  };

  private calculateTotalScoreForVendor(
    evaluationDto: CreateVendorEvaluationDto,
  ): {
    totalScore: number;
    maxPossibleScore: number;
  } {
    const {
      securityAdherence,
      ndaProactive,
      productQuality,
      responsiveness,
      deadlineAdherence,
      deliveryProactiveness,
      invoiceClarity,
      overallExperience,
    } = evaluationDto;

    const nonZeroCriteriaCount = [
      securityAdherence,
      ndaProactive,
      productQuality,
      responsiveness,
      deadlineAdherence,
      deliveryProactiveness,
      invoiceClarity,
      overallExperience,
    ].filter(Boolean).length;

    const maxPossibleScore = 5 * nonZeroCriteriaCount;
    const totalScore =
      securityAdherence +
      ndaProactive +
      productQuality +
      responsiveness +
      deadlineAdherence +
      deliveryProactiveness +
      invoiceClarity +
      overallExperience;

    return { totalScore, maxPossibleScore };
  }

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a vendor evaluation based on the provided DTO if it does not already exist.
   * Additionally, a record is created in the history table to log the creation of the vendor evaluation.
   * @param {CreateVendorEvaluationDto} dto - The DTO containing vendor evaluation details.
   * @param {User} user - The user initiating the creation.
   * @returns {Promise<GetVendorEvaluationDto>} - A Promise that resolves to a DTO representing the created vendor evaluation.
   * @throws {Error} - Throws an Error if any unexpected error occurs during the creation process.
   */
  async createVendorEvalution(
    dto: CreateVendorEvaluationDto,
    user: User,
  ): Promise<GetVendorEvaluationDto> {
    try {
      this.validateEvaluationInputs(dto);

      const { totalScore, maxPossibleScore } =
        this.calculateTotalScoreForVendor(dto);

      const evalutionTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const evalution = await prisma.vendorEvaluation.create({
            data: {
              supplier: dto.supplierId
                ? {
                    connect: {
                      id: dto.supplierId,
                    },
                  }
                : undefined,
              serviceType: dto.serviceType,
              securityAdherence: dto.securityAdherence,
              ndaProactive: dto.ndaProactive,
              productQuality: dto.productQuality,
              responsiveness: dto.responsiveness,
              deadlineAdherence: dto.deadlineAdherence,
              deliveryProactiveness: dto.deliveryProactiveness,
              invoiceClarity: dto.invoiceClarity,
              overallExperience: dto.overallExperience,
              date: dto.date,
              totalScore: totalScore,
              maxPossibleScore: maxPossibleScore,
              note: dto.note,
              user: user.id
                ? {
                    connect: {
                      id: user.id,
                    },
                  }
                : undefined,
            },
            select: this.selectArgs,
          });

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.VENDOR_EVALUATION,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: evalution.id,
              log: {
                userId: user.id,
                name: user.name,
                vendorEvaluationId: evalution.id,
              },
            },
          });
          return evalution;
        },
      );
      this.logger.log(
        `Evaluation history for newly created evaluation with ID: "${evalutionTransaction.id}"`,
      );
      this.logger.log(
        `Evaluation created successfully with ID: "${evalutionTransaction.id}"`,
      );
      return evalutionTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        this.logger.log(error);
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to create evaluation :${error}`);
      throw error;
    }
  }

  async findSupplierEvaluations(
    filters?: GetAllQueryParamsDto,
  ): Promise<GetSupplierEvaluationFilteredResponseDto> {
    try {
      const page: number | null = filters?.page ? filters.page : null;
      const limit: number | undefined = filters?.limit
        ? filters.limit
        : undefined;

      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [filters?.sortBy || SORT_BY.CREATED_AT]:
          filters?.sortOrder || SORT_ORDER.DESC,
      };

      const whereOptions: Prisma.VendorEvaluationWhereInput = {
        ...(filters?.searchInput
          ? {
              OR: [
                {
                  supplier: {
                    name: {
                      contains: filters.searchInput,
                      mode: 'insensitive',
                    },
                  },
                },
              ],
            }
          : {}),
      };

      // Fetch evaluations with supplier data
      const evaluations = await this.prisma.vendorEvaluation.findMany({
        where: whereOptions,
        select: {
          ...this.selectArgs,
        },
        orderBy,
        take: limit,
        skip,
      });

      // Group evaluations by supplier
      const groupedEvaluations = evaluations.reduce(
        (acc, evaluation) => {
          const supplierId = evaluation.supplier.id;
          if (!acc[supplierId]) {
            acc[supplierId] = {
              supplier: evaluation.supplier,
              evaluations: [],
            };
          }
          acc[supplierId].evaluations.push(evaluation);
          return acc;
        },
        {} as Record<string, VendorEvaluationGroupedResponseDto>,
      );

      const groupedEvaluationsArray = Object.values(groupedEvaluations);

      // Count the number of unique suppliers
      const supplierCount = groupedEvaluationsArray.length;

      this.logger.log(`Fetched ${supplierCount} supplier(s) successfully`);

      return { data: groupedEvaluationsArray, count: supplierCount };
    } catch (error) {
      this.logger.log(`Failed to fetch the evaluation(s): ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves vendor evaluations based on specified filters for pagination and sorting.
   * @param {GetAllQueryParamsDto} filters - Optional filters for pagination and sorting.
   * @returns {Promise<GetVendorEvaluationFilterResponseDto>} - A Promise that resolves to a DTO representing the filtered vendor evaluations.
   * @throws {Error} - Throws an Error if any unexpected error occurs during the retrieval process.
   */
  async findAllVendorEvaluations(
    filters?: GetAllVendorEvaluationQueryParamsDto,
  ): Promise<GetVendorEvaluationFilteredResponseDto> {
    try {
      const page: number | null = filters?.page ? filters.page : null;
      const limit: number | undefined = filters?.limit
        ? filters.limit
        : undefined;

      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [filters?.sortBy || SORT_BY.CREATED_AT]:
          filters?.sortOrder || SORT_ORDER.DESC,
      };

      const whereOptions: Prisma.VendorEvaluationWhereInput = {
        ...(filters?.searchInput
          ? {
              OR: [
                {
                  serviceType: {
                    equals: filters.searchInput,
                    mode: 'insensitive',
                  },
                },
              ],
            }
          : {}),
        ...(filters?.supplierId
          ? {
              supplier: {
                id: {
                  equals: filters.supplierId,
                },
              },
            }
          : {}),
      };

      const evalutions = await this.prisma.vendorEvaluation.findMany({
        where: whereOptions,
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      });

      const count = await this.prisma.vendorEvaluation.count({
        where: whereOptions,
      });

      this.logger.log(`Fetched ${count} evaluation(s) successfully`);

      return { data: evalutions, count };
    } catch (error) {
      this.logger.log(`Failed to fetch the evaluation(s): ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves details of a specific vendor evaluation based on the provided ID.
   * @param {string} id - The unique identifier of the vendor evaluation.
   * @returns {Promise<GetVendorEvaluationDto>} - A Promise that resolves to a DTO representing the vendor evaluation details.
   * @throws {NotFoundException} - Throws a NotFoundException if the specified vendor evaluation is not found.
   * @throws {Error} - Throws an Error if any unexpected error occurs during the retrieval process.
   */
  async findOneVendorEvaluation(id: string): Promise<GetVendorEvaluationDto> {
    try {
      const evaluation = await this.prisma.vendorEvaluation.findFirst({
        where: { id },
        select: {
          ...this.selectArgs,
        },
      });

      if (!evaluation) {
        this.logger.log(`Evaluation details with id: ${id} not found`);
        throw new NotFoundException(
          EvaluationMessageConsts.EVALUATION_NOT_FOUND,
        );
      }

      this.logger.log(
        `Fetched evaluation details with id: ${evaluation.id} successfully`,
      );

      return evaluation;
    } catch (error) {
      this.logger.log(`Failed to fetch the evaluation details: ${error}`);
      throw error;
    }
  }

  /**
   * Updates the details of a specific vendor evaluation based on the provided ID and DTO.
   * Additionally, a record is created in the history table to log the update of the vendor evaluation.
   * @param {string} id - The unique identifier of the vendor evaluation to be updated.
   * @param {CreateVendorEvaluationDto} dto - The DTO containing updated vendor evaluation details.
   * @param {User} user - The user initiating the update.
   * @returns {Promise<GetVendorEvaluationDto>} - A Promise that resolves to a DTO representing the updated vendor evaluation.
   * @throws {NotFoundException} - Throws a NotFoundException if the specified vendor evaluation is not found.
   * @throws {Error} - Throws an Error if any unexpected error occurs during the update process.
   */
  async updateVendorEvaluation(
    id: string,
    dto: CreateVendorEvaluationDto,
    user: User,
  ): Promise<GetVendorEvaluationDto> {
    try {
      this.validateEvaluationInputs(dto);

      const { totalScore, maxPossibleScore } =
        this.calculateTotalScoreForVendor(dto);

      const transaction = await this.prisma.$transaction(async (prisma) => {
        const evaluation: VendorEvaluation =
          await prisma.vendorEvaluation.findFirst({
            where: {
              id,
            },
          });

        if (!evaluation) {
          this.logger.log(`Evaluation detail with id: ${id} not found`);
          throw new NotFoundException(
            EvaluationMessageConsts.EVALUATION_NOT_FOUND,
          );
        }

        const updatedEvaluation = await prisma.vendorEvaluation.update({
          where: {
            id,
          },
          data: {
            supplier: dto.supplierId
              ? {
                  connect: {
                    id: dto.supplierId,
                  },
                }
              : undefined,
            serviceType: dto.serviceType,
            securityAdherence: dto.securityAdherence,
            ndaProactive: dto.ndaProactive,
            productQuality: dto.productQuality,
            responsiveness: dto.responsiveness,
            deadlineAdherence: dto.deadlineAdherence,
            deliveryProactiveness: dto.deliveryProactiveness,
            invoiceClarity: dto.invoiceClarity,
            overallExperience: dto.overallExperience,
            totalScore: totalScore,
            maxPossibleScore: maxPossibleScore,
            note: dto.note,
            user: user.id
              ? {
                  connect: {
                    id: user.id,
                  },
                }
              : undefined,
          },
          select: this.selectArgs,
        });

        this.logger.log(
          `Evaluation with id: ${updatedEvaluation.id} updated successfully`,
        );

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.VENDOR_EVALUATION,
            action: HistoryActions.UPDATED,
            date: new Date(),
            entityId: updatedEvaluation.id,
            log: {
              userId: user.id,
              name: user.name,
              vendorEvaluationId: updatedEvaluation.id,
              updatedFields: getUpdatedFields(evaluation, dto),
            },
          },
        });

        this.logger.log(
          `Successfully created history for updated evaluation details with id:${updatedEvaluation.id}`,
        );

        return updatedEvaluation;
      });
      return transaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update evaluation detail: ${error}`);
      throw error;
    }
  }
}
