import { ApiProperty } from '@nestjs/swagger';
import { EvaluationFrequency } from '@prisma-clients/asset-management-backend';
import {
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  Max,
  IsDateString,
} from 'class-validator';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

export class CreateVendorEvaluationDto {
  @ApiProperty({
    description: 'Supplier Id',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: true,
  })
  @IsUUID()
  supplierId: string;

  @ApiProperty({
    description: 'Type of service',
    type: 'string',
    required: true,
    example: 'PestControl',
  })
  @IsString()
  serviceType: string;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 5,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  securityAdherence: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 5,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  ndaProactive: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 5,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  productQuality: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 5,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  responsiveness: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 4,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  deadlineAdherence: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 4,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  deliveryProactiveness: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 4,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  invoiceClarity: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    required: false,
    example: 4,
  })
  @IsNumber()
  @Min(0)
  @Max(5)
  @IsOptional()
  overallExperience: number;

  @ApiProperty({
    description: 'Form created on',
    type: 'date',
    example: '2023-12-30T03:47:55.344Z',
    required: true,
  })
  @IsDateString()
  date: Date;

  @ApiProperty({
    description: 'Additional note if any',
    type: 'string',
    example: 'Include any additional information about the evaluation....',
    required: false,
  })
  @IsOptional()
  note: string;
}

export class GetVendorEvaluationDto {
  @ApiProperty({
    description: 'The unique identifier for the Vendor Form',
    example: '073c2022-a405-456a-9d33-844e2c39ac58',
    type: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Supplier details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'supplier-name',
    },
  })
  supplier: {
    id: string;
    name: string;
    evaluationFrequency: EvaluationFrequency;
    evaluatorDepartment: {
      id: string;
      name: string;
    };
  };

  @ApiProperty({
    description: 'Type of service',
    type: 'string',
    example: 'PestControl',
  })
  serviceType: string;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 5,
  })
  securityAdherence: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 5,
  })
  ndaProactive: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 4,
  })
  productQuality: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 4,
  })
  responsiveness: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 4,
  })
  deadlineAdherence: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 3,
  })
  deliveryProactiveness: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 5,
  })
  invoiceClarity: number;

  @ApiProperty({
    description: 'Feedback score from 1 to 5',
    type: 'number',
    example: 5,
  })
  overallExperience: number;

  @ApiProperty({
    description: 'Form created on',
    type: 'date',
    example: '01-01-2024',
  })
  date: Date;

  @ApiProperty({
    description: 'Total feedback score',
    type: 'number',
    example: 35,
  })
  totalScore: number;

  @ApiProperty({
    description: 'Maximum feedback score',
    type: 'number',
    example: 40,
  })
  maxPossibleScore: number;

  @ApiProperty({
    description: 'Additional note if any',
    type: 'string',
    example: 'Include any additional information about the evaluation....',
    required: false,
  })
  @IsOptional()
  note: string;
}

export class GetVendorEvaluationFilteredResponseDto {
  @ApiProperty({
    description: 'The data payload of the response.',
  })
  data: GetVendorEvaluationDto[];
  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}

export class GetAllVendorEvaluationQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Supplier Id',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  @IsString()
  @IsOptional()
  supplierId: string;
}

type Supplier = {
  id: string;
  name: string;
  evaluationFrequency: EvaluationFrequency;
  evaluatorDepartment: {
    id: string;
    name: string;
  };
};

export type VendorEvaluationGroupedResponseDto = {
  supplier: Supplier;
  evaluations: GetVendorEvaluationDto[];
};

export class GetSupplierEvaluationFilteredResponseDto {
  @ApiProperty({
    description: 'The data payload of the response.',
  })
  data: VendorEvaluationGroupedResponseDto[];

  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}
