import { Test, TestingModule } from '@nestjs/testing';
import { VendorEvaluationController } from './vendor-evaluation.controller';
import { VendorEvaluationService } from './vendor-evaluation.service';
import {
  CreateVendorEvaluationDto,
  GetVendorEvaluationDto,
} from './dto/vendor-evaluation.dto';
import { HttpStatus, NotFoundException } from '@nestjs/common';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { Request } from 'express';
import { SUPPLIER_NOT_FOUND } from 'src/constants/message-constants';
import { EvaluationMessageConsts } from 'src/constants/evaluation-constants';
import { AppModule } from 'src/app.module';
describe('VendorEvaluationController', () => {
  let controller: VendorEvaluationController;
  let service: VendorEvaluationService;
  const evaluationData: GetVendorEvaluationDto = {
    id: 'b29c334f-233e-4ff0-83f4-91064ca01b79',
    supplier: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'codecraft',
      evaluationFrequency: 'QUARTERLY',
      evaluatorDepartment: {
        id: 'b29c334f-233e-4ff0-83f4-91064ca01b79',
        name: 'Development',
      },
    },
    serviceType: 'PESTCONTROL',
    securityAdherence: 5,
    ndaProactive: 5,
    productQuality: 5,
    responsiveness: 5,
    deadlineAdherence: 4,
    deliveryProactiveness: 4,
    invoiceClarity: 4,
    overallExperience: 4,
    totalScore: 36,
    maxPossibleScore: 40,
    date: new Date('2024-01-31T09:18:16.167Z'),
    note: 'any note',
  };
  const request = {
    user: { id: '9bdb13ea-dfcc-4157-b678-8e67bee7bb4f', name: 'John' },
  } as unknown as Request;
  const vendorEvaluation: CreateVendorEvaluationDto = {
    supplierId: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    serviceType: 'PESTCONTROL',
    securityAdherence: 2,
    ndaProactive: 2,
    productQuality: 2,
    responsiveness: 2,
    deadlineAdherence: 2,
    deliveryProactiveness: 2,
    invoiceClarity: 4,
    overallExperience: 4,
    date: new Date('10-02-3024'),
    note: 'any note',
  };
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    controller = module.get<VendorEvaluationController>(
      VendorEvaluationController,
    );
    service = module.get<VendorEvaluationService>(VendorEvaluationService);
  });
  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
  describe('create vendorEvaluation api', () => {
    it('should create vendor evaluation successfully', async () => {
      const expectedResponse: HTTPResponseDto<GetVendorEvaluationDto> = {
        statusCode: HttpStatus.CREATED,
        data: evaluationData,
        message: 'Created vendor evaluation successfully',
      };
      jest
        .spyOn(service, 'createVendorEvalution')
        .mockResolvedValue(expectedResponse.data);
      const result = await controller.createVendorEvalution(
        vendorEvaluation,
        request,
      );
      expect(result).toEqual(expectedResponse);
    });
    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'createVendorEvalution')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));
      try {
        await controller.createVendorEvalution(vendorEvaluation, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });
  describe('get all evaluation api', () => {
    it('should return a status code of 200 for fetching all consumables', async () => {
      jest.spyOn(service, 'findAllVendorEvaluations').mockResolvedValue({
        data: [evaluationData],
        count: 10,
      });
      const result = await controller.findAllVendorEvaluations();
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([evaluationData]);
      expect(result.count).toBe(10);
      expect(result.message).toBe('Vendor evaluations retrieved successfully');
    });
    it('should return an empty array if no consumables are present in database', async () => {
      jest.spyOn(service, 'findAllVendorEvaluations').mockResolvedValue({
        data: [],
        count: 0,
      });
      const result = await controller.findAllVendorEvaluations();
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.message).toBe('Vendor evaluations retrieved successfully');
    });
  });
  describe('get evaluation by id api', () => {
    it('should return the evaluation with specified id and status code 200', async () => {
      jest
        .spyOn(service, 'findOneVendorEvaluation')
        .mockResolvedValue(evaluationData);
      const result = await controller.findOneVendorEvaluation(
        evaluationData.id,
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(evaluationData);
      expect(result.message).toBe(
        'Vendor evaluation detail retrieved successfully',
      );
    });
    it('should throw not found exception when evaluation not found', async () => {
      jest
        .spyOn(service, 'findOneVendorEvaluation')
        .mockRejectedValue(
          new NotFoundException(EvaluationMessageConsts.EVALUATION_NOT_FOUND),
        );
      try {
        await controller.findOneVendorEvaluation(evaluationData.id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(
          EvaluationMessageConsts.EVALUATION_NOT_FOUND,
        );
        expect(error.status).toBe(404);
      }
    });
  });
  describe('update evaluation api', () => {
    it('should return the updated evaluation object with status code of 200', async () => {
      jest
        .spyOn(service, 'updateVendorEvaluation')
        .mockResolvedValue(evaluationData);
      const result = await controller.updateVendorEvaluation(
        evaluationData.id,
        vendorEvaluation,
        request,
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(evaluationData);
      expect(result.message).toBe(
        'Vendor evaluation detail updated successfully',
      );
    });
    it('should throw not found exception when evaluation not found', async () => {
      jest
        .spyOn(service, 'updateVendorEvaluation')
        .mockRejectedValue(
          new NotFoundException(EvaluationMessageConsts.EVALUATION_NOT_FOUND),
        );
      try {
        await controller.updateVendorEvaluation(
          evaluationData.id,
          vendorEvaluation,
          request,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(
          EvaluationMessageConsts.EVALUATION_NOT_FOUND,
        );
        expect(error.status).toBe(404);
      }
    });
    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'updateVendorEvaluation')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));
      try {
        await controller.updateVendorEvaluation(
          evaluationData.id,
          vendorEvaluation,
          request,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });
});
