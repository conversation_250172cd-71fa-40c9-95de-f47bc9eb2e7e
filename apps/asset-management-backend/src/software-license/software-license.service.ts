import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { AssignmentService } from 'src/assignment/assignment.service';
import { PurchaseService } from 'src/purchase/purchase.service';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import {
  CreateLicenseDto,
  LicenseDto,
  LicensesWithCountDto,
  UpdateLicenseDto,
} from './dto/software-license.dto';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import { User } from 'types';
import {
  LICENSE_EXISTS,
  LICENSE_NOT_FOUND,
} from 'src/constants/message-constants';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  setDateWithZeroTime,
  updateFieldGroups,
} from '../utility';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { DocumentService } from 'src/document/document.service';
import { AwsService } from 'src/aws/aws.service';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';
import { UnassignService } from 'src/unassign/unassign.service';

@Injectable()
export class SoftwareLicenseService {
  private logger = new Logger('SoftwareLicenseService');
  private selectArgs = {
    id: true,
    name: true,
    productKey: true,
    licenseHolderName: true,
    licenseHolderEmail: true,
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    expiryDate: true,
    termination: true,
    maintenanceRequired: true,
    reassignable: true,
    totalQuantity: true,
    availableQuantity: true,
    customFields: true,
    note: true,
  };
  constructor(
    private readonly prisma: PrismaService,
    private readonly assignmentService: AssignmentService,
    private readonly purchaseService: PurchaseService,
    private readonly documentService: DocumentService,
    private readonly awsService: AwsService,
    private readonly unassignService: UnassignService,
  ) {}

  /**
   * This method creates a software license based on the provided DTO if not already exists.
   * Also creates a record in history table for the created software license.
   * @param {CreateLicenseDto} dto The DTO (Data Transfer Object) containing software license details.
   * @returns {Promise<LicenseDto>} A Promise that resolves to a LicenseDto representing the created license.
   * @throws ConflictException if a license with the same name already exists.
   *@throws {Error} if any other unexpected error occurs during the creation process.
   */
  async createSoftwareLicense(
    dto: CreateLicenseDto,
    user: User,
  ): Promise<LicenseDto> {
    try {
      if (dto.purchaseInfo.quantity < 0) {
        throw new BadRequestException(`Quantity must be greater than zero`);
      }
      const transaction = await this.prisma.$transaction(async (prisma) => {
        const license = await prisma.softwareLicense.count({
          where: {
            OR: [
              { name: { equals: dto.name, mode: 'insensitive' } },
              { productKey: dto.productKey },
            ],
            isDeleted: false,
          },
        });

        if (license) {
          this.logger.log(
            `Software License with the name '${dto.name}' or product key '${dto.productKey}' already exists`,
          );

          throw new ConflictException(LICENSE_EXISTS);
        }
        const fieldGroups: string[] = dto.customFields
          ? dto.customFields['fieldgroups']
          : [];

        const createdSoftwareLicense = await prisma.softwareLicense.create({
          data: {
            name: dto.name,
            productKey: dto.productKey,
            manufacturer: dto.manufacturerId
              ? {
                  connect: {
                    id: dto.manufacturerId,
                  },
                }
              : undefined,
            category: dto.categoryId
              ? {
                  connect: {
                    id: dto.categoryId,
                  },
                }
              : undefined,

            totalQuantity: dto.purchaseInfo?.quantity,
            availableQuantity: dto.purchaseInfo?.quantity,
            licenseHolderName: dto.licenseHolderName,
            licenseHolderEmail: dto.licenseHolderEmail,
            expiryDate: setDateWithZeroTime(dto.expiryDate),
            termination: dto.termination
              ? setDateWithZeroTime(dto.termination)
              : undefined,
            reassignable: dto.reassignable,
            maintenanceRequired: dto.maintenanceRequired,
            customFields: dto.customFields,
            fieldGroups: {
              connect: fieldGroups.map((fieldGroupId) => ({
                id: fieldGroupId,
              })),
            },
            note: dto.note,
          },
          select: this.selectArgs,
        });

        this.logger.log(
          `Software License with id: ${createdSoftwareLicense.id} created successfully`,
        );

        const purchaseDetails = await this.purchaseService.createPurchase(
          TypeOfCategory.SOFTWARE_LICENSE,
          dto.purchaseInfo,
          createdSoftwareLicense.id,
          user,
        );

        this.logger.log(
          `Purchase created for software license with id: ${createdSoftwareLicense.id}`,
        );

        await prisma.history.create({
          data: {
            date: new Date(),
            changeInTable: ChangesOcccuredIn.SOFTWARE_LICENSE,
            action: HistoryActions.CREATED,
            entityId: createdSoftwareLicense?.id,
            log: {
              userId: user.id,
              name: user.name,
              licenseId: createdSoftwareLicense?.id,
            },
          },
        });

        this.logger.log(
          `History for newly created license with id: ${createdSoftwareLicense.id} created successfully`,
        );

        return { ...createdSoftwareLicense, purchaseInfo: purchaseDetails };
      });
      return transaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create software license: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves a paginated list of software licenses based on the provided query parameters.
   * @param {GetAllQueryParamsDto} [params] - Optional query parameters for filtering, sorting, and pagination.
   * @returns {Promise<LicensesWithCountDto>} - A promise resolving to an object containing the list of software licenses and total count.
   */
  async getAllLicenses(
    params?: GetAllQueryParamsDto,
  ): Promise<LicensesWithCountDto> {
    try {
      // Extract page and limit from the query parameters, setting them to undefined if not provided.
      const page: number | null = params?.page ? params.page : null;
      const limit: number | undefined = params?.limit
        ? params.limit
        : undefined;

      // Calculate the number of records to skip based on pagination parameters.
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Define the orderBy object for sorting based on query parameters.
      const orderBy = {
        [params?.sortBy || SORT_BY.CREATED_AT]:
          params?.sortOrder || SORT_ORDER.DESC,
      };

      // Filtering software licenses by name, productKey, and manufacturer name, and excluding deleted records.
      const whereOptions: Prisma.SoftwareLicenseWhereInput = {
        isDeleted: false,
        ...(params?.searchInput
          ? {
              OR: [
                {
                  name: {
                    contains: params.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  productKey: {
                    contains: params.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  licenseHolderName: {
                    contains: params.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  manufacturer: {
                    name: { contains: params.searchInput, mode: 'insensitive' },
                  },
                },
              ],
            }
          : {}),
      };

      // Fetching software licenses with specified options, selected fields, sorting, and pagination.
      const licenses = await this.prisma.softwareLicense.findMany({
        where: whereOptions,
        select: {
          id: true,
          name: true,
          productKey: true,
          expiryDate: true,
          licenseHolderName: true,
          licenseHolderEmail: true,
          manufacturer: {
            select: {
              id: true,
              name: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          totalQuantity: true,
          availableQuantity: true,
        },
        orderBy,
        take: limit,
        skip,
      });

      // Total count of software licenses that match the specified criteria.
      const count = await this.prisma.softwareLicense.count({
        where: whereOptions,
      });

      this.logger.log(`Fetched ${count} license(s) successfully`);

      return { data: licenses, count };
    } catch (error) {
      this.logger.log(`Failed to fetch the software licenses: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves information about a software license based on the specified ID.
   * @param {string} licenseId - The ID of the license to be retrieved.
   * @returns {Promise<LicenseDto>} - A Promise that resolves to LicenseDto representing the license that is retrieved.
   * @throws {NotFoundException} Throws when the specified license is not found.
   * @throws {Error} - Thrown for other errors encountered during the retrieval process.
   */
  async getLicenseById(licenseId: string): Promise<LicenseDto> {
    try {
      // Fetch license details using the specified ID.
      const license = await this.prisma.softwareLicense.findFirst({
        where: { id: licenseId, isDeleted: false },
        select: this.selectArgs,
      });

      // If the license is not found, throw a NotFoundException.
      if (!license) {
        this.logger.log(`License ${licenseId} not found`);
        throw new NotFoundException(LICENSE_NOT_FOUND);
      }

      this.logger.log(`Fetched license ${license.id} successfully`);

      return license;
    } catch (error) {
      this.logger.log(`Failed to fetch the software license: ${error}`);
      throw error;
    }
  }

  /**
   * This method verifies the existence of the software license.
   * by its ID and updates the license with the provided data.
   * @param {string} licenseId The ID of the license to update.
   * @param {UpdateLicenseDto} dto The DTO containing updated license details.
   * @returns {Promise<LicenseDto>}A Promise that resolves to a LicenseDto representing the updated license.
   * @throws NotFoundException if the license with the specified ID is not found or has been deleted.
   * @throws {Error} if any other unexpected error occurs during the creation process.
   */
  async updateSoftwareLicense(
    licenseId: string,
    dto: UpdateLicenseDto,
    user: User,
    isRenew: string,
  ): Promise<LicenseDto> {
    try {
      const transaction = await this.prisma.$transaction(async (prisma) => {
        const license = await prisma.softwareLicense.findFirst({
          where: { id: licenseId, isDeleted: false },
          select: this.selectArgs,
        });

        if (!license) {
          this.logger.log(`Software License with id: ${licenseId} not found`);
          throw new NotFoundException(LICENSE_NOT_FOUND);
        }

        /**
         * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
         **/
        await updateFieldGroups(
          prisma,
          'softwareLicense',
          licenseId,
          license,
          dto,
        );

        const licenseCount = await prisma.softwareLicense.count({
          where: {
            NOT: {
              name: {
                equals: license.name,
                mode: 'insensitive',
              },
            },
            OR: [
              { name: { equals: dto.name, mode: 'insensitive' } },
              { productKey: dto.productKey },
            ],
            isDeleted: false,
          },
        });

        if (licenseCount) {
          this.logger.log(
            `Software License with the name '${dto.name}' or product key '${dto.productKey}' already exists`,
          );
          throw new ConflictException(LICENSE_EXISTS);
        }
        const updatedSoftwareLicense = await prisma.softwareLicense.update({
          where: {
            id: licenseId,
          },
          data: {
            name: dto.name,
            productKey: dto.productKey,
            manufacturer: dto.manufacturerId
              ? {
                  connect: {
                    id: dto.manufacturerId,
                  },
                }
              : undefined,
            category: dto.categoryId
              ? {
                  connect: {
                    id: dto.categoryId,
                  },
                }
              : undefined,
            licenseHolderName: dto.licenseHolderName,
            licenseHolderEmail: dto.licenseHolderEmail,
            expiryDate: setDateWithZeroTime(dto.expiryDate),
            termination: dto.termination
              ? setDateWithZeroTime(dto.termination)
              : undefined,
            reassignable: dto.reassignable,
            maintenanceRequired: dto.maintenanceRequired,
            customFields: dto.customFields,
            note: dto.note,
          },
          select: this.selectArgs,
        });

        this.logger.log(
          `Software License with id: ${license.id} updated successfully`,
        );

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.SOFTWARE_LICENSE,
            action:
              isRenew === 'renew'
                ? HistoryActions.RENEWED
                : HistoryActions.UPDATED,
            date: new Date(),
            entityId: updatedSoftwareLicense.id,
            log: {
              userId: user.id,
              name: user.name,
              licenseId: updatedSoftwareLicense.id,
              updatedFields: getUpdatedFields(license, updatedSoftwareLicense),
            },
          },
        });

        this.logger.log(
          `Successfully created history for updated license with id:${updatedSoftwareLicense.id}`,
        );

        return updatedSoftwareLicense;
      });
      return transaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update software license: ${error}`);
      throw error;
    }
  }

  /**
   * This method sets the 'isDeleted' flag to true in the database, effectively marking the license as deleted, provided that the license exists.
   * @param {string} licenseId - The ID of the license to be deleted.
   * @param {User} user - The user performing the deletion.
   * @returns {Promise<boolean>} A Promise that resolves to true if the deletion is successful.
   * @throws {NotFoundException} Throws when the specified license is not found.
   * @throws {Error} - Thrown for other errors encountered during the deletion process.
   */
  async deleteLicense(licenseId: string, user: User): Promise<boolean> {
    try {
      const licenseTransaction = await this.prisma.$transaction(
        async (prisma) => {
          // Count the number of non-deleted software licenses with the specified ID.
          const license = await prisma.softwareLicense.count({
            where: { id: licenseId, isDeleted: false },
          });

          const licenseInfo = await prisma.softwareLicense.findFirst({
            where: {
              id: licenseId,
              isDeleted: false,
            },
          });

          // If the license is not found, throw a NotFoundException.
          if (!license) {
            this.logger.log(`License ${licenseId} not found`);
            throw new NotFoundException(LICENSE_NOT_FOUND);
          }

          // Update the 'isDeleted' flag to true, marking the license as deleted.
          const deletedLicense = await prisma.softwareLicense.update({
            where: { id: licenseId },
            data: { isDeleted: true, customFields: {} },
          });

          this.logger.log(
            `The isDeleted flag of the license ${deletedLicense.id} is updated to true successfully`,
          );

          /**
           * Disconnects existing field groups associated with the specified accessory.
           **/
          await disconnectFieldGroups(
            prisma,
            'softwareLicense',
            licenseId,
            licenseInfo,
          );
          this.logger.log(`License disconnected from all field groups`);

          // Delete software license documents
          const documentId = await prisma.document.findMany({
            where: {
              entityId: licenseId,
            },
            select: {
              id: true,
            },
          });
          if (documentId) {
            documentId.map(async (document) => {
              await this.documentService.deleteDocument(document.id, user);
            });
            this.logger.log(
              `The documents related to software license ID:${licenseId} deleted successfully`,
            );
          }
          // Create a history entry for the deletion operation.
          await prisma.history.create({
            data: {
              date: new Date(),
              changeInTable: ChangesOcccuredIn.SOFTWARE_LICENSE,
              action: HistoryActions.DELETED,
              entityId: deletedLicense.id,
              log: {
                userId: user.id,
                name: user.name,
                licenseId: deletedLicense.id,
              },
            },
          });

          this.logger.log(
            `History for the deletion operation of license ${deletedLicense.id} is added successfully`,
          );

          return true;
        },
      );
      return licenseTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to delete the software license: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves all assignments for a given license ID.
   * @param {string} licenseId - The ID of the license for which assignments are to be retrieved.
   * @returns {Promise<AssignmentResponseDto[]>} A Promise that resolves to an array of AssignmentResponseDto representing the assignments.
   */
  async getAllAssignments(licenseId: string): Promise<AssignmentResponseDto[]> {
    return this.assignmentService.findAllByEntityId(licenseId);
  }

  /**
   * This method unassigns a license from a user.
   * @param {string} assignmentId - The ID of the assignment record in the assignment table to unassign.
   * @param {User} user - The user from whom the license will be unassigned.
   * @returns {Promise<boolean>} A Promise that resolves to a boolean indicating whether the unassignment was successful.
   */
  async unassignLicense(
    dto: UnassignmentRequestDto,
    assignmentId: string,
    user: User,
  ): Promise<boolean> {
    const assignmentInfo =
      await this.assignmentService.findAssignmentById(assignmentId);

    const unassignedDetails = {
      unAssignedNote: dto.note,
      notifyUser: dto.notifyUser,
      assignedUser: assignmentInfo.user?.name,
      assignedNote: assignmentInfo.note,
      assignedDate: assignmentInfo.date,
      typeOfCategory: TypeOfCategory.SOFTWARE_LICENSE,
      entityId: assignmentInfo.entityId,
      user: user,
      assignedUserEmail: assignmentInfo.user?.email,
    };

    // If notifyUser is there but assignedUser is not, pass the notifyUser for email notification
    if (!assignmentInfo.user && dto.notifyUser && dto.notifyUser.length > 0) {
      await this.unassignService.createUnassignmentRecord(
        unassignedDetails,
        user,
      );
    } else if (assignmentInfo.user) {
      // If assignedUser exists, proceed with creating the unassignment record
      await this.unassignService.createUnassignmentRecord(
        unassignedDetails,
        user,
      );
    }

    return this.assignmentService.unAssign(assignmentId, user);
  }

  /**
   * This method creates the purchase details for the license entity.
   * @param {CreatePurchaseDto} dto - The data transfer object containing the purchase details.
   * @param {string} licenseId - The ID of the license for which the purchase details is created.
   * @param {User} user - The user who created the record to purchase (for recording the history).
   * @returns {Promise<PurchaseResponseDto>} A promise representing the result of the purchase details that is created.
   */
  async createPurchaseDetails(
    dto: CreatePurchaseDto,
    licenseId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.createPurchase(
      TypeOfCategory.SOFTWARE_LICENSE,
      dto,
      licenseId,
      user,
    );
  }

  /**
   * This method fetches the purchase history for the license entity.
   * @param {string} licenseId - The ID of the license for which the purchase history is retrieved.
   * @returns {Promise<PurchaseResponseDto[]>} A promise representing an array of purchase history details for the license entity.
   */
  async getPurchaseHistory(
    licenseId: string,
  ): Promise<GetAllPurchasesResponse> {
    return this.purchaseService.getEntityPurchaseDetails(licenseId);
  }

  /**
   * This method assigns a software license to a user based on the provided assignment details.
   * @param {CreateAssignmentDto} dto - The data transfer object containing assignment details.
   * @param {User} user - The user to whom the software license will be assigned.
   * @returns {Promise<AssignmentResponseDto>} A Promise that resolves to an object representing the assignment response.
   */
  async assignLicense(
    dto: CreateAssignmentDto,
    user: User,
  ): Promise<AssignmentResponseDto> {
    const assignmentDetails = await this.assignmentService.assign(
      TypeOfCategory.SOFTWARE_LICENSE,
      dto,
      user,
    );
    await this.awsService.assignmentNotifyEmail(assignmentDetails);
    this.logger.log('The mail sent successfully to mentioned notify users');
    return assignmentDetails;
  }

  /**
   * Retrieves the history of a license based on its ID and specified query parameters.
   *
   * @param {string} licenseId - The ID of the license for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If the license with the provided ID is not found.
   */
  async getLicenseHistory(
    licenseId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const license = await this.prisma.softwareLicense.count({
      where: {
        id: licenseId,
        isDeleted: false,
      },
    });

    if (!license) {
      this.logger.log(`License with id ${licenseId} not found`);
      throw new NotFoundException(LICENSE_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    // TODO: Search functionality to be implemented

    const history = await this.prisma.history.findMany({
      where: {
        entityId: licenseId,
      },
      select: {
        action: true,
        date: true,
        log: true,
        changeInTable: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: licenseId,
      },
    });

    this.logger.log(`Fetched history for license with id ${licenseId}`);

    return { history, count };
  }

  async downloadSoftwareLicenses(): Promise<string> {
    const softwareLicenses = await this.prisma.softwareLicense.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        name: true,
        productKey: true,
        licenseHolderName: true,
        licenseHolderEmail: true,
        manufacturer: {
          select: {
            name: true,
          },
        },
        category: {
          select: {
            name: true,
          },
        },
        expiryDate: true,
        termination: true,
        maintenanceRequired: true,
        reassignable: true,
        totalQuantity: true,
        availableQuantity: true,
        customFields: true,
        note: true,
      },
    });
    const parsedDataPromises = softwareLicenses.map(
      async ({
        category,
        manufacturer,
        customFields,
        expiryDate,
        termination,
        ...rest
      }) => {
        const categoryName = category?.name;
        const manufacturerName = manufacturer?.name;
        const dateOfExpire = new Date(expiryDate).toLocaleDateString();
        const dateOfTermination = termination
          ? new Date(termination).toLocaleDateString()
          : '-';

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          categoryName,
          manufacturerName,
          dateOfExpire,
          dateOfTermination,
          ...customFieldData,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/licenses',
    );
    return fileName;
  }

  /**
   * Updates a purchase record.
   * @param {UpdatePurchaseDto} dto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    dto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    consumableId: string,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      consumableId,
    );
  }
}
