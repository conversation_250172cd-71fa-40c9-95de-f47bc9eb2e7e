import { Test, TestingModule } from '@nestjs/testing';
import { SoftwareLicenseService } from './software-license.service';
import { AppModule } from 'src/app.module';

describe('SoftwareLicenseService', () => {
  let service: SoftwareLicenseService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<SoftwareLicenseService>(SoftwareLicenseService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
