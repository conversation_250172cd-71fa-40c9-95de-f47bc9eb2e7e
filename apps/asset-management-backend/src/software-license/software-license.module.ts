import { Module } from '@nestjs/common';
import { SoftwareLicenseService } from './software-license.service';
import { SoftwareLicenseController } from './software-license.controller';
import { PurchaseService } from 'src/purchase/purchase.service';
import { AssignmentService } from 'src/assignment/assignment.service';
import { DocumentService } from 'src/document/document.service';
import { AbilityModule } from 'src/ability/ability.module';
import { UnassignService } from 'src/unassign/unassign.service';

@Module({
  imports: [AbilityModule],
  controllers: [SoftwareLicenseController],
  providers: [
    SoftwareLicenseService,
    PurchaseService,
    AssignmentService,
    DocumentService,
    UnassignService,
  ],
})
export class SoftwareLicenseModule {}
