import { Test, TestingModule } from '@nestjs/testing';
import { SoftwareLicenseController } from './software-license.controller';
import { AppModule } from 'src/app.module';

describe('SoftwareLicenseController', () => {
  let controller: SoftwareLicenseController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<SoftwareLicenseController>(
      SoftwareLicenseController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
