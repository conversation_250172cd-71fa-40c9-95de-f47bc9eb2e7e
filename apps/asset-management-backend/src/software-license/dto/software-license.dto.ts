import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsDateString,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
} from 'src/purchase/dto/purchase.dto';
import { JsonValue } from 'types';

export class UpdateLicenseDto {
  @ApiProperty({
    description: 'Name of the software license',
    type: 'string',
    example: 'Adobe',
    required: true,
  })
  @IsString()
  @IsNotEmpty({ message: 'Software License name cannot be empty' })
  name: string;

  @ApiProperty({
    description: 'Product key of the software license',
    example: '114cf6f5-bb22-3081-b25b-e2ddb9db5b03',
    required: false,
  })
  @IsString()
  @IsOptional()
  productKey: string;

  @ApiProperty({
    description: 'Name of the license holder',
    example: 'jane doe',
    required: false,
  })
  @IsString()
  @IsOptional()
  licenseHolderName: string;

  @ApiProperty({
    description: 'Email of the license holder',
    example: '<EMAIL>',
    required: false,
  })
  @IsEmail()
  @IsOptional()
  licenseHolderEmail: string;

  @ApiProperty({
    description: 'Manufacturer ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  manufacturerId: string;

  @ApiProperty({
    description: 'Category ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2w45',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  categoryId: string;

  @ApiProperty({
    description: 'Expiry date of the software license',
    example: '2024-01-13T14:32:02.773Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  expiryDate: Date;

  @ApiProperty({
    description: 'Termination date of the software license',
    example: '2024-01-13T14:32:02.773Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  termination: Date;

  @ApiProperty({
    description: 'A boolean indicating whether the license is maintained',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  maintenanceRequired: boolean;

  @ApiProperty({
    description: 'A boolean indicating whether the license is reassignable',
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  reassignable: boolean;

  @ApiProperty({
    description: 'Additional notes related to the software license',
    type: 'string',
    example:
      'This license is for version 2.0 and includes support for one year.',
    required: false,
  })
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreateLicenseDto extends UpdateLicenseDto {
  @ApiProperty({
    description: 'Purchase details of the software license',
    type: CreatePurchaseDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreatePurchaseDto)
  purchaseInfo: CreatePurchaseDto;
}

export class LicenseListDto extends EntityDto {
  @ApiProperty({
    description: 'Product key of the software license',
    type: 'string',
    example: '114cf6f5-bb22-3081-b25b-e2ddb9db5b03',
  })
  productKey: string;

  @ApiProperty({
    description: 'Name of the license holder',
    type: 'string',
    example: 'Jane Doe',
  })
  licenseHolderName: string;

  @ApiProperty({
    description: 'Email of the license holder',
    type: 'string',
    example: '<EMAIL>',
  })
  licenseHolderEmail: string;

  @ApiProperty({
    description: 'Manufacturer of the software license',
    type: EntityDto,
  })
  manufacturer: EntityDto;

  @ApiProperty({
    description: 'Category of the software license',
    type: EntityDto,
  })
  category: EntityDto;

  @ApiProperty({
    description: 'Expiry date of the software license',
  })
  expiryDate: Date;

  @ApiProperty({
    description: 'Total quantity of the software license',
    type: 'number',
    example: 25,
  })
  totalQuantity: number;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class LicensesWithCountDto {
  @ApiProperty({
    description: 'Software licenses data',
    type: 'array',
    example: [LicenseListDto],
    isArray: true,
  })
  data: LicenseListDto[];

  @ApiProperty({
    description: 'Total count of software licenses',
    type: 'number',
    example: 25,
  })
  count: number;
}

export class LicenseDto extends LicenseListDto {
  @ApiProperty({
    description: 'Termination date of the software license',
  })
  termination: Date;

  @ApiProperty({
    description: 'A boolean indicating whether the license is maintained',
    type: 'boolean',
    example: false,
  })
  maintenanceRequired: boolean;

  @ApiProperty({
    description: 'A boolean indicating whether the license is reassignable',
    type: 'boolean',
    example: true,
  })
  reassignable: boolean;

  @ApiProperty({
    description: 'Additional notes related to the software license',
    type: 'string',
    example:
      'This license is for version 2.0 and includes support for one year.',
  })
  note: string;

  @ApiProperty({
    description: 'Purchase details of the software license',
    type: PurchaseResponseDto,
  })
  purchaseInfo?: PurchaseResponseDto;
}
