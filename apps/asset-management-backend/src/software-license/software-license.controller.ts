import {
  Controller,
  Post,
  Body,
  Param,
  HttpStatus,
  Put,
  ParseUUIDPipe,
  Logger,
  Req,
  Get,
  Query,
  Delete,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { SoftwareLicenseService } from './software-license.service';
import {
  HTTPResponseDto,
  GetAllQueryParamsDto,
  GetAllResponseDto,
} from 'src/common/http/response.dto';
import {
  CreateLicenseDto,
  LicenseDto,
  LicenseListDto,
  UpdateLicenseDto,
} from './dto/software-license.dto';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import {
  INTERNAL_ERROR,
  INVALID_ENTITY_TYPE,
  INVALID_UUID_FORMAT,
  LICENSE_EXISTS,
  LICENSE_NOT_FOUND,
} from 'src/constants/message-constants';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';

@ApiTags('Software License')
@Controller('software-license')
@UseGuards(PermissionGuard)
export class SoftwareLicenseController {
  private logger = new Logger('SoftwareLicenseController');

  constructor(
    private readonly softwareLicenseService: SoftwareLicenseService,
  ) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created the software license',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<LicenseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: LICENSE_EXISTS,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This api allows to create a new software license if not exists',
    summary: 'Create a software license',
  })
  @ApiExtraModels(HTTPResponseDto<LicenseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SOFTWARE_LICENSE))
  async createSoftwareLicense(
    @Body() dto: CreateLicenseDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<LicenseDto>> {
    this.logger.log('API to create software license');
    const { user } = req;
    const license = await this.softwareLicenseService.createSoftwareLicense(
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: license,
      message: 'Software License created successfully',
    };
  }

  @Put(':licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated the software license',
    schema: { $ref: getSchemaPath(HTTPResponseDto<LicenseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Software License with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LICENSE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the software license details',
    summary: 'Update software license',
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter license Id to update',
  })
  @ApiQuery({
    name: 'isRenew',
    type: 'string',
    description: 'Indicates if the license is being renewed',
  })
  @ApiExtraModels(HTTPResponseDto<LicenseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.SOFTWARE_LICENSE))
  async updateSoftwareLicenseDetails(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
    @Body() dto: UpdateLicenseDto,
    @Req() req: Request,
    @Query('isRenew') isRenew: string,
  ): Promise<HTTPResponseDto<LicenseDto>> {
    this.logger.log('API to update software license');
    const { user } = req;
    const license = await this.softwareLicenseService.updateSoftwareLicense(
      licenseId,
      dto,
      user,
      isRenew,
    );

    return {
      statusCode: HttpStatus.OK,
      data: license,
      message: 'Successfully updated the software license',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all the software licenses',
    schema: { $ref: getSchemaPath(GetAllResponseDto<LicenseListDto[]>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API fetches all the software licenses with optional query parameters to search by name, productKey and manufacturer.',
    summary: 'Fetch all the software licenses',
  })
  @ApiExtraModels(GetAllResponseDto<LicenseListDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SOFTWARE_LICENSE))
  async getAllLicenses(
    @Query() params?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<LicenseListDto[]>> {
    this.logger.log('API to fetch all the software licenses');

    const result = await this.softwareLicenseService.getAllLicenses(params);

    return {
      statusCode: HttpStatus.OK,
      data: result.data,
      count: result.count,
      message: 'Successfully fetched all the software licenses',
    };
  }

  @Get(':licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched the software license',
    schema: { $ref: getSchemaPath(HTTPResponseDto<LicenseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LICENSE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API fetches a software license based on the specified ID.',
    summary: 'Fetch the software license based on the ID',
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the license ID',
  })
  @ApiExtraModels(HTTPResponseDto<LicenseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SOFTWARE_LICENSE))
  async getLicenseById(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
  ): Promise<HTTPResponseDto<LicenseDto>> {
    this.logger.log('API to fetch a software license based on the ID');

    const license = await this.softwareLicenseService.getLicenseById(licenseId);

    return {
      statusCode: HttpStatus.OK,
      data: license,
      message: 'Successfully fetched the software license',
    };
  }

  @Post('assign')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully assigned software license',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to assign a license to users',
    summary: 'Assign a license',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SOFTWARE_LICENSE))
  async assignLicense(
    @Body() dto: CreateAssignmentDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    this.logger.log('API to assign license to user');

    const { user } = request;
    const assignmentDetails = await this.softwareLicenseService.assignLicense(
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: assignmentDetails,
      message: 'Successfully assigned software license',
    };
  }

  @Delete(':licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully deleted the software license',
    schema: { $ref: getSchemaPath(HTTPResponseDto<boolean>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LICENSE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API soft deletes a software license.',
    summary: 'Delete a software license',
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the license ID to delete',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.SOFTWARE_LICENSE))
  async deleteLicense(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to delete a software license');

    const { user } = req;
    const deletedStatus = await this.softwareLicenseService.deleteLicense(
      licenseId,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: deletedStatus,
      message: 'Successfully deleted the software license',
    };
  }

  @Get('assignments/:licenseId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto[]>)
  @ApiOkResponse({
    description: 'Successfully fetched the assignments',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto[]>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LICENSE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API fetches all the software license assignments based on the specified ID.',
    summary: 'Fetch all the assignments',
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the license ID to fetch all assignments',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SOFTWARE_LICENSE))
  async getAllAssignments(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
  ): Promise<HTTPResponseDto<AssignmentResponseDto[]>> {
    this.logger.log(
      'API to fetch all the software license assignments based on the ID',
    );

    const assignments =
      await this.softwareLicenseService.getAllAssignments(licenseId);

    return {
      statusCode: HttpStatus.OK,
      data: assignments,
      message: 'Successfully fetched the assignments',
    };
  }

  @Delete('unassign/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully unassigned the software license',
    schema: { $ref: getSchemaPath(HTTPResponseDto<boolean>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LICENSE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API unassigns a software license.',
    summary: 'Unassign a software license',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '7da45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the assignment ID to unassign',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.SOFTWARE_LICENSE))
  async unassignLicense(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() req: Request,
    @Body() dto: UnassignmentRequestDto,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to unassign a software license');

    const { user } = req;
    const unassignedStatus = await this.softwareLicenseService.unassignLicense(
      dto,
      assignmentId,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: unassignedStatus,
      message: 'Successfully unassigned the software license',
    };
  }

  @Post('purchase/:licenseId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description:
      'Successfully created purchase details for the software license',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the license ID to create a purchase record',
  })
  @ApiOperation({
    description: 'This API creates purchase details for a software license',
    summary: 'Create purchase details for a software license',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SOFTWARE_LICENSE))
  async createPurchaseDetails(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
    @Body() dto: CreatePurchaseDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    this.logger.log('API to create purchase details for a software license');

    const { user } = request;
    const purchaseDetails =
      await this.softwareLicenseService.createPurchaseDetails(
        dto,
        licenseId,
        user,
      );

    return {
      statusCode: HttpStatus.CREATED,
      data: purchaseDetails,
      message: 'Successfully created purchase details for the software license',
    };
  }

  @Get('purchases/:licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description:
      'Successfully fetched the purchase history of the software license',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<PurchaseResponseDto[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the license ID to fetch the purchase history',
  })
  @ApiOperation({
    description: 'This API fetches the purchase history of a software license',
    summary: 'Fetch purchase history of a software license',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SOFTWARE_LICENSE))
  async getPurchaseHistory(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
  ): Promise<GetAllResponseDto<PurchaseResponseDto[]>> {
    this.logger.log('API to fetch the purchase history of a software license');

    const { purchaseDetails, count } =
      await this.softwareLicenseService.getPurchaseHistory(licenseId);

    return {
      statusCode: HttpStatus.OK,
      data: purchaseDetails,
      count,
      message:
        'Successfully fetched the purchase history of the software license',
    };
  }

  @Get('history/:licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched license history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'licenseId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter license Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch license history',
    summary: 'Fetch license history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SOFTWARE_LICENSE))
  async getLicenseHistory(
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const { history, count } =
      await this.softwareLicenseService.getLicenseHistory(
        licenseId,
        queryParams,
      );

    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'License history fetched successfully',
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the software licenses info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the software licenses info',
  })
  async downloadSoftwareLicenses(): Promise<HTTPResponseDto<string>> {
    const fileName =
      await this.softwareLicenseService.downloadSoftwareLicenses();
    return {
      data: fileName,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }

  @Put('purchase-update/:purchaseId/:licenseId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Purchase details updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid purchase ID',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_UUID_FORMAT,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to update purchase record',
  })
  @ApiOperation({
    description: 'This API updates the purchase record',
    summary: 'Update the purchase record',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.APPLIANCE))
  async updatePurchaseRecord(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
    @Body() dto: UpdatePurchaseDto,
    @Req() request: Request,
    @Param('licenseId', new ParseUUIDPipe()) licenseId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const updatedPurchase =
      await this.softwareLicenseService.updatePurchaseRecord(
        dto,
        purchaseId,
        user,
        licenseId,
      );
    return {
      statusCode: HttpStatus.OK,
      data: updatedPurchase,
      message: 'Purchase record updated successfully',
    };
  }
}
