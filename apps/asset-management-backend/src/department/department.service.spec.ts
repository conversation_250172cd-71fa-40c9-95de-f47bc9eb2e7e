import { Test, TestingModule } from '@nestjs/testing';
import { DepartmentService } from './department.service';
import { AppModule } from 'src/app.module';

describe('DepartmentService', () => {
  let service: DepartmentService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<DepartmentService>(DepartmentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
