import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>Arra<PERSON>,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';

export class CreateDepartmentDto {
  @IsString()
  @IsNotEmpty()
  @Matches(/^(?!.*\d+$)(?!.*\d)(?!^\W)[\w\s\W]+$/, {
    message: 'Invalid format',
  })
  @ApiProperty({ example: 'Web Development' })
  name: string;

  @ApiProperty({
    example: '999fc45f-bb45-4de5-9252-047b9ba155e9',
    description: 'The department role ID',
    required: true,
  })
  @IsUUID()
  roleId: string;

  @ApiProperty({
    example: '999fc45f-bb45-4de5-9252-047b9ba155e9',
    description: 'The department supplier ID',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  supplierId: string;

  @ApiProperty({
    example: ['999fc45f-bb45-4de5-9252-047b9ba155e9'],
    description: 'The department user IDs',
    required: false,
  })
  @IsArray()
  @IsOptional()
  userIds: string[];
}

export class UpdateDepartmentDto extends CreateDepartmentDto {}

export class DepartmentResponseDto {
  @ApiProperty({ example: '999fc45f-bb45-4de5-9252-047b9ba155e9' })
  id: string;

  @ApiProperty({ example: 'Web Development' })
  name: string;

  @ApiProperty({ example: false })
  isDeleted?: boolean;

  @ApiProperty({
    example: '999fc45f-bb45-4de5-9252-047b9ba155e9',
    description: 'The department role ID',
  })
  role: { id: string; name: string };

  @ApiProperty({
    example: '999fc45f-bb45-4de5-9252-047b9ba155e9',
    description: 'The department supplier ID',
  })
  Supplier: { id: string; name: string }[];

  @ApiProperty({
    example: '999fc45f-bb45-4de5-9252-047b9ba155e9',
    description: 'The department users',
  })
  User: { id: string; name: string }[];
}
