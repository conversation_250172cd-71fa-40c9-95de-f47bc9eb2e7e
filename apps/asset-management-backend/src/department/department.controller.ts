import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DepartmentService } from './department.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
} from './dto/department.dto';
import {
  GetAllQueryParamsDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  DEPARTMENT_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { Request } from 'express';

@ApiTags('Department')
@Controller('department')
@UseGuards(PermissionGuard)
export class DepartmentController {
  private logger = new Logger('DepartmentController');
  constructor(private readonly departmentService: DepartmentService) {}

  @Get()
  @ApiBearerAuth('access-token')
  @CheckPolicies(
    new PolicyHandler(
      [Action.READ, Action.CREATE, Action.UPDATE],
      [Subject.SUPPLIER, Subject.DEPARTMENT],
    ),
  )
  async getAllDepartments(@Query() dto?: GetAllQueryParamsDto) {
    this.logger.log('API to get all departments');
    const departments = await this.departmentService.getAllDepartments(dto);
    return {
      statusCode: HttpStatus.OK,
      data: departments,
      message: 'Departments fetched succesfully',
    };
  }

  @Get(':departmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched department',
    schema: { $ref: getSchemaPath(HTTPResponseDto<DepartmentResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Department with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: DEPARTMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch department with specified Id',
    summary: 'Fetches department with given Id',
  })
  @ApiParam({
    name: 'departmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter department Id',
  })
  @ApiExtraModels(HTTPResponseDto<DepartmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.DEPARTMENT))
  async getDepartmentById(
    @Param('departmentId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<DepartmentResponseDto>> {
    this.logger.log('API to fetch department with specified Id');

    const department: DepartmentResponseDto =
      await this.departmentService.getDepartmentById(id);

    return {
      statusCode: HttpStatus.OK,
      data: department,
      message: 'Successfully fetched department',
    };
  }

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Succesfully created a department',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<DepartmentResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'No Permission',
    schema: {
      example: {
        statusCode: HttpStatus.FORBIDDEN,
        message:
          'Access Denied: You do not have the necessary permissions to perform this action',
        error: 'Forbidden',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Department already exists',
    schema: {
      example: {
        statusCode: 409,
        message: 'Department name already exists',
        error: 'Conflict',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiExtraModels(HTTPResponseDto<DepartmentResponseDto>)
  @CheckPolicies(new PolicyHandler([Action.CREATE], [Subject.DEPARTMENT]))
  async createDepartment(
    @Body() dto: CreateDepartmentDto,
  ): Promise<HTTPResponseDto<DepartmentResponseDto>> {
    this.logger.log('API to get add department');
    const department = await this.departmentService.createDepartment(dto);
    return {
      statusCode: HttpStatus.CREATED,
      data: department,
      message: 'Succesfully created a department',
    };
  }

  @Put(':departmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated department',
    schema: { $ref: getSchemaPath(HTTPResponseDto<DepartmentResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Department with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: DEPARTMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the department details',
    summary: 'Update department',
  })
  @ApiParam({
    name: 'departmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter department Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<DepartmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.DEPARTMENT))
  async updateDepartmentDetails(
    @Param('departmentId', new ParseUUIDPipe()) id: string,
    @Body() dto: UpdateDepartmentDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<DepartmentResponseDto>> {
    this.logger.log('API to update department details');

    const { user } = request;
    const department: DepartmentResponseDto =
      await this.departmentService.updateDepartment(id, dto, user);

    return {
      statusCode: HttpStatus.OK,
      data: department,
      message: 'Successfully updated department',
    };
  }

  @Delete(':departmentId')
  @CheckPolicies(new PolicyHandler([Action.DELETE], [Subject.DEPARTMENT]))
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted department Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiForbiddenResponse({
    description: 'No Permission',
    schema: {
      example: {
        statusCode: HttpStatus.FORBIDDEN,
        message:
          'Access Denied: You do not have the necessary permissions to perform this action',
        error: 'Forbidden',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: DEPARTMENT_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad request response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the department',
    summary: 'Delete department',
  })
  @ApiParam({
    name: 'departmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter department Id to delete',
  })
  async deleteDepartmentById(
    @Param('departmentId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<boolean>> {
    const departmentDeleteStatus: boolean =
      await this.departmentService.deleteDepartmentById(id);
    return {
      statusCode: HttpStatus.OK,
      data: departmentDeleteStatus,
      message: 'department deleted successfully',
    };
  }
}
