import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateDepartmentDto,
  DepartmentResponseDto,
  UpdateDepartmentDto,
} from './dto/department.dto';
import {
  DEPARTMENT_ALREADY_EXIST,
  DEPARTMENT_NOT_FOUND,
} from 'src/constants/message-constants';
import {
  ChangesOcccuredIn,
  Department,
  HistoryActions,
} from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { getUpdatedFields } from 'src/utility';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

@Injectable()
export class DepartmentService {
  private logger = new Logger('DepartmentService');
  private selectArgs = {
    id: true,
    name: true,
    role: {
      select: {
        id: true,
        name: true,
      },
    },
    Supplier: {
      select: {
        id: true,
        name: true,
      },
    },
    User: {
      select: {
        id: true,
        name: true,
      },
    },
  };
  constructor(private readonly prisma: PrismaService) {}

  /**
   * This service returns all the departments.
   *
   * @returns All departments
   */
  async getAllDepartments(
    dto: GetAllQueryParamsDto,
  ): Promise<DepartmentResponseDto[]> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | null = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    return await this.prisma.department.findMany({
      where: {
        isDeleted: false,
      },
      select: this.selectArgs,
      take: limit,
      skip,
    });
  }

  async getDepartmentById(
    departmentId: string,
  ): Promise<DepartmentResponseDto> {
    const department = await this.prisma.department.findFirst({
      where: { id: departmentId, isDeleted: false },
      select: this.selectArgs,
    });

    if (!department) {
      this.logger.log(`Department with id: ${departmentId} not found`);
      throw new NotFoundException(DEPARTMENT_NOT_FOUND);
    }

    return department;
  }

  async createDepartment(
    dto: CreateDepartmentDto,
  ): Promise<DepartmentResponseDto> {
    const department = await this.prisma.department.findFirst({
      where: {
        name: dto.name,
        isDeleted: false,
      },
    });

    if (department) {
      throw new ConflictException(DEPARTMENT_ALREADY_EXIST);
    }

    const createdDepartment = await this.prisma.department.create({
      data: {
        name: dto.name,
        role: {
          connect: {
            id: dto.roleId,
          },
        },
        Supplier: dto.supplierId
          ? {
              connect: {
                id: dto.supplierId,
              },
            }
          : undefined,
        User: dto.userIds
          ? {
              connect: dto.userIds?.map((id) => ({ id })),
            }
          : undefined,
      },
      select: this.selectArgs,
    });

    this.logger.log('Department created successfully');

    await this.prisma.history.create({
      data: {
        changeInTable: ChangesOcccuredIn.DEPARTMENT,
        action: HistoryActions.CREATED,
        date: new Date(),
        entityId: createdDepartment.id,
        log: {
          departmentId: createdDepartment.id,
          departmentName: createdDepartment.name,
        },
      },
    });
    this.logger.log("History for 'create' created sucessfully");

    if (dto.userIds?.length) {
      await this.prisma.user.updateMany({
        where: { id: { in: dto.userIds } },
        data: {
          roleId: dto.roleId,
        },
      });
    }
    return createdDepartment;
  }

  async updateDepartment(
    departmentId: string,
    dto: UpdateDepartmentDto,
    user: User,
  ): Promise<DepartmentResponseDto> {
    const department = await this.prisma.department.findFirst({
      where: {
        id: departmentId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });

    if (!department) {
      throw new NotFoundException('The department was not found');
    }

    const updatedDepartment = await this.prisma.department.update({
      where: { id: departmentId },
      data: {
        name: dto.name,
        role: {
          connect: {
            id: dto.roleId,
          },
        },
        Supplier: dto.supplierId
          ? {
              connect: {
                id: dto.supplierId,
              },
            }
          : undefined,
        User: dto.userIds
          ? {
              set: dto.userIds?.map((id) => ({ id })),
            }
          : undefined,
      },
      select: this.selectArgs,
    });

    this.logger.log(`Department ${departmentId} updated successfully`);

    // Create a history entry for the department update
    await this.prisma.history.create({
      data: {
        changeInTable: ChangesOcccuredIn.DEPARTMENT,
        action: HistoryActions.UPDATED,
        date: new Date(),
        entityId: departmentId,
        log: {
          userId: user.id,
          name: user.name,
          departmentId: departmentId,
          updatedFields: getUpdatedFields(department, updatedDepartment),
        },
      },
    });

    this.logger.log("History for 'update' created successfully");

    await this.updateDepartmentUsersAndRoles(
      departmentId,
      dto.userIds,
      department,
      dto.roleId,
    );
    return updatedDepartment;
  }

  async deleteDepartmentById(departmentId: string) {
    const department: number = await this.prisma.department.count({
      where: {
        id: departmentId,
        isDeleted: false,
      },
    });

    if (!department) {
      this.logger.log(`Department with specified id not found`);
      throw new NotFoundException(DEPARTMENT_NOT_FOUND);
    }

    const deletedDepartment: Department = await this.prisma.department.update({
      where: {
        id: departmentId,
      },
      data: {
        isDeleted: true,
      },
    });

    this.logger.log('Department isDeleted updated to true successfully');

    await this.prisma.history.create({
      data: {
        changeInTable: ChangesOcccuredIn.DEPARTMENT,
        action: HistoryActions.DELETED,
        date: new Date(),
        entityId: deletedDepartment.id,
        log: {
          departmentId: deletedDepartment.id,
          departmentName: deletedDepartment.name,
        },
      },
    });

    this.logger.log("History for 'delete' created sucessfully");
    return true;
  }

  async updateDepartmentUsersAndRoles(
    departmentId: string,
    updatedUserIds: string[],
    department: DepartmentResponseDto,
    departmentRoleId: string,
  ): Promise<string> {
    const currentUserIds = department.User.map((user) => user.id);

    // Determine users to add and remove
    const usersToAdd = updatedUserIds.filter(
      (id) => !currentUserIds.includes(id),
    );
    const usersToRemove = currentUserIds.filter(
      (id) => !updatedUserIds.includes(id),
    );

    const prismaOperations = [];

    // Assign role to new users
    if (usersToAdd.length > 0) {
      prismaOperations.push(
        this.prisma.user.updateMany({
          where: { id: { in: usersToAdd } },
          data: { roleId: departmentRoleId },
        }),
      );
      this.logger.log(
        `Assigned role ${departmentRoleId} to ${usersToAdd.length} new users in department ${departmentId}`,
      );
    }

    // Remove role from removed users
    if (usersToRemove.length > 0) {
      const role = await this.prisma.role.findFirst({
        where: {
          name: 'employee',
        },
        select: {
          id: true,
        },
      });
      prismaOperations.push(
        this.prisma.user.updateMany({
          where: { id: { in: usersToRemove } },
          data: { roleId: role.id },
        }),
      );
      this.logger.log(
        `Removed role from ${usersToRemove.length} users in department ${departmentId}`,
      );
    }

    // Update role for existing users if roleId has changed
    if (departmentRoleId !== department.role.id) {
      prismaOperations.push(
        this.prisma.user.updateMany({
          where: { id: { in: updatedUserIds } },
          data: { roleId: departmentRoleId }, // Use updated roleId
        }),
      );
      this.logger.log(
        `Updated role to ${departmentRoleId} for ${updatedUserIds.length} users in department ${departmentId}`,
      );
    }

    // Execute all Prisma operations in a transaction
    await this.prisma.$transaction(prismaOperations);

    return `Updated roles for department ${departmentId}`;
  }
}
