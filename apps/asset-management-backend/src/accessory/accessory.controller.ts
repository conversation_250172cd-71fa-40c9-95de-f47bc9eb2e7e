import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  ParseUUIDPipe,
  Put,
  HttpStatus,
  Query,
  Req,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { AccessoryService } from './accessory.service';
import {
  AccessoriesFilterQueryParamsDto,
  AccessoryResponseDto,
  CreateAccessoryDto,
  UpdateAccessoryDto,
} from './dto/accessory.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ACCESSORY_NOT_FOUND,
  INTERNAL_ERROR,
  INVALID_UUID_FORMAT,
} from '../constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';

@ApiTags('Accessories')
@Controller('accessories')
@UseGuards(PermissionGuard)
export class AccessoryController {
  private logger = new Logger('AccessoryController');
  constructor(private readonly accessoryService: AccessoryService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiExtraModels(HTTPResponseDto<AccessoryResponseDto>)
  @ApiCreatedResponse({
    description: 'Accessory created successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AccessoryResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: ['Validation failed (uuid is expected)'],
              error: 'Bad Request',
            },
            summary: 'Example:UUID',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    summary: 'Create a new accessory',
    description: 'This API allows to create a new accessory',
  })
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ACCESSORY))
  async create(
    @Body() createAccessoryDto: CreateAccessoryDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AccessoryResponseDto>> {
    const accessory = await this.accessoryService.create(
      createAccessoryDto,
      request.user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: accessory,
      message: 'Successfully created accessory',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetAllResponseDto<AccessoryResponseDto>)
  @ApiOkResponse({
    description: 'Successfully retreived the accessories',
    schema: { $ref: getSchemaPath(GetAllResponseDto<AccessoryResponseDto>) },
  })
  @ApiNotFoundResponse({
    description: 'ACCESSORY_NOT_FOUND',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ACCESSORY_NOT_FOUND,
        error: 'Not found error',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'INTERNAL_SERVER_ERROR',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'INTERNAL_SERVER_ERROR',
        error: 'Internal server error',
      },
    },
  })
  @ApiOperation({
    summary: 'Get all accessory details',
    description:
      'This API allows to fetch accessories with an optional quary parameter to search by name',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ACCESSORY))
  async findAll(
    @Query() findAllDto?: AccessoriesFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<AccessoryResponseDto[]>> {
    const { accessories, count } =
      await this.accessoryService.findAll(findAllDto);

    return {
      statusCode: HttpStatus.OK,
      data: accessories,
      count,
      message: 'Successfully fetched all accessories',
    };
  }

  @Get(':accessoryId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<AccessoryResponseDto>)
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched accessory',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AccessoryResponseDto>) },
  })
  @ApiNotFoundResponse({
    description: 'Accessory not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Accessory not found',
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: '28c4f6ac-8402-4f1f-961d-8abf4222a837',
  })
  @ApiOperation({ summary: 'Get accessory details by id' })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ACCESSORY))
  async findOne(
    @Param('accessoryId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<AccessoryResponseDto>> {
    const accessory = await this.accessoryService.findOne(id);

    return {
      statusCode: HttpStatus.OK,
      data: accessory,
      message: 'Successfully fetched accessorry by Id',
    };
  }

  @Put(':accessoryId')
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiExtraModels(HTTPResponseDto<AccessoryResponseDto>)
  @ApiOkResponse({
    description: 'Accessory Updated successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AccessoryResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request error',
    content: {
      'application/json': {
        examples: {
          exmaple1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Validation failed (uuid is expected)',
              error: 'Bad Request',
            },
            summary: 'Example:UUID',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    content: {
      'application/json': {
        examples: {
          exmaple1: {
            value: {
              statusCode: HttpStatus.NOT_FOUND,
              message: ACCESSORY_NOT_FOUND,
              error: 'Not found',
            },
            summary: 'Example:Accessory',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: 'd4c1463f-6236-4edb-907b-dd1a5c0dadad',
  })
  @ApiOperation({ summary: 'Update accessory by accessory id' })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ACCESSORY))
  async update(
    @Param('accessoryId', new ParseUUIDPipe()) id: string,
    @Body() updateAccessory: UpdateAccessoryDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AccessoryResponseDto>> {
    const accessory = await this.accessoryService.update(
      id,
      updateAccessory,
      request.user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: accessory,
      message: 'Successfully updated accessory by Id',
    };
  }

  @Delete(':accessoryId')
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOkResponse({
    description: 'Accessory Deleted successfully',
    schema: {
      example: true,
    },
  })
  @ApiNotFoundResponse({
    description: 'Accessory Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ACCESSORY_NOT_FOUND,
        error: 'Not Found Response',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: 'Accessory already deleted',
              error: 'Conflict',
            },
            summary: 'Example:Accessory',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: '7236a1db-5e42-4494-933f-435bacd9e9c4',
  })
  @ApiOperation({
    summary: 'Delete accessory by accessory id',
    description: 'This API soft deletes accessory by Accessory id.',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.ACCESSORY))
  async remove(
    @Param('accessoryId', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const deletedAccessory = await this.accessoryService.remove(
      id,
      request.user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: deletedAccessory,
      message: 'Successfully deleted accessory',
    };
  }

  @Post('assign')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully assigned accessory',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to assign a accessory to users',
    summary: 'Assign a accessory',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ACCESSORY))
  async assignAccessory(
    @Body() dto: CreateAssignmentDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    const { user } = request;
    const assignmentDetails = await this.accessoryService.assignAccessory(
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: assignmentDetails,
      message: 'Successfully assigned accessory',
    };
  }

  @Delete('unassign/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully unassigned the accessory',
    schema: { $ref: getSchemaPath(HTTPResponseDto<boolean>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API unassigns a accessory',
    summary: 'Unassign a software accessory',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '7da45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the assignment ID to unassign',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ACCESSORY))
  async unassignAccessory(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() request: Request,
    @Body() dto: UnassignmentRequestDto,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const unassignedStatus = await this.accessoryService.unassignAccessory(
      dto,
      assignmentId,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: unassignedStatus,
      message: 'Successfully unassigned the accessory',
    };
  }

  @Get('assignments/:accessoryId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto[]>)
  @ApiOkResponse({
    description: 'Successfully retreived the assignments',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto[]>) },
  })
  @ApiNotFoundResponse({
    description: 'ACCESSORY_NOT_FOUND',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ACCESSORY_NOT_FOUND,
        error: 'Not found error',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'INTERNAL_SERVER_ERROR',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'INTERNAL_SERVER_ERROR',
        error: 'Internal server error',
      },
    },
  })
  @ApiOperation({
    summary: 'Get all assignmnets details',
    description: 'This API allows to fetch assignments of a accessory',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ACCESSORY))
  async findAllAssignments(
    @Param('accessoryId', new ParseUUIDPipe()) accessoryId: string,
  ): Promise<HTTPResponseDto<AssignmentResponseDto[]>> {
    const assignments =
      await this.accessoryService.getAllAssignments(accessoryId);

    return {
      statusCode: HttpStatus.OK,
      data: assignments,
      message: 'Successfully fetched all assignments',
    };
  }

  @Get('purchases/:accessoryId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched purchase history for accessory',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<PurchaseResponseDto[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter accessory Id to fetch purchase history',
  })
  @ApiOperation({
    description: 'This API allows to fetch purchase history for accessories',
    summary: 'Fetch purchase history of a accessory',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ACCESSORY))
  async getPurchaseHistory(
    @Param('accessoryId', new ParseUUIDPipe()) accessoryId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<PurchaseResponseDto[]>> {
    this.logger.log('API to get purchase history for accessory');

    const { purchaseDetails, count } =
      await this.accessoryService.getPurchaseHistory(accessoryId, queryParams);

    return {
      statusCode: HttpStatus.OK,
      data: purchaseDetails,
      count,
      message: 'Successfully fetched purchase history for accessory',
    };
  }

  @Get('history/:accessoryId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched accessory history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter accessory Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch accessory history',
    summary: 'Fetch accessory history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ACCESSORY))
  async getAccessoryHistory(
    @Param('accessoryId', new ParseUUIDPipe()) accessoryId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    this.logger.log('API to fetch accessory history');
    const { history, count } = await this.accessoryService.getAccessoryHistory(
      accessoryId,
      queryParams,
    );

    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'Accessory history fetched successfully',
    };
  }

  @Post('purchase/:accessoryId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created purchase details for accessory',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'accessoryId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter accessory Id to create purchase record',
  })
  @ApiOperation({
    description: 'This api allows to create purchase details for a accessory',
    summary: 'Create purchase details for a accessory',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ACCESSORY))
  async createPurchaseDetails(
    @Param('accessoryId', new ParseUUIDPipe()) accessoryId: string,
    @Body() dto: CreatePurchaseDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    this.logger.log('API to create purchase details for accessory');

    const { user } = request;
    const purchaseDetails = await this.accessoryService.createPurchaseDetails(
      dto,
      accessoryId,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: purchaseDetails,
      message: 'Successfully created purchase details for accessory',
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the accessories info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the accessories info',
  })
  async downloadAccessories(): Promise<HTTPResponseDto<string>> {
    const fileName = await this.accessoryService.downloadAccessories();
    return {
      data: fileName,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }
  @Put('purchase-update/:purchaseId/:accessoryId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Purchase details updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid purchase ID',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_UUID_FORMAT,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to update purchase record',
  })
  @ApiOperation({
    description: 'This API updates the purchase record',
    summary: 'Update the purchase record',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ASSET))
  async updatePurchaseRecord(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
    @Body() dto: UpdatePurchaseDto,
    @Req() request: Request,
    @Param('accessoryId', new ParseUUIDPipe()) accessoryId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const updatedPurchase = await this.accessoryService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      accessoryId,
    );
    return {
      statusCode: HttpStatus.OK,
      data: updatedPurchase,
      message: 'Purchase record updated successfully',
    };
  }

  @Delete('purchase/:purchaseId')
  @ApiOkResponse({
    description: 'Purchase deleted successfully',
    schema: {
      example: {
        statusCode: HttpStatus.OK,
        data: true,
        message: 'Successfully deleted purchase',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Purchase not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ACCESSORY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to delete',
  })
  async deletePurchase(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
  ): Promise<HTTPResponseDto<boolean>> {
    const deleteResult = await this.accessoryService.deletePurchase(purchaseId);

    return {
      statusCode: HttpStatus.OK,
      data: deleteResult,
      message: 'Successfully deleted purchase',
    };
  }
}
