import { Test, TestingModule } from '@nestjs/testing';
import { AccessoryService } from './accessory.service';
import { AppModule } from 'src/app.module';

describe('AccessorysService', () => {
  let service: AccessoryService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<AccessoryService>(AccessoryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
