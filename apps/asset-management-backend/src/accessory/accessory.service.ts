import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  Prisma,
  TypeOfCategory,
} from '@prisma/client/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  updateFieldGroups,
  upperSnakeCaseToCamelCase,
} from '../utility';
import {
  ACCESSORY_EXIST,
  ACCESSORY_NOT_FOUND,
} from '../constants/message-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
} from '@prisma-clients/asset-management-backend';
import {
  AccessoriesFilterQueryParamsDto,
  AccessoryResponseDto,
  CreateAccessoryDto,
  GetAllAccessoriesResponsePayload,
  UpdateAccessoryDto,
} from './dto/accessory.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { User } from 'types';
import { PurchaseService } from 'src/purchase/purchase.service';
import { AssignmentService } from 'src/assignment/assignment.service';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';
import { DocumentService } from 'src/document/document.service';
import { UnassignService } from 'src/unassign/unassign.service';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';

@Injectable()
export class AccessoryService {
  private logger = new Logger('AccessoryService');
  private selectArgs = {
    id: true,
    name: true,
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    modelNumber: true,
    totalQuantity: true,
    availableQuantity: true,
    minQuantity: true,
    note: true,
    customFields: true,
    accessoryImageUrl: true,
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly purchaseService: PurchaseService,
    private readonly assignmentService: AssignmentService,
    private readonly awsService: AwsService,
    private readonly documentService: DocumentService,
    private readonly unassignService: UnassignService,
  ) {}

  /**
   * Creates a new accessory.
   * @param {CreateAccessoryDto} accessoryDto - The data for creating the accessory.
   * @returns {Promise<AccessoryResponseDto>} A Promise of the created AccessoryResponseDto.
   * @throws {ConflictException} Throws ConflictException if the accessory name already exists.
   */
  async create(
    accessoryDto: CreateAccessoryDto,
    user: User,
  ): Promise<AccessoryResponseDto> {
    try {
      if (accessoryDto.minQuantity > accessoryDto.purchaseInfo.quantity) {
        throw new BadRequestException(
          `Quantity must be greater than minimum quantity`,
        );
      }

      return await this.prisma.$transaction(async (prisma) => {
        const accessoryCount: number = await prisma.accessory.count({
          where: {
            OR: [
              {
                name: {
                  equals: accessoryDto.name,
                  mode: 'insensitive',
                },
              },
              accessoryDto.modelNumber
                ? {
                    modelNumber: {
                      equals: accessoryDto.modelNumber,
                    },
                  }
                : undefined,
            ],
            isDeleted: false,
          },
        });

        if (accessoryCount !== 0) {
          this.logger.log(ACCESSORY_EXIST);
          throw new ConflictException(ACCESSORY_EXIST);
        }

        const fieldGroups: string[] = accessoryDto.customFields
          ? accessoryDto.customFields['fieldgroups']
          : [];

        //Create new accessory
        const accessory = await prisma.accessory.create({
          data: {
            name: accessoryDto.name,
            location: accessoryDto.location
              ? {
                  connect: {
                    id: accessoryDto.location,
                  },
                }
              : undefined,
            modelNumber: accessoryDto.modelNumber,
            totalQuantity: accessoryDto.purchaseInfo.quantity,
            availableQuantity: accessoryDto.purchaseInfo.quantity,
            minQuantity: accessoryDto.minQuantity,
            note: accessoryDto.note,
            accessoryImageUrl: accessoryDto.accessoryImageUrl,
            customFields: accessoryDto.customFields,
            fieldGroups: {
              connect: fieldGroups.map((fieldGroupId) => ({
                id: fieldGroupId,
              })),
            },
            category: accessoryDto.categoryId
              ? {
                  connect: {
                    id: accessoryDto.categoryId,
                  },
                }
              : undefined,
            manufacturer: accessoryDto.manufacturerId
              ? {
                  connect: {
                    id: accessoryDto.manufacturerId,
                  },
                }
              : undefined,
          },
          select: this.selectArgs,
        });
        this.logger.log(`Accessory Created with id:${accessory.id}`);

        await this.purchaseService.createPurchase(
          TypeOfCategory.ACCESSORY,
          accessoryDto.purchaseInfo,
          accessory.id,
          user,
        );

        this.logger.log(
          `Purchase created for accessory with id: ${accessory.id}`,
        );

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ACCESSORY,
            action: HistoryActions.CREATED,
            date: new Date(),
            entityId: accessory.id,
            log: {
              userId: user.id,
              name: user.name,
              accessoryId: accessory.id,
            },
          },
        });
        this.logger.log(
          `Accessory history created for accessory with id ${accessory.id}`,
        );

        return accessory;
      });
    } catch (error) {
      this.logger.log(`Error while creating Accessory`);
      // If something went wrong, delete new uploaded Image.
      if (accessoryDto.accessoryImageUrl) {
        await this.awsService.deleteFile(accessoryDto.accessoryImageUrl);
        this.logger.log(
          'Accessory image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error.code === 'P2025' &&
        error instanceof Prisma.PrismaClientKnownRequestError
      ) {
        checkErrorAndThrowNotFoundError(error);
      } else throw error;
    }
  }

  /**
   * Finds all accessories based on the provided query parameters.
   * @param {GetAllRequestQueryParamsDto} findAllDto - Query parameters for finding accessories.
   * @returns {Promise<GetAllAccessoryResponseDto['data']>} A Promise of the fetched accessory data.
   */
  async findAll(
    findAllDto?: AccessoriesFilterQueryParamsDto,
  ): Promise<GetAllAccessoriesResponsePayload> {
    const page: number | null = findAllDto?.page ? findAllDto.page : null;
    const limit: number | null = findAllDto?.limit
      ? findAllDto.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [findAllDto?.sortBy || 'createdAt']: findAllDto?.sortOrder || 'desc',
    };
    const allowedLocation = findAllDto?.location;
    const whereOptions: Prisma.AccessoryWhereInput = findAllDto?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: findAllDto.searchInput,
                mode: 'insensitive',
              },
            },
            {
              category: {
                name: {
                  contains: findAllDto.searchInput,
                  mode: 'insensitive',
                },
              },
            },
            {
              modelNumber: {
                contains: findAllDto.searchInput,
                mode: 'insensitive',
              },
            },
          ],
        }
      : undefined;

    const accessories: AccessoryResponseDto[] =
      await this.prisma.accessory.findMany({
        where: {
          ...whereOptions,
          ...(allowedLocation
            ? {
                location: {
                  name: { equals: allowedLocation, mode: 'insensitive' },
                },
              }
            : {}),
          isDeleted: false,
        },
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      });

    const count: number = await this.prisma.accessory.count({
      where: {
        ...whereOptions,
        ...(allowedLocation
          ? {
              location: {
                name: { equals: allowedLocation, mode: 'insensitive' },
              },
            }
          : {}),
        isDeleted: false,
      },
    });

    this.logger.log(`Fetched ${count} accessories successfully`);

    return {
      accessories,
      count,
    };
  }

  /**
   * Finds a single accessory by ID.
   * @param {string} id - The ID of the accessory to find.
   * @returns {Promise<AccessoryResponseDto>} A Promise of the fetched AccessoryResponseDto.
   * @throws {NotFoundException} Throws NotFoundException if the accessory is not found.
   */
  async findOne(id: string): Promise<AccessoryResponseDto> {
    const accessory = await this.prisma.accessory.findFirst({
      where: {
        id,
        isDeleted: false,
      },
      select: this.selectArgs,
    });

    if (!accessory) {
      this.logger.log(`accessory of id ${id} not found.`);
      throw new NotFoundException(ACCESSORY_NOT_FOUND);
    }

    this.logger.log(`Successfully fetched accessory of id ${accessory.id}`);

    return accessory;
  }

  /**
   * Update an existing accessory.
   * @param {string} id - The ID of the accessory to update.
   * @param {UpdateAccessoryDto} accessoryDto - The updated data for the accessory.
   * @returns {Promise<AccessoryResponseDto>} A Promise of the updated AccessoryResponseDto.
   * @throws {NotFoundException} Throws NotFoundException if the accessory is not found.
   */
  async update(
    id: string,
    accessoryDto: UpdateAccessoryDto,
    user: User,
  ): Promise<AccessoryResponseDto> {
    let accessory: AccessoryResponseDto;
    try {
      return await this.prisma.$transaction(async (prisma) => {
        accessory = await prisma.accessory.findFirst({
          where: {
            id,
            isDeleted: false,
          },
          select: this.selectArgs,
        });

        if (!accessory) {
          this.logger.log(`${ACCESSORY_NOT_FOUND} for id ${id}`);
          throw new NotFoundException(ACCESSORY_NOT_FOUND);
        }

        /**
         * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
         **/
        await updateFieldGroups(
          prisma,
          'accessory',
          id,
          accessory,
          accessoryDto,
        );

        const accessoryCount: number = await prisma.accessory.count({
          where: {
            NOT: {
              name: {
                equals: accessory.name,
                mode: 'insensitive',
              },
            },
            OR: [
              {
                name: {
                  equals: accessoryDto.name,
                  mode: 'insensitive',
                },
              },
              accessoryDto.modelNumber
                ? {
                    modelNumber: {
                      equals: accessoryDto.modelNumber,
                      mode: 'insensitive',
                    },
                  }
                : undefined,
            ],
            isDeleted: false,
          },
        });

        if (accessoryCount) {
          this.logger.log(ACCESSORY_EXIST);
          throw new ConflictException(ACCESSORY_EXIST);
        }

        const updatedAccessory = await prisma.accessory.update({
          where: {
            id,
          },
          data: {
            name: accessoryDto.name,
            category: accessoryDto.categoryId
              ? {
                  connect: {
                    id: accessoryDto.categoryId,
                  },
                }
              : undefined,
            manufacturer: accessoryDto.manufacturerId
              ? {
                  connect: {
                    id: accessoryDto.manufacturerId,
                  },
                }
              : undefined,
            location: accessoryDto.location
              ? {
                  connect: {
                    id: accessoryDto.location,
                  },
                }
              : undefined,
            modelNumber: accessoryDto.modelNumber,
            minQuantity: accessoryDto.minQuantity,
            note: accessoryDto.note,
            customFields: accessoryDto.customFields,
            accessoryImageUrl: accessoryDto.accessoryImageUrl,
          },
          select: this.selectArgs,
        });
        this.logger.log(`Successfully updated accessory with id ${id}`);

        // If update successfully, delete previous image attached with accessory
        if (
          updatedAccessory &&
          accessory.accessoryImageUrl &&
          accessory.accessoryImageUrl !== accessoryDto.accessoryImageUrl
        ) {
          this.awsService.deleteFile(accessory.accessoryImageUrl);
          this.logger.log('Accessory image on s3 bucket deleted successfully');
        }

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ACCESSORY,
            action: HistoryActions.UPDATED,
            date: new Date(),
            entityId: updatedAccessory.id,
            log: {
              userId: user.id,
              name: user.name,
              accessoryId: updatedAccessory.id,
              updatedFields: getUpdatedFields(accessory, updatedAccessory),
            },
          },
        });
        this.logger.log(
          `Accessory history created for accessory with id ${id}`,
        );

        return updatedAccessory;
      });
    } catch (error) {
      this.logger.log(`Error while updating Accessory`);
      // If something went wrong, delete new uploaded Image.
      if (
        accessoryDto.accessoryImageUrl &&
        accessory.accessoryImageUrl !== accessoryDto.accessoryImageUrl
      ) {
        await this.awsService.deleteFile(accessoryDto.accessoryImageUrl);
        this.logger.log(
          'Accessory image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error.code === 'P2025' &&
        error instanceof Prisma.PrismaClientKnownRequestError
      ) {
        checkErrorAndThrowNotFoundError(error);
      } else throw error;
    }
  }

  /**
   * Removes an accessory by setting its `isDeleted` flag to true.
   * @param {string} id - The ID of the accessory to remove.
   * @returns {Promise<boolean>} A Promise indicating the success of the removal operation.
   * @throws Throws an error if the accessory is not found.
   */
  async remove(id: string, user: User): Promise<boolean> {
    const accessorryTransaction = await this.prisma.$transaction(
      async (prisma) => {
        const accessory = await prisma.accessory.findFirst({
          where: {
            id,
            isDeleted: false,
          },
        });

        if (!accessory) {
          this.logger.log(`${ACCESSORY_NOT_FOUND} for id ${id}`);
          throw new NotFoundException(ACCESSORY_NOT_FOUND);
        }

        const deletedAccessory = await prisma.accessory.update({
          where: {
            id,
          },
          data: {
            isDeleted: true,
            customFields: {},
          },
        });

        this.logger.log(`Successfully deleted accessory of id ${id}`);

        /**
         * Disconnects existing field groups associated with the specified accessory.
         **/
        await disconnectFieldGroups(prisma, 'accessory', id, accessory);
        this.logger.log(`Asset disconnected from all field groups`);

        // If accessory deleted successfully, delete image attached with accessory.
        if (accessory.accessoryImageUrl && deletedAccessory) {
          await this.awsService.deleteFile(accessory.accessoryImageUrl);

          this.logger.log(
            'Accessory image uploaded on s3 bucket deleted successfully',
          );
        }
        // Delete accessory documents
        const documentId = await prisma.document.findMany({
          where: {
            entityId: id,
          },
          select: {
            id: true,
          },
        });
        if (documentId) {
          documentId.map(async (document) => {
            await this.documentService.deleteDocument(document.id, user);
          });
          this.logger.log(
            `The documents related to accessory ID:${id} deleted successfully`,
          );
        }

        await this.prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ACCESSORY,
            action: HistoryActions.DELETED,
            entityId: deletedAccessory.id,
            date: new Date(),
            log: {
              userId: user.id,
              name: user.name,
              accessoryId: deletedAccessory.id,
            },
          },
        });

        this.logger.log(
          `Accessory history created for accessory with id ${id}`,
        );

        return true;
      },
    );
    return accessorryTransaction;
  }

  /**
   * Assigns a accessory to the user id specified in the dto.
   *
   * @param {CreateAssignmentDto} dto - The data transfer object containing assignment details.
   * @param {User} user - The user who assigned the accessory (to record history).
   * @returns {Promise<AssignmentResponseDto>} A promise representing the result of the assignment creation.
   */
  async assignAccessory(
    dto: CreateAssignmentDto,
    user: User,
  ): Promise<AssignmentResponseDto> {
    const assignmentDetails = await this.assignmentService.assign(
      TypeOfCategory.ACCESSORY,
      dto,
      user,
    );
    await this.awsService.assignmentNotifyEmail(assignmentDetails, dto.tag);
    this.logger.log('The mail sent successfully to mentioned notify users');

    return assignmentDetails;
  }

  /**
   * This method unassigns a accessory from a user.
   * @param {string} assignmentId - The ID of the assignment record in the assignment table to unassign.
   * @param {User} user - The user who unassigned the accessory (to record history).
   * @returns {Promise<boolean>} A Promise that resolves to a boolean indicating whether the unassignment was successful.
   */
  async unassignAccessory(
    dto: UnassignmentRequestDto,
    assignmentId: string,
    user: User,
  ): Promise<boolean> {
    const assignmentInfo =
      await this.assignmentService.findAssignmentById(assignmentId);
    const unassignedDetails = {
      unAssignedNote: dto.note,
      notifyUser: dto.notifyUser,
      assignedUser: assignmentInfo.user.name,
      assignedNote: assignmentInfo.note,
      assignedDate: assignmentInfo.date,
      typeOfCategory: TypeOfCategory.ACCESSORY,
      entityId: assignmentInfo.entityId,
      user: user,
      assignedUserEmail: assignmentInfo.user.email,
    };
    await this.unassignService.createUnassignmentRecord(
      unassignedDetails,
      user,
    );
    return this.assignmentService.unAssign(assignmentId, user);
  }

  /**
   * This method unassigns a accessory from a user.
   * @param {string} accessoryId - The ID of the assignment record in the assignment table to unassign.
   * @returns {Promise<AssignmentResponseDto[]>} A Promise that resolves to an arrary of assignments for the given accessory
   */
  async getAllAssignments(
    accessoryId: string,
  ): Promise<AssignmentResponseDto[]> {
    return this.assignmentService.findAllByEntityId(accessoryId);
  }

  /**
   * Retrieves the purchase history for the specified entity.
   *
   * @param {string} entityId - The identifier of the entity for which the purchase history is requested.
   * @returns {Promise<PurchaseResponseDto[]>} A promise representing an array of purchase history details for
   * the specified entity.
   */
  async getPurchaseHistory(
    entityId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllPurchasesResponse> {
    this.logger.log('API to get purchase history for accessory');
    return this.purchaseService.getEntityPurchaseDetails(entityId, queryParams);
  }

  /**
   * Retrieves the history of an accessory based on its ID and specified query parameters.
   *
   * @param {string} accessoryId - The ID of the accessory for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If the accessory with the provided ID is not found.
   */
  async getAccessoryHistory(
    accessoryId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const accessory = await this.prisma.accessory.count({
      where: {
        id: accessoryId,
        isDeleted: false,
      },
    });

    if (!accessory) {
      this.logger.log(`Accessory with id ${accessoryId} not found`);
      throw new NotFoundException(ACCESSORY_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    const history = await this.prisma.history.findMany({
      where: {
        entityId: accessoryId,
      },
      select: {
        action: true,
        date: true,
        log: true,
        changeInTable: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: accessoryId,
      },
    });

    this.logger.log(`Fetched history for accessory with id ${accessoryId}`);

    return { history, count };
  }

  /**
   * Creates purchase details for a accessory entity.
   *
   * @param {CreatePurchaseDto} dto - The data transfer object containing purchase details.
   * @param {string} entityId - The identifier of the entity for which the purchase is being created.
   * @param {User} user - The user who created the record for purchase (to record history).
   * @throws {BadRequestException} Throws an exception if the purchase type is not accessory.
   * @returns {Promise<PurchaseResponseDto>} A promise representing the result of the purchase created.
   */
  async createPurchaseDetails(
    dto: CreatePurchaseDto,
    entityId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.createPurchase(
      TypeOfCategory.ACCESSORY,
      dto,
      entityId,
      user,
    );
  }

  async downloadAccessories(): Promise<string> {
    const accessories = await this.prisma.accessory.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        name: true,
        category: {
          select: {
            name: true,
          },
        },
        manufacturer: {
          select: {
            name: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        modelNumber: true,
        totalQuantity: true,
        availableQuantity: true,
        minQuantity: true,
        note: true,
        customFields: true,
      },
    });
    const parsedDataPromises = accessories.map(
      async ({ category, manufacturer, location, customFields, ...rest }) => {
        const categoryName = category?.name;
        const manufacturerName = manufacturer?.name;
        const locationName = location?.name;

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          categoryName,
          manufacturerName,
          locationName,
          ...customFieldData,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/accessories',
    );
    return fileName;
  }

  /**
   * Updates a purchase record.
   * @param {UpdatePurchaseDto} dto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    dto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    accessoryId: string,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      accessoryId,
    );
  }

  /**
   * Deletes a purchase associated with an accessory.
   * @param {string} purchaseId - The ID of the purchase to delete.
   * @param {User} user - The user performing the deletion (to record history).
   * @returns {Promise<boolean>} A Promise indicating the success of the deletion operation.
   * @throws {NotFoundException} Throws an error if the purchase is not found.
   */
  async deletePurchase(purchaseId: string): Promise<boolean> {
    try {
      const deletedPurchase = await this.prisma.purchase.delete({
        where: {
          id: purchaseId,
        },
        select: {
          entityId: true,
          typeOfPurchase: true,
          quantity: true,
        },
      });
      const entityType = upperSnakeCaseToCamelCase(
        deletedPurchase.typeOfPurchase,
      );
      await this.prisma[entityType].update({
        where: {
          id: deletedPurchase.entityId,
        },
        data: {
          totalQuantity: {
            decrement: deletedPurchase.quantity,
          },
          availableQuantity: {
            decrement: deletedPurchase.quantity,
          },
        },
      });
      if (!deletedPurchase) {
        this.logger.log(`Purchase with id ${purchaseId} not found.`);
        throw new NotFoundException(
          `Purchase with id ${purchaseId} not found.`,
        );
      }

      this.logger.log(`Successfully deleted purchase with id ${purchaseId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error while deleting purchase: ${error.message}`);
      throw error;
    }
  }
}
