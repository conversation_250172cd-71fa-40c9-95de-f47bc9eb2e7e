import { Test, TestingModule } from '@nestjs/testing';
import { AccessoryController } from './accessory.controller';
import { AppModule } from 'src/app.module';

describe('AccessoryController', () => {
  let controller: AccessoryController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<AccessoryController>(AccessoryController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
