import {
  IsS<PERSON>,
  IsOptional,
  <PERSON>Int,
  <PERSON>U<PERSON><PERSON>,
  IsNotEmpty,
  Min,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreatePurchaseDto } from 'src/purchase/dto/purchase.dto';
import { Type } from 'class-transformer';
import { JsonValue } from 'types';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { EntityDto } from 'src/common/dto/entity.dto';

export class UpdateAccessoryDto {
  @ApiProperty({
    description: 'Name of the accessory',
    example: 'Keyboard',
    required: true,
  })
  @IsNotEmpty({ message: 'Accessory name cannot be empty' })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'CategoryId of the accessory',
    example: '08b9858c-73dc-491f-af22-132834fad68e',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  categoryId: string;

  @ApiProperty({
    description: 'ManufaturerId of the accessory',
    example: '28c4f6ac-8402-4f1f-961d-8abf4222a837',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  manufacturerId: string;

  @ApiProperty({
    description: 'Location of the accessory',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  location: string;

  @ApiProperty({
    description: 'ModelNumber of the accessory',
    example: 'Logitech AB-123S',
    required: false,
  })
  @IsOptional()
  @IsString()
  modelNumber: string;

  @ApiProperty({
    description: 'Minimum Quantity of the accessory',
    example: 15,
    required: false,
  })
  @IsOptional()
  @Min(0)
  @IsInt()
  minQuantity: number;

  @ApiProperty({
    description: 'Description note of the accessory',
    example: 'This is a sample note on accessory',
    required: false,
  })
  @IsOptional()
  @IsString()
  note: string;

  @ApiProperty({
    description: 'Image URL of the accessory',
    example: 'https://example.com/image.jpg',
    required: false,
  })
  @IsOptional()
  accessoryImageUrl: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreateAccessoryDto extends UpdateAccessoryDto {
  @ApiProperty({
    description: 'Purchase details for accessory',
    type: CreatePurchaseDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreatePurchaseDto)
  purchaseInfo: CreatePurchaseDto;
}

export class AccessoryResponseDto {
  @ApiProperty({ example: '999fc45f-bb45-4de5-9252-047b9ba155e9' })
  id: string;

  @ApiProperty({
    example: 'Keyboard',
  })
  name: string;

  @ApiProperty({
    description: 'Manufacturer details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'manufacturer-name',
    },
  })
  manufacturer: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Category details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'category-name',
    },
  })
  category: {
    id: string;
    name: string;
  };

  @ApiProperty({
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Bangalore',
    },
  })
  location: EntityDto;

  @ApiProperty({
    example: 'Logitech AB-123S',
  })
  modelNumber: string;

  @ApiProperty({
    example: 123,
  })
  totalQuantity: number;

  @ApiProperty({
    example: 15,
  })
  minQuantity: number;

  @ApiProperty({
    example: 'This is a sample note on accessory.',
  })
  note: string;

  @ApiProperty({
    example: 'https://example.com/image.jpg',
  })
  accessoryImageUrl: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class GetAllAccessoriesResponsePayload {
  @ApiProperty({
    description: 'Accessories response data',
    isArray: true,
    type: AccessoryResponseDto,
  })
  accessories: AccessoryResponseDto[];

  @ApiProperty({
    description: 'Total count of accessories',
    type: 'number',
    example: 100,
  })
  count: number;
}

export class AccessoriesFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Filter appliance based on category',
    type: 'string',
    example: 'laptop',
    required: false,
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({
    description: 'Order assets based on location',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  location?: string;
}
