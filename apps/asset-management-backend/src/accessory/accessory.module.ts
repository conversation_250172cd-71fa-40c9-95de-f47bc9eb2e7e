import { Module } from '@nestjs/common';
import { AccessoryService } from './accessory.service';
import { AccessoryController } from './accessory.controller';
import { PurchaseService } from 'src/purchase/purchase.service';
import { AssignmentService } from 'src/assignment/assignment.service';
import { DocumentService } from 'src/document/document.service';
import { AbilityModule } from 'src/ability/ability.module';
import { UnassignService } from 'src/unassign/unassign.service';

@Module({
  imports: [AbilityModule],
  controllers: [AccessoryController],
  providers: [
    AccessoryService,
    PurchaseService,
    AssignmentService,
    DocumentService,
    UnassignService,
  ],
})
export class AccessoryModule {}
