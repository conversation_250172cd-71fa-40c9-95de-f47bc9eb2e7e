import { Module } from '@nestjs/common';
import { ContractsService } from './contracts.service';
import { ContractsController } from './contracts.controller';
import { PrismaService } from 'src/prisma/prisma.service';
import { DocumentService } from 'src/document/document.service';

@Module({
  providers: [ContractsService, PrismaService, DocumentService],
  controllers: [ContractsController],
})
export class ContractsModule {}
