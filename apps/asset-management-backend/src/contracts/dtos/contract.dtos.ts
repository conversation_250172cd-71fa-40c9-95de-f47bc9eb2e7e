import { ApiProperty } from '@nestjs/swagger';
import { ContractType } from '@prisma-clients/asset-management-backend';
import { IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';

export class CreateContractDto {
  @ApiProperty({
    description: 'Name of the contract',
    type: 'string',
    required: true,
    example: 'Test contract',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Type of contract',
    enum: ContractType,
    example: ContractType.CONTRACTS,
  })
  @IsEnum(ContractType)
  @IsOptional()
  type: ContractType;

  @ApiProperty({
    description: 'Start date of contract',
    type: 'date',
  })
  @IsOptional()
  startDate: Date;

  @ApiProperty({
    description: 'End date of contract',
    type: 'date',
  })
  @IsOptional()
  endDate: Date;

  @ApiProperty({
    description: 'Location',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
  })
  @IsOptional()
  @IsUUID()
  location: string;

  @ApiProperty({
    description: 'Address regarding the contract',
    type: 'string',
    example: 'Main road, Banglore',
  })
  @IsOptional()
  address: string;

  @ApiProperty({
    description: 'The supplier ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsUUID()
  @IsOptional()
  vendorId: string;

  @ApiProperty({
    description: 'The name of the contractors',
    type: 'array',
    example: "['Justin']",
  })
  @IsOptional()
  contactName: string[];

  @ApiProperty({
    description: 'The emails of the contractors',
    type: 'array',
    example: "['<EMAIL>']",
  })
  @IsOptional()
  contactEmail: string[];

  @ApiProperty({
    description: 'The contact number of the contractors',
    type: 'array',
    example: "['1234567890']",
  })
  @IsOptional()
  contactNumber: string[];
}

export class UpdateContractDto extends CreateContractDto {}

export class GetContractResponseDto {
  @ApiProperty({
    description: 'The supplier ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the contract',
    type: 'string',
    example: 'Test contract',
  })
  name: string;

  @ApiProperty({
    description: 'Type of contract',
    enum: ContractType,
    example: ContractType.AGREEMENTS,
  })
  type: ContractType;

  @ApiProperty({
    description: 'Start date of contract',
    type: 'date',
  })
  startDate: Date;

  @ApiProperty({
    description: 'End date of contract',
    type: 'date',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Location',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Banglore',
    },
  })
  location: EntityDto;

  @ApiProperty({
    description: 'Address regarding the contract',
    type: 'string',
    example: 'Main road, Banglore',
  })
  address: string;

  @ApiProperty({
    description: 'The supplier ID and name',
    example: "'be229c1c-1dc2-4660-8be3-8628e0096847','justin' ",
  })
  vendor: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'The name of the contractors',
    type: 'array',
    example: "['Justin']",
  })
  contactName: string[];

  @ApiProperty({
    description: 'The emails of the contractors',
    type: 'array',
    example: "['<EMAIL>']",
  })
  contactEmail: string[];

  @ApiProperty({
    description: 'The contact number of the contractors',
    type: 'array',
    example: "['1234567890']",
  })
  contactNumber: string[];
}

export class GetAllContractsResponseDto {
  @ApiProperty({
    description: 'Contract response data',
    type: 'array',
    example: [GetContractResponseDto],
    isArray: true,
  })
  data: GetContractResponseDto[];

  @ApiProperty({
    description: 'Total count of contracts',
    type: 'number',
    example: 100,
  })
  count: number;
}
