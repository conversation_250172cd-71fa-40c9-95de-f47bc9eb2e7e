import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateContractDto,
  GetAllContractsResponseDto,
  GetContractResponseDto,
  UpdateContractDto,
} from './dtos/contract.dtos';
import { User } from 'types';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  getUpdatedFields,
  setDateWithZeroTime,
} from 'src/utility';
import { DocumentService } from 'src/document/document.service';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import {
  CONTRACT_EXISTS,
  CONTRACT_NOT_FOUND,
} from 'src/constants/message-constants';

@Injectable()
export class ContractsService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly documentService: DocumentService,
  ) {}
  private logger = new Logger('ContractsService');
  private selectedArgs = {
    id: true,
    name: true,
    type: true,
    startDate: true,
    endDate: true,
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    address: true,
    vendor: {
      select: {
        id: true,
        name: true,
      },
    },
    contactName: true,
    contactEmail: true,
    contactNumber: true,
  };

  /**
   * Creates a new contract.
   * @param {CreateContractDto} dto - The contract data to be created.
   * @param {User} user - The user performing the contract creation.
   * @returns {Promise<GetContractResponseDto>} A promise that resolves to the created contract.
   * @throws {Error} Throws an error if contract creation fails.
   */
  async createContract(
    dto: CreateContractDto,
    user: User,
  ): Promise<GetContractResponseDto> {
    try {
      const prismaTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const count = await prisma.contracts.findFirst({
            where: {
              name: {
                equals: dto.name,
                mode: 'insensitive',
              },
            },
          });
          if (count) {
            this.logger.error(CONTRACT_EXISTS);
            throw new ConflictException(CONTRACT_EXISTS);
          }
          const createdContract = await prisma.contracts.create({
            data: {
              name: dto.name,
              type: dto.type,
              startDate: dto.startDate
                ? setDateWithZeroTime(dto.startDate)
                : undefined,
              endDate: dto.endDate
                ? setDateWithZeroTime(dto.endDate)
                : undefined,
              location: dto.location
                ? {
                    connect: {
                      id: dto.location,
                    },
                  }
                : undefined,
              address: dto.address,
              contactName: dto.contactName,
              contactEmail: dto.contactEmail,
              contactNumber: dto.contactNumber,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
            },
            select: this.selectedArgs,
          });
          this.logger.log('Contract created successfully');
          await prisma.history.create({
            data: {
              action: HistoryActions.CREATED,
              changeInTable: ChangesOcccuredIn.CONTRACTS,
              date: new Date(),
              entityId: createdContract.id,
              log: {
                userId: user.id,
                name: user.name,
                contractId: createdContract.id,
              },
            },
          });
          this.logger.log("History for 'contract create' created successfully");
          return createdContract;
        },
      );
      return prismaTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create contract: ${error.message}`);
      throw error;
    }
  }

  /**
   * Deletes a contract.
   * @param {string} contractId - The ID of the contract to be deleted.
   * @param {User} user - The user performing the contract deletion.
   * @returns {Promise<boolean>} A promise that resolves to true if the contract is deleted successfully.
   * @throws {NotFoundException} Throws a NotFoundException if the contract with the specified ID is not found.
   * @throws {Error} Throws an error if contract deletion fails.
   */
  async deleteContract(contractId: string, user: User): Promise<boolean> {
    const contract = await this.prisma.contracts.findFirst({
      where: {
        id: contractId,
        isDeleted: false,
      },
      select: this.selectedArgs,
    });
    if (!contract) {
      this.logger.error(
        `The contract not found with specified ID:${contractId}`,
      );
      throw new NotFoundException(
        `The contract not found with specified ID:${contractId}`,
      );
    }
    await this.prisma.contracts.update({
      where: {
        id: contractId,
      },
      data: {
        isDeleted: true,
      },
    });
    this.logger.log('Contract deleted successfully');
    const documentId = await this.prisma.document.findMany({
      where: {
        entityId: contractId,
      },
      select: {
        id: true,
      },
    });
    if (documentId) {
      Promise.allSettled(
        documentId.map(async (document) => {
          await this.documentService.deleteDocument(document.id, user);
        }),
      );
      this.logger.log(
        `The documents related to contract ID:${contractId} deleted successfully`,
      );
    }
    await this.prisma.history.create({
      data: {
        action: HistoryActions.DELETED,
        changeInTable: ChangesOcccuredIn.CONTRACTS,
        date: new Date(),
        entityId: contract.id,
        log: {
          userId: user.id,
          name: user.name,
          contractId: contract.id,
        },
      },
    });
    this.logger.log("History for 'contract delete' created successfully");
    return true;
  }

  /**
   * Retrieves a contract by its ID.
   * @param {string} contractId - The ID of the contract to retrieve.
   * @returns {Promise<GetContractResponseDto>} A promise that resolves to the retrieved contract.
   * @throws {NotFoundException} Throws a NotFoundException if the contract with the specified ID is not found.
   */
  async getContractById(contractId: string): Promise<GetContractResponseDto> {
    const contract = await this.prisma.contracts.findFirst({
      where: {
        id: contractId,
        isDeleted: false,
      },
      select: this.selectedArgs,
    });
    if (!contract) {
      this.logger.error(
        `The contract not found with specified ID:${contractId}`,
      );
      throw new NotFoundException(
        `The contract not found with specified ID:${contractId}`,
      );
    }
    this.logger.log('The contract fetched successfully');
    return contract;
  }

  /**
   * Updates a contract.
   * @param {string} contractId - The ID of the contract to update.
   * @param {UpdateContractDto} dto - The updated contract data.
   * @param {User} user - The user performing the contract update.
   * @returns {Promise<GetContractResponseDto>} A promise that resolves to the updated contract.
   * @throws {NotFoundException} Throws a NotFoundException if the contract with the specified ID is not found.
   * @throws {Error} Throws an error if contract update fails.
   */
  async updateContract(
    contractId: string,
    dto: UpdateContractDto,
    user: User,
  ): Promise<GetContractResponseDto> {
    try {
      const prismaTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const contract = await prisma.contracts.findFirst({
            where: {
              id: contractId,
              isDeleted: false,
            },
            select: this.selectedArgs,
          });
          if (!contract) {
            this.logger.error(
              `The contract not found with specified ID:${contractId}`,
            );
            throw new NotFoundException(
              `The contract not found with specified ID:${contractId}`,
            );
          }
          const updatedContract = await prisma.contracts.update({
            where: {
              id: contractId,
            },
            data: {
              name: dto.name,
              type: dto.type,
              startDate: dto.startDate
                ? setDateWithZeroTime(dto.startDate)
                : undefined,
              endDate: dto.endDate
                ? setDateWithZeroTime(dto.endDate)
                : undefined,
              location: dto.location
                ? {
                    connect: {
                      id: dto.location,
                    },
                  }
                : undefined,
              address: dto.address,
              contactName: dto.contactName,
              contactEmail: dto.contactEmail,
              contactNumber: dto.contactNumber,
              vendor: dto.vendorId
                ? {
                    connect: {
                      id: dto.vendorId,
                    },
                  }
                : undefined,
            },
            select: this.selectedArgs,
          });
          this.logger.log('Contract updated successfully');
          await prisma.history.create({
            data: {
              action: HistoryActions.UPDATED,
              changeInTable: ChangesOcccuredIn.CONTRACTS,
              date: new Date(),
              entityId: updatedContract.id,
              log: {
                userId: user.id,
                name: user.name,
                contractId: updatedContract.id,
                updatedFileds: getUpdatedFields(contract, updatedContract),
              },
            },
          });
          this.logger.log(
            "History for 'contract update' created successfully ",
          );
          return updatedContract;
        },
      );
      return prismaTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to update the contract: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves all contracts based on query parameters.
   * @param {GetAllQueryParamsDto} dto - The query parameters for filtering, pagination, and sorting.
   * @returns {Promise<GetAllContractsResponseDto>} A promise that resolves to an object containing the fetched contracts and their count.
   * @throws {Error} Throws an error if the retrieval process fails.
   */
  async getAllContracts(
    dto: GetAllQueryParamsDto,
  ): Promise<GetAllContractsResponseDto> {
    // Extract page and limit from the query parameters, setting them to undefined if not provided.
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    // Calculate the number of records to skip based on pagination parameters.
    const skip: number = page && limit ? (page - 1) * limit : 0;

    // Define the orderBy object for sorting based on query parameters.
    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };
    const whereOptions: Prisma.ContractsWhereInput = {
      isDeleted: false,
      ...(dto?.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                vendor: {
                  name: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              },
            ],
          }
        : undefined),
    };
    const [contracts, count] = await this.prisma.$transaction([
      this.prisma.contracts.findMany({
        where: whereOptions,
        take: limit,
        skip,
        orderBy,
        select: this.selectedArgs,
      }),
      this.prisma.contracts.count({
        where: whereOptions,
      }),
    ]);
    this.logger.log('Successfully fetched all contracts');
    return {
      data: contracts,
      count,
    };
  }

  /**
   * Retrieves all history related to a contract based on contract ID and query parameters.
   * @param {string} contractId - The ID of the contract to retrieve history for.
   * @param {GetAllQueryParamsDto} dto - The query parameters for filtering, pagination, and sorting.
   * @returns {Promise<GetEntityHistoryResponse>} A promise that resolves to an object containing the fetched history and its count.
   * @throws {NotFoundException} Throws a NotFoundException if the contract with the specified ID is not found.
   * @throws {Error} Throws an error if the retrieval process fails.
   */
  async getAllContractHistory(
    contractId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const contract = await this.prisma.contracts.findFirst({
      where: {
        id: contractId,
        isDeleted: false,
      },
    });
    if (!contract) {
      this.logger.error('The contract is not found for this ID');
      throw new NotFoundException(CONTRACT_NOT_FOUND);
    }
    // Extract page and limit from the query parameters, setting them to undefined if not provided.
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    // Calculate the number of records to skip based on pagination parameters.
    const skip: number = page && limit ? (page - 1) * limit : 0;

    // Define the orderBy object for sorting based on query parameters.
    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };
    const [history, count] = await this.prisma.$transaction([
      this.prisma.history.findMany({
        where: {
          entityId: contractId,
        },
        take: limit,
        skip,
        orderBy,
        select: {
          action: true,
          log: true,
          date: true,
        },
      }),
      this.prisma.history.count({
        where: {
          entityId: contractId,
        },
      }),
    ]);
    this.logger.log('All history related to a contract fetched successfully');
    return {
      history,
      count,
    };
  }
}
