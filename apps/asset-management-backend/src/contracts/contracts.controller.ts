import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { ContractsService } from './contracts.service';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  CONTRACT_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import {
  CreateContractDto,
  GetContractResponseDto,
  UpdateContractDto,
} from './dtos/contract.dtos';
import { Request } from 'express';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';

@Controller('contracts')
@ApiTags('Contracts')
export class ContractsController {
  constructor(private readonly contractsService: ContractsService) {}
  private logger = new Logger('ContractsController');

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Contract created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a contract',
    summary: 'Create a contract',
  })
  @ApiExtraModels(HTTPResponseDto<GetContractResponseDto>)
  async createContract(
    @Body() dto: CreateContractDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetContractResponseDto>> {
    const { user } = request;
    this.logger.log('API to create a contract');
    const contract = await this.contractsService.createContract(dto, user);
    return {
      message: 'Contract created successfully',
      data: contract,
      statusCode: HttpStatus.CREATED,
    };
  }

  @Delete(':contractId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Contract deleted Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Contract with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CONTRACT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete a contract',
    summary: 'Delete a contract',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  async deleteContract(
    @Param('contractId', new ParseUUIDPipe()) contractId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const isDeleted = await this.contractsService.deleteContract(
      contractId,
      user,
    );
    return {
      message: 'The contract deleted successfully',
      data: isDeleted,
      statusCode: HttpStatus.OK,
    };
  }

  @Get(':contractId')
  @ApiBearerAuth('access-token')
  @ApiFoundResponse({
    description: 'Contract fetched Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Contract with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CONTRACT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch a contract',
    summary: 'Fetch a contract',
  })
  @ApiExtraModels(HTTPResponseDto<GetContractResponseDto>)
  async getContractById(
    @Param('contractId', new ParseUUIDPipe()) contractId: string,
  ): Promise<HTTPResponseDto<GetContractResponseDto>> {
    this.logger.log('API to fetch a contract');
    const contract = await this.contractsService.getContractById(contractId);
    return {
      message: 'Contract fetched successfully',
      data: contract,
      statusCode: HttpStatus.FOUND,
    };
  }

  @Put(':contractId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Contract updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to update a contract',
    summary: 'Update a contract',
  })
  @ApiExtraModels(HTTPResponseDto<GetContractResponseDto>)
  async updateContract(
    @Param('contractId', new ParseUUIDPipe()) contractId: string,
    @Body() dto: UpdateContractDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetContractResponseDto>> {
    const { user } = request;
    this.logger.log('API to update the contract');
    const updatedContract = await this.contractsService.updateContract(
      contractId,
      dto,
      user,
    );
    return {
      message: 'Contract updated successfully',
      data: updatedContract,
      statusCode: HttpStatus.OK,
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'All contracts fetched Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all contracts',
    summary: 'Fetch all contracts',
  })
  @ApiExtraModels(HTTPResponseDto<GetContractResponseDto[]>)
  async getAllContracts(
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetContractResponseDto[]>> {
    this.logger.log('API to fetch all contracts');
    const contracts = await this.contractsService.getAllContracts(dto);
    return {
      message: 'Successfully fetched all contracts',
      data: contracts.data,
      count: contracts.count,
      statusCode: HttpStatus.OK,
    };
  }

  @Get('history/:contractId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'All history related to a contract fetched Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all history related to contract',
    summary: 'Fetch all contract history',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  async getAllContractsHistory(
    @Param('contractId', new ParseUUIDPipe()) contractId: string,
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<HTTPResponseDto<GetEntityHistoryResponse>> {
    this.logger.log('API to fetch all contract history');
    const history = await this.contractsService.getAllContractHistory(
      contractId,
      dto,
    );
    return {
      message: 'All contract history fetched successfully',
      data: history,
      statusCode: HttpStatus.OK,
    };
  }
}
