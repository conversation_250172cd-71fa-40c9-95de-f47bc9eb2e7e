import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  GetTragetResourcesDto,
  GetTransferDto,
  TransferRequestDto,
} from './dtos/transfer-dto';
import { User } from 'types';
import {
  ChangesOcccuredIn,
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { upperSnakeCaseToCamelCase } from 'src/utility';
import { ConsumablesService } from 'src/consumables/consumables.service';

@Injectable()
export class TransferService {
  constructor(private readonly prisma: PrismaService) {}
  private logger = new Logger('TransferService');
  private selectArgs = {
    id: true,
    from: true,
    to: true,
    notes: true,
    typeOfCategory: true,
    user: {
      select: {
        name: true,
      },
    },
    quantity: true,
    createdAt: true,
  };

  async resourceTransfer(
    dto: TransferRequestDto,
    user: User,
    sourceId: string,
    categoryType: TypeOfCategory,
  ): Promise<boolean> {
    const entityTable = upperSnakeCaseToCamelCase(categoryType);
    const sourceResource = await this.prisma[entityTable].findFirst({
      where: {
        id: sourceId,
      },
    });
    if (dto.quantity > sourceResource.availableQuantity) {
      this.logger.error('Quantity must not exceed available quantity');
      throw new BadRequestException(
        'Quantity must not exceed available quantity',
      );
    }
    await this.prisma[entityTable].update({
      where: {
        id: sourceId,
      },
      data: {
        totalQuantity: {
          decrement: dto.quantity,
        },
        availableQuantity: {
          decrement: dto.quantity,
        },
      },
    });
    await this.prisma[entityTable].update({
      where: {
        id: dto.to,
      },
      data: {
        totalQuantity: {
          increment: dto.quantity,
        },
        availableQuantity: {
          increment: dto.quantity,
        },
      },
    });
    await this.createTransferRecord(dto, user, sourceId, categoryType);
    return true;
  }

  async createTransferRecord(
    dto: TransferRequestDto,
    user: User,
    sourceId: string,
    categoryType: TypeOfCategory,
  ): Promise<GetTransferDto> {
    const createdTransferRecord = await this.prisma.transfer.create({
      data: {
        from: sourceId,
        to: dto.to,
        typeOfCategory: categoryType,
        notes: dto.notes,
        user: {
          connect: {
            id: user.id,
          },
        },
        quantity: dto.quantity,
      },
      select: this.selectArgs,
    });
    this.logger.log('Transfer record created successfully');

    await this.prisma.history.create({
      data: {
        action: HistoryActions.CREATED,
        changeInTable: ChangesOcccuredIn.TRANSFER,
        date: new Date(),
        entityId: createdTransferRecord.id,
        log: {
          userId: user.id,
          name: user.name,
          transferId: createdTransferRecord.id,
        },
      },
    });
    this.logger.log('History created for transfer create');

    return createdTransferRecord;
  }

  async getTargetResources(
    sourceId: string,
    categoryType: TypeOfCategory,
  ): Promise<GetTragetResourcesDto[]> {
    const entityTable = upperSnakeCaseToCamelCase(categoryType);
    const entityCategory = await this.prisma[entityTable].findFirst({
      where: {
        id: sourceId,
      },
      select: {
        category: {
          select: {
            name: true,
          },
        },
      },
    });
    const targetResources = await this.prisma[entityTable].findMany({
      where: {
        category: {
          name: {
            equals: entityCategory?.category?.name ?? '',
          },
        },
        NOT: {
          id: sourceId,
        },
        isDeleted: false,
      },
      select: {
        id: true,
        name: true,
      },
    });
    return targetResources;
  }

  async getTransferredResources(id: string) {
    const resources = await this.prisma.transfer.findMany({
      where: {
        isDeleted: false,
        OR: [
          {
            from: id,
          },
          {
            to: id,
          },
        ],
      },
      select: this.selectArgs,
      orderBy: {
        createdAt: 'desc',
      },
    });
    const entities = {};
    return Promise.all(
      resources.map(async (item) => {
        const { from, to, typeOfCategory, ...rest } = item;

        const selectArgs = this.getSelectArgs(typeOfCategory);

        let fromEntity;
        let toEntity;

        if (from in entities) {
          fromEntity = entities[from];
        } else {
          fromEntity = await this.prisma[
            upperSnakeCaseToCamelCase(typeOfCategory)
          ].findFirst({
            where: {
              id: from,
              isDeleted: false,
            },
            select: selectArgs,
          });
          entities[from] = fromEntity;
        }

        if (to in entities) {
          toEntity = entities[to];
        } else {
          toEntity = await this.prisma[
            upperSnakeCaseToCamelCase(typeOfCategory)
          ].findFirst({
            where: {
              id: to,
              isDeleted: false,
            },
            select: selectArgs,
          });
          entities[to] = toEntity;
        }

        return {
          ...rest,
          fromEntity,
          toEntity,
        };
      }),
    );
  }

  private getSelectArgs(typeOfCategory: TypeOfCategory) {
    switch (typeOfCategory) {
      case TypeOfCategory.CONSUMABLE:
        return ConsumablesService.selectArgs;
    }
  }
}
