import { ApiProperty } from '@nestjs/swagger';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class TransferRequestDto {
  @ApiProperty({
    description: 'The destination resource ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  @IsString()
  @IsUUID()
  to: string;

  @ApiProperty({
    description: 'The note related to transfer',
    type: 'string',
  })
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'The quantity of resource transfer',
    type: 'number',
    example: '7',
  })
  @IsNumber()
  quantity: number;
}

export class GetTransferDto {
  @ApiProperty({
    description: 'The transfer ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'The source resource ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  from: string;

  @ApiProperty({
    description: 'The destination resource ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  to: string;

  @ApiProperty({
    description: 'The note related to transfer',
    type: 'string',
  })
  notes?: string;

  @ApiProperty({
    description: 'The user who performed the transfer',
    example: 'sks',
  })
  user: { name: string };

  @ApiProperty({
    description: 'The transfer entity category type',
    enum: TypeOfCategory,
    example: TypeOfCategory.CONSUMABLE,
  })
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'The quantity of resource transfer',
    type: 'number',
    example: '7',
  })
  quantity: number;
}

export class GetTragetResourcesDto {
  @ApiProperty({
    description: 'The target entity id',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'The target entity name',
    type: 'string',
    example: 'Bottle',
  })
  name: string;
}

export class GetTransferredResources {
  @ApiProperty({
    description: 'The transfer ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'The source resource info',
  })
  fromEntity: any;

  @ApiProperty({
    description: 'The destination resource info',
  })
  toEntity: any;

  @ApiProperty({
    description: 'The note related to transfer',
    type: 'string',
  })
  notes?: string;

  @ApiProperty({
    description: 'The user who performed the transfer',
    example: 'sks',
  })
  user: { name: string };

  @ApiProperty({
    description: 'The quantity of resource transfer',
    type: 'number',
    example: '7',
  })
  quantity: number;
}
