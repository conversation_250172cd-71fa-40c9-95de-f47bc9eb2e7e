import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { TransferService } from './transfer.service';
import {
  GetTragetResourcesDto,
  GetTransferredResources,
  TransferRequestDto,
} from './dtos/transfer-dto';
import { Request } from 'express';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { INTERNAL_ERROR } from 'src/constants/message-constants';

@Controller('transfer')
@ApiTags('Transfers')
export class TransferController {
  constructor(private readonly transferService: TransferService) {}

  @Post('/:sourceId/:categoryType')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Transfer record created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a transfer record',
    summary: 'Create a transfer record',
  })
  async resourceTransfer(
    @Body() dto: TransferRequestDto,
    @Req() request: Request,
    @Param('sourceId', new ParseUUIDPipe()) sourceId: string,
    @Param('categoryType') categoryType: TypeOfCategory,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const transferResponse = await this.transferService.resourceTransfer(
      dto,
      user,
      sourceId,
      categoryType,
    );
    return {
      message: 'Resource transferred successfully',
      data: transferResponse,
      statusCode: HttpStatus.CREATED,
    };
  }

  @Get('/:sourceId/:categoryType')
  @ApiOperation({
    summary: 'API to get the target resources details',
    description:
      'API to get the target resources by providing source id and entity type',
  })
  @ApiOkResponse({
    description: 'Successfully fetched the target resources',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    description: 'Source Id',
    name: 'sourceId',
    required: true,
    example: '0334f4f3-48e6-42e6-9c5d-168c62bc7295',
  })
  @ApiParam({
    description: 'Category type',
    name: 'categoryType',
    required: true,
    example: 'CONSUMABLE',
  })
  @ApiBearerAuth('access-token')
  async getTargetResources(
    @Param('sourceId', new ParseUUIDPipe()) sourceId: string,
    @Param('categoryType') categoryType: TypeOfCategory,
  ): Promise<HTTPResponseDto<GetTragetResourcesDto[]>> {
    const targetResources = await this.transferService.getTargetResources(
      sourceId,
      categoryType,
    );
    return {
      data: targetResources,
      message: 'Target resources fetched successfully',
      statusCode: HttpStatus.OK,
    };
  }

  @Get('/:entityId')
  @ApiOperation({
    summary: 'API to get the transferred resources',
    description: 'API to get the transferred resources by providing entity id',
  })
  @ApiOkResponse({
    description: 'Successfully fetched the transferred resources',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    description: 'Entity Id',
    name: 'entityId',
    required: true,
    example: '0334f4f3-48e6-42e6-9c5d-168c62bc7295',
  })
  @ApiBearerAuth('access-token')
  async getTransferredResources(
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
  ): Promise<HTTPResponseDto<GetTransferredResources[]>> {
    const resources =
      await this.transferService.getTransferredResources(entityId);
    return {
      message: 'All transferred resources fetched successfully',
      data: resources,
      statusCode: HttpStatus.OK,
    };
  }
}
