import { Module } from '@nestjs/common';
import { AssetService } from './asset.service';
import { AssetController } from './asset.controller';
import { AssignmentService } from 'src/assignment/assignment.service';
import { PurchaseService } from 'src/purchase/purchase.service';
import { AuditService } from 'src/audit/audit.service';
import { DocumentService } from 'src/document/document.service';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [AssetController],
  providers: [
    AssetService,
    AssignmentService,
    PurchaseService,
    AuditService,
    DocumentService,
    AssetController,
  ],
  exports: [AssetService, AssetController],
})
export class AssetModule {}
