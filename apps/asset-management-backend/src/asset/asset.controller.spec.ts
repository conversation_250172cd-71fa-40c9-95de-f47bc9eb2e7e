import { Test, TestingModule } from '@nestjs/testing';
import { AssetController } from './asset.controller';
import { AssetService } from './asset.service';
import { CreateAssetDto, GetAssetResponseDto } from './dto/asset.dto';
import { Currency } from '@prisma-clients/asset-management-backend';
import { ConflictException, NotFoundException } from '@nestjs/common';
import {
  ASSET_MODELS_NOT_FOUND,
  ASSET_MODEL_NOT_FOUND,
  ASSET_NOT_FOUND,
  ASSET_OR_MODEL_NUMBER_EXIST,
  ASSET_STATUS_NOT_FOUND,
} from 'src/constants/message-constants';
import { SUPPLIER_NOT_FOUND } from 'src/constants';
import { Request } from 'express';
import { AppModule } from 'src/app.module';

describe('AssetController', () => {
  let controller: AssetController;
  let service: AssetService;

  const dto: CreateAssetDto = {
    assetName: 'asset-name',
    assetModelId: '9d625315-81be-4e76-a857-58d57f4c94f4',
    assetStausId: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
    assetTag: 'FSKLJDFLSKJ',
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    serialNumber: 'serial-number',
    warranty: 10,
    endOfLife: new Date(),
    requestedById: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    note: 'note',
    assetImageUrl: '',
    purchaseInfo: {
      orderNumber: 'order-number',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      purchasedById: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    },
    customFields: {},
  };

  const asset: GetAssetResponseDto = {
    id: 'c23fde36-3ddb-47a7-a3f9-c1821bb8ca50',
    assetName: 'asset-name',
    assetModel: {
      id: '9d625315-81be-4e76-a857-58d57f4c94f4',
      modelName: 'assetModel-name',
    },
    assetStatus: {
      id: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
      name: 'status-name',
    },
    location: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    serialNumber: 'serial-number',
    warranty: 10,
    endOfLife: new Date(),
    requestedBy: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'vinayak',
    },
    note: 'note',
    assetImageUrl: '',
  };

  const request = {
    user: { id: '5d7f3bda-f529-4387-9434-cf6ab340979e', name: 'John' },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<AssetController>(AssetController);
    service = module.get<AssetService>(AssetService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create asset api', () => {
    it('should return a status of 201 for sucessfully creating asset', async () => {
      jest.spyOn(service, 'createAsset').mockResolvedValue(asset);

      const result = await controller.createAsset(dto, request);

      expect(result.statusCode).toBe(201);
      expect(result.data).toEqual(asset);
      expect(result.message).toBe('asset created successfully');
    });

    it('should throw conflict exception when asset already exists', async () => {
      jest
        .spyOn(service, 'createAsset')
        .mockRejectedValue(new ConflictException(ASSET_OR_MODEL_NUMBER_EXIST));

      try {
        await controller.createAsset(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        expect(error.message).toBe(ASSET_OR_MODEL_NUMBER_EXIST);
        expect(error.status).toBe(409);
      }
    });

    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'createAsset')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));

      try {
        await controller.createAsset(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when assetModel not found', async () => {
      jest
        .spyOn(service, 'createAsset')
        .mockRejectedValue(new NotFoundException(ASSET_MODELS_NOT_FOUND));

      try {
        await controller.createAsset(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_MODELS_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when asset Status not found', async () => {
      jest
        .spyOn(service, 'createAsset')
        .mockRejectedValue(new NotFoundException(ASSET_STATUS_NOT_FOUND));

      try {
        await controller.createAsset(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_STATUS_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('get all assets api', () => {
    it('should return a status code of 200 for fetching all assets', async () => {
      jest.spyOn(service, 'getAllAssets').mockResolvedValue({
        assets: [asset],
        count: 10,
      });

      const result = await controller.getAllAssets();

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([asset]);
      expect(result.count).toBe(10);
      expect(result.message).toBe('Successfully fetched assets');
    });

    it('should return an empty array if no assets are present in database', async () => {
      jest.spyOn(service, 'getAllAssets').mockResolvedValue({
        assets: [],
        count: 0,
      });

      const result = await controller.getAllAssets();

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.message).toBe('Successfully fetched assets');
    });
  });

  describe('get asset by id api', () => {
    it('should return the asset with specified id and status code 200', async () => {
      jest.spyOn(service, 'getAssetById').mockResolvedValue(asset);

      const result = await controller.getAssetById(asset.id);

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(asset);
      expect(result.message).toBe('Successfully fetched asset');
    });

    it('should throw not found exception when asset not found', async () => {
      jest
        .spyOn(service, 'getAssetById')
        .mockRejectedValue(new NotFoundException(ASSET_NOT_FOUND));

      try {
        await controller.getAssetById(asset.id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('update asset api', () => {
    it('should return the updated asset object with status code of 200', async () => {
      jest.spyOn(service, 'updateAsset').mockResolvedValue(asset);

      const result = await controller.updateAssetDetails(
        asset.id,
        dto,
        request,
      );

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(asset);
      expect(result.message).toBe('Successfully updated asset');
    });

    it('should throw not found exception when asset not found', async () => {
      jest
        .spyOn(service, 'updateAsset')
        .mockRejectedValue(new NotFoundException(ASSET_NOT_FOUND));

      try {
        await controller.updateAssetDetails(asset.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'updateAsset')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));

      try {
        await controller.updateAssetDetails(asset.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when asset Model not found', async () => {
      jest
        .spyOn(service, 'updateAsset')
        .mockRejectedValue(new NotFoundException(ASSET_MODEL_NOT_FOUND));

      try {
        await controller.updateAssetDetails(asset.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_MODEL_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when asset status not found', async () => {
      jest
        .spyOn(service, 'updateAsset')
        .mockRejectedValue(new NotFoundException(ASSET_STATUS_NOT_FOUND));

      try {
        await controller.updateAssetDetails(asset.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_STATUS_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('delete asset api (soft delete)', () => {
    it('should soft delete the asset with a status code of 200', async () => {
      jest.spyOn(service, 'deleteAsset').mockResolvedValue(true);

      const result = await controller.deleteAsset(asset.id, request);

      expect(result.statusCode).toBe(200);
      expect(result.data).toBe(true);
      expect(result.message).toBe('asset deleted successfully');
    });

    it('should throw not found exception when asset is not found', async () => {
      jest
        .spyOn(service, 'deleteAsset')
        .mockRejectedValue(new NotFoundException(ASSET_NOT_FOUND));

      try {
        await controller.deleteAsset(asset.id, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(ASSET_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });
});
