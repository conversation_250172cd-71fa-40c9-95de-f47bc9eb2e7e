import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpStatus,
  Query,
  ParseUUIDPipe,
  Put,
  Req,
  Logger,
  UseGuards,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { AssetService } from './asset.service';
import {
  AssetsFilterQueryParamsDto,
  CreateAssetDto,
  GetAllAssetsResponseDto,
  GetAllUnAssignedAssetResponseDto,
  GetAssetResponseDto,
  UnassignAssetRequestDto,
  UpdateAssetDto,
} from './dto/asset.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ASSET_EXIST,
  ASSET_NOT_FOUND,
  AUDIT_NOT_FOUND,
  INTERNAL_ERROR,
  INVALID_ENTITY_TYPE,
  INVALID_UUID_FORMAT,
} from 'src/constants/message-constants';
import { Request } from 'express';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import {
  CreateAuditDto,
  GetAllAuditsResponseDto,
  GetAuditResponseDto,
} from 'src/audit/dto/audit.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Assets')
@Controller('assets')
@UseGuards(PermissionGuard)
export class AssetController {
  private logger = new Logger('AssetController');
  constructor(private readonly assetService: AssetService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created Asset',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetAssetResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: ASSET_EXIST,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a new Assets if not exists',
    summary: 'Create an asset',
  })
  @ApiExtraModels(HTTPResponseDto<GetAssetResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET))
  async createAsset(
    @Body() dto: CreateAssetDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetAssetResponseDto>> {
    this.logger.log('API to create asset with purchase details');

    const { user } = request;
    const asset: GetAssetResponseDto = await this.assetService.createAsset(
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: asset,
      message: 'asset created successfully',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched Asset/s',
    schema: { $ref: getSchemaPath(GetAllAssetsResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all Assets with an optional quary parameter to search by name',
    summary: 'Fetches all Assets',
  })
  @ApiExtraModels(GetAllAssetsResponseDto)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getAllAssets(
    @Query() dto?: AssetsFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<GetAssetResponseDto[]>> {
    this.logger.log('API to fetch all assets');

    const { assets, count } = await this.assetService.getAllAssets(dto);

    return {
      statusCode: HttpStatus.OK,
      data: assets,
      count: count,
      message: 'Successfully fetched assets',
    };
  }

  @Get('unassigned-assets')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched unassigned Asset/s',
    schema: { $ref: getSchemaPath(GetAllAssetsResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all unassigned Assets with an optional quary parameter to search by name',
    summary: 'Fetches all unassigned Assets',
  })
  @ApiExtraModels(GetAllAssetsResponseDto)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getAllUnassignedAssets(
    @Query() dto?: AssetsFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<GetAssetResponseDto[]>> {
    this.logger.log('API to fetch all unassigned assets');

    const { unassignedAssets, count } =
      await this.assetService.getAllUnassignedAssets(dto);

    return {
      statusCode: HttpStatus.OK,
      data: unassignedAssets,
      count: count,
      message: 'Successfully fetched unassigned assets',
    };
  }

  @Get(':assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched asset',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetAssetResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Asset with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch asset with specified Id',
    summary: 'Fetches asset with given Id',
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<GetAssetResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getAssetById(
    @Param('assetId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<GetAssetResponseDto>> {
    this.logger.log('API to fetch asset with specified Id');

    const asset: GetAssetResponseDto = await this.assetService.getAssetById(id);

    return {
      statusCode: HttpStatus.OK,
      data: asset,
      message: 'Successfully fetched asset',
    };
  }

  @Put(':assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated asset',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetAssetResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'asset with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the asset details',
    summary: 'Update asset',
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ASSET))
  async updateAssetDetails(
    @Param('assetId', new ParseUUIDPipe()) id: string,
    @Body() dto: UpdateAssetDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetAssetResponseDto>> {
    this.logger.log('API to update asset details');

    const { user } = request;
    const asset: GetAssetResponseDto = await this.assetService.updateAsset(
      id,
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: asset,
      message: 'Successfully updated asset',
    };
  }

  @Delete(':assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted asset Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad request response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the asset',
    summary: 'Delete asset',
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.ASSET))
  async deleteAsset(
    @Param('assetId', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to soft delete asset');

    const { user } = request;
    const assetDeletedStatus: boolean = await this.assetService.deleteAsset(
      id,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: assetDeletedStatus,
      message: 'asset deleted successfully',
    };
  }

  @Post('assign')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully assigned assets',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to assign a asset to users',
    summary: 'Assign a asset',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET))
  async assignAsset(
    @Body() dto: CreateAssignmentDto,
    @Req() request: Request,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    console.log(dto.assetStausId);
    this.logger.log('API to assign asset to user');

    const { user } = request;
    const assignmentDetails = await this.assetService.assignAsset(
      dto,
      user,
      file?.buffer,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: assignmentDetails,
      message: 'Successfully assigned asset',
    };
  }

  @Put('unassign/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully unassigned the asset',
    schema: { $ref: getSchemaPath(HTTPResponseDto<boolean>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API unassigns a asset',
    summary: 'Unassign a software asset',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '7da45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the assignment ID to unassign',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ASSET))
  async unAssignAsset(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() request: Request,
    @Body() unassignAssetInfo: UnassignAssetRequestDto,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const unassignedStatus = await this.assetService.unassignAsset(
      assignmentId,
      user,
      unassignAssetInfo,
    );

    return {
      statusCode: HttpStatus.OK,
      data: unassignedStatus,
      message: 'Successfully unassigned the asset',
    };
  }

  @Get('purchases/:assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched purchase history for asset',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<PurchaseResponseDto[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to fetch purchase history',
  })
  @ApiOperation({
    description: 'This API allows to fetch purchase history for assets',
    summary: 'Fetch purchase history of a asset',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getPurchaseHistory(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<PurchaseResponseDto[]>> {
    this.logger.log('API to get purchase history for asset');

    const { purchaseDetails, count } =
      await this.assetService.getPurchaseHistory(assetId, queryParams);

    return {
      statusCode: HttpStatus.OK,
      data: purchaseDetails,
      count,
      message: 'Successfully fetched purchase details for asset',
    };
  }

  @Get('assignments/:assetId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto[]>)
  @ApiOkResponse({
    description: 'Successfully retreived the assignments',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto[]>) },
  })
  @ApiNotFoundResponse({
    description: 'ASSET_NOT_FOUND',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_NOT_FOUND,
        error: 'Not found error',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'INTERNAL_SERVER_ERROR',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'INTERNAL_SERVER_ERROR',
        error: 'Internal server error',
      },
    },
  })
  @ApiOperation({
    summary: 'Get all assignmnets details',
    description: 'This API allows to fetch assignments of a asset',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async findAllAssignments(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
  ): Promise<HTTPResponseDto<AssignmentResponseDto[]>> {
    const assignments = await this.assetService.getAllAssignments(assetId);

    return {
      statusCode: HttpStatus.OK,
      data: assignments,
      message: 'Successfully fetched all assignments',
    };
  }

  @Get('audit/:assetId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<GetAllAuditsResponseDto>)
  @ApiOkResponse({
    description: 'Successfully retreived the audits',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetAllAuditsResponseDto>) },
  })
  @ApiNotFoundResponse({
    description: 'AUDIT_NOT_FOUND',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: AUDIT_NOT_FOUND,
        error: 'Not found error',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'INTERNAL_SERVER_ERROR',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'INTERNAL_SERVER_ERROR',
        error: 'Internal server error',
      },
    },
  })
  @ApiOperation({
    summary: 'Get all audit details',
    description: 'This API allows to fetch audits of a asset',
  })
  async findAllAuditDetails(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
  ): Promise<HTTPResponseDto<GetAllAuditsResponseDto>> {
    const audits = await this.assetService.getAuditDetails(assetId);

    return {
      statusCode: HttpStatus.OK,
      data: audits,
      message: 'Successfully fetched all audits',
    };
  }

  @Get('history/:assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched asset history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch asset history',
    summary: 'Fetch asset history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  async getAssetHistory(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const { history, count } = await this.assetService.getAssetHistory(
      assetId,
      queryParams,
    );

    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'audit History fetched successfully',
    };
  }

  @Post('purchase/:assetId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created purchase details for assets',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the asset ID to create a purchase record',
  })
  @ApiOperation({
    description: 'This API creates purchase details for assets',
    summary: 'Create purchase details for assets',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET))
  async createPurchaseDetails(
    @Param('assetId', new ParseUUIDPipe()) licenseId: string,
    @Body() dto: CreatePurchaseDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    this.logger.log('API to create purchase details for assets');

    const { user } = request;
    const purchaseDetails = await this.assetService.createPurchaseDetails(
      dto,
      licenseId,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: purchaseDetails,
      message: 'Successfully created purchase details for the assets',
    };
  }

  @Post('audit/:assetId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created audit details for assets',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetAuditResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the asset ID to create a audit record',
  })
  @ApiOperation({
    description: 'This API creates audit details for assets',
    summary: 'Create audit details for assets',
  })
  @ApiExtraModels(HTTPResponseDto<GetAuditResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET))
  async createAuditDetails(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
    @Body() dto: CreateAuditDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetAuditResponseDto>> {
    this.logger.log('API to create audit details for assets');

    const { user } = request;
    const auditDetails = await this.assetService.createAuditDetails(
      dto,
      assetId,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: auditDetails,
      message: 'Successfully created audit details for the assets',
    };
  }

  @Put('purchase-update/:purchaseId/:assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Purchase details updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid purchase ID',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_UUID_FORMAT,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to update purchase record',
  })
  @ApiOperation({
    description: 'This API updates the purchase record',
    summary: 'Update the purchase record',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ASSET))
  async updatePurchaseRecord(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
    @Body() dto: UpdatePurchaseDto,
    @Req() request: Request,
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const updatedPurchase = await this.assetService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      assetId,
    );
    return {
      statusCode: HttpStatus.OK,
      data: updatedPurchase,
      message: 'Purchase record updated successfully',
    };
  }

  @Get('/unassignedAssets/:assetId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all unassigned details for asset',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetAllUnAssignedAssetResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Asset with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Asset not found',
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all unassigned details for an asset',
    summary: 'Fetches all unassigned details for an asset with given Id',
  })
  @ApiParam({
    name: 'assetId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset Id to fetch unassigned details',
  })
  @ApiExtraModels(HTTPResponseDto<GetAllUnAssignedAssetResponseDto>)
  async getAllUnassignedDetailsByAssetId(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<HTTPResponseDto<GetAllUnAssignedAssetResponseDto>> {
    this.logger.log(
      'API to fetch all unassigned details for asset with specified Id',
    );

    const unassignedDetails: GetAllUnAssignedAssetResponseDto =
      await this.assetService.getAllUnassignedDetailsForAsset(dto, assetId);

    return {
      statusCode: HttpStatus.OK,
      data: unassignedDetails,
      message: 'Successfully fetched all unassigned details for asset',
    };
  }

  @Get('/assignment/:assetId')
  async getAssetAssignmentDetail(
    @Param('assetId', new ParseUUIDPipe()) assetId: string,
  ) {
    const assetInfo = await this.assetService.getAssetAssignmentDetail(assetId);

    return {
      statusCode: HttpStatus.OK,
      data: assetInfo,
      message: 'Successfully fetched assigned asset detail',
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the assets info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the assets info',
  })
  async downloadAssets(): Promise<HTTPResponseDto<string>> {
    const fileUrl = await this.assetService.downloadAssets();
    return {
      data: fileUrl,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }
}
