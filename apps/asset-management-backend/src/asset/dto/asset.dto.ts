import { ApiProperty } from '@nestjs/swagger';
import { Audit } from '@prisma/client/asset-management-backend';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsUUID,
  IsArray,
  IsDateString,
  IsNumber,
} from 'class-validator';
import { CreatePurchaseDto } from 'src/purchase/dto/purchase.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { JsonValue } from 'types';
import { EntityDto } from 'src/common/dto/entity.dto';

export class UpdateAssetDto {
  @ApiProperty({
    description: 'asset name',
    type: 'string',
    example: 'laptop',
    required: false,
  })
  @IsOptional()
  @IsString()
  assetName: string;

  @ApiProperty({
    description: 'Serial Number of the Asset',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: false,
  })
  @IsOptional()
  @IsString()
  serialNumber: string;

  @ApiProperty({
    description: 'assetTag of the Asset',
    type: 'string',
    example: '6546186456546',
    required: false,
  })
  @IsOptional()
  @IsString()
  assetTag: string;

  @ApiProperty({
    description: 'Model number of the Asset',
    type: 'string',
    example: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  assetModelId: string;

  @ApiProperty({
    description: 'status of the Asset',
    type: 'string',
    example: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  assetStausId: string;

  @ApiProperty({
    description: 'Id of the user who requested the asset/s',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  requestedById?: string;

  @ApiProperty({
    description: 'Location of asset',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  location: string;

  @ApiProperty({
    description: 'warranty of the asset',
    type: 'number',
    example: '5',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  warranty: number;

  @ApiProperty({
    description: 'lifespan of  the asset',
    example: '2023-12-22T06:17:36.215Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  endOfLife: Date;

  @ApiProperty({
    description: 'URL for asset image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
    required: false,
  })
  @IsOptional()
  assetImageUrl: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example:
      'Include any additional information about the laptop in this note....',
    required: false,
  })
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreateAssetDto extends UpdateAssetDto {
  @ApiProperty({
    description: 'Purchase details for asset',
    type: CreatePurchaseDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreatePurchaseDto)
  purchaseInfo: CreatePurchaseDto;
}

export class AssetsFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Order assets based on location',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  location?: string;

  @ApiProperty({
    description: 'Filter assets based on status',
    type: 'string',
    example: 'Assigned',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: 'string';

  @ApiProperty({
    description: 'Filter assets based on category',
    type: [String],
    example: 'laptop',
    required: false,
  })
  @IsOptional()
  category?: string[];
}

export class AssetIdDto {
  @ApiProperty({
    description: 'Asset Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: true,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  id: string;
}

export class GetAssetResponseDto {
  @ApiProperty({
    description: 'asset Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'asset name',
    type: 'string',
    example: 'laptop',
  })
  assetName: string;

  @ApiProperty({
    description: 'serial number',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  serialNumber: string;

  @ApiProperty({
    description: 'assetModel details',
    example: {
      id: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
      modelName: 'assetModel-name',
    },
  })
  assetModel: {
    id: string;
    modelName: string;
  };

  @ApiProperty({
    description: 'assetStatus details',
    example: {
      id: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
      name: 'assetStatus-name',
    },
  })
  assetStatus: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Id of the user who requested the asset/s',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'John',
    },
  })
  requestedBy: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Location of asset',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Bangalore',
    },
  })
  location: EntityDto;

  @ApiProperty({
    description: 'warranty of the asset',
    type: 'number',
    example: 2,
  })
  warranty: number;

  @ApiProperty({
    description: 'lifespan of  the asset',
    type: 'Date',
    example: '2023-12-22T06:17:36.215Z',
  })
  endOfLife: Date;

  @ApiProperty({
    description: 'URL for asset image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
  })
  assetImageUrl: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'Include any additional information in this note....',
  })
  note: string;

  @ApiProperty({
    description: 'Array of audits associated with the asset',
    example: [],
    required: true,
    isArray: true,
  })
  @IsArray()
  audits?: Audit[];

  @ApiProperty({
    description: 'Assigend user details',
    type: 'object',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'John',
    },
  })
  assignedUser?: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  category?: { name: string };
}

export class GetAllAssetsResponseDto {
  @ApiProperty({
    description: 'Fetched assets response',
    isArray: true,
    type: GetAssetResponseDto,
  })
  assets: GetAssetResponseDto[];

  @ApiProperty({
    description: 'Total count of assets',
    type: 'number',
    example: 100,
  })
  count: number;
}

export class UnassignAssetRequestDto {
  @ApiProperty({
    description: 'The unique identifier representing the status of the asset',
    type: 'string',
    example: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
  })
  @IsOptional()
  @IsUUID()
  assetStausId: string;

  @ApiProperty({
    description:
      'Additional information or notes regarding the unassignment of the asset',
    type: 'string',
    example: [
      'Asset returned to warehouse',
      'Transferred to another department',
    ],
  })
  @IsOptional()
  @IsString()
  unAssignedNote: string;

  @ApiProperty({
    description: 'The date and time when the asset was unassigned',
    type: 'Date',
    example: '2024-03-14T10:00:01.199Z',
  })
  @IsOptional()
  @IsDateString()
  unAssignedDate: Date;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  notifyUser: { id: string }[];
}

export class UnAssignAssetDataDto {
  @ApiProperty({
    description: 'The date and time when the asset was assigned',
    type: 'Date',
    example: '2024-03-14T10:00:01.199Z',
  })
  @IsOptional()
  @IsDateString()
  assignedDate: Date;

  @ApiProperty({
    description:
      'Additional information or notes regarding the assignment of the asset',
    type: 'string',
    example: 'This Asset is Currently Assigned',
  })
  @IsOptional()
  @IsString()
  assignedNote: string;

  @ApiProperty({
    description:
      'The username of the user to whom the asset is currently assigned',
    type: 'string',
    example: 'john_doe',
  })
  @IsOptional()
  @IsString()
  assignedUser: string;

  @ApiProperty({
    description:
      'Additional information or notes regarding the unassignment of the asset',
    type: 'string',
    example: [
      'Asset returned to warehouse',
      'Transferred to another department',
    ],
  })
  @IsOptional()
  @IsString()
  unAssignedNote: string;

  @ApiProperty({
    description: 'The unique identifier representing the asset',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  @IsUUID()
  assetId: string;

  @ApiProperty({
    description: 'The date and time when the asset was unassigned',
    type: 'Date',
    example: '2024-03-14T10:00:01.199Z',
  })
  @IsOptional()
  @IsDateString()
  unAssignedDate: Date;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  @IsArray()
  notifyUser: { id: string }[];

  @ApiProperty({
    description: 'The unassign entity ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  @IsUUID()
  entityId: string;

  @ApiProperty({
    description: 'The unassigned user email',
    type: 'string',
    example: '<EMAIL>',
  })
  @IsOptional()
  assignedUserEmail: string;
}

export class GetUnAssignedAssetResponseDto {
  @ApiProperty({
    description: 'The ID of the unassigned asset',
    type: 'string',
    example: '630d018c-**************-116fa80b4547',
  })
  id: string;

  @ApiProperty({
    description: 'The date and time when the asset was unassigned',
    type: 'Date',
    example: '2024-03-14T10:00:01.199Z',
  })
  unAssignedDate: Date;

  @ApiProperty({
    description: 'The ID of the user who unassigned the asset',
    type: 'string',
    example: '5bb8dedf-119d-4eb0-88ac-59344c20b109',
  })
  userId: string;

  @ApiProperty({
    description:
      'Additional information or notes regarding the assignment of the asset',
    type: 'string',
    example: 'Assigned to John Doe',
  })
  assignedNote: string;

  @ApiProperty({
    description:
      'Additional information or notes regarding the unassignment of the asset',
    type: 'string',
    example: 'Returned to warehouse',
  })
  unAssignedNote: string;

  @ApiProperty({
    description: 'The date and time when the asset was assigned',
    type: 'string',
    example: '2024-03-14T09:00:00.000Z',
  })
  assignedDate: Date;

  @ApiProperty({
    description: 'The ID of the asset',
    type: 'string',
    example: '925553c0-a6bc-46fa-9f88-39796514c1d4',
  })
  assetId: string;
}

export class GetAllUnAssignedAssetResponseDto {
  @ApiProperty({
    description: 'Fetched unassigned details response',
    isArray: true,
    type: GetAssetResponseDto,
  })
  unAssignedAssetDetails: GetUnAssignedAssetResponseDto[];

  @ApiProperty({
    description: 'Total count of unassigned assets',
    type: 'number',
    example: 100,
  })
  count: number;
}
