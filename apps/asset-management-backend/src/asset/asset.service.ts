import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  AssetIdDto,
  AssetsFilterQueryParamsDto,
  CreateAssetDto,
  GetAllAssetsResponseDto,
  GetAllUnAssignedAssetResponseDto,
  GetAssetResponseDto,
  UnAssignAssetDataDto,
  UnassignAssetRequestDto,
  UpdateAssetDto,
} from './dto/asset.dto';
import { ASSET_EXIST, ASSET_NOT_FOUND } from 'src/constants/message-constants';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  setDateWithZeroTime,
  updateFieldGroups,
} from '../utility';
import { Asset, Prisma } from '@prisma/client/asset-management-backend';
import {
  ChangesOcccuredIn,
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import { User } from 'types';
import { AssignmentService } from 'src/assignment/assignment.service';
import { PurchaseService } from 'src/purchase/purchase.service';

import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import {
  CreateAuditDto,
  GetAllAuditsResponseDto,
  GetAuditResponseDto,
  UpdateAuditDto,
} from 'src/audit/dto/audit.dto';
import { AuditService } from 'src/audit/audit.service';
import { AwsService } from 'src/aws/aws.service';
import { PrismaService } from 'src/prisma/prisma.service';
import { DocumentService } from 'src/document/document.service';
import { compareAsc } from 'date-fns';
@Injectable()
export class AssetService {
  private logger = new Logger('AssetService');
  private selectArgs = {
    id: true,
    assetName: true,
    assetModel: {
      select: {
        id: true,
        modelName: true,
        category: {
          select: {
            name: true,
          },
        },
      },
    },
    assetTag: true,
    assetStatus: {
      select: {
        id: true,
        name: true,
      },
    },
    requestedBy: {
      select: {
        id: true,
        name: true,
      },
    },
    serialNumber: true,
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    warranty: true,
    endOfLife: true,
    note: true,
    assetImageUrl: true,
    customFields: true,
    Audit: {
      select: {
        id: true,
        lastAuditDate: true,
        nextAuditDate: true,
        auditImageUrls: true,
      },
    },
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly assignmentService: AssignmentService,
    private readonly purchaseService: PurchaseService,
    private readonly auditService: AuditService,
    private readonly awsService: AwsService,
    private readonly documentService: DocumentService,
  ) {}

  /**
   * This method creates a asset based on the provided DTO if not already exists.
   * Also creates a record in history table for the created asset.
   *
   * @param {CreateAssetsDto} dto The DTO (Data Transfer Object) containing asset details.
   * @returns {Promise<GetAssetResponseDto>} A Promise that resolves to an assetResponsePayload representing the created asset.
   * @throws ConflictException if a asset with the same name already exists.
   * @throws NotFoundException if the supplier, manufacturer, or category with the specified IDs are not found.
   */
  async createAsset(
    dto: CreateAssetDto,
    user: User,
  ): Promise<GetAssetResponseDto> {
    try {
      const isEowValid = compareAsc(
        new Date(dto.endOfLife),
        new Date(dto.purchaseInfo.purchaseDate),
      );
      if (isEowValid < 0) {
        this.logger.error('EOW must be after purchase date.');
        throw new BadRequestException('EOW must be after purchase date');
      }
      const assetTransaction: GetAssetResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const asset: number = await prisma.asset.count({
            where: {
              OR: [
                {
                  serialNumber: dto.serialNumber,
                },
                {
                  assetTag: dto.assetTag,
                },
              ],
              isDeleted: false,
            },
          });

          if (asset) {
            this.logger.log(`${dto.assetName} is already exist`);
            throw new ConflictException(ASSET_EXIST);
          }

          const fieldGroups: string[] = dto.customFields
            ? dto.customFields['fieldgroups']
            : [];

          const createdAsset: GetAssetResponseDto = await prisma.asset.create({
            data: {
              assetName: dto.assetName,
              assetTag: dto.assetTag,
              assetModel: dto.assetModelId
                ? {
                    connect: {
                      id: dto.assetModelId,
                    },
                  }
                : undefined,
              assetStatus: dto.assetStausId
                ? {
                    connect: {
                      id: dto.assetStausId,
                    },
                  }
                : undefined,
              requestedBy: dto.requestedById
                ? {
                    connect: {
                      id: dto.requestedById,
                    },
                  }
                : undefined,
              serialNumber: dto.serialNumber,
              location: dto.location
                ? {
                    connect: {
                      id: dto.location,
                    },
                  }
                : undefined,
              warranty: dto.warranty,
              endOfLife: dto.endOfLife
                ? setDateWithZeroTime(dto.endOfLife)
                : null,
              assetImageUrl: dto.assetImageUrl,
              note: dto.note,
              customFields: dto.customFields,
              fieldGroups: {
                connect: fieldGroups.map((fieldGroupId) => ({
                  id: fieldGroupId,
                })),
              },
            },
            select: this.selectArgs,
          });

          await this.purchaseService.createPurchase(
            TypeOfCategory.ASSET,
            dto.purchaseInfo,
            createdAsset.id,
            user,
          );

          this.logger.log(
            `Asset with id: ${createdAsset.id} created successfully`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.ASSET,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdAsset.id,
              log: {
                userId: user.id,
                name: user.name,
                assetId: createdAsset.id,
              },
            },
          });

          this.logger.log("History for 'create' created successfully");

          return createdAsset;
        });
      return assetTransaction;
    } catch (error) {
      // If something went wrong, delete only new uploaded Image.
      if (dto.assetImageUrl) {
        this.awsService.deleteFile(dto.assetImageUrl);
        this.logger.log(
          'Asset image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create asset: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves a paginated list of assets based on optional query parameters.
   * It applies pagination, sorting, and search functionality if provided in the query parameters.
   * @param {GetAllAssetQueryParamsDto} [dto] (Optional) The DTO containing query parameters for retrieving assets.
   * @returns {Promise<GetAllAssetsResponseDto['data']>} A Promise that resolves to an GetAllassetsResponse['data'] containing a paginated list of assets and the total count.
   */
  async getAllAssets(
    dto?: AssetsFilterQueryParamsDto,
  ): Promise<GetAllAssetsResponseDto> {
    try {
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | null = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
      };

      const officeLocation = dto?.location;
      const status = dto?.status;
      const category: string[] = Array.isArray(dto?.category)
        ? dto.category
        : dto?.category
        ? [dto.category]
        : [];

      const whereOptions: Prisma.AssetWhereInput = dto?.searchInput
        ? {
            OR: [
              {
                assetName: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                serialNumber: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                assetModel: {
                  modelName: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              },
              {
                assetTag: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : undefined;
      const [assets, assetCount, assetAssignments] =
        await this.prisma.$transaction([
          this.prisma.asset.findMany({
            where: {
              ...whereOptions,
              ...(officeLocation
                ? {
                    location: {
                      name: { equals: officeLocation, mode: 'insensitive' },
                    },
                  }
                : {}),
              ...(status
                ? {
                    assetStatus: {
                      name: { equals: status, mode: 'insensitive' },
                    },
                  }
                : {}),
              ...(category.length > 0
                ? {
                    assetModel: {
                      category: {
                        name: {
                          in: category,
                          mode: 'insensitive',
                        },
                      },
                    },
                  }
                : undefined),
              isDeleted: false,
            },
            select: this.selectArgs,
            orderBy,
            take: limit,
            skip,
          }),
          this.prisma.asset.count({
            where: {
              ...whereOptions,
              ...(officeLocation
                ? {
                    location: {
                      name: { equals: officeLocation, mode: 'insensitive' },
                    },
                  }
                : {}),
              ...(status
                ? {
                    assetStatus: {
                      name: { equals: status, mode: 'insensitive' },
                    },
                  }
                : {}),
              ...(category.length > 0
                ? {
                    assetModel: {
                      category: {
                        name: {
                          in: category,
                          mode: 'insensitive',
                        },
                      },
                    },
                  }
                : undefined),
              isDeleted: false,
            },
          }),
          this.prisma.assignment.findMany({
            where: { typeOfAssignment: TypeOfCategory.ASSET },
            select: {
              entityId: true,
              user: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          }),
        ]);

      let searchInUserName = false;
      let assetsData = assets;
      if (dto?.searchInput && assets.length === 0) {
        searchInUserName = true;
        assetsData = await this.prisma.asset.findMany({
          where: {
            ...(officeLocation
              ? {
                  location: {
                    id: { equals: officeLocation },
                  },
                }
              : {}),
            ...(status
              ? {
                  assetStatus: {
                    name: { equals: status, mode: 'insensitive' },
                  },
                }
              : {}),
            ...(category.length > 0
              ? {
                  assetModel: {
                    category: {
                      name: {
                        in: category,
                        mode: 'insensitive',
                      },
                    },
                  },
                }
              : undefined),
            isDeleted: false,
          },
          select: this.selectArgs,
          orderBy,
        });
      }

      this.logger.log(`Fetched ${assetCount} asset/s successfully`);
      let assetsWithAssignedUser = assetsData.map((asset) => {
        const assetAssignment = assetAssignments.find(
          (assignment) => asset.id === assignment.entityId,
        );

        return {
          ...asset,
          assignedUser: assetAssignment?.user ?? null,
        };
      });

      if (searchInUserName) {
        assetsWithAssignedUser = assetsWithAssignedUser.filter(
          (a) =>
            a.assignedUser?.name
              ?.toLowerCase()
              .includes(dto?.searchInput?.toLowerCase()),
        );
      }

      return {
        assets: assetsWithAssignedUser,
        count: assetCount,
      };
    } catch (error) {
      this.logger.log(`Failed to fetch assets: ${error}`);
      throw error;
    }
  }

  async getAllUnassignedAssets(dto?: AssetsFilterQueryParamsDto) {
    try {
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | null = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
      };

      const officeLocation = dto?.location;

      const whereOptions: Prisma.AssetWhereInput = dto?.searchInput
        ? {
            OR: [
              {
                assetName: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                serialNumber: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                assetModel: {
                  modelName: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              },
            ],
          }
        : undefined;

      const assetAssignments = await this.prisma.assignment.findMany({
        where: { typeOfAssignment: TypeOfCategory.ASSET },
        select: {
          entityId: true,
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      const assignedAssetIds = assetAssignments.map(
        (assignment) => assignment.entityId,
      );

      const [unassignedAssets, unassignedAssetsCount] =
        await this.prisma.$transaction([
          this.prisma.asset.findMany({
            where: {
              id: { notIn: assignedAssetIds },
              ...whereOptions,
              ...(officeLocation
                ? {
                    location: {
                      name: { equals: officeLocation, mode: 'insensitive' },
                    },
                  }
                : {}),
              isDeleted: false,
            },
            select: this.selectArgs,
            orderBy,
            take: limit,
            skip,
          }),

          this.prisma.asset.count({
            where: {
              id: { notIn: assignedAssetIds },
              ...whereOptions,
              ...(officeLocation
                ? {
                    location: {
                      name: { equals: officeLocation, mode: 'insensitive' },
                    },
                  }
                : {}),
              isDeleted: false,
            },
          }),
        ]);
      return {
        unassignedAssets: unassignedAssets,
        count: unassignedAssetsCount,
      };
    } catch (error) {
      this.logger.log(`Failed to fetch unassigned assets: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrives asset with the specified ID that is not deleted.
   *
   * @param {AssetIdDto['id']} assetId The ID of the asset to retrieve.
   * @returns {Promise<GetAssetResponseDto>} A Promise that resolves to an assetResponsePayload representing the retrieved asset.
   * @throws NotFoundException if the asset with the specified ID is not found or has been deleted.
   */
  async getAssetById(assetId: string): Promise<GetAssetResponseDto> {
    try {
      const asset: GetAssetResponseDto = await this.prisma.asset.findFirst({
        where: { id: assetId, isDeleted: false },
        select: this.selectArgs,
      });

      if (!asset) {
        this.logger.log(`asset with id: ${assetId} not found`);
        throw new NotFoundException(ASSET_NOT_FOUND);
      }

      const assetAssignment = await this.prisma.assignment.findFirst({
        where: { entityId: asset.id, typeOfAssignment: TypeOfCategory.ASSET },
        select: {
          entityId: true,
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      const assignedUser = assetAssignment?.user ?? null;
      return { ...asset, assignedUser };
    } catch (error) {
      this.logger.error(
        `Error fetching asset with id ${assetId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * This method verifies the existence of the asset
   * by its ID and updates the asset with the provided data.
   *
   * @param {AssetIdDto['id']} assetId The ID of the asset to update.
   * @param {UpdateAssetDto} dto The DTO containing updated asset details.
   * @returns {Promise<GetAssetResponseDto>} A Promise that resolves to an assetResponsePayload representing the updated asset.
   * @throws NotFoundException if the asset with the specified ID is not found or has been deleted.
   */
  async updateAsset(
    assetId: AssetIdDto['id'],
    dto: UpdateAssetDto,
    user: User,
  ): Promise<GetAssetResponseDto> {
    let asset: GetAssetResponseDto;
    try {
      const assetTransaction: GetAssetResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          asset = await this.prisma.asset.findFirst({
            where: {
              id: assetId,
              isDeleted: false,
            },
            select: this.selectArgs,
          });

          if (!asset) {
            this.logger.log(`asset with id: ${assetId} not found`);
            throw new NotFoundException(ASSET_NOT_FOUND);
          }

          /**
           * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
           **/
          await updateFieldGroups(prisma, 'asset', assetId, asset, dto);

          // Check for duplicate asset name or serial number
          const assetCount: number = await prisma.asset.count({
            where: {
              NOT: [
                {
                  assetName: {
                    equals: asset.assetName,
                    mode: 'insensitive',
                  },
                },
                {
                  serialNumber: asset.serialNumber,
                },
              ],
              OR: [
                {
                  serialNumber: dto.serialNumber,
                },
                {
                  assetTag: dto.assetTag,
                },
              ],
              isDeleted: false,
            },
          });

          if (assetCount) {
            this.logger.log('Asset or Model number already exist');
            throw new ConflictException(ASSET_EXIST);
          }

          const updatedAsset: GetAssetResponseDto =
            await this.prisma.asset.update({
              where: {
                id: assetId,
              },
              data: {
                assetName: dto.assetName,
                assetTag: dto.assetTag,
                assetModel: dto.assetModelId
                  ? {
                      connect: {
                        id: dto.assetModelId,
                      },
                    }
                  : undefined,
                assetStatus: dto.assetStausId
                  ? {
                      connect: {
                        id: dto.assetStausId,
                      },
                    }
                  : undefined,
                serialNumber: dto.serialNumber,
                location: dto.location
                  ? {
                      connect: {
                        id: dto.location,
                      },
                    }
                  : undefined,
                warranty: dto.warranty,
                endOfLife: dto.endOfLife
                  ? setDateWithZeroTime(dto.endOfLife)
                  : null,
                assetImageUrl: dto.assetImageUrl,
                note: dto.note,
                customFields: dto.customFields,
              },
              select: this.selectArgs,
            });

          this.logger.log(`asset with id: ${asset.id} updated successfully`);

          //If update successfully, delete previous image attached with asset
          if (
            updatedAsset &&
            asset.assetImageUrl &&
            asset.assetImageUrl !== dto.assetImageUrl
          ) {
            this.awsService.deleteFile(asset.assetImageUrl);
            this.logger.log(
              'Asset image uploaded on s3 bucket deleted successfully',
            );
          }

          await this.prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.ASSET,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedAsset.id,
              log: {
                userId: user.id,
                name: user.name,
                assetId: updatedAsset.id,
                updatedFields: getUpdatedFields(asset, updatedAsset),
              },
            },
          });

          this.logger.log("History for 'update' created successfully");

          return updatedAsset;
        });

      return assetTransaction;
    } catch (error) {
      // If something went wrong, delete only new uploaded Image.
      if (dto.assetImageUrl && asset.assetImageUrl !== dto.assetImageUrl) {
        await this.awsService.deleteFile(dto.assetImageUrl);
        this.logger.log(
          'Asset image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update asset: ${error}`);
      throw error;
    }
  }

  /**
   * This method marks the Asset as deleted in the database by setting the 'isDeleted' flag to true,
   * if the Asset exists
   *
   * @param {AssetIdDto['id']} AssetId The ID of the Asset to delete.
   * @returns {Promise<boolean>} A Promise that resolves to an boolean value representing the status of delete operation.
   * @throws NotFoundException if the Asset with the specified ID is not found or has already been deleted.
   */
  async deleteAsset(assetId: string, user: User): Promise<boolean> {
    const assetTransaction = await this.prisma.$transaction(async (prisma) => {
      const asset: Asset = await prisma.asset.findFirst({
        where: {
          id: assetId,
          isDeleted: false,
        },
      });

      if (!asset) {
        this.logger.log(`asset with id: ${assetId} not found`);
        throw new NotFoundException(ASSET_NOT_FOUND);
      }

      const assetModelId = asset.assetModelId;

      // Update the asset to mark it as deleted
      const deletedAsset: Asset = await prisma.asset.update({
        where: {
          id: assetId,
        },
        data: {
          isDeleted: true,
          customFields: {},
          assetModel: assetModelId
            ? {
                disconnect: assetId === assetModelId,
              }
            : undefined,
        },
      });
      this.logger.log('Asset isDeleted updated to true successfully');

      /**
       * Disconnects existing field groups associated with the specified accessory.
       **/
      await disconnectFieldGroups(prisma, 'asset', assetId, asset);
      this.logger.log(`Asset disconnected from all field groups`);

      //If asset deleted, delete image attached with asset.
      if (asset.assetImageUrl && deletedAsset) {
        await this.awsService.deleteFile(asset.assetImageUrl);

        this.logger.log(
          'Asset image uploaded on s3 bucket deleted successfully',
        );
      }
      // Delete asset documents
      const documentId = await prisma.document.findMany({
        where: {
          entityId: assetId,
        },
        select: {
          id: true,
        },
      });
      if (documentId) {
        documentId.map(async (document) => {
          await this.documentService.deleteDocument(document.id, user);
        });
        this.logger.log(
          `The documents related to asset ID:${assetId} deleted successfully`,
        );
      }
      await prisma.history.create({
        data: {
          changeInTable: ChangesOcccuredIn.ASSET,
          action: HistoryActions.DELETED,
          date: new Date(),
          entityId: deletedAsset.id,
          log: {
            userId: user.id,
            name: user.name,
            assetId: deletedAsset.id,
          },
        },
      });
      this.logger.log(`History for ${assetId} created sucessfully`);
      return true;
    });
    return assetTransaction;
  }

  /**
   * Assigns a asset to the user id specified in the dto.
   *
   * @param {CreateAssignmentDto} dto - The data transfer object containing assignment details.
   * @param {User} user - The user who assigned the asset (to record history).
   * @returns {Promise<AssignmentResponseDto>} A promise representing the result of the assignment creation.
   */
  async assignAsset(
    dto: CreateAssignmentDto,
    user: User,
    file?: Buffer,
  ): Promise<AssignmentResponseDto> {
    //Check if the asset has already been assigned
    const existingAssignment = await this.assignmentService.findAllByEntityId(
      dto.entityId,
    );

    if (existingAssignment.length > 0) {
      // Asset is already assigned, you may throw an error or handle it as appropriate
      throw new BadRequestException('Asset is already assigned');
    }

    const assignmentDetails =
      await this.assignmentService.createAssignmentRecord(
        TypeOfCategory.ASSET,
        dto,
        user,
      );
    await this.prisma.asset.update({
      where: {
        id: dto.entityId,
      },
      data: {
        assetStatus: dto.assetStausId
          ? {
              connect: {
                id: dto.assetStausId,
              },
            }
          : undefined,
      },
    });
    if (file) {
      await this.awsService.uploadItDeclarationForm(file, assignmentDetails.id);
    }
    await this.awsService.sendAcknowledgementEmail(assignmentDetails);
    await this.awsService.assignmentNotifyEmail(assignmentDetails);
    this.logger.log('The mail sent successfully to mentioned notify users');
    return assignmentDetails;
  }

  /**
   * Creates unassignment details for an asset.
   * @param {UnAssignAssetDataDto} unassignmentDetails - Details of the unassignment.
   * @param {User} user - The user associated with the unassignment.
   * @throws {Error} Throws an error if creation of unassignment details fails.
   */
  async createUnassignmentDetails(
    unassignmentDetails: UnAssignAssetDataDto,
    user: User,
  ): Promise<boolean> {
    const notifyUserIds =
      unassignmentDetails.notifyUser !== undefined
        ? unassignmentDetails.notifyUser.length > 0
          ? unassignmentDetails.notifyUser.map((user) => ({ id: user.id }))
          : undefined
        : undefined;
    const unassignedDetails = await this.prisma.unAssignedDetails.create({
      data: {
        assignedDate: unassignmentDetails.assignedDate,
        unAssignedDate: new Date(),
        asset: unassignmentDetails.assetId
          ? {
              connect: {
                id: unassignmentDetails.assetId,
              },
            }
          : undefined,
        user: user.id
          ? {
              connect: {
                id: user.id,
              },
            }
          : undefined,
        assignedNote: unassignmentDetails.assignedNote,
        unAssignedNote: unassignmentDetails.unAssignedNote,
        assignedUser: unassignmentDetails.assignedUser,
        notifyUser: {
          connect: notifyUserIds,
        },
        entityId: unassignmentDetails.entityId,
        typeOfCategory: TypeOfCategory.ASSET,
      },
      select: {
        notifyUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        typeOfCategory: true,
        entityId: true,
        assignedUser: true,
        assignedDate: true,
        unAssignedNote: true,
        unAssignedDate: true,
      },
    });
    await this.awsService.unassignmentNotifyEmail(
      unassignedDetails,
      unassignmentDetails.assignedUserEmail,
    );
    this.logger.log('The mail sent successfully to mentioned notify users');
    this.logger.log('The unassignment record created successfully');
    return true;
  }

  /**
   * This method unassigns a asset from a user.
   * @param {string} assignmentId - The ID of the assignment record in the assignment table to unassign.
   * @param {User} user - The user who unassigned the asset (to record history).
   * @returns {Promise<boolean>} A Promise that resolves to a boolean indicating whether the unassignment was successful.
   */
  async unassignAsset(
    assignmentId: string,
    user: User,
    unassignAssetInfo: UnassignAssetRequestDto,
  ): Promise<boolean> {
    await this.prisma.$transaction(async (prisma) => {
      const assignmentInfo =
        await this.assignmentService.findAssignmentById(assignmentId);
      await prisma.asset.update({
        where: {
          id: assignmentInfo.entityId,
        },
        data: {
          assetStatus: unassignAssetInfo.assetStausId
            ? {
                connect: {
                  id: unassignAssetInfo.assetStausId,
                },
              }
            : undefined,
        },
      });
      const unAssignedDetails = {
        unAssignedDate: unassignAssetInfo.unAssignedDate,
        unAssignedNote: unassignAssetInfo.unAssignedNote,
        assetStausId: unassignAssetInfo.assetStausId,
        assignedUser: assignmentInfo.user.name,
        assignedNote: assignmentInfo.note,
        assignedDate: assignmentInfo.date,
        assetId: assignmentInfo.entityId,
        entityId: assignmentInfo.entityId,
        notifyUser: unassignAssetInfo.notifyUser,
        assignedUserEmail: assignmentInfo.user.email,
      };
      await this.createUnassignmentDetails(unAssignedDetails, user);
      await this.assignmentService.deleteAssignmentRecord(assignmentId, user);
    });
    return true;
  }

  /**
  /**
   * Retrieves the purchase history for the specified entity.
   *
   * @param {string} entityId - The identifier of the entity for which the purchase history is requested.
   * @returns {Promise<PurchaseResponseDto[]>} A promise representing an array of purchase history details for
   * the specified entity.
   */
  async getPurchaseHistory(
    entityId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllPurchasesResponse> {
    return this.purchaseService.getEntityPurchaseDetails(entityId, queryParams);
  }

  /**
   * This method unassigns a asset from a user.
   * @param {string} assetId - The ID of the assignment record in the assignment table to unassign.
   * @returns {Promise<AssignmentResponseDto[]>} A Promise that resolves to an arrary of assignments for the given asset
   */
  async getAllAssignments(assetId: string): Promise<AssignmentResponseDto[]> {
    return this.assignmentService.findAllByEntityId(assetId);
  }

  /**
   * Retrieves the history of a asset based on its ID and specified query parameters.
   *
   * @param {string} assetId - The ID of the asset for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If the asset with the provided ID is not found.
   */
  async getAssetHistory(
    assetId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const asset = await this.prisma.asset.count({
      where: {
        id: assetId,
        isDeleted: false,
      },
    });

    if (!asset) {
      this.logger.log(`Asset with id ${assetId} not found`);
      throw new NotFoundException(ASSET_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    // TODO: Search functionality to be implemented

    const history = await this.prisma.history.findMany({
      where: {
        entityId: assetId,
      },
      select: {
        action: true,
        date: true,
        log: true,
        changeInTable: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: assetId,
      },
    });

    this.logger.log(`Fetched history for asset with id ${assetId}`);

    return { history, count };
  }

  /**
   * Retrieves the audit details for the specified assetId.
   *
   * @param {string} assetId - The identifier of the entity for which the audit detials is requested.
   * @returns {Promise<GetAuditResponseDto[]>} A promise representing an array of audit details for
   * the specified asset.
   */
  async getAuditDetails(assetId: string): Promise<GetAllAuditsResponseDto> {
    return this.auditService.getAuditDetails(assetId);
  }

  /**
   * This method verifies the existence of the audit
   * by its ID and updates the audit with the provided data.
   *
   * @param {AuditIdDto['id']} auditId The ID of the asset to update.
   * @param {UpdateAuditDto} dto The DTO containing updated audit details.
   * @returns {Promise<GetAuditResponseDto>} A Promise that resolves to an assetResponsePayload representing the updated audit.
   * @throws NotFoundException if the audit with the specified ID is not found or has been deleted.
   */
  async auditAsset(
    auditId: string,
    dto: UpdateAuditDto,
    user: User,
  ): Promise<GetAuditResponseDto> {
    return this.auditService.updateAudit(auditId, dto, user);
  }

  /**
   * This method delete a audit from a asset.
   * @param {string} auditId - The ID of the audit record in the audit table to delete.
   * @param {User} user - The user who audit the asset (to record history).
   * @returns {Promise<boolean>} A Promise that resolves to a boolean indicating whether the delete was successful.
   */
  async deleteAssetAudit(auditId: string, user: User): Promise<boolean> {
    return this.auditService.deleteAudit(auditId, user);
  }

  /**
   * This method creates the purchase details for the asset entity.
   * @param {CreatePurchaseDto} dto - The data transfer object containing the purchase details.
   * @param {string} assetId - The ID of the asset for which the purchase details is created.
   * @param {User} user - The user who created the record to purchase (for recording the history).
   * @returns {Promise<PurchaseResponseDto>} A promise representing the result of the purchase details that is created.
   */
  async createPurchaseDetails(
    dto: CreatePurchaseDto,
    assetId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.createPurchase(
      TypeOfCategory.ASSET,
      dto,
      assetId,
      user,
    );
  }

  /**
   * This method creates the audit details for the asset entity.
   * @param {CreateAuditDto} dto - The data transfer object containing the audit details.
   * @param {string} assetId - The ID of the asset for which the audit details is created.
   * @param {User} user - The user who created the record to audit (for recording the history).
   * @returns {Promise<GetAuditResponseDto>} A promise representing the result of the audit details that is created.
   */
  async createAuditDetails(
    dto: CreateAuditDto,
    assetId: string,
    user: User,
  ): Promise<GetAuditResponseDto> {
    return this.auditService.createAudit(assetId, dto, user);
  }

  /**
   * Updates a purchase record.
   * @param {UpdatePurchaseDto} dto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    dto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    entityId: string,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      entityId,
    );
  }

  /**
   * Retrieves all unassigned details for a given asset based on query parameters.
   * @param {GetAllUnassignedAssetQueryParamsDto} getAllUnAssignedAssetDto - DTO containing query parameters for filtering and pagination.
   * @param {string} assetId - The ID of the asset to retrieve unassigned details for.
   * @returns {Promise<GetAllUnAssignedAssetResponseDto>} A Promise resolving to an object containing unassigned asset details and count.
   * @throws {Error} Throws an error if retrieval of unassigned details fails.
   */
  async getAllUnassignedDetailsForAsset(
    getAllUnAssignedAssetDto: GetAllQueryParamsDto,
    assetId: string,
  ): Promise<GetAllUnAssignedAssetResponseDto> {
    try {
      const page: number | null = getAllUnAssignedAssetDto?.page
        ? getAllUnAssignedAssetDto.page
        : null;
      const limit: number | null = getAllUnAssignedAssetDto?.limit
        ? getAllUnAssignedAssetDto.limit
        : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [getAllUnAssignedAssetDto?.sortBy || 'unAssignedDate']:
          getAllUnAssignedAssetDto?.sortOrder || 'desc',
      };
      const whereOptions = getAllUnAssignedAssetDto.searchInput
        ? {
            assetId: {
              equals: getAllUnAssignedAssetDto.searchInput || assetId,
            },
          }
        : undefined;
      const unAssignedAssetDetails =
        await this.prisma.unAssignedDetails.findMany({
          where: {
            assetId,
            ...whereOptions,
          },
          skip,
          take: limit,
          orderBy,
        });

      const count = await this.prisma.unAssignedDetails.count({
        where: {
          assetId,
          ...whereOptions,
        },
      });

      return {
        unAssignedAssetDetails,
        count,
      };
    } catch (error) {
      this.logger.log('Failed to retrieve unassigned details', error);
      throw error;
    }
  }

  async getAssetAssignmentDetail(assetId: string) {
    const assetInfo = await this.prisma.asset.findFirst({
      where: {
        id: assetId,
      },
      select: {
        assetName: true,
        assetImageUrl: true,
      },
    });
    const assetAssignments = await this.prisma.assignment.findFirst({
      where: { entityId: assetId },
      select: {
        id: true,
        user: true,
        isPending: true,
      },
    });
    return {
      ...assetInfo,
      ...assetAssignments,
    };
  }

  async downloadAssets(): Promise<string> {
    const data = await this.prisma.asset.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        assetName: true,
        assetModel: {
          select: {
            modelName: true,
            category: {
              select: {
                name: true,
              },
            },
          },
        },
        assetTag: true,
        assetStatus: {
          select: {
            name: true,
          },
        },
        requestedBy: {
          select: {
            name: true,
          },
        },
        serialNumber: true,
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        warranty: true,
        endOfLife: true,
        note: true,
        customFields: true,
      },
    });

    const parsedDataPromises = data.map(
      async ({
        assetModel,
        customFields,
        assetStatus,
        requestedBy,
        endOfLife,
        location,
        ...rest
      }) => {
        const assetModelName = assetModel?.modelName;
        const assetModelCategory = assetModel?.category?.name;
        const assetStatusName = assetStatus?.name;
        const requestedUserName = requestedBy?.name;
        const locationName = location.name;

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          endOfLife: endOfLife?.toLocaleDateString(),
          assetModelName,
          assetModelCategory,
          ...customFieldData,
          assetStatusName,
          requestedUserName,
          locationName,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/assets',
    );
    return fileName;
  }
}
