import { Test, TestingModule } from '@nestjs/testing';
import { AssetService } from './asset.service';
import {
  AssetModel,
  AssetStatus,
  Currency,
  EvaluationFrequency,
  Supplier,
  TransactionFrequency,
  User,
} from '@prisma-clients/asset-management-backend';
import { CreateAssetDto, GetAssetResponseDto } from './dto/asset.dto';
import { isUUID } from 'class-validator';
import { Request } from 'express';
import { ConflictException, NotFoundException } from '@nestjs/common';
import {
  ASSET_MODEL_NOT_FOUND,
  ASSET_NOT_FOUND,
  ASSET_OR_MODEL_NUMBER_EXIST,
  ASSET_STATUS_NOT_FOUND,
} from 'src/constants/message-constants';
import { SUPPLIER_NOT_FOUND } from 'src/constants';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('AssetService', () => {
  let service: AssetService;
  let prismaService: PrismaService;

  const supplier: Supplier = {
    id: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
    name: 'example-supplier',
    address: 'example',
    zipCode: '123456',
    contactName: null,
    contactEmail: null,
    contactPhoneNumber: null,
    note: null,
    supplierImageUrl: null,
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    notifyTo: [],
  };

  const assetStatus: AssetStatus = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Active',
    color: '#00FF00',
    note: 'Status Active',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const requestedBy: User = {
    id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    email: '<EMAIL>',
    name: 'Vinayak',
    roleId: '3d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    phoneNumber: '',
    employeeId: '',
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    createdAt: undefined,
    updatedAt: undefined,
    isDeleted: false,
  };

  const assetModel: AssetModel = {
    id: '9d625315-81be-4e76-a857-58d57f4c94f4',
    modelName: 'example',
    categoryId: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    manufacturerId: '073c2022-a405-456a-9d',
    modelNumber: '5d7f3bda-f529-4387-9434',
    note: null,
    assetModelImageUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const dto: CreateAssetDto = {
    assetName: 'asset-name',
    assetTag: 'FSKLJDFLSKJ',
    assetModelId: assetModel.id,
    assetStausId: assetStatus.id,
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    serialNumber: 'serial-number',
    warranty: 10,
    endOfLife: new Date(),
    requestedById: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    note: 'note',
    assetImageUrl: null,
    purchaseInfo: {
      orderNumber: 'order-number',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      purchasedById: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    },
    customFields: {},
  };

  const asset: GetAssetResponseDto = {
    id: 'c23fde36-3ddb-47a7-a3f9-c1821bb8ca50',
    assetName: 'asset-name',
    assetModel: {
      id: assetModel.id,
      modelName: assetModel.modelName,
    },
    assetStatus: {
      id: assetStatus.id,
      name: assetStatus.name,
    },
    requestedBy: {
      id: requestedBy.id,
      name: requestedBy.name,
    },
    location: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    serialNumber: 'serial-number',
    warranty: 10,
    endOfLife: new Date(),
    note: 'note',
    assetImageUrl: null,
    assignedUser: {
      id: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      name: 'John',
    },
  };

  const assignment = {
    entityId: asset.id,
    user: {
      id: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      name: 'John',
    },
  };

  const request = {
    user: { id: '5d7f3bda-f529-4387-9434-cf6ab340979e', name: 'John' },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<AssetService>(AssetService);
    prismaService = service['prisma'];

    expect(isUUID(asset.id)).toBe(true);
    expect(isUUID(supplier.id)).toBe(true);
    expect(isUUID(assetModel.id)).toBe(true);
    expect(isUUID(assetStatus.id)).toBe(true);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create asset service', () => {
    it('should create a asset when not exists', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(asset);

      const result = await service.createAsset(dto, request.user);

      expect(result).toEqual(asset);
    });

    it('should throw conflict exception when asset already exists', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(ASSET_OR_MODEL_NUMBER_EXIST));

      try {
        await service.createAsset(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        expect(error.message).toEqual(ASSET_OR_MODEL_NUMBER_EXIST);
      }
    });

    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createAsset(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when asset Model does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(ASSET_MODEL_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createAsset(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(ASSET_MODEL_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when asset Status does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(ASSET_STATUS_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createAsset(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(ASSET_STATUS_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });

  describe('get all assets service', () => {
    it('should return all the assets in an array when no search input provided', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue([[asset], 1, [assignment]]);

      const result = await service.getAllAssets();

      expect(result).toEqual({
        assets: [asset],
        count: 1,
      });
    });

    it('should return an empty array when no assets are present', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue([[], 0, [assignment]]);

      const result = await service.getAllAssets();

      expect(result).toEqual({
        assets: [],
        count: 0,
      });
    });
  });

  describe('update asset service', () => {
    it('should update the asset with specified id with given data', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(asset);

      const result = await service.updateAsset(asset.id, dto, request.user);

      expect(result).toEqual(asset);
    });

    it('should throw not found exception when the asset is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(ASSET_NOT_FOUND));

      try {
        await service.updateAsset(asset.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual(ASSET_NOT_FOUND);
      }
    });

    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateAsset(asset.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when asset Model does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(ASSET_MODEL_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateAsset(asset.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(ASSET_MODEL_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when asset Status does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(ASSET_STATUS_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateAsset(asset.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(ASSET_STATUS_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });
});
