import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateLocationDto {
  @ApiProperty({
    description: 'Location name',
    type: 'string',
    example: 'Bangalore',
    required: true,
  })
  @IsString()
  name: string;
}

export class UpdateLocationDto extends CreateLocationDto {}

export class GetLocationResponseDto {
  @ApiProperty({
    description: 'Location Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'Location name',
    type: 'string',
    example: 'Bangalore',
  })
  name: string;
}

export class GetAllLocationsResponseDto {
  @ApiProperty({
    description: 'Locations response data',
    isArray: true,
    type: GetLocationResponseDto,
  })
  locations: GetLocationResponseDto[];

  @ApiProperty({
    description: 'Total count of locations',
    type: 'number',
    example: 100,
  })
  count: number;
}
