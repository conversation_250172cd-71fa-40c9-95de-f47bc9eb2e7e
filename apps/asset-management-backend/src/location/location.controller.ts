import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  Req,
  Logger,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
  Query,
} from '@nestjs/common';
import { LocationService } from './location.service';
import {
  CreateLocationDto,
  GetLocationResponseDto,
  UpdateLocationDto,
} from './dto/location.dto';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import {
  INTERNAL_ERROR,
  LOCATION_ALREADY_EXIST,
  LOCATION_NOT_FOUND,
} from 'src/constants/message-constants';

@Controller('location')
@ApiTags('Location')
@UseGuards(PermissionGuard)
export class LocationController {
  private logger = new Logger('LocationController');
  constructor(private readonly locationService: LocationService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created location',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetLocationResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: LOCATION_ALREADY_EXIST,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a new location if not exists',
    summary: 'Create a location',
  })
  @ApiExtraModels(HTTPResponseDto<GetLocationResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.LOCATION))
  async createLocation(
    @Body() createLocationDto: CreateLocationDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetLocationResponseDto>> {
    this.logger.log('API to create location');

    const { user } = request;
    const location: GetLocationResponseDto =
      await this.locationService.createLocation(createLocationDto, user);

    return {
      statusCode: HttpStatus.CREATED,
      data: location,
      message: 'Location created successfully',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched location/s',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetLocationResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all locations with an optional quary parameter to search by name',
    summary: 'Fetches all locations',
  })
  @ApiExtraModels(GetAllResponseDto<GetLocationResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.LOCATION))
  async findAllLocations(
    @Query() params?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetLocationResponseDto[]>> {
    this.logger.log('API to fetch all locations');

    const { locations, count } =
      await this.locationService.findAllLocations(params);

    return {
      statusCode: HttpStatus.OK,
      data: locations,
      count: count,
      message: 'Successfully fetched locations',
    };
  }

  @Get(':locationId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched location',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetLocationResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Location with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LOCATION_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch location with specified Id',
    summary: 'Fetches location with given Id',
  })
  @ApiParam({
    name: 'locationId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter location Id to fetch',
  })
  @ApiExtraModels(HTTPResponseDto<GetLocationResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.LOCATION))
  async findLocationById(
    @Param('locationId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<GetLocationResponseDto>> {
    this.logger.log('API to fetch location with specified Id');

    const location: GetLocationResponseDto =
      await this.locationService.findLocationById(id);

    return {
      statusCode: HttpStatus.OK,
      data: location,
      message: 'Successfully fetched location',
    };
  }

  @Put(':locationId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated location details',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetLocationResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Location with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LOCATION_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the location details',
    summary: 'Update location details',
  })
  @ApiParam({
    name: 'locationId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter location Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<GetLocationResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.LOCATION))
  async updateLocation(
    @Param('locationId', new ParseUUIDPipe()) id: string,
    @Body() updateLocationDto: UpdateLocationDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetLocationResponseDto>> {
    this.logger.log('API to update location details');

    const { user } = request;
    const location: GetLocationResponseDto =
      await this.locationService.updateLocation(id, updateLocationDto, user);

    return {
      statusCode: HttpStatus.OK,
      data: location,
      message: 'Successfully updated location details',
    };
  }

  @Delete(':locationId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted location Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: LOCATION_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the location',
    summary: 'Delete location',
  })
  @ApiParam({
    name: 'locationId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter location Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.LOCATION))
  async deleteLocation(
    @Param('locationId', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to soft delete location');

    const { user } = request;
    const locationDeletedStatus: boolean =
      await this.locationService.deleteLocation(id, user);

    return {
      statusCode: HttpStatus.OK,
      data: locationDeletedStatus,
      message: 'Location deleted successfully',
    };
  }
}
