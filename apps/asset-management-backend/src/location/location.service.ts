import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateLocationDto,
  GetAllLocationsResponseDto,
  GetLocationResponseDto,
  UpdateLocationDto,
} from './dto/location.dto';
import { User } from 'types';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  LOCATION_ALREADY_EXIST,
  LOCATION_NOT_FOUND,
} from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

@Injectable()
export class LocationService {
  private logger = new Logger('LocationService');

  constructor(private readonly prisma: PrismaService) {}

  async createLocation(
    createLocationDto: CreateLocationDto,
    user: User,
  ): Promise<GetLocationResponseDto> {
    try {
      const createdLocation: GetLocationResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const count: number = await this.prisma.locations.count({
            where: {
              name: {
                equals: createLocationDto.name,
                mode: 'insensitive',
              },
              isDeleted: false,
            },
          });

          if (count) {
            this.logger.log('Location already exist');
            throw new ConflictException(LOCATION_ALREADY_EXIST);
          }

          const location: GetLocationResponseDto =
            await prisma.locations.create({
              data: {
                name: createLocationDto.name,
              },
              select: {
                id: true,
                name: true,
              },
            });

          this.logger.log(
            `Location with id: ${location.id} created successfully`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.LOCATIONS,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: location.id,
              log: {
                userId: user.id,
                name: user.name,
                locationId: location.id,
              },
            },
          });

          this.logger.log("History for location 'create' created successfully");

          return location;
        });

      return createdLocation;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create location: ${error}`);
      throw error;
    }
  }

  async findAllLocations(
    params?: GetAllQueryParamsDto,
  ): Promise<GetAllLocationsResponseDto> {
    const orderBy = {
      [params?.sortBy || 'createdAt']: params?.sortOrder || 'desc',
    };

    const whereOptions: Prisma.ConsumableWhereInput = params?.searchInput
      ? {
          name: {
            contains: params.searchInput,
            mode: 'insensitive',
          },
        }
      : undefined;

    const [locations, count] = await this.prisma.$transaction([
      this.prisma.locations.findMany({
        where: {
          ...whereOptions,
          isDeleted: false,
        },
        select: {
          id: true,
          name: true,
        },
        orderBy,
      }),
      this.prisma.locations.count({
        where: {
          ...whereOptions,
          isDeleted: false,
        },
      }),
    ]);

    this.logger.log(`Fetched ${count} location/s successfully`);

    return {
      locations,
      count,
    };
  }

  async findLocationById(id: string): Promise<GetLocationResponseDto> {
    const location: GetLocationResponseDto =
      await this.prisma.locations.findFirst({
        where: { id, isDeleted: false },
        select: {
          id: true,
          name: true,
        },
      });

    if (!location) {
      this.logger.log(`Location with id: ${id} not found`);
      throw new NotFoundException(LOCATION_NOT_FOUND);
    }

    return location;
  }

  async updateLocation(
    id: string,
    updateLocationDto: UpdateLocationDto,
    user: User,
  ): Promise<GetLocationResponseDto> {
    let location: GetLocationResponseDto;
    try {
      const locationTransaction: GetLocationResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          location = await this.prisma.locations.findFirst({
            where: {
              id,
              isDeleted: false,
            },
            select: {
              id: true,
              name: true,
            },
          });

          if (!location) {
            this.logger.log(`Location with id: ${id} not found`);
            throw new NotFoundException(LOCATION_NOT_FOUND);
          }

          const locationCount: number = await this.prisma.locations.count({
            where: {
              NOT: {
                name: {
                  equals: location.name,
                  mode: 'insensitive',
                },
              },
              OR: [
                {
                  name: {
                    equals: updateLocationDto.name,
                    mode: 'insensitive',
                  },
                },
              ],
              isDeleted: false,
            },
          });

          if (locationCount) {
            this.logger.log('Location already exist');
            throw new ConflictException(LOCATION_ALREADY_EXIST);
          }

          const updatedLocation: GetLocationResponseDto =
            await prisma.locations.update({
              where: {
                id,
              },
              data: {
                name: updateLocationDto.name,
              },
              select: {
                id: true,
                name: true,
              },
            });

          this.logger.log(
            `Location with id: ${updatedLocation.id} updated successfully`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.LOCATIONS,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedLocation.id,
              log: {
                userId: user.id,
                name: user.name,
                locationId: updatedLocation.id,
                updatedFields: getUpdatedFields(location, updatedLocation),
              },
            },
          });

          this.logger.log("History for location 'update' created successfully");

          return updatedLocation;
        });

      return locationTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update location: ${error}`);
      throw error;
    }
  }

  async deleteLocation(id: string, user: User): Promise<boolean> {
    const locationTransation: boolean = await this.prisma.$transaction(
      async (prisma) => {
        const location = await prisma.locations.findFirst({
          where: {
            id,
            isDeleted: false,
          },
          select: {
            _count: {
              select: {
                accessory: { where: { isDeleted: false } },
                appliance: { where: { isDeleted: false } },
                asset: { where: { isDeleted: false } },
                audit: { where: { isDeleted: false } },
                consumable: { where: { isDeleted: false } },
                contracts: { where: { isDeleted: false } },
                policies: { where: { isDeleted: false } },
                supplier: { where: { isDeleted: false } },
              },
            },
          },
        });

        if (!location) {
          this.logger.log(`Location with specified id not found`);
          throw new NotFoundException(LOCATION_NOT_FOUND);
        }

        const isLocationUsed = Object.values(location._count).some(
          (value) => value > 0,
        );
        if (isLocationUsed) {
          this.logger.log(
            `Location with specified id is in use and cannot be deleted`,
          );
          throw new BadRequestException(
            `Delete related records in ${Object.keys(location._count)
              .filter((key) => location._count[key] > 0)
              .join(', ')} to delete this location`,
          );
        }

        const deletedLocation: GetLocationResponseDto =
          await prisma.locations.update({
            where: {
              id,
            },
            data: {
              isDeleted: true,
            },
          });

        this.logger.log('Location isDeleted updated to true successfully');

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.LOCATIONS,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: deletedLocation.id,
            log: {
              userId: user.id,
              name: user.name,
              consumableId: deletedLocation.id,
            },
          },
        });

        this.logger.log("History for 'delete' created sucessfully");
        return true;
      },
    );

    return locationTransation;
  }
}
