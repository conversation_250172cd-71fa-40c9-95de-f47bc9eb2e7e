import {
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  GetAllReminderResponsesDto,
  GetReminderResponseDto,
  ReminderDto,
} from './dto/reminder.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { isBefore, isEqual } from 'date-fns';
import {
  checkErrorAndThrowNotFoundError,
  getUpdatedFields,
  setDateWithZeroTime,
  upperSnakeCaseToCamelCase,
} from 'src/utility';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  ReminderCategory,
} from '@prisma-clients/asset-management-backend';
import {
  ENTITY_NOT_FOUND,
  INVALID_REMINDER_DATE,
  REMINDER_NOT_FOUND,
  USER_NOT_FOUND,
  USER_NOT_SUBSCRIBED_TO_REMINDER,
} from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { User } from 'types';

@Injectable()
export class ReminderService {
  constructor(private readonly prisma: PrismaService) {}

  private logger = new Logger('ReminderService');

  private selectArgs = {
    id: true,
    title: true,
    startDate: true,
    endDate: true,
    notes: true,
    notifyUser: {
      select: {
        id: true,
        email: true,
      },
    },
  };

  /**
   * Creates a reminder record with the provided data.
   * @param createReminderDto - The data for creating the reminder.
   * @param categoryType - The category type of the entity.
   * @param entityId - Id of the entity.
   * @returns A promise that resolves to the created reminder.
   */
  async createReminder(
    createReminderDto: ReminderDto,
    categoryType: ReminderCategory,
    entityId: string,
    user: User,
  ): Promise<GetReminderResponseDto> {
    try {
      const reminderCreateTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const entityType = upperSnakeCaseToCamelCase(categoryType);
          const entity = await prisma[entityType].findFirst({
            where: {
              id: entityId,
              isDeleted: false,
            },
          });
          if (!entity) {
            this.logger.error(`Entity with ID: ${entityId} not found`);
            throw new NotFoundException(ENTITY_NOT_FOUND);
          }
          if (isBefore(new Date(createReminderDto.startDate), new Date())) {
            throw new NotAcceptableException(INVALID_REMINDER_DATE);
          }

          const notifyUserIds =
            createReminderDto.notifyUser !== undefined
              ? createReminderDto.notifyUser.length > 0
                ? createReminderDto.notifyUser.map((user) => ({
                    id: user.id,
                  }))
                : undefined
              : undefined;

          const createOptions: Prisma.ReminderCreateInput = {
            [entityType]: {
              connect: {
                id: entityId,
              },
            },
            title: createReminderDto.title,
            typeOfCategory: categoryType,
            startDate: setDateWithZeroTime(createReminderDto.startDate),
            endDate: setDateWithZeroTime(createReminderDto.endDate),
            notes: createReminderDto.notes,
            notifyUser: {
              connect: notifyUserIds,
            },
          };

          const createdReminder = await prisma.reminder.create({
            data: {
              ...createOptions,
            },
            select: this.selectArgs,
          });

          this.logger.log(`Reminder created with ID: ${createdReminder.id}`);
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.REMINDER,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdReminder.id,
              log: {
                userId: user.id,
                name: user.name,
                reminderId: createdReminder.id,
              },
            },
          });
          this.logger.log(`History for 'create reminder' created successfully`);
          return createdReminder;
        },
      );
      return reminderCreateTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create reminder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Updates existing reminder record with the specified ID using the provided data.
   * @param reminderId - The ID of the reminder record to update.
   * @param updateReminderDto - The data for updating the reminder.
   * @param categoryType - The category type of the entity.
   * @returns A promise that resolves to the updated reminder.
   */
  async updateReminder(
    reminderId: string,
    updateReminderDto: ReminderDto,
    user: User,
    categoryType: ReminderCategory,
  ): Promise<GetReminderResponseDto> {
    try {
      const reminderUpdateTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const reminder = await prisma.reminder.findFirst({
            where: {
              id: reminderId,
              typeOfCategory: categoryType,
            },
            include: {
              notifyUser: true,
            },
          });
          if (!reminder) {
            this.logger.error(`Reminder with ID: ${reminderId} not found`);
            throw new NotFoundException(REMINDER_NOT_FOUND);
          }
          const today = new Date().setHours(0, 0, 0, 0);
          if (
            !isEqual(
              new Date(reminder.startDate),
              new Date(setDateWithZeroTime(updateReminderDto.startDate)),
            ) &&
            isBefore(
              new Date(setDateWithZeroTime(updateReminderDto.startDate)),
              today,
            )
          ) {
            throw new NotAcceptableException(INVALID_REMINDER_DATE);
          }

          const currentUsers = reminder.notifyUser.map((user) => user.id);

          const newUsers = updateReminderDto.notifyUser.map((user) => user.id);

          const usersNotInUpdatedDto = currentUsers
            .filter((user) => !newUsers.includes(user))
            .map((userId) => ({ id: userId }));

          const notifyUserIds =
            updateReminderDto.notifyUser !== undefined
              ? updateReminderDto.notifyUser.length > 0
                ? updateReminderDto.notifyUser.map((user) => ({
                    id: user.id,
                  }))
                : undefined
              : undefined;

          const updateOptions: Prisma.ReminderUpdateInput = {
            title: updateReminderDto.title,
            startDate: setDateWithZeroTime(updateReminderDto.startDate),
            endDate: setDateWithZeroTime(updateReminderDto.endDate),
            notes: updateReminderDto.notes,
            notifyUser: {
              disconnect: usersNotInUpdatedDto,
              connect: notifyUserIds,
            },
          };
          const updatedReminder = await prisma.reminder.update({
            where: {
              id: reminderId,
            },
            data: {
              ...updateOptions,
            },
            select: this.selectArgs,
          });
          this.logger.log(`Reminder updated with ID: ${updatedReminder.id}`);
          await prisma.history.create({
            data: {
              action: HistoryActions.UPDATED,
              changeInTable: ChangesOcccuredIn.REMINDER,
              entityId: updatedReminder.id,
              date: new Date(),
              log: {
                userId: user.id,
                name: user.name,
                reminderId: updatedReminder.id,
                updatedFields: getUpdatedFields(reminder, updatedReminder),
              },
            },
          });
          this.logger.log("History for 'update reminder' created successfully");
          return updatedReminder;
        },
      );
      return reminderUpdateTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to update reminder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves reminder record by its ID.
   * @param reminderId - The ID of the reminder record to retrive.
   * @param categoryType - The category type of the entity.
   * @returns  A Promise resolving to the retrieved reminder response DTO.
   */
  async getReminderById(
    reminderId: string,
    categoryType: ReminderCategory,
  ): Promise<GetReminderResponseDto> {
    try {
      const reminder = await this.prisma.reminder.findFirst({
        where: {
          id: reminderId,
          typeOfCategory: categoryType,
        },
        include: {
          notifyUser: true,
        },
      });
      if (!reminder) {
        this.logger.error(`Reminder with ID: ${reminderId} not found`);
        throw new NotFoundException(REMINDER_NOT_FOUND);
      }
      this.logger.log('Reminder fetched successfully');
      return reminder;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to fetch reminder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves a list of reminders based on the provided query parameters.
   * @param queryFilters - The data transfer object containing query parameters.
   * @returns A Promise resolving to an object containing the list of reminders and the total count.
   */
  async getAllReminders(
    queryFilters?: GetAllQueryParamsDto,
  ): Promise<GetAllReminderResponsesDto> {
    try {
      const page: number | null = queryFilters?.page ? queryFilters.page : null;
      const limit: number | undefined = queryFilters?.limit
        ? queryFilters.limit
        : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [queryFilters?.sortBy || 'createdAt']:
          queryFilters?.sortOrder || 'desc',
      };

      const reminders = await this.prisma.reminder.findMany({
        orderBy,
        take: limit,
        skip,
        include: {
          notifyUser: true,
        },
      });

      const count = await this.prisma.reminder.count({});
      this.logger.log(`Fetched ${count} reminders successfully`);
      return { data: reminders, count };
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to fetch reminders: ${error.message}`);
      throw error;
    }
  }
  /**
   * Deletes reminder record with the specified ID.
   * @param reminderId - The ID of the reminder record to delete.
   * @param categoryType - The category type of the entity.
   * @returns  A Promise resolving to true if the deletion is successful.
   */
  async deleteReminder(
    reminderId: string,
    user: User,
    categoryType: ReminderCategory,
  ): Promise<boolean> {
    try {
      const reminder = await this.prisma.reminder.findFirst({
        where: {
          id: reminderId,
          typeOfCategory: categoryType,
        },
      });
      if (!reminder) {
        this.logger.error(
          `Reminder not found with the specified ID: ${reminderId}`,
        );
        throw new NotFoundException(REMINDER_NOT_FOUND);
      }
      await this.prisma.reminder.delete({
        where: {
          id: reminderId,
        },
      });
      this.logger.log(`Reminder with ID ${reminderId} deleted sucessfully`);
      await this.prisma.history.create({
        data: {
          changeInTable: ChangesOcccuredIn.REMINDER,
          action: HistoryActions.DELETED,
          date: new Date(),
          entityId: reminder.id,
          log: {
            userId: user.id,
            name: user.name,
            reminderId: reminder.id,
          },
        },
      });
      this.logger.log(`History for 'delete reminder' created successfully`);
      return true;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to delete reminder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves all reminders for a specific entity
   * @param categoryType The category type of the entity
   * @param entityId - Id of the entity
   * @returns A promise that resolves to a list of reminders
   */
  async getAllRemindersBasedOnEntity(
    categoryType: ReminderCategory,
    entityId: string,
  ): Promise<GetReminderResponseDto[]> {
    try {
      const entityType = upperSnakeCaseToCamelCase(categoryType);
      const entity = await this.prisma[entityType].findFirst({
        where: {
          id: entityId,
          isDeleted: false,
        },
      });
      if (!entity) {
        this.logger.error(`Entity with ID: ${entityId} not found`);
        throw new NotFoundException(ENTITY_NOT_FOUND);
      }
      const reminders = await this.prisma.reminder.findMany({
        where: {
          [entityType]: {
            id: entityId,
            isDeleted: false,
          },
        },
        select: this.selectArgs,
      });
      return reminders;
    } catch (error) {
      this.logger.error(`Failed to fetch reminders: ${error.message}`);
      throw error;
    }
  }

  /**
   * Unsubscribes a user from a reminder.
   * @param user - The user object containing user details.
   * @param reminderId - The ID of the reminder to unsubscribe from
   * @returns A promise that resolves to the updated reminder details.
   */
  async unsubscribeReminder(user: User, reminderId: string): Promise<boolean> {
    try {
      await this.prisma.$transaction(async (prisma) => {
        const userExist = await prisma.user.findFirst({
          where: {
            id: user.id,
            isDeleted: false,
          },
        });
        if (!userExist) {
          this.logger.error(`User with ID: ${user.id} not found`);
          throw new NotFoundException(USER_NOT_FOUND);
        }
        const reminderExist = await prisma.reminder.findFirst({
          where: {
            id: reminderId,
          },
        });
        if (!reminderExist) {
          this.logger.error(`Reminder with ID: ${reminderId} not found`);
          throw new NotFoundException(REMINDER_NOT_FOUND);
        }
        const userSubscribed = await prisma.reminder.findFirst({
          where: {
            id: reminderId,
            notifyUser: {
              some: {
                id: user.id,
              },
            },
          },
        });
        if (!userSubscribed) {
          this.logger.error(
            `User with ID: ${user.id} is not subscribed to reminder with ID: ${reminderId}`,
          );
          throw new NotFoundException(USER_NOT_SUBSCRIBED_TO_REMINDER);
        }
        const updatedReminder = await prisma.reminder.update({
          where: {
            id: reminderId,
          },
          data: {
            notifyUser: {
              disconnect: {
                id: user.id,
              },
            },
          },
          select: this.selectArgs,
        });
        this.logger.log(
          `User with ID: ${user.id} unsubcribed the reminder with ID:${reminderId}`,
        );
        return updatedReminder;
      });
      return true;
    } catch (error) {
      this.logger.error(`Failed to unsubscribe reminder: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves active reminders for a given user
   * @param user - The user for whom to retrieve active reminders.
   * @returns Active reminders for the user.
   */
  async getUserActiveReminders(user: User) {
    try {
      const userExist = await this.prisma.user.findFirst({
        where: {
          id: user.id,
          isDeleted: false,
        },
      });
      if (!userExist) {
        this.logger.error(`User with ID: ${user.id} not found`);
        throw new NotFoundException(USER_NOT_FOUND);
      }
      const today = new Date();
      const activeReminders = await this.prisma.reminder.findMany({
        where: {
          notifyUser: {
            some: {
              id: user.id,
            },
          },
          AND: [
            {
              startDate: {
                lte: today,
              },
            },
            {
              endDate: {
                gte: today,
              },
            },
          ],
        },
        select: {
          id: true,
          title: true,
          notes: true,
          startDate: true,
          endDate: true,
          typeOfCategory: true,
          asset: {
            select: {
              assetName: true,
            },
          },
          softwareLicense: {
            select: {
              name: true,
            },
          },
          policy: {
            select: {
              name: true,
            },
          },
        },
      });

      return activeReminders;
    } catch (error) {
      this.logger.error(`Failed to fetch active reminders: ${error.message}`);
      throw error;
    }
  }
}
