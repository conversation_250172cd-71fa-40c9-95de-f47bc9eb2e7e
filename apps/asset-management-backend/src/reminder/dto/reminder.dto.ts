import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { MinMax } from 'src/common/custom-validators';

export class ReminderDto {
  @ApiProperty({
    description: 'The Reminder title',
    type: 'string',
    example: 'Renew Policy',
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'The reminder start date',
    type: 'date',
    example: new Date(),
  })
  @IsNotEmpty()
  @IsDateString()
  startDate: Date;

  @ApiProperty({
    description: 'The reminder end date',
    type: 'date',
    example: new Date(),
  })
  @IsNotEmpty()
  @IsDateString()
  @MinMax('startDate')
  endDate: Date;

  @ApiProperty({
    description: 'The Reminder notes',
    type: 'string',
    example: 'Renew Policy',
  })
  @IsString()
  @IsOptional()
  notes: string;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: [{ id: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48' }],
  })
  @IsOptional()
  @IsArray()
  notifyUser: { id: string }[];
}

export class GetReminderResponseDto {
  @ApiProperty({
    description: 'The reminder ID',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  id: string;

  @ApiProperty({
    description: 'Title of the reminder',
    type: 'string',
    example: 'Policy Expiry',
  })
  title: string;

  @ApiProperty({
    description: 'Reminder start date',
    type: 'date',
    example: '2024-05-07T07:24:10.836Z',
  })
  startDate: Date;

  @ApiProperty({
    description: 'Reminder end date',
    type: 'date',
    example: '2024-05-07T07:24:10.836Z',
  })
  endDate: Date;

  @ApiProperty({
    description: 'Notes for the reminder',
    type: 'string',
    example: 'Policy Expiry',
  })
  notes: string;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: [
      {
        id: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
        email: '<EMAIL>',
      },
    ],
  })
  @IsOptional()
  @IsArray()
  notifyUser: { id: string; email: string }[];
}

export class GetAllReminderResponsesDto {
  @ApiProperty({
    description: 'Reminder response data',
    isArray: true,
  })
  data: GetReminderResponseDto[];

  @ApiProperty({
    description: 'Total count of reminder',
    type: 'number',
    example: 100,
  })
  count: number;
}
