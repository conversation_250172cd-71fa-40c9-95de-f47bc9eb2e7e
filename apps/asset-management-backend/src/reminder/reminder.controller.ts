import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  HttpStatus,
  Logger,
  ParseUUIDPipe,
  Put,
  Req,
  UseGuards,
  ParseEnumPipe,
} from '@nestjs/common';
import { ReminderService } from './reminder.service';
import { GetReminderResponseDto, ReminderDto } from './dto/reminder.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  INTERNAL_ERROR,
  REMINDER_NOT_FOUND,
  USER_NOT_FOUND,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { ReminderCategory } from '@prisma-clients/asset-management-backend';
import { ReminderGuard } from './reminder.guard';

@Controller('reminder')
@ApiTags('Reminder')
export class ReminderController {
  constructor(private readonly reminderService: ReminderService) {}
  private logger = new Logger('ReminderController');

  @Post(':categoryType/:entityId')
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Reminder created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetReminderResponseDto>),
    },
  })
  @ApiParam({
    name: 'entityId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter entity Id to create reminder',
  })
  @ApiParam({
    name: 'categoryType',
    enum: ReminderCategory,
    example: 'ASSET',
    description: 'Enter reminder category type',
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a reminder',
    summary: 'Create a reminder',
  })
  @ApiExtraModels(HTTPResponseDto<GetReminderResponseDto>)
  async createReminder(
    @Body() createReminderDto: ReminderDto,
    @Param('categoryType', new ParseEnumPipe(ReminderCategory))
    categoryType: ReminderCategory,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetReminderResponseDto>> {
    this.logger.log('API to create reminder');

    const reminder = await this.reminderService.createReminder(
      createReminderDto,
      categoryType,
      entityId,
      request.user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      message: 'Reminder Created Successfully',
      data: reminder,
    };
  }

  @Put('unsubscribe/:reminderId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'reminderId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter reminder id to unsubscribe reminder',
  })
  @ApiOkResponse({
    description: 'Successfully unsubscribed reminder',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetReminderResponseDto>),
    },
  })
  @ApiNotFoundResponse({
    description: 'Reminder with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: REMINDER_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to unsubscribe reminder',
    summary: 'Unsubscribe reminder',
  })
  @ApiExtraModels(HTTPResponseDto<GetReminderResponseDto>)
  async unsubscribeReminder(
    @Param('reminderId', new ParseUUIDPipe()) reminderId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to unsubscribe reminder');

    const updatedReminder = await this.reminderService.unsubscribeReminder(
      request.user,
      reminderId,
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Reminder unsubscribed Successfully',
      data: updatedReminder,
    };
  }

  @Put(':categoryType/:reminderId')
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully updated reminder',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetReminderResponseDto>),
    },
  })
  @ApiParam({
    name: 'reminderId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter reminder Id to update the data',
  })
  @ApiParam({
    name: 'categoryType',
    enum: ReminderCategory,
    example: 'ASSET',
    description: 'Enter reminder category type',
  })
  @ApiNotFoundResponse({
    description: 'Reminder with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: REMINDER_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to update reminder',
    summary: 'Update reminder',
  })
  @ApiExtraModels(HTTPResponseDto<GetReminderResponseDto>)
  async updateReminder(
    @Param('categoryType', new ParseEnumPipe(ReminderCategory))
    categoryType: ReminderCategory,
    @Param('reminderId', new ParseUUIDPipe()) reminderId: string,
    @Body() updateReminderDto: ReminderDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetReminderResponseDto>> {
    this.logger.log('API to update reminder');

    const updatedReminder = await this.reminderService.updateReminder(
      reminderId,
      updateReminderDto,
      request.user,
      categoryType,
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'Reminder updated Successfully',
      data: updatedReminder,
    };
  }

  @Get(':categoryType/:reminderId')
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiFoundResponse({
    description: 'Successfully fetched reminder',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetReminderResponseDto>),
    },
  })
  @ApiParam({
    name: 'reminderId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter reminder Id to fetch the data',
  })
  @ApiParam({
    name: 'categoryType',
    enum: ReminderCategory,
    example: 'ASSET',
    description: 'Enter reminder category type',
  })
  @ApiNotFoundResponse({
    description: 'Reminder with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: REMINDER_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch reminder',
    summary: 'Fetch reminder',
  })
  @ApiExtraModels(HTTPResponseDto<GetReminderResponseDto>)
  async getReminderById(
    @Param('categoryType', new ParseEnumPipe(ReminderCategory))
    categoryType: ReminderCategory,
    @Param('reminderId', new ParseUUIDPipe()) reminderId: string,
  ): Promise<HTTPResponseDto<GetReminderResponseDto>> {
    this.logger.log('API to retrive reminder');
    const reminder = await this.reminderService.getReminderById(
      reminderId,
      categoryType,
    );
    return {
      statusCode: HttpStatus.FOUND,
      message: 'Reminder fetched successfully',
      data: reminder,
    };
  }

  @Get()
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully fetched all reminders',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetReminderResponseDto[]>),
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all reminders',
    summary: 'Fetch all reminders',
  })
  @ApiExtraModels(GetAllResponseDto<GetReminderResponseDto[]>)
  async getAllReminders(
    @Query() queryFilters?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetReminderResponseDto[]>> {
    this.logger.log('API to fetch all reminders');
    const reminders = await this.reminderService.getAllReminders(queryFilters);

    return {
      statusCode: HttpStatus.OK,
      data: reminders.data,
      count: reminders.count,
      message: 'Successfully fetched reminders',
    };
  }

  @Delete(':categoryType/:reminderId')
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted reminder',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<boolean>),
    },
  })
  @ApiParam({
    name: 'reminderId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter reminder Id to delete reminder data',
  })
  @ApiParam({
    name: 'categoryType',
    enum: ReminderCategory,
    example: 'ASSET',
    description: 'Enter reminder category type',
  })
  @ApiNotFoundResponse({
    description: 'Reminder with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: REMINDER_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete reminder',
    summary: 'Delete reminder',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  async deleteReminder(
    @Param('categoryType', new ParseEnumPipe(ReminderCategory))
    categoryType: ReminderCategory,
    @Param('reminderId', new ParseUUIDPipe()) reminderId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to delete reminder');
    const isDeleted = await this.reminderService.deleteReminder(
      reminderId,
      request.user,
      categoryType,
    );

    return {
      statusCode: HttpStatus.OK,
      message: 'The reminder deleted successfully',
      data: isDeleted,
    };
  }

  @Get('/entity/:categoryType/:entityId')
  @UseGuards(ReminderGuard)
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'entityId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter entity Id to fetch reminders',
  })
  @ApiParam({
    name: 'categoryType',
    enum: ReminderCategory,
    example: 'ASSET',
    description: 'Enter reminder category type',
  })
  @ApiOkResponse({
    description: 'Successfully fetched all reminders for particular entity',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetReminderResponseDto[]>),
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all reminders for particular entity',
    summary: 'Fetch all reminders for particular entity',
  })
  @ApiExtraModels(HTTPResponseDto<GetReminderResponseDto[]>)
  async getAllRemindersBasedOnEntity(
    @Param('categoryType', new ParseEnumPipe(ReminderCategory))
    categoryType: ReminderCategory,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
  ): Promise<HTTPResponseDto<GetReminderResponseDto[]>> {
    this.logger.log('API to fetch all reminders for particular entity');

    const reminders = await this.reminderService.getAllRemindersBasedOnEntity(
      categoryType,
      entityId,
    );

    return {
      statusCode: HttpStatus.OK,
      data: reminders,
      message: 'Successfully fetched reminders',
    };
  }

  @Get('/active')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'User Not Found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: USER_NOT_FOUND,
        error: 'Not found error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all active reminders of user',
    summary: 'Fetch all active reminders of user',
  })
  async getUserActiveReminders(@Req() request: Request) {
    this.logger.log('API to fetch all active reminders of user');

    const activeReminders = await this.reminderService.getUserActiveReminders(
      request.user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: activeReminders,
      message: 'Successfully fetched user active reminders',
    };
  }
}
