import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON> } from './reminder.controller';
import { AppModule } from 'src/app.module';

describe('ReminderController', () => {
  let controller: ReminderController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<ReminderController>(ReminderController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
