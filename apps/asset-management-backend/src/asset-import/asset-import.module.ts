import { Module } from '@nestjs/common';
import { AssetImportController } from './asset-import.controller';
import { AssetModelModule } from 'src/asset-model/asset-model.module';
import { StatusModule } from 'src/status/status.module';
import { AssetModule } from 'src/asset/asset.module';
import { UsersModule } from 'src/users/users.module';
import { AssetImportService } from './asset-import.service';
import { CategoryModule } from 'src/category/category.module';
import { ManufacturerModule } from 'src/manufacturer/manufacturer.module';
import { FieldGroupModule } from 'src/field-group/field-group.module';
import { CustomFieldModule } from 'src/custom-field/custom-field.module';

@Module({
  imports: [
    AssetModelModule,
    StatusModule,
    AssetModule,
    UsersModule,
    CategoryModule,
    ManufacturerModule,
    FieldGroupModule,
    CustomFieldModule,
  ],
  controllers: [AssetImportController],
  providers: [AssetImportService],
})
export class AssetImportModule {}
