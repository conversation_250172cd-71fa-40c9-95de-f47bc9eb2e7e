import { JsonValue } from 'types';

// For all the keys from csv file, we are replacing keys used in asset table.
export const csvToAssetKeyMap = {
  Username: 'employeeName',
  Email: 'emailId',
  'Serial Number': 'serialNumber',
  Name: 'assetName',
  RAM: 'ram-GB',
  'Physical Memory (MB)': 'physicalMemroy-MB',
  'Asset Id': 'assetTag',
  Category: 'category',
  Model: 'assetModelId',
  Manufacturer: 'manufacturer',
  'Operating System': 'operatingSystem',
  'OS CD Key': 'OsCdKey',
  'No of Processors': 'numberOfProcessors',
  'Computer Full Name': 'assetFullName',
  'OS Version': 'osVersion',
  'Warranty End Date': 'endOfLife',
  IMEI: 'IMEI',
  'Model no.': 'modelNumber',
  'Product Name': 'productName',
  Status: 'status',
  Location: 'location',
  'Current Status': 'currenStatus',
  Remarks: 'remarks',
};

// Type for initial assets obtained from parsing the csv file
export interface InitialAsset {
  employeeName?: string;
  emailId?: string;
  serialNumber?: string;
  assetName?: string;
  'ram-GB'?: string;
  'physicalMemroy-MB'?: string;
  assetTag?: string;
  category?: string;
  assetModelId?: string;
  manufacturer?: string;
  operatingSystem?: string;
  OsCdKey?: string;
  numberOfProcessors?: string;
  assetFullName?: string;
  osVersion?: string;
  endOfLife?: string;
  IMEI?: string;
  productName?: string;
  modelNumber?: string;
  status?: string;
  location?: string;
  currenStatus?: string;
  remarks?: string;
}

export interface Asset {
  employeeName?: string;
  emailId?: string;
  employeeId?: string;
  serialNumber: string;
  assetName: string;
  assetStatus?: string;
  'ram-GB'?: string;
  'physicalMemroy-MB'?: string;
  assetTag: string;
  category?: string;
  assetModelId?: string;
  manufacturer?: string;
  operatingSystem?: string;
  OsCdKey?: string;
  numberOfProcessors?: string;
  assetFullName?: string;
  osVersion?: string;
  endOfLife?: Date;
  location: string;
  customFields?: JsonValue;
  IMEI?: string;
  productName?: string;
  modelNumber?: string;
  notes?: string;
}
