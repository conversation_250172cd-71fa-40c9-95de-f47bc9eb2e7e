import {
  BadRequestException,
  Injectable,
  Logger,
  UnprocessableEntityException,
  UploadedFile,
} from '@nestjs/common';
import * as csv from 'csv-parser';
import { AssetModelService } from 'src/asset-model/asset-model.service';
import { AssetService } from 'src/asset/asset.service';
import { StatusService } from 'src/status/status.service';
import { UsersService } from 'src/users/users.service';
import {
  Asset,
  InitialAsset,
  csvToAssetKeyMap,
} from './interface/asset-import-interface';
import { CreateAssetDto } from 'src/asset/dto/asset.dto';
import { User } from 'types';
import { CategoryService } from 'src/category/category.service';
import { ManufacturerService } from 'src/manufacturer/manufacturer.service';
import { GetAllManufacturersFilterResponseDto } from 'src/manufacturer/dto/manufacturer.dto';
import {
  camelCasetoPascalCaseWithSpaces,
  toPascalCaseWithSpace,
} from 'src/utility';
import { FieldGroupService } from 'src/field-group/field-group.service';
import { CustomFieldService } from 'src/custom-field/custom-field.service';
import { Readable } from 'stream';
import { extname } from 'path';

@Injectable()
export class AssetImportService {
  private logger = new Logger('AssetImportService');

  constructor(
    private readonly assetModelService: AssetModelService,
    private readonly statusService: StatusService,
    private readonly assetService: AssetService,
    private readonly usersService: UsersService,
    private readonly categoryService: CategoryService,
    private readonly manufacturerService: ManufacturerService,
    private readonly fieldGroupService: FieldGroupService,
    private readonly customFieldService: CustomFieldService,
  ) {}

  /**
   * Checks if any asset model with the given name is already present in the database
   * @returns true if asset model is already present, else false
   */
  async isAssetModelPresent(modelName: string): Promise<boolean> {
    const assetModels = await this.assetModelService.getAllAssetModels();
    return !!assetModels.data.find(
      (assetModel) =>
        assetModel.modelName.toLowerCase() === modelName.toLowerCase(),
    );
  }

  async createManufacturers(newManufacturers: Set<string>, user: User) {
    const manufacturers = (
      await this.manufacturerService.findAll()
    ).manufacturers.map((manufacturer) => manufacturer.name.toLowerCase());
    try {
      for (const newManufacturer of newManufacturers) {
        if (!manufacturers.includes(newManufacturer.toLowerCase())) {
          await this.manufacturerService.create(
            {
              name: toPascalCaseWithSpace(newManufacturer),
              contactName: '',
              contactEmail: '',
              contactPhoneNumber: '',
              manufacturerImageUrl: '',
              note: '',
            },
            user,
          );
        }
      }
      this.logger.log('Manufacturers created successfully!');
    } catch (error) {
      this.logger.log('Error creating Manufacturers:', error);
      throw new Error(`Error creating Manufacturers: ${error}`);
    }
  }

  /**
   * Function to create new categories for assets.
   */
  async createCategories(categoriesSet: Set<string>, user: User) {
    const existingCategories = (
      await this.categoryService.findAllCategory()
    ).data.map((category) => category.name);

    try {
      for (const newCategory of categoriesSet) {
        if (!existingCategories.includes(toPascalCaseWithSpace(newCategory))) {
          await this.categoryService.createCategory(
            {
              name: toPascalCaseWithSpace(newCategory.toString()),
              typeOfCategory: 'ASSET',
              note: null,
            },
            user,
          );
        }
      }
      this.logger.log('Categories created successfully!');
    } catch (error) {
      this.logger.log('Error creating categories:', error);
      throw new Error(`Error creating categories: ${error}`);
    }
  }

  /**
   * This function transforms the raw asset object extracted from csv file, and adds some additional information
   * that is required for an asset based on the existing information
   */
  transformAsset(asset: InitialAsset): Asset {
    try {
      if (!asset.serialNumber || !asset.assetTag) {
        this.logger.log(
          `Missing one or more required value in asset ${asset.assetName}`,
        );
        throw new Error('Missing required values');
      }

      const assetLocation: string = asset.assetTag.includes('B')
        ? 'BANGALORE'
        : asset.assetTag.includes('M')
        ? 'MANGALORE'
        : undefined;
      const notes = asset.currenStatus
        ? asset.currenStatus + '. ' + asset.remarks ?? ''
        : '';
      const status = asset.status?.toLowerCase().startsWith('dead')
        ? 'Archived'
        : 'Inventory';

      const newAssetName =
        asset.manufacturer +
        (asset.assetModelId !== asset.manufacturer
          ? ' ' + asset.assetModelId
          : '');

      const newAsset: Asset = {
        ...asset,
        assetName: newAssetName,
        assetTag: asset.assetTag,
        serialNumber: asset.serialNumber,
        assetStatus: status,
        location: assetLocation,
        endOfLife: null,
        notes: notes,
      };
      return newAsset;
    } catch (error) {
      this.logger.log(
        `Invalid asset Format for asset: ${asset.assetName}, ${error}`,
      );
      throw new Error('Invalid asset Format');
    }
  }

  /**
   * Function to create new asset models. Creates asset model if not exists in database.
   */
  async createAssetModel(
    asset: Asset,
    manufacturersInfo: GetAllManufacturersFilterResponseDto,
    user: User,
  ) {
    const categories = await this.categoryService.findAllCategory();
    const isPresent = await this.isAssetModelPresent(asset.assetModelId);
    if (!isPresent) {
      const categoryId = categories.data.find(
        (category) =>
          category.name.toLowerCase() === asset.category.toLowerCase(),
      )?.id;
      const manufacturerId = manufacturersInfo.manufacturers.find(
        (manufacturer) =>
          asset.manufacturer
            .toLowerCase()
            .startsWith(manufacturer.name.toLowerCase()),
      )?.id;
      await this.assetModelService.createAssetModel(
        {
          modelName: asset.assetModelId.toString().trim(),
          modelNumber: null,
          note: null,
          assetModelImageUrl: null,
          categoryId,
          manufacturerId,
        },
        user,
      );
    }
  }

  /**
   * Function to create new Field group with the provided name. Creates field group if not exists in database
   */
  async createFieldGroup(fieldGroupName: string, user: User) {
    const existingFieldGroups =
      await this.fieldGroupService.getAllFieldGroups();
    const isPresent = existingFieldGroups.data.some(
      (fieldGroup) => fieldGroup.fieldGroupName === fieldGroupName,
    );

    if (!isPresent) {
      await this.fieldGroupService.createFieldGroup(
        {
          fieldGroupName: fieldGroupName,
        },
        user,
      );
    }
  }

  /**
   * This function creates all the custom fields based on the new fields from the asset data.
   */
  async createCustomFields(
    assetAttributes: string[],
    fieldGroupId: string,
    user: User,
  ) {
    // Array of asset attribute names which are already defined, we can add column names which we want to ignore from csv file
    const existingFormInputs = [
      'assetName',
      'assetTag',
      'serialNumber',
      'assetStausId',
      'assetModelId',
      'location',
      'endOfLife',
      'assetStatus',
      'warranty',
      'emailId',
      'category',
      'manufacturer',
      'physicalMemroy-MB',
      'productName',
      'Display Name',
      'employeeName',
      'modelNumber',
    ];

    const existingCustomFields =
      await this.customFieldService.getAllCustomFields();

    // Get the custom fields which are new and does not exists in database
    const newCustomFields = assetAttributes.filter(
      (key) =>
        !existingFormInputs.includes(key) &&
        !existingCustomFields.data.some(
          (customField) => customField.placeholderText === key,
        ) &&
        key !== '',
    );
    for (const field of newCustomFields) {
      await this.customFieldService.createCustomField(
        {
          fieldName: camelCasetoPascalCaseWithSpaces(field),
          fieldType: 'TEXT',
          placeholderText: field,
          fieldGroupIds: [fieldGroupId],
        },
        user,
      );
    }
  }

  /**
   * Retireves assets data from a CSV file, converts the rows into asset objects with the key as the first row values
   */
  async getAssetsFromCsvFile(file: Express.Multer.File, user: User) {
    const buffer = file.buffer.toString('utf-8');
    const bufferStream = new Readable();
    bufferStream.push(buffer);
    bufferStream.push(null);
    const newAssetCategories: Set<string> = new Set();
    const newManufacturers: Set<string> = new Set();
    const extractedAssets: Asset[] = [];

    // Convert each row into an object of type Asset. First row will contain all the keys. These keys will be altered
    // to match the column names of asset schema.
    bufferStream
      .pipe(csv())
      .on('data', (data) => {
        const assetData: InitialAsset = {};
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            const mappedKey = csvToAssetKeyMap[key.trim()] || key;
            assetData[mappedKey.trim()] = data[key];
          }
        }

        // Transform some data according to our requirement from raw data.
        const transformedAsset: Asset = this.transformAsset(assetData);
        extractedAssets.push(transformedAsset);
        newAssetCategories.add(transformedAsset.category.toLowerCase());
        newManufacturers.add(
          transformedAsset.manufacturer.toLowerCase().trim(),
        );
      })
      .on('end', async () => {
        await Promise.all([this.createCategories(newAssetCategories, user)]);
      });
    await Promise.all([this.createManufacturers(newManufacturers, user)]);

    return extractedAssets;
  }

  /**
   * Service to import the asset data from the uploaded csv file.
   * @param file uploaded csv file containing asset data
   * @returns the count of assets created
   */
  async importAssets(@UploadedFile() file: Express.Multer.File) {
    try {
      if (!file) {
        throw new BadRequestException('No file found');
      }

      // Check and validate the file format.
      if (
        file.mimetype !== 'text/csv' &&
        extname(file.originalname) !== '.csv'
      ) {
        throw new UnprocessableEntityException(
          'Invalid file format. Expected ".csv" file.',
        );
      }
      const currentUser = await this.usersService.findOne(
        process.env.ADMIN_USER_ID,
      );
      if (!currentUser) {
        throw new BadRequestException('User not found');
      }

      const user: User = {
        id: currentUser.id,
        email: currentUser.email,
        name: currentUser.name,
        role: currentUser.role,
      };

      // Get all the assets from the csv file.
      const extractedAssets: Asset[] = await this.getAssetsFromCsvFile(
        file,
        user,
      );

      // Retrieve manufacturer, status, users data for connceting to asset.
      const status = await this.statusService.getAllStatus();

      const manufacturers = await this.manufacturerService.findAll();
      for (const asset of extractedAssets) {
        await this.createAssetModel(asset, manufacturers, user);
      }

      const fieldGroup = (
        await this.fieldGroupService.getAllFieldGroups()
      ).data.find(
        (fieldGroup) => fieldGroup.fieldGroupName === 'Mobile Additional Info',
      );

      const customFields = await this.customFieldService.getAllCustomFields();
      const assetModels = await this.assetModelService.getAllAssetModels();

      // setting ids for each assets, ids: assetModelId, userId, assetStatusId
      const assetData: Asset[] = extractedAssets.map((asset) => {
        // Get the asset model id for the asset.
        const assetModel = assetModels.data.find(
          (model) => model.modelName === asset.assetModelId,
        );
        asset.assetModelId = assetModel?.id;

        // Get the asset status
        const assetStatus = status.data.find(
          (currentAssetStatus) =>
            currentAssetStatus.name.toLocaleLowerCase().trim() ===
            asset.assetStatus?.toLowerCase().trim(),
        );
        asset.assetStatus = assetStatus?.id;

        const customFieldData: { [key: string]: string } = {};

        // Creating custom fields object
        for (const customField of customFields.data) {
          const customFieldValue = asset[customField.placeholderText];
          if (customFieldValue !== undefined && customFieldValue !== 'NA') {
            customFieldData[customField.id] = customFieldValue;
          }
        }

        asset.customFields = {
          fieldgroups: [fieldGroup.id],
          data: customFieldData,
        };

        return asset;
      });

      // Get the data necessary for creating an asset.
      const createAssetDtos: CreateAssetDto[] = assetData.map(
        ({
          assetName,
          assetTag,
          serialNumber,
          assetStatus,
          assetModelId,
          location,
          endOfLife,
          customFields,
          notes,
        }) => ({
          assetName,
          assetTag,
          serialNumber,
          assetStausId: assetStatus,
          assetModelId,
          location,
          endOfLife,
          purchaseInfo: {
            orderNumber: '',
            purchaseCost: 0,
            quantity: 1,
          },
          warranty: null,
          assetImageUrl: null,
          note: notes,
          customFields,
        }),
      );
      let completedTransactions = 0;

      // Insert asset and assgin asset based on the returned asset Id.
      for (const newAsset of createAssetDtos) {
        const createdAssetResponse = await this.assetService.createAsset(
          newAsset,
          user,
        );
        if (createdAssetResponse) {
          completedTransactions++;
        }
      }
      if (completedTransactions === undefined || completedTransactions === 0) {
        throw new Error('Failed to import assets');
      }
      return completedTransactions;
    } catch (error) {
      this.logger.log(`Failed to import asset data ${error}`);
    }
  }
}
