import {
  Controller,
  HttpStatus,
  Logger,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { AssetImportService } from './asset-import.service';
import {
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiCreatedResponse,
  ApiBadRequestResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiTags,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import { INTERNAL_ERROR } from 'src/constants/message-constants';

@ApiTags('Asset-import')
@Controller('asset-import')
export class AssetImportController {
  private logger = new Logger('AssetImportController');
  constructor(private readonly assetImportService: AssetImportService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiCreatedResponse({
    description: 'Assets imported successfully',
  })
  @ApiBadRequestResponse({
    description: 'Bad request error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: ['No file found'],
              error: 'Bad Request',
            },
            summary: 'Example: File not found',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    summary: 'Import asset data from CSV file',
    description: 'This API allows to import asset data from CSV file',
  })
  async importAssets(@UploadedFile() file: Express.Multer.File) {
    this.logger.log('API to import assets from csv file');

    const countOfAssetsAdded = await this.assetImportService.importAssets(file);

    return {
      statusCode: HttpStatus.CREATED,
      message: `Added ${
        countOfAssetsAdded ? countOfAssetsAdded : 0
      } asset data`,
    };
  }
}
