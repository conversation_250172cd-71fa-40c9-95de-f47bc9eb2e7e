import { ApiProperty } from '@nestjs/swagger';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';

export class CreateDocumentDto {
  @ApiProperty({
    description: 'The Document title',
    type: 'string',
    example: 'The direction of asset usage',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The file data',
    type: 'Buffer',
    required: true,
  })
  @IsString()
  fileName: string;

  @ApiProperty({
    description: 'The notes associated with the document',
    type: 'string',
    example: 'The specifications of the asset',
    required: false,
  })
  @IsString()
  @IsOptional()
  note: string;
}
export class GetDocumentResponseDto {
  @ApiProperty({
    description: 'The document id',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  id: string;

  @ApiProperty({
    description: 'The document name',
    type: 'string',
    example: 'The asset health',
  })
  name: string;

  @ApiProperty({
    description: 'The document note',
    type: 'string',
    example: 'High valuable asset',
  })
  note: string;

  @ApiProperty({
    description: 'The document file',
    type: 'file',
    example: 'The document file like pdf,png, etc.',
  })
  fileName: string;

  @ApiProperty({
    description: 'The document uploaded date',
    type: 'date',
    example: '2024-03-08T06:55:25.060Z',
  })
  uploadedOn: Date;

  @ApiProperty({
    description: 'The document uploaded user id',
    type: 'uuid',
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  uploadedBy: {
    id: string;
    name: string;
  };
}
export class GetAllDocumentsResponseDto {
  @ApiProperty({
    description: 'Document response data',
    isArray: true,
    type: GetDocumentResponseDto,
  })
  documents: GetDocumentResponseDto[];

  @ApiProperty({
    description: 'Total count of support document',
    type: 'number',
    example: 100,
  })
  count: number;
}

export class GetAllDocumentQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'The document type',
    type: 'string',
    example: 'ASSET',
  })
  @IsEnum(TypeOfCategory)
  @IsOptional()
  typeOfDocument: TypeOfCategory;

  @ApiProperty({
    description: 'Entity Id',
    type: 'strig',
    required: false,
    example: 'be229c1c-1dc2-4660-8be3-8628e0096847',
  })
  @IsOptional()
  @IsUUID()
  entityId: string;
}
