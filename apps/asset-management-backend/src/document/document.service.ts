import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateDocumentDto,
  GetAllDocumentQueryParamsDto,
  GetAllDocumentsResponseDto,
  GetDocumentResponseDto,
} from './dtos/document.dto';
import { checkErrorAndThrowNotFoundError } from 'src/utility';
import {
  ChangesOcccuredIn,
  Prisma,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { DOCUMENT_NOT_FOUND } from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class DocumentService {
  private logger = new Logger('DocumentService');
  private selectArgs = {
    id: true,
    name: true,
    note: true,
    fileName: true,
    uploadedOn: true,
    typeOfDocument: true,
    uploadedBy: {
      select: {
        id: true,
        name: true,
      },
    },
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  /**
   * Asynchronously creates a new document in the database and logs the creation in the history.
   * @param {CreateDocumentDto} dto - The data transfer object containing information about the document to be created.
   * @param {User} user - The user who is creating the document.
   * @returns {Promise<{ fileName: string, id: string }>} A promise that resolves with an object containing the created document's filename and ID.
   * @throws {NotFoundError} Throws a NotFoundError if the provided user ID does not exist.
   * @throws {Error} Throws an Error if the creation process fails.
   */
  async createDocument(
    dto: CreateDocumentDto,
    user: User,
    categortType: TypeOfCategory,
    entityId: string,
  ): Promise<GetDocumentResponseDto> {
    try {
      const documentTransction = await this.prisma.$transaction(
        async (prisma) => {
          const newDocument = await prisma.document.create({
            data: {
              entityId: entityId,
              fileName: dto.fileName,
              name: dto.name,
              note: dto.note,
              typeOfDocument: categortType,
              uploadedBy: user.id
                ? {
                    connect: {
                      id: user.id,
                    },
                  }
                : undefined,
            },
            select: this.selectArgs,
          });
          this.logger.log(`Document created with id: ${newDocument.id}`);
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.DOCUMENT,
              action: 'CREATED',
              date: new Date(),
              entityId: newDocument.id,
              log: {
                userId: user.id,
                name: user.name,
                documentId: newDocument.id,
              },
            },
          });
          this.logger.log("History for 'document create' created successfully");
          return newDocument;
        },
      );
      return documentTransction;
    } catch (error) {
      if (dto.fileName) {
        await this.awsService.deleteFile(dto.fileName);
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create a document: ${error.message}`);
      throw error;
    }
  }

  /**
   * Asynchronously deletes a document from the database and removes its associated file from an AWS S3 bucket.
   * @param {string} documentId - The ID of the document to be deleted.
   * @param {User} user - The user initiating the document deletion.
   * @returns {Promise<boolean>} A promise that resolves with a boolean indicating whether the deletion was successful.
   * @throws {NotFoundException} Throws a NotFoundException if the document with the specified ID is not found.
   * @throws {Error} Throws an Error if the deletion process fails.
   */
  async deleteDocument(documentId: string, user: User): Promise<boolean> {
    try {
      const documentTransction = await this.prisma.$transaction(
        async (prisma) => {
          const checkDocument = await prisma.document.count({
            where: {
              id: documentId,
            },
          });

          if (!checkDocument) {
            this.logger.error('The document not found');
            throw new NotFoundException(
              `The document with specified id:${documentId} is not found`,
            );
          }
          const deletedDocument = await this.prisma.document.delete({
            where: {
              id: documentId,
            },
          });

          this.logger.log(`The document deleted successfully`);

          await this.prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.DOCUMENT,
              action: 'DELETED',
              date: new Date(),
              entityId: deletedDocument.id,
              log: {
                userId: user.id,
                name: user.name,
                documentId: deletedDocument.id,
              },
            },
          });
          this.logger.log(
            "The history for 'document delete' craeted successfully",
          );
          return deletedDocument;
        },
      );
      await this.awsService.deleteFile(documentTransction.fileName);

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete document ${error.message}`);
      throw error;
    }
  }

  /**
   * Asynchronously retrieves a document by its ID.
   * @param {string} documentId - The ID of the document to retrieve.
   * @param {User} user - The user requesting the document.
   * @returns {Promise<GetDocumentDto>} A promise that resolves with the document details.
   * @throws {NotFoundException} Throws a NotFoundException if the document with the specified ID is not found.
   * @throws {Error} Throws an Error if the retrieval process fails.
   */
  async getDocumentById(documentId: string): Promise<GetDocumentResponseDto> {
    const document: GetDocumentResponseDto =
      await this.prisma.document.findFirst({
        where: {
          id: documentId,
        },
        select: this.selectArgs,
      });
    if (!document) {
      this.logger.error(
        `The document not found with specified id: ${documentId}`,
      );
      throw new NotFoundException(DOCUMENT_NOT_FOUND);
    }
    return document;
  }

  /**
   * Asynchronously retrieves all documents based on the provided query parameters.
   * @param {GetAllQueryParamsDto} [dto] - Optional query parameters for pagination, sorting, and search.
   * @returns {Promise<GetAllDocumentsResponseDto>} A promise that resolves with an object containing the retrieved documents and their count.
   * @throws {Error} Throws an Error if the retrieval process fails.
   */
  async getAllDocuments(
    dto?: GetAllDocumentQueryParamsDto,
  ): Promise<GetAllDocumentsResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | null = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'uploadedOn']: dto?.sortOrder || 'desc',
    };

    const whereOptions: Prisma.DocumentWhereInput = dto?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: dto.searchInput,
                mode: 'insensitive',
              },
            },
            {
              note: {
                contains: dto.searchInput,
                mode: 'insensitive',
              },
            },
          ],
        }
      : undefined;

    const [documents, count] = await this.prisma.$transaction([
      this.prisma.document.findMany({
        where: {
          ...whereOptions,
          entityId: dto.entityId,
          typeOfDocument: dto.typeOfDocument,
        },
        select: this.selectArgs,
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.document.count({
        where: {
          ...whereOptions,
          typeOfDocument: dto.typeOfDocument,
        },
      }),
    ]);
    this.logger.log(`Total number of documents ${count} fetched successfully`);
    return {
      documents,
      count,
    };
  }

  /**
   * Asynchronously retrieves the history of changes for a specific document.
   * @param {string} documentId - The ID of the document for which to retrieve the history.
   * @param {GetAllQueryParamsDto} dto - Optional query parameters for pagination and sorting.
   * @returns {Promise<GetEntityHistoryResponse>} A promise that resolves with an object containing the document's change history and its count.
   * @throws {NotFoundException} Throws a NotFoundException if the document with the specified ID is not found.
   * @throws {Error} Throws an Error if the retrieval process fails.
   */
  async getDocumentHistory(
    documentId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const document = await this.prisma.history.count({
      where: {
        entityId: documentId,
      },
    });
    if (!document) {
      this.logger.error(
        `The document not found with specified Id: ${documentId}`,
      );
      throw new NotFoundException('The document not found');
    }
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | null = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
    };

    const [history, count] = await this.prisma.$transaction([
      this.prisma.history.findMany({
        where: {
          entityId: documentId,
        },
        select: {
          action: true,
          date: true,
          log: true,
        },
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.history.count({
        where: {
          entityId: documentId,
        },
      }),
    ]);
    this.logger.log(`Fetched history for document with id: ${documentId}`);
    return {
      history,
      count,
    };
  }

  /**
   * Retrieves the signed URL of a file associated with a document ID.
   * @param {string} documentId - The ID of the document.
   * @returns {Promise<string>} A Promise resolving to an object containing the file URL.
   * @throws {NotFoundException} Throws a NotFoundException if the document or file is not found.
   */
  async getFileUrl(documentId: string): Promise<string> {
    const document = await this.prisma.document.findFirst({
      where: {
        id: documentId,
      },
    });
    if (!document) {
      this.logger.error('The document not found');
      throw new NotFoundException('The document not found');
    }
    return await this.awsService.getFileUrl(document.fileName);
  }
}
