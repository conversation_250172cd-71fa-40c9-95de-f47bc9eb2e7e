import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseEnumPipe,
  ParseUUIDPipe,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DocumentService } from './document.service';
import {
  CreateDocumentDto,
  GetAllDocumentQueryParamsDto,
  GetDocumentResponseDto,
} from './dtos/document.dto';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  DOCUMENT_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { DocumentGuard } from './document.guard';
@ApiTags('Documents')
@Controller('document')
@UseGuards(DocumentGuard)
export class DocumentController {
  private logger = new Logger('DocumentController');
  constructor(private readonly documentService: DocumentService) {}

  @Post(':categoryType/:entityId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Document created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'categoryType',
    enum: TypeOfCategory,
    example: 'ASSET',
    description: 'Enter entity of documnet to upload',
  })
  @ApiOperation({
    description: 'This api allows to create a document',
    summary: 'Create a document',
  })
  @ApiExtraModels(HTTPResponseDto<GetDocumentResponseDto>)
  async createDocument(
    @Body() dto: CreateDocumentDto,
    @Param('categoryType', new ParseEnumPipe(TypeOfCategory))
    categoryType: TypeOfCategory,
    @Param('entityId', new ParseUUIDPipe())
    entityId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetDocumentResponseDto>> {
    const { user } = request;
    this.logger.log('Document create API with necessary data');
    const createdDocument = await this.documentService.createDocument(
      dto,
      user,
      categoryType,
      entityId,
    );
    return {
      statusCode: HttpStatus.CREATED,
      message: 'Document created',
      data: createdDocument,
    };
  }

  @Delete(':categoryType/:documentId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted the documents',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetDocumentResponseDto>),
    },
  })
  @ApiParam({
    name: 'documentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter document Id to delete document permanently',
  })
  @ApiNotFoundResponse({
    description: 'Document with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: DOCUMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiParam({
    name: 'categoryType',
    enum: TypeOfCategory,
    example: 'ASSET',
    description: 'Enter entity of documnet to upload',
  })
  @ApiOperation({
    description: 'This api allows to delete a document',
    summary: 'Delete a document',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  async deleteDocument(
    @Param('categoryType', new ParseEnumPipe(TypeOfCategory))
    categoryType: TypeOfCategory,
    @Param('documentId', new ParseUUIDPipe())
    documentId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    this.logger.log('Document delete API');
    const isDeleted = await this.documentService.deleteDocument(
      documentId,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: isDeleted,
      message: 'Document successfully deleted',
    };
  }

  @Get(':categoryType/:documentId')
  @ApiNotFoundResponse({
    description: 'Document with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: DOCUMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiParam({
    name: 'categoryType',
    enum: TypeOfCategory,
    example: 'ASSET',
    description: 'Enter entity of documnet to upload',
  })
  @ApiParam({
    name: 'documentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter document Id to fetch document data',
  })
  @ApiBearerAuth('access-token')
  @ApiFoundResponse({
    description: 'File deleted Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch a document',
    summary: 'Get a document',
  })
  @ApiExtraModels(HTTPResponseDto<GetDocumentResponseDto>)
  async getDocumentById(
    @Param('categoryType', new ParseEnumPipe(TypeOfCategory))
    categoryType: TypeOfCategory,
    @Param('documentId', new ParseUUIDPipe())
    documentId: string,
  ): Promise<HTTPResponseDto<GetDocumentResponseDto>> {
    this.logger.log('The API to get particular document');
    const obtainedDocument =
      await this.documentService.getDocumentById(documentId);
    return {
      statusCode: HttpStatus.FOUND,
      data: obtainedDocument,
      message: 'Successfully document fetched',
    };
  }

  @Get()
  @ApiOkResponse({
    description: 'Successfully fetched the documents',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetDocumentResponseDto>),
    },
  })
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to fetch all documents',
    summary: 'Fetch all documents',
  })
  async getAllDocuments(
    @Query() dto?: GetAllDocumentQueryParamsDto,
  ): Promise<GetAllResponseDto<GetDocumentResponseDto[]>> {
    this.logger.log('The API to fetch all documents');
    const { documents, count } =
      await this.documentService.getAllDocuments(dto);
    return {
      statusCode: HttpStatus.OK,
      data: documents,
      count: count,
      message: 'Successfully fetched the all Documents',
    };
  }

  @Get('history/:categoryType/:documentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched document history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid access token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'documentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter document Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch document history',
    summary: 'Fetch document history',
  })
  async getDocumentHistory(
    @Param('categoryType', new ParseEnumPipe(TypeOfCategory))
    categoryType: TypeOfCategory,
    @Param('documentId', new ParseUUIDPipe())
    documentId: string,
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    this.logger.log('The API to fetch document history');
    const { history, count } = await this.documentService.getDocumentHistory(
      documentId,
      dto,
    );
    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'Successfully fetched the document history',
    };
  }

  @Get('file-url/:categoryType/:documentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully provided the URL to fetch the file',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid access token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'categoryType',
    enum: TypeOfCategory,
    example: 'ASSET',
    description: 'Enter entity of documnet to upload',
  })
  @ApiParam({
    name: 'documentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter document Id to generate URL',
  })
  @ApiOperation({
    description: 'This API allows to create presigned URL',
    summary: 'Generate presigned URL',
  })
  async getFileUrl(
    @Param('categoryType', new ParseEnumPipe(TypeOfCategory))
    categoryType: TypeOfCategory,
    @Param('documentId', new ParseUUIDPipe())
    documentId: string,
  ): Promise<HTTPResponseDto<string>> {
    this.logger.log('API to create presigned URL');
    const fileUrl = await this.documentService.getFileUrl(documentId);
    return {
      statusCode: HttpStatus.OK,
      data: fileUrl,
      message: 'The presigned URL generated successfully',
    };
  }
}
