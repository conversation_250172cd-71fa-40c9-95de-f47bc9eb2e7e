import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { AbilityFactory } from 'src/ability/ability.factory/ability.factory';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { PERMISSION_DENIED_MESSAGE } from 'src/constants/message-constants';

const categorySubjectMap = {
  ACCESSORY: 'accessories',
  ASSET: 'assets',
  SOFTWARE_LICENSE: 'licenses',
  CONSUMABLE: 'consumables',
  APPLIANCE: 'appliances',
  INSURANCE: 'insurance',
  POLICY: 'policies',
  SERVICE: 'services',
  SUPPLIER: 'suppliers',
};

@Injectable()
export class DocumentGuard implements CanActivate {
  constructor(private readonly abilityFactory: AbilityFactory) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const categoryType =
      request.params.categoryType || request.query.typeOfDocument;
    const httpMethod = request.method;

    const ability = this.abilityFactory.defineAbility(user);

    let action: Action;
    switch (httpMethod) {
      case 'POST':
        action = Action.CREATE;
        break;
      case 'PUT':
      case 'PATCH':
        action = Action.UPDATE;
        break;
      case 'DELETE':
        action = Action.DELETE;
        break;
      case 'GET':
        action = Action.READ;
        break;
      default:
        throw new ForbiddenException('Invalid HTTP method');
    }

    const canPerformAction = ability.can(
      action,
      categorySubjectMap[categoryType] as Subject,
    );

    if (!canPerformAction) {
      throw new ForbiddenException(PERMISSION_DENIED_MESSAGE);
    }

    return true;
  }
}
