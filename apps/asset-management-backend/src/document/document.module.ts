import { Module } from '@nestjs/common';
import { DocumentController } from './document.controller';
import { DocumentService } from './document.service';
import { AwsService } from 'src/aws/aws.service';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [DocumentController],
  providers: [DocumentService, AwsService],
})
export class DocumentModule {}
