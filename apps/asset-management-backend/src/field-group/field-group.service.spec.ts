import { ConflictException, NotFoundException } from '@nestjs/common';
import { TestingModule, Test } from '@nestjs/testing';
import { FieldGroup } from '@prisma-clients/asset-management-backend';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';
import { FieldGroupService } from './field-group.service';
import {
  CreateFieldGroupDto,
  GetFieldGroupResponseDto,
} from './dto/create-field-group.dto';
import {
  FIELDGROUP_EXISTS,
  FIELDGROUP_NOT_FOUND,
} from 'src/constants/message-constants';

describe('FieldGroupService', () => {
  let service: FieldGroupService;
  let prismaService: PrismaService;
  const dto: CreateFieldGroupDto = {
    fieldGroupName: 'Hardware info',
  };

  const fieldGroup: GetFieldGroupResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    fieldGroupName: 'Hardware info',
    customFields: [],
  };

  const user = {
    id: '29c2c660-be0c-4104-80fc-92b9831eed9f',
    email: '<EMAIL>',
    name: '<PERSON> Doe',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        customFields: ['read', 'create', 'update', 'delete'],
      },
    },
    phoneNumber: '',
    kekaId: '',
    departmentId: '',
    createdAt: undefined,
    updatedAt: undefined,
    isDeleted: false,
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
    service = module.get<FieldGroupService>(FieldGroupService);
    prismaService = service['prisma'];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createFieldGroup', () => {
    it('should create a new field group', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(fieldGroup);
      const result = await service.createFieldGroup(dto, user);
      expect(result).toEqual(fieldGroup);
    });

    it('should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(FIELDGROUP_EXISTS));
      expect(
        async () => await service.createFieldGroup(dto, user),
      ).rejects.toThrowError(new ConflictException(FIELDGROUP_EXISTS));
    });
  });

  describe('getFieldGroupById', () => {
    it('should retrieve field group requested ', async () => {
      const id = '073c2022-a405-456a-9d33-844e2c39ac58';
      const expectedResponse: GetFieldGroupResponseDto = {
        id,
        fieldGroupName: 'Hardware information',
      };
      jest
        .spyOn(prismaService.fieldGroup, 'findFirst')
        .mockResolvedValue(expectedResponse as FieldGroup);
      const result = await service.getFieldGroupById(id);
      expect(result).toEqual(expectedResponse);
    });

    it('should throw NotFoundException when field group is not found', async () => {
      const nonExistentId = 'nonExistentId';
      jest.spyOn(prismaService.fieldGroup, 'findFirst').mockResolvedValue(null);
      await expect(
        async () => await service.getFieldGroupById(nonExistentId),
      ).rejects.toThrowError(new NotFoundException(FIELDGROUP_NOT_FOUND));
    });
  });

  describe('updateFieldGroup', () => {
    const id = '0466888a-3296-4a6c-9620-b1c04ee5c2c9';
    const createFieldGroupDto: CreateFieldGroupDto = {
      fieldGroupName: 'Hardware info',
    };

    const updateFieldGroupDto: CreateFieldGroupDto = {
      fieldGroupName: 'Hardware info',
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update field group by id successfully', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(updateFieldGroupDto);
      const result = await service.updateFieldGroup(
        id,
        createFieldGroupDto,
        user,
      );
      expect(result).toEqual(updateFieldGroupDto);
    });

    it('should throw NotFoundException when field group is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(FIELDGROUP_NOT_FOUND));
      await expect(
        service.updateFieldGroup(id, createFieldGroupDto, user),
      ).rejects.toThrowError(new NotFoundException(FIELDGROUP_NOT_FOUND));
    });

    it('should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(FIELDGROUP_EXISTS));
      expect(
        async () =>
          await service.updateFieldGroup(id, updateFieldGroupDto, user),
      ).rejects.toThrowError(new ConflictException(FIELDGROUP_EXISTS));
    });
  });

  describe('deleteFieldGroup', () => {
    const id = '073c2022-a405-456a-9d33-844e2c39ac58';
    it('should delete field group successfully', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(true);
      const result = await service.deleteFieldGroup(id, user);
      expect(result).toBe(true);
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should throw NotFoundException when field group is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(FIELDGROUP_NOT_FOUND));
      await expect(service.deleteFieldGroup(id, user)).rejects.toThrowError(
        new NotFoundException(FIELDGROUP_NOT_FOUND),
      );
    });
  });
});
