import { Module } from '@nestjs/common';
import { FieldGroupService } from './field-group.service';
import { FieldGroupController } from './field-group.controller';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [FieldGroupController],
  providers: [FieldGroupService],
  exports: [FieldGroupService],
})
export class FieldGroupModule {}
