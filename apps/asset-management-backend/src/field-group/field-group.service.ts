import {
  ConflictException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateFieldGroupDto,
  CreatedFieldGroupResponseDto,
  FieldGroupSortBy,
  GetAllFieldGroupsResponseDto,
  GetFieldGroupResponseDto,
  SortOrder,
} from './dto/create-field-group.dto';
import { User } from 'types';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CUSTOM_FIELD_IN_USE_ERROR,
  FIELDGROUP_EXISTS,
  FIELDGROUP_NOT_FOUND,
  FIELD_GROUP_DELETION_NOT_PERMITTED,
} from 'src/constants/message-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { ENTITIES } from 'src/constants';

@Injectable()
export class FieldGroupService {
  private logger = new Logger('FieldGroupService');
  private selectArgs = {
    id: true,
    fieldGroupName: true,
    customFields: true,
    assets: true,
    appliances: true,
    consumables: true,
    licenses: true,
    accessories: true,
    policies: true,
  };
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new field group.
   * @param {CreateFieldGroupDto} createFieldGroupDto - The DTO containing all the data for creating fieldgroup.
   * @returns {Promise<CreatedFieldGroupResponseDto>} - A Promise resolving to the fieldgroup created.
   */
  async createFieldGroup(
    createFieldGroupDto: CreateFieldGroupDto,
    user: User,
  ): Promise<CreatedFieldGroupResponseDto> {
    try {
      const fieldGroupTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const existingFieldGroup: CreatedFieldGroupResponseDto =
            await prisma.fieldGroup.findFirst({
              where: {
                fieldGroupName: createFieldGroupDto.fieldGroupName,
                isDeleted: false,
              },
            });
          if (existingFieldGroup) {
            this.logger.log(
              `Field group with name: "${existingFieldGroup.fieldGroupName}" already exists`,
            );
            throw new ConflictException(FIELDGROUP_EXISTS);
          }

          const createdFieldGroup = await prisma.fieldGroup.create({
            data: {
              fieldGroupName: createFieldGroupDto.fieldGroupName,
              customFields: {
                connect: createFieldGroupDto.customFieldIds
                  ? createFieldGroupDto.customFieldIds.map((customFieldId) => {
                      return { id: customFieldId };
                    })
                  : undefined,
              },
            },
            select: {
              id: true,
              fieldGroupName: true,
            },
          });

          await this.prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.FIELDGROUP,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdFieldGroup.id,
              log: {
                userId: user.id,
                name: user.name,
                FieldGroupId: createdFieldGroup.id,
              },
            },
          });
          return createdFieldGroup;
        },
      );
      this.logger.log(
        `Created history for newly created field group with ID: "${fieldGroupTransaction.id}"`,
      );
      this.logger.log(
        `Field group created successfully with ID: "${fieldGroupTransaction.id}"`,
      );
      return fieldGroupTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        this.logger.log(error);
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to create field group:${error}`);
      throw error;
    }
  }

  /**
   * Retrieves all the field group records.
   * @param {GetAllQueryParamsDto} dto - The DTO containing all the filters for sorting and selecting field groups.
   * @returns {Promise<GetAllQueryParamsDto>} - A Promise resolving to the list of all field groups record.
   */
  async getAllFieldGroups(
    dto?: GetAllQueryParamsDto,
  ): Promise<GetAllFieldGroupsResponseDto> {
    try {
      // Pagination
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | undefined = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Sorting
      const orderBy = {
        [dto?.sortBy || FieldGroupSortBy.CREATEDAT]:
          dto?.sortOrder || SortOrder.DESC,
      };
      const where: Prisma.FieldGroupWhereInput = {
        isDeleted: false,
        ...(dto?.searchInput
          ? {
              OR: [
                {
                  fieldGroupName: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              ],
            }
          : {}),
      };
      const select: Prisma.FieldGroupSelect = {
        ...this.selectArgs,
      };

      const retrieveAllFieldGroups = await this.prisma.fieldGroup.findMany({
        orderBy,
        take: limit,
        skip,
        where,
        select,
      });
      const formatedResponse = retrieveAllFieldGroups?.map((fieldGroup) => ({
        id: fieldGroup.id,
        fieldGroupName: fieldGroup.fieldGroupName,
        customFields: fieldGroup.customFields,
        assets: fieldGroup.assets,
        appliances: fieldGroup.appliances,
        accessories: fieldGroup.accessories,
        licenses: fieldGroup.licenses,
        consumables: fieldGroup.consumables,
        policies: fieldGroup.policies,
      }));

      const totalCount = await this.prisma.fieldGroup.count({ where });
      this.logger.log(`Successfully retrieved all field groups`);
      return {
        data: formatedResponse,
        count: totalCount,
      };
    } catch (error) {
      this.logger.log(`Failed to fetch field groups: ${error}`);
      throw error;
    }
  }

  /**
   * Fetches a field group record with the provided data.
   * @param {string} fieldGroupId - The ID of the field group record to be retrieved.
   * @throws {NotFoundException} - Throws NotFoundException if the field group with the specified ID is not found.
   * @returns {Promise<GetFieldGroupResponseDto>} - A Promise resolving to the data of field group record.
   */
  async getFieldGroupById(
    fieldGroupId: string,
  ): Promise<GetFieldGroupResponseDto> {
    const retrievedData: GetFieldGroupResponseDto =
      await this.prisma.fieldGroup.findFirst({
        where: { id: fieldGroupId, isDeleted: false },
        select: {
          ...this.selectArgs,
          assets: { select: { customFields: true } },
          appliances: { select: { customFields: true } },
          accessories: { select: { customFields: true } },
          consumables: { select: { customFields: true } },
          licenses: { select: { customFields: true } },
          policies: { select: { customFields: true } },
        },
      });
    if (!retrievedData) {
      this.logger.log(`Field group with ID: ${fieldGroupId} not found`);
      throw new NotFoundException(FIELDGROUP_NOT_FOUND);
    }
    this.logger.log(
      `Field group with ID: "${fieldGroupId}" retrieved successfully`,
    );
    return retrievedData;
  }

  /**
   * Updates a field group record with the provided data.
   * @param {string} fieldGroupId - The ID of the field group record to update.
   * @param {CreateCustomFieldDto} updateFieldGroupDto - The DTO containing the updated  field group information.
   * @param {User} user - The user performing the update.
   * @throws {NotFoundException} - Throws NotFoundException if the field group with the specified ID is not found.
   * @throws {ConflictException} - Throws ConflictException if the field group name provided is already present.
   * @returns {Promise<CreatedFieldGroupResponseDto>} - A Promise resolving to the updated field group record.
   */
  async updateFieldGroup(
    fieldGroupId: string,
    updateFieldGroupDto: CreateFieldGroupDto,
    user: User,
  ): Promise<CreatedFieldGroupResponseDto> {
    try {
      const fieldGroupTransaction = await this.prisma.$transaction(
        async (prisma) => {
          // Fetching the field group data by ID, including its associated custom fields
          const fieldGroupData = await prisma.fieldGroup.findFirst({
            where: { id: fieldGroupId, isDeleted: false },
            include: {
              customFields: true,
              assets: true,
              accessories: true,
              consumables: true,
              appliances: true,
              licenses: true,
              policies: true,
            },
          });
          if (!fieldGroupData) {
            this.logger.log(
              `Field group with id: "${fieldGroupId}" not found / already deleted`,
            );
            throw new NotFoundException(FIELDGROUP_NOT_FOUND);
          }
          const existingFieldGroupData = await prisma.fieldGroup.findFirst({
            where: {
              fieldGroupName: updateFieldGroupDto.fieldGroupName,
              isDeleted: false,
              NOT: {
                id: fieldGroupId,
              },
            },
          });
          if (existingFieldGroupData) {
            this.logger.log(
              `Field group with name: "${updateFieldGroupDto.fieldGroupName}" already exists`,
            );
            throw new ConflictException(FIELDGROUP_EXISTS);
          }

          const { customFieldIds, ...updateFieldGroupData } =
            updateFieldGroupDto;

          /**
           *  An array containing the IDs of custom fields linked to the field group.
           */
          const linkedCustomFieldIds = fieldGroupData.customFields.map(
            (field) => field.id,
          );

          const entitiesCustomFieldIds = ENTITIES.flatMap(
            (entityType) =>
              fieldGroupData[entityType]?.flatMap((entity) =>
                Object.keys(entity.customFields['data']),
              ),
          );

          /**
           * An array containing the IDs of custom fields that are both associated with the field group
           * and present in the entities' custom field data.
           */
          const matchingCustomFieldIds = linkedCustomFieldIds.filter((id) =>
            entitiesCustomFieldIds.includes(id),
          );

          const hasMatchingCustomFieldIds = matchingCustomFieldIds.every((id) =>
            customFieldIds.includes(id),
          );

          if (!hasMatchingCustomFieldIds) {
            this.logger.log(
              `Editing not allowed for custom fields in use in field group with ID: ${fieldGroupId}`,
            );
            throw new ForbiddenException(CUSTOM_FIELD_IN_USE_ERROR);
          } else {
            // Updating the field group data in the database
            const updatedFieldGroup = await prisma.fieldGroup.update({
              where: { id: fieldGroupId },
              data: {
                // Disconnecting the existing custom field relations and connecting the new ones
                customFields: {
                  disconnect: fieldGroupData.customFields.map((field) => ({
                    id: field.id,
                  })),
                  connect: customFieldIds.map((customFieldId) => ({
                    id: customFieldId,
                  })),
                },
                ...updateFieldGroupData,
              },
              select: {
                id: true,
                fieldGroupName: true,
              },
            });

            await prisma.history.create({
              data: {
                changeInTable: ChangesOcccuredIn.FIELDGROUP,
                action: HistoryActions.UPDATED,
                date: new Date(),
                entityId: fieldGroupId,
                log: {
                  userId: user.id,
                  name: user.name,
                  fieldGroupId: fieldGroupId,
                  updatedFields: getUpdatedFields(
                    fieldGroupData,
                    updatedFieldGroup,
                  ),
                },
              },
            });
            this.logger.log(
              `Created history for newly updated field group with ID: "${fieldGroupId}"`,
            );
            this.logger.log(
              `Field group with ID: "${fieldGroupId} updated successfully"`,
            );
            return updatedFieldGroup;
          }
        },
      );
      return fieldGroupTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to update field group: ${error}`);
      throw error;
    }
  }

  /**
   *  This function is responsible for deleting a field group by id.
   * @param {string} fieldGroupId  The  field group ID for which details is to be deleted.
   * @throws {NotFoundException} Throws NotFoundException if the  field group with the specified ID is not found.
   * @returns Response message including status code, success status and a message.
   */
  async deleteFieldGroup(fieldGroupId: string, user: User) {
    try {
      const fieldGroupTransaction: boolean = await this.prisma.$transaction(
        async (prisma) => {
          const fieldGroup = await prisma.fieldGroup.findUnique({
            where: {
              id: fieldGroupId,
            },
            include: {
              assets: true,
              accessories: true,
              consumables: true,
              appliances: true,
              licenses: true,
              policies: true,
            },
          });

          if (!fieldGroup || fieldGroup.isDeleted) {
            this.logger.log(
              `Field group with id: "${fieldGroupId}" not found / already deleted`,
            );
            throw new NotFoundException(FIELDGROUP_NOT_FOUND);
          }

          // Check if any related entities are using the field group
          const entitiesUsingFieldGroup = [
            ...fieldGroup.assets,
            ...fieldGroup.accessories,
            ...fieldGroup.consumables,
            ...fieldGroup.appliances,
            ...fieldGroup.licenses,
            ...fieldGroup.policies,
          ];

          // If there are entities using this field group, throw an error
          if (entitiesUsingFieldGroup.length > 0) {
            this.logger.log(
              `Field group with id: "${fieldGroupId}" cannot be deleted`,
            );
            throw new ForbiddenException(FIELD_GROUP_DELETION_NOT_PERMITTED);
          }

          const deleteFieldGroup = await prisma.fieldGroup.update({
            where: {
              id: fieldGroupId,
            },
            data: {
              isDeleted: true,
            },
          });
          this.logger.log(
            `Successfully deleted field group with id:${deleteFieldGroup.id}`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.FIELDGROUP,
              action: HistoryActions.DELETED,
              date: new Date(),
              entityId: fieldGroupId,
              log: {
                userId: user.id,
                userName: user.name,
                fieldGroupId: fieldGroupId,
              },
            },
          });
          this.logger.log(
            `Created history for newly deleted field group with ID: "${fieldGroupId}"`,
          );

          const customFieldsUsedInFieldGroup =
            await prisma.customFields.findMany({
              where: {
                FieldGroups: {
                  some: {
                    id: fieldGroupId,
                  },
                },
              },
              select: {
                id: true,
              },
            });

          // Disconnect the custom field from all associated field groups
          await prisma.fieldGroup.update({
            where: {
              id: fieldGroupId,
            },
            data: {
              customFields: {
                disconnect: customFieldsUsedInFieldGroup.map((customField) => ({
                  id: customField.id,
                })),
              },
              isDeleted: true,
            },
          });
          this.logger.log(
            'Successfully disconnected the field groups from all associated custom fields',
          );

          this.logger.log(
            `Removed field group with ID: "${fieldGroupId}" from all associated custom fields`,
          );

          return true;
        },
      );

      return fieldGroupTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to delete field group: ${error}`);
      throw error;
    }
  }
}
