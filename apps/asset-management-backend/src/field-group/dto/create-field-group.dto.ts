import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { GetCustomFieldResponseDto } from 'src/custom-field/dto/custom-field.dto';
import { IsArrayOfUUIDs } from 'src/utility';
import { JsonValue } from 'types';

/**
 * An interface representing a custom field data type.
 */
export interface customFieldDataType {
  /**
   * The custom field data.
   */
  customFields: JsonValue;
}

export class CreateFieldGroupDto {
  @ApiProperty({
    description: 'Field group name',
    type: 'string',
    example: 'Laptop hardware information',
    required: true,
  })
  @IsString()
  fieldGroupName: string;

  @ApiProperty({
    description: `Array of Custom field id's which the Field-group uses`,
    type: 'array',
    example:
      '["01eb719c-98e2-4b2f-ab04-3a313bff2d70", "01eb719c-98e2-4b2f-ab04-3a313bff2d72"]',
    required: false,
  })
  @IsArrayOfUUIDs()
  @IsOptional()
  customFieldIds?: string[];
}
export class CreatedFieldGroupResponseDto {
  @ApiProperty({
    description: 'Field group Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'Field group name',
    type: 'string',
    example: 'Laptop hardware information',
  })
  fieldGroupName: string;
}

export enum FieldGroupSortBy {
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
  FIELDNAME = 'fieldName',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export class GetFieldGroupResponseDto extends CreatedFieldGroupResponseDto {
  @ApiProperty({
    description: 'Custom fields for the field-group',
    type: 'array',
    example: [
      {
        id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
        fieldName: 'Processor',
        fieldType: 'text',
        placeholderText: 'Enter the processor',
      },
    ],
  })
  @IsArray()
  customFields?: GetCustomFieldResponseDto[];
  assets?: customFieldDataType[];
}

export class GetAllFieldGroupsResponseDto {
  @ApiProperty({
    description: 'The data payload of the response of all fieldgroups',
    isArray: true,
    type: CreatedFieldGroupResponseDto,
  })
  data: GetFieldGroupResponseDto[];

  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}
