import {
  HttpStatus,
  ConflictException,
  InternalServerErrorException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { TestingModule, Test } from '@nestjs/testing';
import { AppModule } from 'src/app.module';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import {
  HTTPResponseDto,
  GetAllQueryParamsDto,
  GetAllResponseDto,
} from 'src/common/http/response.dto';
import {
  CreatedFieldGroupResponseDto,
  CreateFieldGroupDto,
  GetFieldGroupResponseDto,
} from './dto/create-field-group.dto';
import { FieldGroupController } from './field-group.controller';
import { FieldGroupService } from './field-group.service';
import { User } from 'types';
import { Request } from 'express';

describe('FieldGroupController', () => {
  let controller: FieldGroupController;
  let service: FieldGroupService;

  const user: User = {
    id: 'user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        customFields: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<FieldGroupController>(FieldGroupController);
    service = module.get<FieldGroupService>(FieldGroupService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const validFieldGroupId = '073c2022-a405-456a-9d33-844e2c39ac58';
  const invalidFieldGroupId = 'nonExistentId';
  const fieldGroupData: CreatedFieldGroupResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    fieldGroupName: 'Hardware information',
  };

  describe('createFieldGroup', () => {
    it('should create field group successfully', async () => {
      const createFieldGroupDto: CreateFieldGroupDto = {
        fieldGroupName: 'Hardware information',
      };

      const expectedResponse: HTTPResponseDto<GetFieldGroupResponseDto> = {
        statusCode: HttpStatus.CREATED,
        data: fieldGroupData,
        message: 'Field group created successfully',
      };

      jest
        .spyOn(service, 'createFieldGroup')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.createFieldGroup(
        createFieldGroupDto,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle conflict when field group already exists', async () => {
      const createFieldGroupDto: CreateFieldGroupDto = {
        fieldGroupName: 'Hardware information',
      };

      jest
        .spyOn(service, 'createFieldGroup')
        .mockRejectedValue(new ConflictException('Field group already exists'));

      await expect(
        controller.createFieldGroup(
          createFieldGroupDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(ConflictException);
    });
  });

  describe('getAllFieldGroups', () => {
    const filters: GetAllQueryParamsDto = {
      searchInput: '',
      page: 1,
      limit: 1,
      sortBy: SORT_BY.CREATED_AT,
      sortOrder: SORT_ORDER.ASC,
    };
    it('should retrieve all field groups successfully', async () => {
      const expectedResponse: GetAllResponseDto<GetFieldGroupResponseDto[]> = {
        statusCode: HttpStatus.OK,
        data: [fieldGroupData],
        count: 1,
        message: 'Field groups retrieved successfully',
      };

      jest
        .spyOn(service, 'getAllFieldGroups')
        .mockResolvedValue(expectedResponse);

      const result = await controller.getAllFieldGroups(filters);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle internal server error', async () => {
      const errorResponse: InternalServerErrorException =
        new InternalServerErrorException('Internal Server Error');

      jest.spyOn(service, 'getAllFieldGroups').mockRejectedValue(errorResponse);
      await expect(controller.getAllFieldGroups(filters)).rejects.toThrowError(
        errorResponse,
      );
    });
  });

  describe('getFieldGroupById', () => {
    it('should retrieve field group by ID successfully', async () => {
      const expectedResponse: HTTPResponseDto<GetFieldGroupResponseDto> = {
        statusCode: HttpStatus.OK,
        data: fieldGroupData,
        message: 'Field group retrieved successfully',
      };

      jest
        .spyOn(service, 'getFieldGroupById')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.getFieldGroupById(validFieldGroupId);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when field group is not found', async () => {
      jest
        .spyOn(service, 'getFieldGroupById')
        .mockRejectedValue(
          new NotFoundException(
            `Field group with id: ${invalidFieldGroupId} not found / already deleted`,
          ),
        );
      await expect(
        controller.getFieldGroupById(invalidFieldGroupId),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors', async () => {
      const id = invalidFieldGroupId;

      jest
        .spyOn(service, 'getFieldGroupById')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(controller.getFieldGroupById(id)).rejects.toThrowError(
        BadRequestException,
      );
    });
  });

  describe('updateFieldGroup', () => {
    const updateCustomFieldDto: CreateFieldGroupDto = {
      fieldGroupName: 'Hardware info.',
    };
    it('should update field group by ID successfully', async () => {
      const expectedResponse = {
        statusCode: HttpStatus.OK,
        data: fieldGroupData,
        message: `Field group updated successfully`,
      };

      jest
        .spyOn(service, 'updateFieldGroup')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.updateFieldGroup(
        validFieldGroupId,
        updateCustomFieldDto,
        user as unknown as Request,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when requested ID is not found', async () => {
      jest
        .spyOn(service, 'updateFieldGroup')
        .mockRejectedValue(
          new NotFoundException(
            `Field group with id: ${invalidFieldGroupId} not found / already deleted`,
          ),
        );

      await expect(
        controller.updateFieldGroup(
          invalidFieldGroupId,
          updateCustomFieldDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bas request with validation errors during update', async () => {
      jest
        .spyOn(service, 'updateFieldGroup')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.updateFieldGroup(
          invalidFieldGroupId,
          updateCustomFieldDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });

  describe('deleteFieldGroup', () => {
    it('should delete field group successfully', async () => {
      const expectedResponse: HTTPResponseDto<boolean> = {
        statusCode: HttpStatus.OK,
        data: true,
        message: 'Field group has been deleted successfully',
      };

      jest.spyOn(service, 'deleteFieldGroup').mockResolvedValue(true);

      const result = await controller.deleteFieldGroup(
        validFieldGroupId,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when deleting non-existent field group', async () => {
      jest
        .spyOn(service, 'deleteFieldGroup')
        .mockRejectedValue(
          new NotFoundException(
            `Field group with id: ${invalidFieldGroupId} not found / already deleted`,
          ),
        );

      await expect(
        controller.deleteFieldGroup(
          invalidFieldGroupId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors during delete', async () => {
      jest
        .spyOn(service, 'deleteFieldGroup')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.deleteFieldGroup(
          invalidFieldGroupId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });
});
