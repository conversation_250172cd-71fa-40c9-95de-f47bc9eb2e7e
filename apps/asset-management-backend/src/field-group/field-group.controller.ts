import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpStatus,
  Put,
  Logger,
  Req,
  Query,
  ParseUUIDPipe,
  UseGuards,
} from '@nestjs/common';
import { FieldGroupService } from './field-group.service';
import {
  CreateFieldGroupDto,
  CreatedFieldGroupResponseDto,
  GetAllFieldGroupsResponseDto,
  GetFieldGroupResponseDto,
} from './dto/create-field-group.dto';
import {
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  FIELDGROUP_EXISTS,
  FIELDGROUP_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('FieldGroup')
@Controller('fieldGroup')
@UseGuards(PermissionGuard)
export class FieldGroupController {
  private logger = new Logger('FieldGroupController');
  constructor(private readonly fieldGroupService: FieldGroupService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiExtraModels(CreatedFieldGroupResponseDto)
  @ApiCreatedResponse({
    description: 'Successfully created field group',
    schema: {
      $ref: getSchemaPath(CreatedFieldGroupResponseDto),
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: FIELDGROUP_EXISTS,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: `This API allows to create a new field group if same field group name doesn't exists`,
    summary: 'Create a field group',
  })
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CUSTOM_FIELD))
  async createFieldGroup(
    @Body() createFieldGroupDto: CreateFieldGroupDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<CreateFieldGroupDto>> {
    this.logger.log('API for creating a new field group');

    const { user } = req;

    const fieldGroupData = await this.fieldGroupService.createFieldGroup(
      createFieldGroupDto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: fieldGroupData,
      message: 'Field group created successfully',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<GetAllFieldGroupsResponseDto>)
  @ApiOkResponse({
    description: 'Successfully fetched all field groups',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetAllFieldGroupsResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all field groups with an optional query parameter to search by name',
    summary: 'Fetches all field groups',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CUSTOM_FIELD))
  async getAllFieldGroups(
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetFieldGroupResponseDto[]>> {
    this.logger.log('API to fetch all field groups');
    const result = await this.fieldGroupService.getAllFieldGroups(dto);
    return {
      statusCode: HttpStatus.OK,
      data: result.data,
      count: result.count,
      message: 'Field groups retrieved successfully',
    };
  }

  @Get(':fieldGroupId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<GetFieldGroupResponseDto>)
  @ApiOkResponse({
    description: 'Successfully retrieved field group by id',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetFieldGroupResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Field group with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: FIELDGROUP_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch detail of field group with specified Id',
    summary: 'Fetches field group with given Id',
  })
  @ApiParam({
    name: 'fieldGroupId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter field group Id to fetch',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CUSTOM_FIELD))
  async getFieldGroupById(
    @Param('fieldGroupId', new ParseUUIDPipe()) fieldGroupId: string,
  ): Promise<HTTPResponseDto<GetFieldGroupResponseDto>> {
    this.logger.log('API for retrieving field group by ID');
    const result = await this.fieldGroupService.getFieldGroupById(fieldGroupId);
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Field group retrieved successfully',
    };
  }

  @Put('/:fieldGroupId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetFieldGroupResponseDto)
  @ApiOkResponse({
    description: 'Successfully updated field group',
    schema: { $ref: getSchemaPath(GetFieldGroupResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Field group with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: FIELDGROUP_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the field group',
    summary: 'Update field group',
  })
  @ApiParam({
    name: 'fieldGroupId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter field group Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.CUSTOM_FIELD))
  async updateFieldGroup(
    @Param('fieldGroupId') fieldGroupId: string,
    @Body() updateFieldGroupDto: CreateFieldGroupDto,
    @Req() req: Request,
  ) {
    const { user } = req;
    const fieldGroupData = await this.fieldGroupService.updateFieldGroup(
      fieldGroupId,
      updateFieldGroupDto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: fieldGroupData,
      message: 'Field group updated successfully',
    };
  }

  @Delete('/:fieldGroupId')
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted the field groups',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetAllFieldGroupsResponseDto>),
    },
  })
  @ApiNotFoundResponse({
    description: 'Field group with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: FIELDGROUP_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to delete the field group',
    summary: 'Delete field group',
  })
  @ApiParam({
    name: 'fieldGroupId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter field group Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.CUSTOM_FIELD))
  async deleteFieldGroup(
    @Param('fieldGroupId', new ParseUUIDPipe()) fieldGroupId: string,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to delete a field group');
    const { user } = req;
    const result = await this.fieldGroupService.deleteFieldGroup(
      fieldGroupId,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Field group has been deleted successfully',
    };
  }
}
