import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as express from 'express';

import * as dotenv from 'dotenv';

async function bootstrap() {
  const logger = new Logger('bootstrap');
  dotenv.config();

  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({ whitelist: true, validateCustomDecorators: true }),
  );
  app.setGlobalPrefix('api/v1');
  app.enableCors();
  app.use(
    express.raw({
      type: ['image/*', 'application/pdf'],
      limit: process.env.FILE_SIZE,
    }),
  );
  app.use('/robots.txt', (req: express.Request, res: express.Response) => {
    res.type('text/plain');
    res.send('User-agent: *\nDisallow: /');
  });
  const config = new DocumentBuilder()
    .addBearerAuth(
      { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
      'access-token',
    )
    .setTitle('Asset Management')
    .setDescription('The API description')
    .setVersion('1.0.0')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
      defaultModelsExpandDepth: 0,
    },
  });

  const port = 5000;
  await app.listen(port);
  logger.log(`Application listening on port ${port}`);
}
bootstrap();
