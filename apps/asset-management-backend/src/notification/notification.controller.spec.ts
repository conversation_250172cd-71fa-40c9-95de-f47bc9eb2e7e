import { Test, TestingModule } from '@nestjs/testing';
import { NotificationController } from './notification.controller';
import { AppModule } from 'src/app.module';

describe('CronController', () => {
  let controller: NotificationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<NotificationController>(NotificationController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
