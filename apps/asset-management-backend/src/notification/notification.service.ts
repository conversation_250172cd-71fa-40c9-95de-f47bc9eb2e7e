import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PrismaService } from 'src/prisma/prisma.service';
import { CronExpression } from '@nestjs/schedule';
import { AwsService } from 'src/aws/aws.service';
import { format, startOfDay, addDays } from 'date-fns';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { NotificationDto, ReminderDto } from './dto/notification.dto';
import {
  APPLIANCE_SERVICE_OFFSET,
  ASSET_EXPIRY_OFFSET,
  CURRENT_DATE_OFFSET,
  LICENSE_EXPIRY_OFFSET,
} from 'src/constants';
import { Subject } from 'src/common/enums/ability.enum';
import { User } from 'types';

@Injectable()
export class NotificationService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}
  private readonly logger = new Logger(NotificationService.name);
  private assetsSelectArgs = {
    id: true,
    assetName: true,
    assetModel: {
      select: {
        id: true,
        modelName: true,
      },
    },
    assetTag: true,
    assetStatus: {
      select: {
        id: true,
        name: true,
      },
    },
    requestedBy: {
      select: {
        id: true,
        name: true,
      },
    },
    serialNumber: true,
    location: true,
    warranty: true,
    endOfLife: true,
    note: true,
    assetImageUrl: true,
    Audit: {
      select: {
        id: true,
        lastAuditDate: true,
        nextAuditDate: true,
        auditImageUrls: true,
      },
    },
  };
  private licenseSelectArgs = {
    id: true,
    name: true,
    productKey: true,
    expiryDate: true,
    licenseHolderName: true,
    licenseHolderEmail: true,
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    totalQuantity: true,
    availableQuantity: true,
  };

  /**
   * Generate a new Date object with an offset from the current date.
   * @param offsetDays The number of days to offset from the current date.
   * @returns A Date object representing the date offset from the current date.
   */
  generateDate(offsetDays: number): Date {
    return startOfDay(addDays(new Date(), offsetDays));
  }
  baseDateFormat = 'iiii, d MMMM yyyy';
  currentDate = this.generateDate(CURRENT_DATE_OFFSET);
  assetExpiry = this.generateDate(ASSET_EXPIRY_OFFSET);
  licenseExpiry = this.generateDate(LICENSE_EXPIRY_OFFSET);
  applianceExpiry = this.generateDate(APPLIANCE_SERVICE_OFFSET);

  async findEntityExpiriesAndServices(user: User): Promise<NotificationDto> {
    // Expired Assets and Licenses
    const entityExpiriesAndServices: NotificationDto = {};

    const userPermissions = user.role.permissions;

    if (userPermissions[Subject.ASSET]) {
      const expiredAssets = await this.prisma.asset.findMany({
        where: {
          endOfLife: {
            lt: this.currentDate,
          },
          isDeleted: false,
        },
        orderBy: {
          endOfLife: 'desc',
        },
        select: this.assetsSelectArgs,
      });

      const assetAssignments = await this.prisma.assignment.findMany({
        where: { typeOfAssignment: TypeOfCategory.ASSET },
        select: {
          entityId: true,
          user: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      const expiredAssetsWithAssigneeDetail = expiredAssets.map((asset) => {
        const assetAssignment = assetAssignments.find(
          (assignment) => asset.id === assignment.entityId,
        );

        return { ...asset, assignedUser: assetAssignment?.user ?? null };
      });

      const assetsAboutToExpire = await this.prisma.asset.findMany({
        where: {
          endOfLife: {
            gte: this.currentDate,
            lte: this.assetExpiry,
          },
          isDeleted: false,
        },
        orderBy: {
          endOfLife: 'asc',
        },
        select: this.assetsSelectArgs,
      });

      const assetsAboutToExpireWithAssigneeDetail = assetsAboutToExpire.map(
        (asset) => {
          const assetAssignment = assetAssignments.find(
            (assignment) => asset.id === assignment.entityId,
          );

          return { ...asset, assignedUser: assetAssignment?.user ?? null };
        },
      );
      entityExpiriesAndServices.assets = {
        expired: expiredAssetsWithAssigneeDetail,
        upcomingExpiry: assetsAboutToExpireWithAssigneeDetail,
      };

      this.logger.log(
        `Number of Assets to be expired: ${assetsAboutToExpire.length}`,
      );
    }
    if (userPermissions[Subject.SOFTWARE_LICENSE]) {
      const expiredLicenses = await this.prisma.softwareLicense.findMany({
        where: {
          expiryDate: {
            lt: this.currentDate,
          },
          isDeleted: false,
        },
        orderBy: {
          expiryDate: 'desc',
        },
        select: this.licenseSelectArgs,
      });

      const licensesAboutToExpire = await this.prisma.softwareLicense.findMany({
        where: {
          expiryDate: {
            gte: this.currentDate,
            lte: this.licenseExpiry,
          },
          isDeleted: false,
        },
        orderBy: {
          expiryDate: 'asc',
        },
        select: this.licenseSelectArgs,
      });
      entityExpiriesAndServices.licenses = {
        expired: expiredLicenses,
        upcomingExpiry: licensesAboutToExpire,
      };

      this.logger.log(
        `Number Licenses to be expired ${licensesAboutToExpire.length} respectively`,
      );
    }
    if (userPermissions[Subject.APPLIANCE]) {
      const appliancesDueForService = await this.prisma.service.findMany({
        where: {
          nextServiceDate: {
            gt: this.currentDate,
            lte: this.applianceExpiry,
          },
          isDeleted: false,
        },
        select: {
          appliances: {
            select: {
              id: true,
              name: true,
              modelNumber: true,
              location: true,
            },
          },
          nextServiceDate: true,
        },
        orderBy: {
          nextServiceDate: 'desc',
        },
      });

      entityExpiriesAndServices.appliances = appliancesDueForService;

      this.logger.log(
        `Number of Appliances that are due for service ${appliancesDueForService.length}`,
      );
    }

    return entityExpiriesAndServices;
  }

  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async emailNotificationForExpiringEntities() {
    const [
      assetsAboutToExpire,
      licensesAboutToExpire,
      appliancesDueForService,
    ] = [
      await this.prisma.asset.findMany({
        where: {
          endOfLife: this.assetExpiry,
          isDeleted: false,
        },
        orderBy: {
          endOfLife: 'desc',
        },
        select: { assetName: true },
      }),
      await this.prisma.softwareLicense.findMany({
        where: {
          expiryDate: this.licenseExpiry,
          isDeleted: false,
        },
        orderBy: {
          expiryDate: 'desc',
        },
        select: { name: true },
      }),
      await this.prisma.service.findMany({
        where: {
          nextServiceDate: this.applianceExpiry,
          isDeleted: false,
        },
        orderBy: {
          nextServiceDate: 'desc',
        },
        select: {
          appliances: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      }),
    ];
    const entitiesToBeNotified = {
      assets: assetsAboutToExpire,
      licenses: licensesAboutToExpire,
      appliances: appliancesDueForService,
    };
    const entityResponse = {
      dates: {
        currentDate: format(this.generateDate(0), 'iiii, d MMMM'),
        assetExpiry: format(this.assetExpiry, this.baseDateFormat),
        licenseExpiry: format(this.licenseExpiry, this.baseDateFormat),
        applianceExpiry: format(this.applianceExpiry, this.baseDateFormat),
      },
      entityData: entitiesToBeNotified,
    };

    if (
      entitiesToBeNotified.assets.length > 0 ||
      entitiesToBeNotified.licenses.length > 0 ||
      entitiesToBeNotified.appliances.length > 0
    ) {
      this.awsService.sendEmailOnEntityExpiry(entityResponse);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async emailNotificationForReminders() {
    const today = new Date();
    const reminders = await this.prisma.reminder.findMany({
      where: {
        AND: [
          {
            startDate: {
              lte: today,
            },
          },
          {
            endDate: {
              gte: today,
            },
          },
        ],
      },
      select: {
        id: true,
        title: true,
        notes: true,
        notifyUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        startDate: true,
        endDate: true,
        asset: {
          select: {
            assetName: true,
          },
        },
        softwareLicense: {
          select: {
            name: true,
          },
        },
        policy: {
          select: {
            name: true,
          },
        },
        appliance: {
          select: {
            name: true,
          },
        },
        typeOfCategory: true,
      },
    });

    // Map to store users and their reminders
    const userReminderMap: { [email: string]: ReminderDto[] } = {};

    reminders.forEach((reminder) => {
      reminder.notifyUser.forEach((user) => {
        if (!userReminderMap[user.email]) {
          userReminderMap[user.email] = [];
        }
        userReminderMap[user.email].push(reminder);
      });
    });

    // Convert the map to an array of objects
    const filteredUsersWithReminder = Object.entries(userReminderMap).map(
      ([user, reminders]) => ({
        user,
        reminders,
      }),
    );
    // Send notification to each users
    filteredUsersWithReminder.forEach(async (userWithReminder) => {
      await this.awsService.ReminderEmailNotification(userWithReminder);
    });
  }
}
