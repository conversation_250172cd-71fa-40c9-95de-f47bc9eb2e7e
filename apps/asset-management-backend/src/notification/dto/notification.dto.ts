import { GetAssetResponseDto } from 'src/asset/dto/asset.dto';
import { ReminderCategory } from '@prisma-clients/asset-management-backend';
import { LicenseListDto } from 'src/software-license/dto/software-license.dto';
import { EntityDto } from 'src/common/dto/entity.dto';

export class NotificationDto {
  assets?: {
    expired: GetAssetResponseDto[];
    upcomingExpiry: GetAssetResponseDto[];
  };
  licenses?: {
    expired: LicenseListDto[];
    upcomingExpiry: LicenseListDto[];
  };
  appliances?: {
    appliances: {
      id?: string;
      name?: string;
      modelNumber?: string;
      location?: EntityDto;
    }[];
    nextServiceDate?: Date;
  }[];
}
export class ReminderDto {
  title: string;
  notes?: string;
  endDate: Date;
  asset?: {
    assetName: string;
  };
  softwareLicense?: {
    name: string;
  };
  policy?: {
    name: string;
  };
  appliance?: {
    name: string;
  };
  typeOfCategory: ReminderCategory;
}
