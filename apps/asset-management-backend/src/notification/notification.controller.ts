import { Controller, Get, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { NotificationDto } from './dto/notification.dto';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { Request } from 'express';

@ApiTags('Notification')
@Controller('/dashboard/notification')
@UseGuards(PermissionGuard)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @ApiBearerAuth('access-token')
  @Get()
  @CheckPolicies(
    new PolicyHandler(Action.READ, [
      Subject.ASSET,
      Subject.APPLIANCE,
      Subject.SOFTWARE_LICENSE,
    ]),
  )
  async findAllEntityUpdates(
    @Req() request: Request,
  ): Promise<HTTPResponseDto<NotificationDto>> {
    const response: NotificationDto =
      await this.notificationService.findEntityExpiriesAndServices(
        request.user,
      );

    return {
      statusCode: HttpStatus.OK,
      data: response,
      message: 'Entity updates retrieved successfully',
    };
  }
}
