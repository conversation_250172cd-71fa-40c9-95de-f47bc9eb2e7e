import { ApiProperty } from '@nestjs/swagger';
import {
  FieldGroup,
  InputType,
} from '@prisma-clients/asset-management-backend';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { IsArrayOfUUIDs } from 'src/utility';
import { JsonValue } from 'types';

/**
 * An interface representing an asset with an ID and custom field data.
 */
export interface AssetWithIdAndCustomField {
  /**
   * The unique identifier of the asset.
   */
  id: string;
  /**
   * The custom field data for the asset.
   */
  customFields: JsonValue;
}

/**
 * An interface representing a field group with an array of assets with IDs and custom field data.
 */
export interface FieldGroupWithAssetType extends FieldGroup {
  /**
   * An array of assets associated with the field group.
   */
  assets: AssetWithIdAndCustomField[];
}

export class CreateCustomFieldDto {
  @ApiProperty({
    description: 'Custom field label name',
    type: 'string',
    example: 'Processor',
    required: true,
  })
  @IsString()
  fieldName: string;

  @ApiProperty({
    description: 'Input type of the custom field',
    enum: InputType,
    example: 'text',
    required: true,
  })
  @IsEnum(InputType)
  fieldType: InputType;

  @ApiProperty({
    description: 'Placeholder text for the input',
    type: 'string',
    example: 'Enter the processor',
    required: false,
  })
  @IsString()
  placeholderText: string;

  @ApiProperty({
    description: `Array of FieldSet id's which uses the custom field`,
    type: 'array',
    example:
      '["01eb719c-98e2-4b2f-ab04-3a313bff2d70", "01eb719c-98e2-4b2f-ab04-3a313bff2d72"]',
    required: false,
  })
  @IsArrayOfUUIDs()
  @IsOptional()
  fieldGroupIds?: string[];
}

export class GetCustomFieldResponseDto {
  @ApiProperty({
    description: 'Custom field Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'Custom field label name',
    type: 'string',
    example: 'Processor',
  })
  fieldName: string;

  @ApiProperty({
    description: 'Input type of the custom field',
    enum: InputType,
    example: 'text',
  })
  fieldType: InputType;

  @ApiProperty({
    description: 'Placeholder text for the input',
    type: 'string',
    example: 'Enter the processor',
  })
  placeholderText: string;
}

export enum CustomFieldSortBy {
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
  FIELDNAME = 'fieldName',
}

export enum SortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export class GetCustomFieldByIdResponseDto extends GetCustomFieldResponseDto {
  @ApiProperty({
    description: 'List of Field groups that uses the custom field',
    type: 'array',
    example: {
      id: 'ef7a6669-083b-49d2-b493-910eea79cdf0',
      fieldSetName: 'Laptop hardware information',
      customFields: [],
    },
  })
  @IsOptional()
  fieldGroups?: FieldGroupWithAssetType[];
}

export class GetAllCustomFieldResponsePayload {
  @ApiProperty({
    description: 'The data payload of the response.',
    isArray: true,
    type: GetCustomFieldByIdResponseDto,
  })
  data: GetCustomFieldByIdResponseDto[];

  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}
