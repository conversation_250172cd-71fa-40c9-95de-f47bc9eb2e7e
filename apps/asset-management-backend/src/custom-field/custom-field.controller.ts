import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpStatus,
  Req,
  Logger,
  Query,
  ParseUUIDPipe,
  Put,
  UseGuards,
} from '@nestjs/common';
import { CustomFieldService } from './custom-field.service';
import {
  CreateCustomFieldDto,
  GetAllCustomFieldResponsePayload,
  GetCustomFieldResponseDto,
} from './dto/custom-field.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  getSchemaPath,
  ApiUnauthorizedResponse,
  ApiConflictResponse,
  ApiInternalServerErrorResponse,
  ApiOperation,
  ApiExtraModels,
  ApiTags,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiParam,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  CUSTOM_FIELD_EXISTS,
  CUSTOM_FIELD_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Custom-field')
@Controller('custom-field')
@UseGuards(PermissionGuard)
export class CustomFieldController {
  private logger = new Logger('CustomFieldController');
  constructor(private readonly customFieldService: CustomFieldService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created custom field',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetCustomFieldResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: CUSTOM_FIELD_EXISTS,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a new custom field if not exists',
    summary: 'Create a custom field',
  })
  @ApiExtraModels(HTTPResponseDto<GetCustomFieldResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CUSTOM_FIELD))
  async createCustomField(
    @Body() createCustomFieldDto: CreateCustomFieldDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<GetCustomFieldResponseDto>> {
    this.logger.log('API for creating a new custom field');

    const { user } = req;

    const customFieldData = await this.customFieldService.createCustomField(
      createCustomFieldDto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: customFieldData,
      message: 'Custom field created successfully',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetAllCustomFieldResponsePayload)
  @ApiOkResponse({
    description: 'Successfully fetched all custom fields',
    schema: {
      $ref: getSchemaPath(GetAllCustomFieldResponsePayload),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all custom fields with an optional query parameter to search by name',
    summary: 'Fetches all custom fields',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CUSTOM_FIELD))
  async getAllCustomFields(
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetCustomFieldResponseDto[]>> {
    this.logger.log('API to fetch all custom fields');
    const result = await this.customFieldService.getAllCustomFields(dto);
    return {
      statusCode: HttpStatus.OK,
      data: result.data,
      count: result.count,
      message: 'Custom fields retrieved successfully',
    };
  }

  @Get(':customFieldId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetCustomFieldResponseDto)
  @ApiOkResponse({
    description: 'Successfully retrieved custom field by id',
    schema: {
      $ref: getSchemaPath(GetCustomFieldResponseDto),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Custom field with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CUSTOM_FIELD_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch detail of custom field with specified Id',
    summary: 'Fetches custom field with given Id',
  })
  @ApiParam({
    name: 'customFieldId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter custom field Id to fetch',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CUSTOM_FIELD))
  async getCustomFieldById(
    @Param('customFieldId', new ParseUUIDPipe()) customFieldId: string,
  ): Promise<HTTPResponseDto<GetCustomFieldResponseDto>> {
    this.logger.log('API for retrieving custom field by ID');
    const result =
      await this.customFieldService.getCustomFieldById(customFieldId);
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Custom field retrieved successfully',
    };
  }

  @Put('/:customFieldId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetCustomFieldResponseDto)
  @ApiOkResponse({
    description: 'Successfully updated custom field',
    schema: { $ref: getSchemaPath(GetCustomFieldResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Custom field with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CUSTOM_FIELD_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the custom field',
    summary: 'Update custom field',
  })
  @ApiParam({
    name: 'customFieldId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter custom field Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.CUSTOM_FIELD))
  async updateCustomField(
    @Param('customFieldId', new ParseUUIDPipe()) customFieldId: string,
    @Body() updateCustomFieldDto: CreateCustomFieldDto,
    @Req() req: Request,
  ) {
    const { user } = req;
    const customFieldData = await this.customFieldService.updateCustomField(
      customFieldId,
      updateCustomFieldDto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: customFieldData,
      message: 'Custom field updated successfully',
    };
  }

  @Delete('/:customFieldId')
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Custom field with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CUSTOM_FIELD_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to delete the custom field',
    summary: 'Delete custom field',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.CUSTOM_FIELD))
  async deleteCustomField(
    @Param('customFieldId', new ParseUUIDPipe()) customFieldId: string,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to delete a custom field');
    const { user } = req;
    const result = await this.customFieldService.deleteCustomField(
      customFieldId,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Custom field has been deleted successfully',
    };
  }
}
