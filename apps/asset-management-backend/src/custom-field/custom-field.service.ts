import {
  ConflictException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateCustomFieldDto,
  CustomFieldSortBy,
  GetAllCustomFieldResponsePayload,
  GetCustomFieldByIdResponseDto,
  GetCustomFieldResponseDto,
  SortOrder,
} from './dto/custom-field.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { User } from 'types';
import {
  CUSTOM_FIELD_DELETION_NOT_PERMITTED,
  CUSTOM_FIELD_EXISTS,
  CUSTOM_FIELD_NOT_FOUND,
  FIELD_GROUP_IN_USE_ERROR,
} from 'src/constants/message-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { ENTITIES } from 'src/constants';

@Injectable()
export class CustomFieldService {
  private logger = new Logger('CustomFieldService');
  private selectArgs = {
    id: true,
    fieldName: true,
    fieldType: true,
    placeholderText: true,
  };
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new custom field record with the provided data.
   *
   * @param {CreateCustomFieldDto} createCustomFieldDto - The DTO containing the new custom field information.
   * @param {User} user - The user performing the creation.
   * @throws {ConflictException} - Throws ConflictException if a custom field with the same name already exists.
   * @returns {Promise<GetCustomFieldResponseDto>} - A Promise resolving to the newly created custom field record.
   */
  async createCustomField(
    createCustomFieldDto: CreateCustomFieldDto,
    user: User,
  ): Promise<GetCustomFieldResponseDto> {
    try {
      const customFieldTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const existingCustomField: GetCustomFieldResponseDto =
            await prisma.customFields.findFirst({
              where: {
                fieldName: createCustomFieldDto.fieldName,
                isDeleted: false,
              },
            });
          if (existingCustomField) {
            this.logger.log(
              `Custom field with field name: "${existingCustomField.fieldName}" already exists`,
            );
            throw new ConflictException(CUSTOM_FIELD_EXISTS);
          }
          const createdCustomField = await prisma.customFields.create({
            data: {
              fieldName: createCustomFieldDto.fieldName,
              fieldType: createCustomFieldDto.fieldType,
              placeholderText: createCustomFieldDto.placeholderText,
              FieldGroups: {
                connect: createCustomFieldDto.fieldGroupIds
                  ? createCustomFieldDto.fieldGroupIds.map((fieldSetId) => {
                      return { id: fieldSetId };
                    })
                  : undefined,
              },
            },
            select: this.selectArgs,
          });

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CUSTOM_FIELD,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdCustomField.id,
              log: {
                userId: user.id,
                name: user.name,
                customFieldId: createdCustomField.id,
              },
            },
          });
          return createdCustomField;
        },
      );
      this.logger.log(
        `Created history for newly created custom field with ID: "${customFieldTransaction.id}"`,
      );
      this.logger.log(
        `Custom field created successfully with ID: "${customFieldTransaction.id}"`,
      );
      return customFieldTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        this.logger.log(error);
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to create custom field :${error}`);
      throw error;
    }
  }

  /**
   * Retrieves all the custom field records.
   * @param {GetAllQueryParamsDto} dto - The DTO containing all the filters for sorting and selecting custom fields.
   * @returns {Promise<GetCustomFieldResponseDto>} - A Promise resolving to the list of all custom field record.
   */
  async getAllCustomFields(
    dto?: GetAllQueryParamsDto,
  ): Promise<GetAllCustomFieldResponsePayload> {
    try {
      // Pagination
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | undefined = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Sorting
      const orderBy = {
        [dto?.sortBy || CustomFieldSortBy.CREATEDAT]:
          dto?.sortOrder || SortOrder.DESC,
      };
      const where: Prisma.CustomFieldsWhereInput = {
        isDeleted: false,
        ...(dto?.searchInput
          ? {
              OR: [
                {
                  fieldName: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              ],
            }
          : {}),
      };
      const select: Prisma.CustomFieldsSelect = {
        ...this.selectArgs,
        FieldGroups: true,
      };

      const retrieveAllCustomFields = await this.prisma.customFields.findMany({
        orderBy,
        take: limit,
        skip,
        where,
        select,
      });
      const formatedResponse = retrieveAllCustomFields?.map((customField) => ({
        id: customField.id,
        fieldName: customField.fieldName,
        fieldType: customField.fieldType,
        placeholderText: customField?.placeholderText,
        fieldSets: customField?.FieldGroups,
      }));

      const totalCount = await this.prisma.customFields.count({ where });
      this.logger.log(`Successfully retrieved all custom fields`);
      return {
        data: formatedResponse,
        count: totalCount,
      };
    } catch (error) {
      this.logger.log(`Failed to fetch custom fields: ${error}`);
      throw error;
    }
  }

  /**
   * Fetches a custom field record with the provided data.
   * @param {string} customFieldId - The ID of the custom field record to be retrieved.
   * @throws {NotFoundException} - Throws NotFoundException if the custom field with the specified ID is not found.
   * @returns {Promise<GetCustomFieldResponseDto>} - A Promise resolving to the data of custom field record.
   */
  async getCustomFieldById(
    customFieldId: string,
  ): Promise<GetCustomFieldResponseDto> {
    const retrievedData: GetCustomFieldByIdResponseDto =
      await this.prisma.customFields.findFirst({
        where: { id: customFieldId, isDeleted: false },
        select: {
          ...this.selectArgs,
          FieldGroups: {
            select: {
              id: true,
              fieldGroupName: true,
              isDeleted: true,
              createdAt: true,
              updatedAt: true,
              assets: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
              accessories: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
              consumables: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
              licenses: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
              appliances: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
              policies: {
                select: {
                  id: true,
                  customFields: true,
                },
              },
            },
          },
        },
      });
    if (!retrievedData) {
      this.logger.log(`Custom field with ID: ${customFieldId} not found`);
      throw new NotFoundException(CUSTOM_FIELD_NOT_FOUND);
    }
    this.logger.log(
      `Custom field with ID: "${customFieldId}" retrieved successfully`,
    );
    // this.logger.log(retrievedData);
    return retrievedData;
  }

  /**
   * Updates a custom field record with the provided data.
   * @param {string} customFieldId - The ID of the custom field record to update.
   * @param {CreateCustomFieldDto} updateCustomFieldDto - The DTO containing the updated custom field information.
   * @param {User} user - The user performing the update.
   * @throws {NotFoundException} - Throws NotFoundException if the custom field with the specified ID is not found.
   * @throws {ConflictException} - Throws ConflictException if the custom field name provided is already present.
   * @returns {Promise<GetCustomFieldResponseDto>} - A Promise resolving to the updated custom field record.
   */
  async updateCustomField(
    customFieldId: string,
    updateCustomFieldDto: CreateCustomFieldDto,
    user: User,
  ): Promise<GetCustomFieldResponseDto> {
    try {
      const customFieldTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const customFieldData = await prisma.customFields.findFirst({
            where: { id: customFieldId, isDeleted: false },
            include: { FieldGroups: true },
          });

          if (!customFieldData) {
            this.logger.log(
              `Custom field with id: "${customFieldId}" not found / already deleted`,
            );
            throw new NotFoundException(CUSTOM_FIELD_NOT_FOUND);
          }

          const existingCustomFieldData = await prisma.customFields.findFirst({
            where: {
              fieldName: updateCustomFieldDto.fieldName,
              isDeleted: false,
              NOT: {
                id: customFieldId,
              },
            },
          });

          if (existingCustomFieldData) {
            this.logger.log(
              `Custom field with name: "${updateCustomFieldDto.fieldName}" already exists`,
            );
            throw new ConflictException(CUSTOM_FIELD_EXISTS);
          }

          const { fieldGroupIds, ...updateCustomFieldData } =
            updateCustomFieldDto;

          // Retrieve connected field groups
          const fieldGroupIdsFromCustomField = customFieldData.FieldGroups.map(
            (group) => group.id,
          );

          const affectedFieldGroups: string[] = [];

          // Fetch all field groups related to the custom field

          const fieldGroups = await this.prisma.fieldGroup.findMany({
            where: {
              id: {
                in: fieldGroupIdsFromCustomField,
              },
            },
            include: {
              assets: {
                select: {
                  customFields: true,
                },
              },
              accessories: {
                select: {
                  customFields: true,
                },
              },
              consumables: {
                select: {
                  customFields: true,
                },
              },
              appliances: {
                select: {
                  customFields: true,
                },
              },
              licenses: {
                select: {
                  customFields: true,
                },
              },
              policies: {
                select: {
                  customFields: true,
                },
              },
            },
          });

          /**
           * Iterates through all field groups and their associated entities (assets, accessories, consumables, appliances, policies, and licenses) to find if a custom field with a given customFieldId exists.
           * If the custom field is found, the function adds the field group's id to the affectedFieldGroups array.
           */
          for (const fieldGroup of fieldGroups) {
            const entities = ENTITIES.flatMap(
              (entityType) => fieldGroup[entityType],
            );

            for (const entity of entities) {
              const customFieldsData = entity.customFields;
              if (customFieldsData['data']?.[customFieldId]) {
                affectedFieldGroups.push(fieldGroup.id);
                break;
              }
            }
          }

          // Check if any affected field groups are included in the provided field group IDs
          if (
            affectedFieldGroups.some((group) => !fieldGroupIds.includes(group))
          ) {
            this.logger.log(
              `Field group in use, editing of custom fields not permitted`,
            );
            throw new ForbiddenException(FIELD_GROUP_IN_USE_ERROR);
          }

          // Updating the custom field data in the database
          const updatedCustomField = await prisma.customFields.update({
            where: { id: customFieldId },
            data: {
              // Disconnecting the existing field group relations and connecting the new ones
              FieldGroups: {
                disconnect: customFieldData.FieldGroups.map((group) => ({
                  id: group.id,
                })),
                connect: fieldGroupIds.map((fieldGroupId) => ({
                  id: fieldGroupId,
                })),
              },
              ...updateCustomFieldData,
            },
            select: this.selectArgs,
          });

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CUSTOM_FIELD,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: customFieldId,
              log: {
                userId: user.id,
                name: user.name,
                customFieldId: customFieldId,
                updatedFields: getUpdatedFields(
                  customFieldData,
                  updatedCustomField,
                ),
              },
            },
          });
          this.logger.log(
            `Created history for newly updated custom field with ID: "${customFieldId}"`,
          );
          this.logger.log(
            `Custom field with ID: "${customFieldId} updated successfully"`,
          );
          return updatedCustomField;
        },
      );
      return customFieldTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to update category: ${error}`);
      throw error;
    }
  }

  /**
   * This function is responsible for deleting a custom field by id.
   * @param customFieldId  The custom field ID for which details is to be deleted.
   * @throws {NotFoundException} Throws NotFoundException if the custom field with the specified ID is not found.
   * @returns Response message including status code, success status and a message.
   */
  async deleteCustomField(customFieldId: string, user: User) {
    try {
      const customFieldTransaction: boolean = await this.prisma.$transaction(
        async (prisma) => {
          const customField = await prisma.customFields.findFirst({
            where: {
              id: customFieldId,
              isDeleted: false,
            },
          });

          if (!customField) {
            this.logger.log(
              `Custom field with id: "${customFieldId}" not found / already deleted`,
            );
            throw new NotFoundException(CUSTOM_FIELD_NOT_FOUND);
          }

          // Checking if there are any field groups using this custom field
          const fieldGroupsUsingCustomField = await prisma.fieldGroup.findMany({
            where: {
              customFields: {
                some: {
                  id: customField.id,
                },
              },
            },
            select: {
              id: true,
            },
          });

          // If there are field groups using this custom field, throw an error
          if (fieldGroupsUsingCustomField.length > 0) {
            this.logger.log(
              `Custom field with id: "${customFieldId}" cannot be deleted`,
            );
            throw new ForbiddenException(CUSTOM_FIELD_DELETION_NOT_PERMITTED);
          }

          // Disconnect the custom field from all associated field groups
          await prisma.customFields.update({
            where: {
              id: customFieldId,
            },
            data: {
              FieldGroups: {
                disconnect: fieldGroupsUsingCustomField.map((fieldGroup) => ({
                  id: fieldGroup.id,
                })),
              },
              isDeleted: true,
            },
          });
          this.logger.log(
            'Successfully disconnected the custom field from all associated field groups',
          );

          // Update the custom field to mark it as deleted
          await prisma.customFields.update({
            where: {
              id: customFieldId,
            },
            data: {
              isDeleted: true,
            },
          });

          this.logger.log(
            `Successfully deleted custom field with id:${customFieldId}`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CUSTOM_FIELD,
              action: HistoryActions.DELETED,
              date: new Date(),
              entityId: customFieldId,
              log: {
                userId: user.id,
                userName: user.name,
                customFieldId: customFieldId,
              },
            },
          });
          this.logger.log(
            `Created history for newly deleted custom field with ID: "${customFieldId}"`,
          );

          return true;
        },
      );

      return customFieldTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to delete custom field: ${error}`);
      throw error;
    }
  }
}
