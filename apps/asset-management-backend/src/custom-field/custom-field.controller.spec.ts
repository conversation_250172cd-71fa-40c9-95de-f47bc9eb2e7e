import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldController } from './custom-field.controller';
import { CustomFieldService } from './custom-field.service';
import { User } from 'types';
import { AppModule } from 'src/app.module';
import {
  CreateCustomFieldDto,
  GetCustomFieldResponseDto,
} from './dto/custom-field.dto';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { Request } from 'express';
import { InputType } from '@prisma-clients/asset-management-backend';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';

describe('CustomFieldController', () => {
  let controller: CustomFieldController;
  let service: CustomFieldService;

  const user: User = {
    id: 'user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        customField: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<CustomFieldController>(CustomFieldController);
    service = module.get<CustomFieldService>(CustomFieldService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const validCustomFieldId = '073c2022-a405-456a-9d33-844e2c39ac58';
  const invalidCustomFieldId = 'nonExistentId';
  const customFieldData: GetCustomFieldResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    fieldName: 'Processor',
    fieldType: 'TEXT',
    placeholderText: 'Enter processor specs',
  };

  describe('createCustomField', () => {
    it('should create custom field successfully', async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        fieldName: 'Processor',
        fieldType: 'TEXT',
        placeholderText: 'Enter processor specs',
      };

      const expectedResponse: HTTPResponseDto<GetCustomFieldResponseDto> = {
        statusCode: HttpStatus.CREATED,
        data: customFieldData,
        message: 'Custom field created successfully',
      };

      jest
        .spyOn(service, 'createCustomField')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.createCustomField(
        createCustomFieldDto,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle conflict when custom field already exists', async () => {
      const createCustomFieldDto: CreateCustomFieldDto = {
        fieldName: 'Processor',
        fieldType: 'TEXT',
        placeholderText: 'Enter processor specs',
      };

      jest
        .spyOn(service, 'createCustomField')
        .mockRejectedValue(
          new ConflictException('Custom field already exists'),
        );

      await expect(
        controller.createCustomField(
          createCustomFieldDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(ConflictException);
    });
  });

  describe('getAllCustomFields', () => {
    const filters: GetAllQueryParamsDto = {
      searchInput: '',
      page: 1,
      limit: 1,
      sortBy: SORT_BY.CREATED_AT,
      sortOrder: SORT_ORDER.ASC,
    };
    it('it should retrieve all custom fields successfully', async () => {
      const expectedResponse: GetAllResponseDto<GetCustomFieldResponseDto[]> = {
        statusCode: HttpStatus.OK,
        data: [customFieldData],
        count: 1,
        message: 'Custom fields retrieved successfully',
      };

      jest
        .spyOn(service, 'getAllCustomFields')
        .mockResolvedValue(expectedResponse);

      const result = await controller.getAllCustomFields(filters);

      expect(result).toEqual(expectedResponse);
    });

    it('it should handle internal server error', async () => {
      const errorResponse: InternalServerErrorException =
        new InternalServerErrorException('Internal Server Error');

      jest
        .spyOn(service, 'getAllCustomFields')
        .mockRejectedValue(errorResponse);
      await expect(controller.getAllCustomFields(filters)).rejects.toThrowError(
        errorResponse,
      );
    });
  });

  describe('getCustomFieldById', () => {
    it('it should retrieve custom field by ID successfully', async () => {
      const expectedResponse: HTTPResponseDto<GetCustomFieldResponseDto> = {
        statusCode: HttpStatus.OK,
        data: customFieldData,
        message: 'Custom field retrieved successfully',
      };

      jest
        .spyOn(service, 'getCustomFieldById')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.getCustomFieldById(validCustomFieldId);

      expect(result).toEqual(expectedResponse);
    });

    it('it should handle not found error when custom field is not found', async () => {
      jest
        .spyOn(service, 'getCustomFieldById')
        .mockRejectedValue(
          new NotFoundException(
            `Custom field with id: ${invalidCustomFieldId} not found / already deleted`,
          ),
        );
      await expect(
        controller.getCustomFieldById(invalidCustomFieldId),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors', async () => {
      const id = invalidCustomFieldId;

      jest
        .spyOn(service, 'getCustomFieldById')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(controller.getCustomFieldById(id)).rejects.toThrowError(
        BadRequestException,
      );
    });
  });

  describe('updateCustomField', () => {
    const updateCustomFieldDto: CreateCustomFieldDto = {
      fieldName: 'Processor',
      fieldType: InputType.NUMBER,
      placeholderText: 'Enter processor',
    };
    it('should update custom field by ID successfully', async () => {
      const expectedResponse = {
        statusCode: HttpStatus.OK,
        data: customFieldData,
        message: `Custom field updated successfully`,
      };

      jest
        .spyOn(service, 'updateCustomField')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.updateCustomField(
        invalidCustomFieldId,
        updateCustomFieldDto,
        user as unknown as Request,
      );
      expect(result).toEqual(expectedResponse);
    });

    it('it should handle not found error when requested ID is not found', async () => {
      jest
        .spyOn(service, 'updateCustomField')
        .mockRejectedValue(
          new NotFoundException(
            `Custom field with id: ${invalidCustomFieldId} not found / already deleted`,
          ),
        );

      await expect(
        controller.updateCustomField(
          invalidCustomFieldId,
          updateCustomFieldDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bas request with validation errors during update', async () => {
      jest
        .spyOn(service, 'updateCustomField')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.updateCustomField(
          invalidCustomFieldId,
          updateCustomFieldDto,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });

  describe('deleteCustomField', () => {
    it('should delete custom field successfully', async () => {
      const expectedResponse: HTTPResponseDto<boolean> = {
        statusCode: HttpStatus.OK,
        data: true,
        message: 'Custom field has been deleted successfully',
      };

      jest.spyOn(service, 'deleteCustomField').mockResolvedValue(true);

      const result = await controller.deleteCustomField(
        validCustomFieldId,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when deleting non-existent custom field', async () => {
      jest
        .spyOn(service, 'deleteCustomField')
        .mockRejectedValue(
          new NotFoundException(
            `Custom field with id: ${invalidCustomFieldId} not found / already deleted`,
          ),
        );

      await expect(
        controller.deleteCustomField(
          invalidCustomFieldId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors during delete', async () => {
      jest
        .spyOn(service, 'deleteCustomField')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.deleteCustomField(
          invalidCustomFieldId,
          user as unknown as Request,
        ),
      ).rejects.toThrowError(BadRequestException);
    });
  });
});
