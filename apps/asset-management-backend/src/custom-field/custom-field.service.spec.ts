import { Test, TestingModule } from '@nestjs/testing';
import { CustomFieldService } from './custom-field.service';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  CreateCustomFieldDto,
  GetCustomFieldResponseDto,
} from './dto/custom-field.dto';
import {
  CustomFields,
  InputType,
} from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { AppModule } from 'src/app.module';
import {
  CUSTOM_FIELD_EXISTS,
  CUSTOM_FIELD_NOT_FOUND,
} from 'src/constants/message-constants';
import { ConflictException, NotFoundException } from '@nestjs/common';

describe('CustomFieldService', () => {
  let service: CustomFieldService;
  let prismaService: PrismaService;

  const dto: CreateCustomFieldDto = {
    fieldName: 'RAM',
    fieldType: InputType.NUMBER,
    placeholderText: 'Enter ram',
  };
  const customField: GetCustomFieldResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    fieldName: 'RAM',
    fieldType: InputType.NUMBER,
    placeholderText: 'Enter ram',
  };

  const user: User = {
    id: '29c2c660-be0c-4104-80fc-92b9831eed9f',
    email: '<EMAIL>',
    name: 'John Doe',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        customField: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<CustomFieldService>(CustomFieldService);
    prismaService = service['prisma'];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe(' createCustomField ', () => {
    it(' should return all required properties of newly created custom field ', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(customField);

      const result = await service.createCustomField(dto, user);

      expect(result).toEqual(customField);
    });

    it('it should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(CUSTOM_FIELD_EXISTS));

      expect(
        async () => await service.createCustomField(dto, user),
      ).rejects.toThrowError(new ConflictException(CUSTOM_FIELD_EXISTS));
    });
  });

  describe('getCustomFieldById', () => {
    it(' should retrieve custom field requested ', async () => {
      const id = '073c2022-a405-456a-9d33-844e2c39ac58';
      const expectedResponse: GetCustomFieldResponseDto = {
        id,
        fieldName: 'RAM',
        fieldType: InputType.NUMBER,
        placeholderText: 'Enter ram',
      };
      jest
        .spyOn(prismaService.customFields, 'findFirst')
        .mockResolvedValue(expectedResponse as CustomFields);
      const result = await service.getCustomFieldById(id);

      expect(result).toEqual(expectedResponse);
    });
    it('should throw NotFoundException when custom field is not found', async () => {
      const nonExistentId = 'nonExistentId';

      jest
        .spyOn(prismaService.customFields, 'findFirst')
        .mockResolvedValue(null);

      await expect(
        async () => await service.getCustomFieldById(nonExistentId),
      ).rejects.toThrowError(new NotFoundException(CUSTOM_FIELD_NOT_FOUND));
    });
  });

  describe('updateCustomField', () => {
    const id = '0466888a-3296-4a6c-9620-b1c04ee5c2c9';
    const createCustomFieldDto: CreateCustomFieldDto = {
      fieldName: 'RAM',
      fieldType: InputType.NUMBER,
      placeholderText: 'Enter ram',
    };

    const updateCustomFieldDto: CreateCustomFieldDto = {
      fieldName: 'RAM',
      fieldType: InputType.NUMBER,
      placeholderText: 'Enter ram amount',
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update custom field by id successfully', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(updateCustomFieldDto);

      const result = await service.updateCustomField(
        id,
        createCustomFieldDto,
        user,
      );

      expect(result).toEqual(updateCustomFieldDto);
    });

    it('should throw NotFoundException when custom field is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(CUSTOM_FIELD_NOT_FOUND));

      await expect(
        service.updateCustomField(id, createCustomFieldDto, user),
      ).rejects.toThrowError(new NotFoundException(CUSTOM_FIELD_NOT_FOUND));
    });

    it('it should throw ConflictException on name conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(CUSTOM_FIELD_EXISTS));

      expect(
        async () =>
          await service.updateCustomField(id, updateCustomFieldDto, user),
      ).rejects.toThrowError(new ConflictException(CUSTOM_FIELD_EXISTS));
    });
  });

  describe('deleteCustomField', () => {
    const id = '073c2022-a405-456a-9d33-844e2c39ac58';

    it('should delete custom field successfully', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(true);

      const result = await service.deleteCustomField(id, user);

      expect(result).toBe(true);
    });
    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should throw NotFoundException when custom field is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(CUSTOM_FIELD_NOT_FOUND));

      await expect(service.deleteCustomField(id, user)).rejects.toThrowError(
        new NotFoundException(CUSTOM_FIELD_NOT_FOUND),
      );
    });
  });
});
