export const ManufacturerMessageConsts = {
  successCreateManufacturer: 'Successfully created manufacturer',
  successGetManufacturer: 'Successfully retrieved manufacturer',
  successUpdateManufacturer: 'Successfully updated manufacturer',
  successDeleteManufacturer: 'Successfully deleted manufacturer',
  successGetAllManufacturers: 'Successfully retrieved all manufacturers',
  notFoundManufacturer: 'No manufacturer found for the provided ID',
  failedCreateManufacturer: 'Unable to create the manufacturer',
  failedGetAllManufacturer: 'Unable to retrieve the list of manufacturers',
  failedUpdateManufacturer: 'Unable to update manufacturer information',
  failedDeleteManufacturer: 'Unable to delete manufacturer',
  failedDeleteManufacturerInEntites:
    'Manufacturer is used in other entities and cannot be deleted',
};
