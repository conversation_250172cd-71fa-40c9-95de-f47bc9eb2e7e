export const CONSUMABLE_OR_MODEL_NUMBER_EXIST =
  'Consumable or Model number already exists';

export const CATEGORY_EXIST = 'Category already exists';
export const SUPPLIER_NOT_FOUND = 'Supplier not found';
export const MANUFACTURER_NOT_FOUND = 'Manufacturer not found';
export const CONSUMABLE_NOT_FOUND = 'Consumable not found';
export const INTERNAL_ERROR = 'Something went wrong';
export const CATEGORY_NOT_FOUND = 'Category not found';
export const ACCESSORY_EXIST = 'Accessory with same name already exists';
export const ACCESSORY_NOT_FOUND = 'Accessory not found';
export const ASSET_OR_MODEL_NUMBER_EXIST =
  'Asset or Model number already exists';
export const ASSET_OR_SERIAL_NUMBER_EXIST =
  'Asset or Serial number already exists';
export const ASSET_MODEL_NAME_EXISTS = 'Asset model name already exits';
export const ASSET_MODEL_NOT_FOUND = 'Asset model not found';
export const ASSET_STATUS_NOT_FOUND = 'Asset status not found';
export const ASSET_MODELS_NOT_FOUND = 'Asset model(s) not found';
export const ASSET_EXIST = 'Serial number or Asset tag already exists';
export const ASSET_NOT_FOUND = 'Asset not found';
export const LICENSE_NOT_FOUND = 'Software License not found';
export const LICENSE_EXISTS =
  'Duplicate software license found with the same name or product key';
export const APPLIANCE_OR_MODEL_NUMBER_EXIST =
  'Appliance or Model number already exists';
export const MODEL_NUMBER_EXIST = 'Model number already exists';
export const ASSIGNMENT_NOT_FOUND = 'Assignment not found';
export const TOTAL_QUANTITY_DOES_NOT_EXIST = 'Total Quantity does not exist';
export const ORDER_NUMBER_ALREADY_EXISTS = 'Order number already exists';
export const INVALID_UUID_FORMAT = 'Invalid UUID format';
export const USER_NOT_FOUND = 'User not found for the given id';
export const INVALID_ENTITY_TYPE = 'Invalid entity type';
export const AUDIT_EXIST = 'Audit already exists';
export const AUDIT_NOT_FOUND = 'Audit not found';
export const AUDIT_DATE_EXISTS = 'Audit date already exists';
export const DOCUMENT_NOT_FOUND = 'Document not found';
export const SERVICE_DATE_EXIST =
  'Another service already exists with the same date';
export const NEXT_SERVICE_DATE_EXIST =
  'Another service already exists with the same next service date';
export const INSURANCE_NOT_FOUND = 'Insurance not found';
export const INSURANCE_FOUND = 'Insurance found';
export const CUSTOM_FIELD_EXISTS = 'Custom field already exists';
export const CUSTOM_FIELD_NOT_FOUND = 'Custom field not found';
export const ROLE_EXIST = 'Role already exist';
export const ROLE_NAME_EXIST = 'Role name already exist';
export const ROLE_PERMISSIONS_EXIST =
  'Role with the same permissions already exists';
export const ROLE_NOT_FOUND = 'Role not found';
export const PERMISSION_DENIED_MESSAGE =
  'Access Denied: You do not have the necessary permissions to perform this action';
export const FIELDGROUP_NOT_FOUND = 'Field group not found';
export const FIELDGROUP_EXISTS = 'Field group already exists';
export const CONTRACT_FOUND = 'Contract found';
export const CONTRACT_NOT_FOUND = 'Contract not found';
export const CONTRACT_EXISTS = 'Contract already exists with the given name';
export const CUSTOM_FIELD_IN_USE_ERROR =
  'Custom field in use, editing not permitted';
export const FIELD_GROUP_IN_USE_ERROR =
  'Field group in use, editing of custom fields not permitted';
export const POLICY_NOT_FOUND = 'Policy not found';
export const POLICY_NAME_EXIST = 'Policy name already exist';
export const REMINDER_NOT_FOUND = 'Reminder not found';
export const REMINDER_ALREADY_EXIST = 'Reminder already exist';
export const ENTITY_NOT_FOUND = 'Entity not found';
export const INVALID_REMINDER_DATE =
  'Reminder must be set to today or a future date';
export const FIELD_GROUP_DELETION_NOT_PERMITTED =
  'Field group is in use, so can not delete it';
export const CUSTOM_FIELD_DELETION_NOT_PERMITTED =
  'Field is in use, so can not delete it';
export const USER_NOT_SUBSCRIBED_TO_REMINDER =
  'User is not subscribed to this reminder';
export const GLOBAL_NOTIFICATION_NOT_FOUND = 'Notification not found';
export const GLOBAL_NOTIFICATION_ALREADY_EXIST =
  'Notification with the given name already exists';
export const LOCATION_ALREADY_EXIST =
  'Location with the given name already exists';
export const LOCATION_NOT_FOUND = 'Location not found';
export const INVALID_QUANTITY = 'Quantity must be grater than 0';
export const DEPARTMENT_NOT_FOUND = 'Department not found';
export const DEPARTMENT_ALREADY_EXIST = 'Department already exist';
