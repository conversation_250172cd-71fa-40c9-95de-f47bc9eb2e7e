import { Test, TestingModule } from '@nestjs/testing';
import { ConsumablesController } from './consumables.controller';
import { ConsumablesService } from './consumables.service';
import {
  CreateConsumableDto,
  GetConsumableResponseDto,
} from './dto/consumable.dto';
import { isUUID } from 'class-validator';
import { ConflictException, NotFoundException } from '@nestjs/common';
import {
  CATEGORY_NOT_FOUND,
  CONSUMABLE_OR_MODEL_NUMBER_EXIST,
  CONSUMABLE_NOT_FOUND,
  MANUFACTURER_NOT_FOUND,
  SUPPLIER_NOT_FOUND,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { Currency } from '@prisma-clients/asset-management-backend';
import { AppModule } from 'src/app.module';

describe('ConsumablesController', () => {
  let controller: ConsumablesController;
  let service: ConsumablesService;

  const dto: CreateConsumableDto = {
    name: 'consumable-name',
    categoryId: '9d625315-81be-4e76-a857-58d57f4c94f4',
    manufacturerId: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    modelNumber: 'model-number',
    minQuantity: 2,
    note: 'note',
    consumableImageUrl: '',
    purchaseInfo: {
      orderNumber: 'order-number',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      supplierId: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      purchasedById: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    },
  };

  const consumable: GetConsumableResponseDto = {
    id: 'c23fde36-3ddb-47a7-a3f9-c1821bb8ca50',
    name: 'consumable-name',
    category: {
      id: '9d625315-81be-4e76-a857-58d57f4c94f4',
      name: 'category-name',
    },
    manufacturer: {
      id: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
      name: 'manufacturer-name',
    },
    location: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    modelNumber: 'model-number',
    totalQuantity: 10,
    availableQuantity: 5,
    minQuantity: 2,
    note: 'note',
    consumableImageUrl: '',
  };

  const request = {
    user: { id: '5d7f3bda-f529-4387-9434-cf6ab340979e', name: 'John' },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<ConsumablesController>(ConsumablesController);
    service = module.get<ConsumablesService>(ConsumablesService);

    expect(isUUID(consumable.id)).toBe(true);
    expect(isUUID(dto.manufacturerId)).toBe(true);
    expect(isUUID(dto.categoryId)).toBe(true);
    expect(isUUID(dto.purchaseInfo.supplierId)).toBe(true);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create consumables api', () => {
    it('should return a status of 201 for sucessfully creating consumable', async () => {
      jest.spyOn(service, 'createConsumable').mockResolvedValue(consumable);

      const result = await controller.createConsumable(dto, request);

      expect(result.statusCode).toBe(201);
      expect(result.data).toEqual(consumable);
      expect(result.message).toBe('Consumable created successfully');
    });

    it('should throw conflict exception when consumable already exists', async () => {
      jest
        .spyOn(service, 'createConsumable')
        .mockRejectedValue(
          new ConflictException(CONSUMABLE_OR_MODEL_NUMBER_EXIST),
        );

      try {
        await controller.createConsumable(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        expect(error.message).toBe(CONSUMABLE_OR_MODEL_NUMBER_EXIST);
        expect(error.status).toBe(409);
      }
    });

    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'createConsumable')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));

      try {
        await controller.createConsumable(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when manufacturer not found', async () => {
      jest
        .spyOn(service, 'createConsumable')
        .mockRejectedValue(new NotFoundException(MANUFACTURER_NOT_FOUND));

      try {
        await controller.createConsumable(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(MANUFACTURER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when category not found', async () => {
      jest
        .spyOn(service, 'createConsumable')
        .mockRejectedValue(new NotFoundException(CATEGORY_NOT_FOUND));

      try {
        await controller.createConsumable(dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(CATEGORY_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('get all consumables api', () => {
    it('should return a status code of 200 for fetching all consumables', async () => {
      jest.spyOn(service, 'getAllConsumables').mockResolvedValue({
        consumables: [consumable],
        count: 10,
      });

      const result = await controller.getAllConsumables();

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([consumable]);
      expect(result.count).toBe(10);
      expect(result.message).toBe('Successfully fetched consumables');
    });

    it('should return an empty array if no consumables are present in database', async () => {
      jest.spyOn(service, 'getAllConsumables').mockResolvedValue({
        consumables: [],
        count: 0,
      });

      const result = await controller.getAllConsumables();

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual([]);
      expect(result.count).toBe(0);
      expect(result.message).toBe('Successfully fetched consumables');
    });
  });

  describe('get consumable by id api', () => {
    it('should return the consumable with specified id and status code 200', async () => {
      jest.spyOn(service, 'getConsumableById').mockResolvedValue(consumable);

      const result = await controller.getConsumableById(consumable.id);

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(consumable);
      expect(result.message).toBe('Successfully fetched consumable');
    });

    it('should throw not found exception when consumable not found', async () => {
      jest
        .spyOn(service, 'getConsumableById')
        .mockRejectedValue(new NotFoundException(CONSUMABLE_NOT_FOUND));

      try {
        await controller.getConsumableById(consumable.id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(CONSUMABLE_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('update consumable api', () => {
    it('should return the updated consumable object with status code of 200', async () => {
      jest.spyOn(service, 'updateConsumable').mockResolvedValue(consumable);

      const result = await controller.updateConsumableDetails(
        consumable.id,
        dto,
        request,
      );

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(consumable);
      expect(result.message).toBe('Successfully updated consumable');
    });

    it('should throw not found exception when consumable not found', async () => {
      jest
        .spyOn(service, 'updateConsumable')
        .mockRejectedValue(new NotFoundException(CONSUMABLE_NOT_FOUND));

      try {
        await controller.updateConsumableDetails(consumable.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(CONSUMABLE_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when supplier not found', async () => {
      jest
        .spyOn(service, 'updateConsumable')
        .mockRejectedValue(new NotFoundException(SUPPLIER_NOT_FOUND));

      try {
        await controller.updateConsumableDetails(consumable.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(SUPPLIER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when Manufacturer not found', async () => {
      jest
        .spyOn(service, 'updateConsumable')
        .mockRejectedValue(new NotFoundException(MANUFACTURER_NOT_FOUND));

      try {
        await controller.updateConsumableDetails(consumable.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(MANUFACTURER_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });

    it('should throw not found exception when category not found', async () => {
      jest
        .spyOn(service, 'updateConsumable')
        .mockRejectedValue(new NotFoundException(CATEGORY_NOT_FOUND));

      try {
        await controller.updateConsumableDetails(consumable.id, dto, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(CATEGORY_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });

  describe('delete consumable api (soft delete)', () => {
    it('should soft delete the consumable with a status code of 200', async () => {
      jest.spyOn(service, 'deleteConsumable').mockResolvedValue(true);

      const result = await controller.deleteConsumable(consumable.id, request);

      expect(result.statusCode).toBe(200);
      expect(result.data).toBe(true);
      expect(result.message).toBe('Consumable deleted successfully');
    });

    it('should throw not found exception when consumable is not found', async () => {
      jest
        .spyOn(service, 'deleteConsumable')
        .mockRejectedValue(new NotFoundException(CONSUMABLE_NOT_FOUND));

      try {
        await controller.deleteConsumable(consumable.id, request);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toBe(CONSUMABLE_NOT_FOUND);
        expect(error.status).toBe(404);
      }
    });
  });
});
