import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, IsString, IsUUID, IsInt } from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { CreatePurchaseDto } from 'src/purchase/dto/purchase.dto';
import { JsonValue } from 'types';

export class UpdateConsumableDto {
  @ApiProperty({
    description: 'Consumable name',
    type: 'string',
    example: 'A4 paper',
    required: true,
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Manufacturer Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: true,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  manufacturerId: string;

  @ApiProperty({
    description: 'Category Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
    required: true,
  })
  @IsOptional()
  @IsString()
  @IsUUID()
  categoryId: string;

  @ApiProperty({
    description: 'Model number',
    type: 'string',
    example: 'AA123-21',
    required: true,
  })
  @IsOptional()
  @IsString()
  modelNumber: string;

  @ApiProperty({
    description: 'Location of consumable',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: true,
  })
  @IsOptional()
  @IsUUID()
  location: string;

  @ApiProperty({
    description: 'Minimum quantity to send notification',
    type: 'number',
    example: 2,
    required: true,
  })
  @IsOptional()
  @IsInt()
  minQuantity: number;

  @ApiProperty({
    description: 'URL for consumable image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
    required: false,
  })
  @IsOptional()
  consumableImageUrl: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'This papers for used only for internal purposes...',
    required: true,
  })
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreateConsumableDto extends UpdateConsumableDto {
  @ApiProperty({
    description: 'Purchase details for consumables',
    type: CreatePurchaseDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreatePurchaseDto)
  purchaseInfo: CreatePurchaseDto;
}

export class GetConsumableResponseDto {
  @ApiProperty({
    description: 'Consumable Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'Consumable name',
    type: 'string',
    example: 'A4 paper',
  })
  name: string;

  @ApiProperty({
    description: 'Manufacturer details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'manufacturer-name',
    },
  })
  manufacturer: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Category details',
    example: {
      id: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
      name: 'category-name',
    },
  })
  category: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Model number',
    type: 'string',
    example: 'AA123-21',
  })
  modelNumber: string;

  @ApiProperty({
    description: 'Location of consumable',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Bangalore',
    },
  })
  location: EntityDto;

  @ApiProperty({
    description: 'Total quantity of consumables',
    type: 'number',
    example: 100,
  })
  totalQuantity: number;

  @ApiProperty({
    description: 'Available quantity of consumables',
    type: 'number',
    example: 50,
  })
  availableQuantity: number;

  @ApiProperty({
    description: 'Minimum quantity below which notification is triggered',
    type: 'number',
    example: 10,
  })
  minQuantity: number;

  @ApiProperty({
    description: 'URL for consumable image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
  })
  consumableImageUrl: string;

  @ApiProperty({
    description: 'Additional notes if any',
    type: 'string',
    example: 'This papers for used only for internal purposes',
  })
  note: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class GetAllConsumablesResponsePayload {
  @ApiProperty({
    description: 'Consumables response data',
    isArray: true,
    type: GetConsumableResponseDto,
  })
  consumables: GetConsumableResponseDto[];

  @ApiProperty({
    description: 'Total count of consumables',
    type: 'number',
    example: 100,
  })
  count: number;
}

export class ConsumablesFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Order assets based on location',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  location?: string;

  @ApiProperty({
    description: 'Filter assets based on category',
    type: 'string',
    example: 'laptop',
    required: false,
  })
  @IsOptional()
  category?: string[];
}
