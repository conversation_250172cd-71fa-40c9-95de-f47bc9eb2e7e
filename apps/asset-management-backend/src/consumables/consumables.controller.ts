import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ConsumablesService } from './consumables.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ConsumablesFilterQueryParamsDto,
  CreateConsumableDto,
  GetConsumableResponseDto,
  UpdateConsumableDto,
} from './dto/consumable.dto';
import {
  CONSUMABLE_OR_MODEL_NUMBER_EXIST,
  CONSUMABLE_NOT_FOUND,
  INTERNAL_ERROR,
  INVALID_UUID_FORMAT,
} from '../constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { Request } from 'express';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';

@ApiTags('Consumables')
@Controller('consumables')
@UseGuards(PermissionGuard)
export class ConsumablesController {
  private logger = new Logger('ConsumablesController');
  constructor(private readonly consumablesService: ConsumablesService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created consumables',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetConsumableResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: CONSUMABLE_OR_MODEL_NUMBER_EXIST,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a new consumable if not exists',
    summary: 'Create a consumable',
  })
  @ApiExtraModels(HTTPResponseDto<GetConsumableResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CONSUMABLE))
  async createConsumable(
    @Body() dto: CreateConsumableDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetConsumableResponseDto>> {
    this.logger.log('API to create conusmbale with purchase details');

    const { user } = request;
    const consumable: GetConsumableResponseDto =
      await this.consumablesService.createConsumable(dto, user);

    return {
      statusCode: HttpStatus.CREATED,
      data: consumable,
      message: 'Consumable created successfully',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched consumable/s',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetConsumableResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all consumables with an optional quary parameter to search by name',
    summary: 'Fetches all consumables',
  })
  @ApiExtraModels(GetAllResponseDto<GetConsumableResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CONSUMABLE))
  async getAllConsumables(
    @Query() dto?: ConsumablesFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<GetConsumableResponseDto[]>> {
    this.logger.log('API to fetch all consumables');

    const { consumables, count } =
      await this.consumablesService.getAllConsumables(dto);

    return {
      statusCode: HttpStatus.OK,
      data: consumables,
      count: count,
      message: 'Successfully fetched consumables',
    };
  }

  @Get(':consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched consumable',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetConsumableResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Consumable with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CONSUMABLE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch consumable with specified Id',
    summary: 'Fetches consumable with given Id',
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<GetConsumableResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CONSUMABLE))
  async getConsumableById(
    @Param('consumableId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<GetConsumableResponseDto>> {
    this.logger.log('API to fetch consumable with specified Id');

    const consumable: GetConsumableResponseDto =
      await this.consumablesService.getConsumableById(id);

    return {
      statusCode: HttpStatus.OK,
      data: consumable,
      message: 'Successfully fetched consumable',
    };
  }

  @Post('assign')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully assigned consumables',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to assign a consumable to users',
    summary: 'Assign a consumable',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CONSUMABLE))
  async assignConsumable(
    @Body() dto: CreateAssignmentDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    this.logger.log('API to assign consumable to user');

    const { user } = request;
    const assignmentDetails = await this.consumablesService.assignConsumable(
      dto,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: assignmentDetails,
      message: 'Successfully assigned consumable',
    };
  }

  @Get('assignments/:consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched assignment details for consumable',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<AssignmentResponseDto[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to fetch assignment details',
  })
  @ApiOperation({
    description: 'This API allows to fetch assignment details for consumables',
    summary: 'Fetch assignment details of a consumable',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CONSUMABLE))
  async getAssignmentDetails(
    @Param('consumableId', new ParseUUIDPipe()) consumableId: string,
  ): Promise<HTTPResponseDto<AssignmentResponseDto[]>> {
    const assignmentDetails =
      await this.consumablesService.getAssignmentDetails(consumableId);

    return {
      statusCode: HttpStatus.OK,
      data: assignmentDetails,
      message: 'Successfully fetched assignment details for consumable',
    };
  }

  @Post('purchase/:consumableId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created purchase details for consumables',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to create purchase record',
  })
  @ApiOperation({
    description: 'This api allows to create purchase details for a consumable',
    summary: 'Create purchase details for a consumable',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.CONSUMABLE))
  async createPurchaseDetails(
    @Param('consumableId', new ParseUUIDPipe()) consumableId: string,
    @Body() dto: CreatePurchaseDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    this.logger.log('API to create purchase details for consumable');

    const { user } = request;
    const purchaseDetails = await this.consumablesService.createPurchaseDetails(
      dto,
      consumableId,
      user,
    );

    return {
      statusCode: HttpStatus.CREATED,
      data: purchaseDetails,
      message: 'Successfully created purchase details for consumable',
    };
  }

  @Get('purchases/:consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched purchase history for consumable',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<PurchaseResponseDto[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to fetch purchase history',
  })
  @ApiOperation({
    description: 'This API allows to fetch purchase history for consumables',
    summary: 'Fetch purchase history of a consumable',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CONSUMABLE))
  async getPurchaseHistory(
    @Param('consumableId', new ParseUUIDPipe()) consumableId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<PurchaseResponseDto[]>> {
    this.logger.log('API to get purchase history for consumable');

    const { purchaseDetails, count } =
      await this.consumablesService.getPurchaseHistory(
        consumableId,
        queryParams,
      );

    return {
      statusCode: HttpStatus.OK,
      data: purchaseDetails,
      count,
      message: 'Successfully fetched purchase history for consumable',
    };
  }

  @Put(':consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated consumable',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetConsumableResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Consumable with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CONSUMABLE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the consumable details',
    summary: 'Update consumable',
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<GetConsumableResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.CONSUMABLE))
  async updateConsumableDetails(
    @Param('consumableId', new ParseUUIDPipe()) id: string,
    @Body() dto: UpdateConsumableDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetConsumableResponseDto>> {
    this.logger.log('API to update consumable details');

    const { user } = request;
    const consumable: GetConsumableResponseDto =
      await this.consumablesService.updateConsumable(id, dto, user);

    return {
      statusCode: HttpStatus.OK,
      data: consumable,
      message: 'Successfully updated consumable',
    };
  }

  @Delete(':consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted consumables Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: CONSUMABLE_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad request response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the consumable',
    summary: 'Delete consumable',
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.CONSUMABLE))
  async deleteConsumable(
    @Param('consumableId', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to soft delete consumable');

    const { user } = request;
    const consumableDeletedStatus: boolean =
      await this.consumablesService.deleteConsumable(id, user);

    return {
      statusCode: HttpStatus.OK,
      data: consumableDeletedStatus,
      message: 'Consumable deleted successfully',
    };
  }

  @Get('history/:consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched consumable history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'consumableId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter consumable Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch consumable history',
    summary: 'Fetch consumable history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.CONSUMABLE))
  async getConsumableHistory(
    @Param('consumableId', new ParseUUIDPipe()) consumableId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const { history, count } =
      await this.consumablesService.getConsumableHistory(
        consumableId,
        queryParams,
      );

    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'Consumable deleted successfully',
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the consumables info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the consumables info',
  })
  async downloadConsumables(): Promise<HTTPResponseDto<string>> {
    const fileName = await this.consumablesService.downloadConsumables();
    return {
      data: fileName,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }

  @Put('purchase-update/:purchaseId/:consumableId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Purchase details updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid purchase ID',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_UUID_FORMAT,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to update purchase record',
  })
  @ApiOperation({
    description: 'This API updates the purchase record',
    summary: 'Update the purchase record',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.APPLIANCE))
  async updatePurchaseRecord(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
    @Body() dto: UpdatePurchaseDto,
    @Req() request: Request,
    @Param('consumableId', new ParseUUIDPipe()) consumableId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const updatedPurchase = await this.consumablesService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      consumableId,
    );
    return {
      statusCode: HttpStatus.OK,
      data: updatedPurchase,
      message: 'Purchase record updated successfully',
    };
  }

  @Delete('unassign/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully unassigned the consumable',
    schema: { $ref: getSchemaPath(HTTPResponseDto<boolean>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request Response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API unassigns a consumable',
    summary: 'Unassign consumable',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '7da45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the assignmentId to unassign a consumable',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.CONSUMABLE))
  async unassignConsumable(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() request: Request,
    @Body() dto: UnassignmentRequestDto,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const unassignedStatus = await this.consumablesService.unassignConsumable(
      dto,
      assignmentId,
      user,
    );

    return {
      statusCode: HttpStatus.OK,
      data: unassignedStatus,
      message: 'Successfully unassigned the consumable',
    };
  }
}
