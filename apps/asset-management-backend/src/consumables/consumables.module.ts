import { Module } from '@nestjs/common';
import { ConsumablesService } from './consumables.service';
import { ConsumablesController } from './consumables.controller';
import { PurchaseService } from 'src/purchase/purchase.service';
import { AssignmentService } from 'src/assignment/assignment.service';
import { DocumentService } from 'src/document/document.service';
import { AbilityModule } from 'src/ability/ability.module';
import { UnassignService } from 'src/unassign/unassign.service';

@Module({
  imports: [AbilityModule],
  controllers: [ConsumablesController],
  providers: [
    ConsumablesService,
    PurchaseService,
    AssignmentService,
    DocumentService,
    UnassignService,
  ],
})
export class ConsumablesModule {}
