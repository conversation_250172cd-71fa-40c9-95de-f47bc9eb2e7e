import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  ConsumablesFilterQueryParamsDto,
  CreateConsumableDto,
  GetAllConsumablesResponsePayload,
  GetConsumableResponseDto,
  UpdateConsumableDto,
} from './dto/consumable.dto';
import {
  CONSUMABLE_OR_MODEL_NUMBER_EXIST,
  CONSUMABLE_NOT_FOUND,
} from '../constants/message-constants';
import {
  ChangesOcccuredIn,
  Consumable,
  HistoryActions,
  Prisma,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  updateFieldGroups,
} from '../utility';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { User } from 'types';
import { PurchaseService } from 'src/purchase/purchase.service';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from 'src/assignment/dto/create-assignment.dto';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import { AssignmentService } from 'src/assignment/assignment.service';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';
import { DocumentService } from 'src/document/document.service';
import { UnassignmentRequestDto } from 'src/unassign/dtos/unassignment-dto';
import { UnassignService } from 'src/unassign/unassign.service';
@Injectable()
export class ConsumablesService {
  private logger = new Logger('ConsumableService');
  static selectArgs = {
    id: true,
    name: true,
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    modelNumber: true,
    totalQuantity: true,
    availableQuantity: true,
    minQuantity: true,
    note: true,
    customFields: true,
    consumableImageUrl: true,
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly assignmentService: AssignmentService,
    private readonly purchaseService: PurchaseService,
    private readonly awsService: AwsService,
    private readonly documentService: DocumentService,
    private readonly unassignService: UnassignService,
  ) {}

  /**
   * This method creates a consumable based on the provided DTO if not already exists.
   * Also creates a record in history table for the created consumable.
   *
   * @param {CreateConsumableDto} dto The DTO (Data Transfer Object) containing consumable details.
   * @returns {Promise<GetConsumableResponseDto>} A Promise that resolves to an ConsumableResponsePayload representing the created consumable.
   * @throws ConflictException if a consumable with the same name already exists.
   * @throws NotFoundException if the supplier, manufacturer, or category with the specified IDs are not found.
   */
  async createConsumable(
    dto: CreateConsumableDto,
    user: User,
  ): Promise<GetConsumableResponseDto> {
    try {
      if (dto.minQuantity > dto.purchaseInfo.quantity) {
        throw new BadRequestException(
          `Quantity must be greater than minimum quantity`,
        );
      }

      const consumableTransaction: GetConsumableResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const consumable: number = await prisma.consumable.count({
            where: {
              OR: [
                {
                  name: {
                    equals: dto.name,
                    mode: 'insensitive',
                  },
                },
                dto.modelNumber
                  ? {
                      modelNumber: {
                        equals: dto.modelNumber,
                      },
                    }
                  : undefined,
              ],
              isDeleted: false,
            },
          });

          if (consumable) {
            this.logger.log('Consumable or Model number already exist');
            throw new ConflictException(CONSUMABLE_OR_MODEL_NUMBER_EXIST);
          }

          const fieldGroups: string[] = dto.customFields
            ? dto.customFields['fieldgroups']
            : [];

          const createdConsumable: GetConsumableResponseDto =
            await prisma.consumable.create({
              data: {
                name: dto.name,
                manufacturer: dto.manufacturerId
                  ? {
                      connect: {
                        id: dto.manufacturerId,
                      },
                    }
                  : undefined,
                category: dto.categoryId
                  ? {
                      connect: {
                        id: dto.categoryId,
                      },
                    }
                  : undefined,
                modelNumber: dto.modelNumber,
                location: dto.location
                  ? {
                      connect: {
                        id: dto.location,
                      },
                    }
                  : undefined,
                totalQuantity: dto.purchaseInfo?.quantity,
                availableQuantity: dto.purchaseInfo?.quantity,
                minQuantity: dto.minQuantity,
                customFields: dto.customFields,
                consumableImageUrl: dto.consumableImageUrl,
                fieldGroups: {
                  connect: fieldGroups.map((fieldGroupId) => ({
                    id: fieldGroupId,
                  })),
                },
                note: dto.note,
              },
              select: ConsumablesService.selectArgs,
            });

          this.logger.log(
            `Consumable with id: ${createdConsumable.id} created successfully`,
          );

          await this.purchaseService.createPurchase(
            TypeOfCategory.CONSUMABLE,
            dto.purchaseInfo,
            createdConsumable.id,
            user,
          );

          this.logger.log(
            `Purchase created for consumable having id: ${createdConsumable.id}`,
          );

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CONSUMABLE,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdConsumable.id,
              log: {
                userId: user.id,
                name: user.name,
                consumableId: createdConsumable.id,
              },
            },
          });

          this.logger.log("History for 'create' created successfully");

          return createdConsumable;
        });

      return consumableTransaction;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (dto.consumableImageUrl) {
        this.awsService.deleteFile(dto.consumableImageUrl);
        this.logger.log(
          'Consumbale image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create consumable: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves a paginated list of consumables based on optional query parameters.
   * It applies pagination, sorting, and search functionality if provided in the query parameters.
   *
   * @param {GetAllConsumablesQueryParamsDto} [dto] (Optional) The DTO containing query parameters for retrieving consumables.
   * @returns {Promise<GetAllConsumablesResponseDto['data']>} A Promise that resolves to an GetAllConsumablesResponse['data'] containing a paginated list of consumables and the total count.
   */
  async getAllConsumables(
    dto?: ConsumablesFilterQueryParamsDto,
  ): Promise<GetAllConsumablesResponsePayload> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | null = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || 'createdAt']: dto?.sortOrder || 'desc',
    };
    const officeLocation = dto?.location;
    const whereOptions: Prisma.ConsumableWhereInput = dto?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: dto.searchInput,
                mode: 'insensitive',
              },
            },
            {
              modelNumber: {
                contains: dto.searchInput,
                mode: 'insensitive',
              },
            },
            {
              category: {
                name: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
            },
          ],
        }
      : undefined;

    const [consumables, count] = await this.prisma.$transaction([
      this.prisma.consumable.findMany({
        where: {
          ...whereOptions,
          ...(officeLocation
            ? {
                location: {
                  name: { equals: officeLocation, mode: 'insensitive' },
                },
              }
            : {}),
          isDeleted: false,
        },
        select: ConsumablesService.selectArgs,
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.consumable.count({
        where: {
          ...whereOptions,
          ...(officeLocation
            ? {
                location: {
                  name: { equals: officeLocation, mode: 'insensitive' },
                },
              }
            : {}),
          isDeleted: false,
        },
      }),
    ]);

    this.logger.log(`Fetched ${count} consumable/s successfully`);

    return {
      consumables,
      count,
    };
  }

  /**
   * This method retrives consumable with the specified ID that is not deleted.
   *
   * @param {ConsumableIdDto['id']} consumableId The ID of the consumable to retrieve.
   * @returns {Promise<GetConsumableResponseDto>} A Promise that resolves to an ConsumableResponsePayload representing the retrieved consumable.
   * @throws NotFoundException if the consumable with the specified ID is not found or has been deleted.
   */
  async getConsumableById(
    consumableId: string,
  ): Promise<GetConsumableResponseDto> {
    const consumable: GetConsumableResponseDto =
      await this.prisma.consumable.findFirst({
        where: { id: consumableId, isDeleted: false },
        select: ConsumablesService.selectArgs,
      });

    if (!consumable) {
      this.logger.log(`Consumable with id: ${consumableId} not found`);
      throw new NotFoundException(CONSUMABLE_NOT_FOUND);
    }

    return consumable;
  }

  /**
   * Assigns a consumable to the user id specified in the dto.
   *
   * @param {CreateAssignmentDto} dto - The data transfer object containing assignment details.
   * @param {User} user - The user who assigned the consumable (to record history).
   * @throws {BadRequestException} Throws an exception if the assignment type is not consumable.
   * @returns {Promise<AssignmentResponseDto>} A promise representing the result of the assignment creation.
   */
  async assignConsumable(
    dto: CreateAssignmentDto,
    user: User,
  ): Promise<AssignmentResponseDto> {
    const assignmentDetails = await this.assignmentService.assign(
      TypeOfCategory.CONSUMABLE,
      dto,
      user,
    );
    await this.awsService.assignmentNotifyEmail(assignmentDetails);
    this.logger.log('The mail sent successfully to mentioned notify users');
    return assignmentDetails;
  }

  async getAssignmentDetails(
    consumableId: string,
  ): Promise<AssignmentResponseDto[]> {
    return this.assignmentService.findAllByEntityId(consumableId);
  }

  /**
   * Creates purchase details for a consumable entity.
   *
   * @param {CreatePurchaseDto} dto - The data transfer object containing purchase details.
   * @param {string} entityId - The identifier of the entity for which the purchase is being created.
   * @param {User} user - The user who created the record for purchase (to record history).
   * @throws {BadRequestException} Throws an exception if the purchase type is not consumable.
   * @returns {Promise<PurchaseResponseDto>} A promise representing the result of the purchase created.
   */
  async createPurchaseDetails(
    dto: CreatePurchaseDto,
    entityId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.createPurchase(
      TypeOfCategory.CONSUMABLE,
      dto,
      entityId,
      user,
    );
  }

  /**
   * Retrieves the purchase history for the specified entity.
   *
   * @param {string} entityId - The identifier of the entity for which the purchase history is requested.
   * @returns {Promise<PurchaseResponseDto[]>} A promise representing an array of purchase history details for
   * the specified entity.
   */
  async getPurchaseHistory(
    entityId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllPurchasesResponse> {
    return this.purchaseService.getEntityPurchaseDetails(entityId, queryParams);
  }

  /**
   * This method verifies the existence of the consumable
   * by its ID and updates the consumable with the provided data.
   *
   * @param {ConsumableIdDto['id']} consumableId The ID of the consumable to update.
   * @param {CreateConsumableDto} dto The DTO containing updated consumable details.
   * @returns {Promise<GetConsumableResponseDto>} A Promise that resolves to an ConsumableResponsePayload representing the updated consumable.
   * @throws NotFoundException if the consumable with the specified ID is not found or has been deleted.
   * @throws NotFoundException if the supplier, manufacturer, or category with the specified IDs are not found.
   */
  async updateConsumable(
    consumableId: string,
    dto: UpdateConsumableDto,
    user: User,
  ): Promise<GetConsumableResponseDto> {
    let consumable: GetConsumableResponseDto;
    try {
      const consumableTransaction: GetConsumableResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          consumable = await prisma.consumable.findFirst({
            where: {
              id: consumableId,
              isDeleted: false,
            },
            select: ConsumablesService.selectArgs,
          });

          if (!consumable) {
            this.logger.log(`Consumable with id: ${consumableId} not found`);
            throw new NotFoundException(CONSUMABLE_NOT_FOUND);
          }

          if (dto.minQuantity > consumable.totalQuantity) {
            throw new BadRequestException(
              `Minimum quantity must be less than quantity`,
            );
          }
          /**
           * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
           **/
          await updateFieldGroups(
            prisma,
            'consumable',
            consumableId,
            consumable,
            dto,
          );

          const consumableCount: number = await prisma.consumable.count({
            where: {
              NOT: {
                name: {
                  equals: consumable.name,
                  mode: 'insensitive',
                },
              },
              OR: [
                {
                  name: {
                    equals: dto.name,
                    mode: 'insensitive',
                  },
                },
                dto.modelNumber
                  ? {
                      modelNumber: {
                        equals: dto.modelNumber,
                        mode: 'insensitive',
                      },
                    }
                  : undefined,
              ],
              isDeleted: false,
            },
          });

          if (consumableCount) {
            this.logger.log('Consumable or Model number already exist');
            throw new ConflictException(CONSUMABLE_OR_MODEL_NUMBER_EXIST);
          }

          const updatedConsumable: GetConsumableResponseDto =
            await prisma.consumable.update({
              where: {
                id: consumableId,
              },
              data: {
                name: dto.name,
                manufacturer: dto.manufacturerId
                  ? {
                      connect: {
                        id: dto.manufacturerId,
                      },
                    }
                  : undefined,
                category: dto.categoryId
                  ? {
                      connect: {
                        id: dto.categoryId,
                      },
                    }
                  : undefined,
                modelNumber: dto.modelNumber,
                location: dto.location
                  ? {
                      connect: {
                        id: dto.location,
                      },
                    }
                  : undefined,
                minQuantity: dto.minQuantity,
                customFields: dto.customFields,
                consumableImageUrl: dto.consumableImageUrl,
                note: dto.note,
              },
              select: ConsumablesService.selectArgs,
            });

          this.logger.log(
            `Consumable with id: ${consumable.id} updated successfully`,
          );

          // If update successfully, delete previous image attached with consumable
          if (
            updatedConsumable &&
            consumable.consumableImageUrl &&
            consumable.consumableImageUrl !== dto.consumableImageUrl
          ) {
            await this.awsService.deleteFile(consumable.consumableImageUrl);
            this.logger.log(
              'Consumbale image uploaded on s3 bucket deleted successfully',
            );
          }

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.CONSUMABLE,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedConsumable.id,
              log: {
                userId: user.id,
                name: user.name,
                consumableId: updatedConsumable.id,
                updatedFields: getUpdatedFields(consumable, updatedConsumable),
              },
            },
          });

          this.logger.log("History for 'update' created successfully");

          return updatedConsumable;
        });

      return consumableTransaction;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (
        dto.consumableImageUrl &&
        consumable.consumableImageUrl !== dto.consumableImageUrl
      ) {
        await this.awsService.deleteFile(dto.consumableImageUrl);
        this.logger.log(
          'Consumbale image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update consumable: ${error}`);
      throw error;
    }
  }

  /**
   * This method marks the consumable as deleted in the database by setting the 'isDeleted' flag to true,
   * if the consumable exists
   *
   * @param {ConsumableIdDto['id']} consumableId The ID of the consumable to delete.
   * @returns {Promise<boolean>} A Promise that resolves to an boolean value representing the status of delete operation.
   * @throws NotFoundException if the consumable with the specified ID is not found or has already been deleted.
   */
  async deleteConsumable(consumableId: string, user: User): Promise<boolean> {
    const consumableTransaction = await this.prisma.$transaction(
      async (prisma) => {
        const consumable = await prisma.consumable.findFirst({
          where: {
            id: consumableId,
            isDeleted: false,
          },
          select: ConsumablesService.selectArgs,
        });

        if (!consumable) {
          this.logger.log(`Consumable with specified id not found`);
          throw new NotFoundException(CONSUMABLE_NOT_FOUND);
        }

        const deletedConsumable: Consumable = await prisma.consumable.update({
          where: {
            id: consumableId,
          },
          data: {
            isDeleted: true,
            customFields: {},
          },
        });

        this.logger.log('Consumable isDeleted updated to true successfully');

        /**
         * Disconnects existing field groups associated with the specified accessory.
         **/
        await disconnectFieldGroups(
          prisma,
          'consumable',
          consumableId,
          consumable,
        );
        this.logger.log(`Consumable disconnected from all field groups`);

        // If deleted successfully, delete image attached with consumable.
        if (consumable.consumableImageUrl && deletedConsumable) {
          await this.awsService.deleteFile(consumable.consumableImageUrl);

          this.logger.log(
            'Consumbale image uploaded on s3 bucket deleted successfully',
          );
        }
        // Delete consuamble documents
        const documentId = await prisma.document.findMany({
          where: {
            entityId: consumableId,
          },
          select: {
            id: true,
          },
        });
        if (documentId) {
          documentId.map(async (document) => {
            await this.documentService.deleteDocument(document.id, user);
          });
          this.logger.log(
            `The documents related to consuamble ID:${consumableId} deleted successfully`,
          );
        }
        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.CONSUMABLE,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: deletedConsumable.id,
            log: {
              userId: user.id,
              name: user.name,
              consumableId: deletedConsumable.id,
            },
          },
        });

        this.logger.log("History for 'delete' created sucessfully");
        return true;
      },
    );
    return consumableTransaction;
  }

  /**
   * Retrieves the history of a consumable based on its ID and specified query parameters.
   *
   * @param {string} consumableId - The ID of the consumable for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If the consumable with the provided ID is not found.
   */
  async getConsumableHistory(
    consumableId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const consumable = await this.prisma.consumable.count({
      where: {
        id: consumableId,
        isDeleted: false,
      },
    });

    if (!consumable) {
      this.logger.log(`Consumable with id ${consumableId} not found`);
      throw new NotFoundException(CONSUMABLE_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    const history = await this.prisma.history.findMany({
      where: {
        entityId: consumableId,
      },
      select: {
        action: true,
        date: true,
        log: true,
        changeInTable: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: consumableId,
      },
    });

    this.logger.log(`Fetched history for consumable with id ${consumableId}`);

    return { history, count };
  }

  async downloadConsumables(): Promise<string> {
    const consumables = await this.prisma.consumable.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        name: true,
        category: {
          select: {
            name: true,
          },
        },
        manufacturer: {
          select: {
            name: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        modelNumber: true,
        totalQuantity: true,
        availableQuantity: true,
        minQuantity: true,
        note: true,
        customFields: true,
      },
    });
    const parsedDataPromises = consumables.map(
      async ({ category, manufacturer, location, customFields, ...rest }) => {
        const categoryName = category?.name;
        const manufacturerName = manufacturer?.name;
        const locationName = location?.name;

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          categoryName,
          manufacturerName,
          locationName,
          ...customFieldData,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/consumables',
    );
    return fileName;
  }

  /**
   * Updates a purchase record.
   * @param {UpdatePurchaseDto} dto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    dto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    consumableId: string,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      consumableId,
    );
  }

  async unassignConsumable(
    dto: UnassignmentRequestDto,
    assignmentId: string,
    user: User,
  ): Promise<boolean> {
    const assignmentInfo =
      await this.assignmentService.findAssignmentById(assignmentId);
    const unassignedDetails = {
      unAssignedNote: dto.note,
      notifyUser: dto.notifyUser,
      assignedUser: assignmentInfo.user.name,
      assignedNote: assignmentInfo.note,
      assignedDate: assignmentInfo.date,
      typeOfCategory: TypeOfCategory.CONSUMABLE,
      entityId: assignmentInfo.entityId,
      user: user,
      assignedUserEmail: assignmentInfo.user.email,
    };
    await this.unassignService.createUnassignmentRecord(
      unassignedDetails,
      user,
    );
    return this.assignmentService.unAssign(assignmentId, user);
  }
}
