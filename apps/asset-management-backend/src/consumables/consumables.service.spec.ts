import { Test, TestingModule } from '@nestjs/testing';
import { ConsumablesService } from './consumables.service';
import {
  GetConsumableResponseDto,
  CreateConsumableDto,
} from './dto/consumable.dto';
import {
  Category,
  Consumable,
  Currency,
  EvaluationFrequency,
  Manufacturer,
  Supplier,
  TransactionFrequency,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { ConflictException, NotFoundException } from '@nestjs/common';
import {
  CATEGORY_NOT_FOUND,
  CONSUMABLE_OR_MODEL_NUMBER_EXIST,
  CONSUMABLE_NOT_FOUND,
  MANUFACTURER_NOT_FOUND,
  SUPPLIER_NOT_FOUND,
} from '../constants/message-constants';
import { isUUID } from 'class-validator';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { Request } from 'express';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('ConsumablesService', () => {
  let service: ConsumablesService;
  let prismaService: PrismaService;

  const supplier: Supplier = {
    id: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
    name: 'example-supplier',
    address: 'example',
    zipCode: '123456',
    contactName: null,
    contactEmail: null,
    contactPhoneNumber: null,
    note: null,
    supplierImageUrl: null,
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const manufacturer: Manufacturer = {
    id: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
    name: 'example',
    contactName: null,
    contactEmail: null,
    contactPhoneNumber: null,
    manufacturerImageUrl: null,
    note: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const category: Category = {
    id: '9d625315-81be-4e76-a857-58d57f4c94f4',
    name: 'example',
    note: null,
    typeOfCategory: TypeOfCategory.CONSUMABLE,
    categoryImageUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const dto: CreateConsumableDto = {
    name: 'consumable-name',
    categoryId: category.id,
    manufacturerId: manufacturer.id,
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    modelNumber: 'model-number',
    minQuantity: 2,
    note: 'note',
    consumableImageUrl: null,
    purchaseInfo: {
      orderNumber: 'order-number',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      supplierId: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      purchasedById: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    },
  };

  const consumable: GetConsumableResponseDto = {
    id: 'c23fde36-3ddb-47a7-a3f9-c1821bb8ca50',
    name: 'consumable-name',
    category: {
      id: category.id,
      name: category.name,
    },
    manufacturer: {
      id: manufacturer.id,
      name: manufacturer.name,
    },
    location: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    modelNumber: 'model-number',
    totalQuantity: 10,
    availableQuantity: 5,
    minQuantity: 2,
    note: 'note',
    consumableImageUrl: null,
  };

  const request = {
    user: { id: '5d7f3bda-f529-4387-9434-cf6ab340979e', name: 'John' },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<ConsumablesService>(ConsumablesService);
    prismaService = service['prisma'];

    expect(isUUID(consumable.id)).toBe(true);
    expect(isUUID(supplier.id)).toBe(true);
    expect(isUUID(manufacturer.id)).toBe(true);
    expect(isUUID(category.id)).toBe(true);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create consumable service', () => {
    it('should create a consumable when not exists', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(consumable);

      const result = await service.createConsumable(dto, request.user);

      expect(result).toEqual(consumable);
    });

    it('should throw conflict exception when consumable already exists', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new ConflictException(CONSUMABLE_OR_MODEL_NUMBER_EXIST),
        );

      try {
        await service.createConsumable(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        expect(error.message).toEqual(CONSUMABLE_OR_MODEL_NUMBER_EXIST);
      }
    });

    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createConsumable(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when manufacturer does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(MANUFACTURER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createConsumable(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(MANUFACTURER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when category does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(CATEGORY_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createConsumable(dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(CATEGORY_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });

  describe('get all consumables service', () => {
    it('should return all the consumables in an array when no search input provided', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue([[consumable], 1]);

      const result = await service.getAllConsumables();

      expect(result).toEqual({
        consumables: [consumable],
        count: 1,
      });
    });

    it('should return an empty array when no consumables are present', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue([[], 0]);

      const result = await service.getAllConsumables();

      expect(result).toEqual({
        consumables: [],
        count: 0,
      });
    });
  });

  describe('get consumable by id service', () => {
    it('should return the consumable with the specified id', async () => {
      jest
        .spyOn(prismaService.consumable, 'findFirst')
        .mockResolvedValue(consumable as unknown as Consumable);

      const result = await service.getConsumableById(consumable.id);

      expect(result).toEqual(consumable);
    });

    it('should throw a not found exception when consumable with specified id not found', async () => {
      jest.spyOn(prismaService.consumable, 'findFirst').mockResolvedValue(null);

      try {
        await service.getConsumableById(consumable.id);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual(CONSUMABLE_NOT_FOUND);
      }
    });
  });

  describe('update consumable service', () => {
    it('should update the consumable with specified id with given data', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(consumable);

      const result = await service.updateConsumable(
        consumable.id,
        dto,
        request.user,
      );

      expect(result).toEqual(consumable);
    });

    it('should throw not found exception when the consumable is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new NotFoundException(CONSUMABLE_NOT_FOUND));

      try {
        await service.updateConsumable(consumable.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(NotFoundException);
        expect(error.message).toEqual(CONSUMABLE_NOT_FOUND);
      }
    });

    it('should throw not found exception when supplier does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(SUPPLIER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateConsumable(consumable.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(SUPPLIER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when manufacturer does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(MANUFACTURER_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateConsumable(consumable.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(MANUFACTURER_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });

    it('should throw not found exception when category does not exist', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError(CATEGORY_NOT_FOUND, {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.updateConsumable(consumable.id, dto, request.user);
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.message).toEqual(CATEGORY_NOT_FOUND);
        expect(error.code).toEqual('P2025');
      }
    });
  });
});
