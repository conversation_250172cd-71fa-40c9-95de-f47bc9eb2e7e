import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { syncEntities } from './scripts/sync-entities';

@Injectable()
export class AppService implements OnModuleInit {
  private logger = new Logger('AppService');

  async onModuleInit() {
    if (process.env.NODE_ENV === 'production') {
      try {
        await syncEntities();
      } catch (error) {
        this.logger.error(error);
      }
    } else {
      this.logger.log('Skipping syncEntities in development mode');
      this.logger.log('Sync manually if needed');
    }
  }
}
