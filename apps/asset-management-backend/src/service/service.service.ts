import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  GetAllServicesResponseDto,
  GetServiceResponseDto,
  ServiceRequestDto,
} from './dto/service-dto';
import { User } from 'types';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  ServiceType,
} from '@prisma-clients/asset-management-backend';
import { SERVICE_NOT_FOUND } from 'src/constants';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getUpdatedFields,
  setDateWithZeroTime,
  updateFieldGroups,
} from 'src/utility';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import {
  ENTITY_NOT_FOUND,
  SERVICE_DATE_EXIST,
} from 'src/constants/message-constants';

@Injectable()
export class ResourceService {
  constructor(private readonly prisma: PrismaService) {}
  private logger = new Logger('ResourceService');
  private selectArgs = {
    id: true,
    name: true,
    note: true,
    assets: {
      select: {
        id: true,
        assetName: true,
      },
    },
    appliances: {
      select: {
        id: true,
        name: true,
      },
    },
    supplier: {
      select: {
        id: true,
        name: true,
      },
    },
    contactName: true,
    contactNumber: true,
    nextServiceDate: true,
    date: true,
    cost: true,
    rating: true,
    serviceImageUrl: true,
    type: true,
    customFields: true,
  };

  /**
   * Creates a new service record.
   *
   * @param {ServiceRequestDto} dto - The data transfer object containing service details.
   * @param {User} user - The user who is creating the service.
   * @returns {Promise<GetServiceResponseDto>} - The created service record.
   * @throws {ConflictException} - If a service record with the same date or next service date already exists.
   */
  async createService(
    dto: ServiceRequestDto,
    user: User,
  ): Promise<GetServiceResponseDto> {
    if (dto.applianceIds?.length > 0 || dto.assetIds?.length > 0) {
      const entityIds = dto.assetIds ? dto.assetIds : dto.applianceIds;
      await Promise.all(
        entityIds.map(async (entity) => {
          const data = await this.prisma[
            dto.type.toLocaleLowerCase()
          ].findFirst({
            where: {
              id: entity,
              isDeleted: false,
            },
          });
          if (!data) {
            this.logger.error(
              'If any of the entities provided are invalid, please double-check',
            );
            throw new NotFoundException('Please provide valid entity ids');
          }
        }),
      );
      const sameDateServices = await this.prisma.service.findFirst({
        where: {
          date: dto.date,
          type: dto.type,
          assets: {
            every: {
              id: {
                in: dto.assetIds,
              },
            },
          },
          appliances: {
            every: {
              id: {
                in: dto.applianceIds,
              },
            },
          },
        },
      });
      if (sameDateServices) {
        this.logger.error(
          'The service record with provided date already exists',
        );
        throw new ConflictException(SERVICE_DATE_EXIST);
      }

      const fieldGroups: string[] = dto.customFields
        ? dto.customFields['fieldgroups']
        : [];
      try {
        const createdService = await this.prisma.service.create({
          data: {
            name: dto.name,
            type: dto.type,
            contactName: dto.contactName,
            contactNumber: dto.contactNumber,
            cost: dto.cost,
            date: setDateWithZeroTime(dto.date),
            nextServiceDate: dto.nextServiceDate
              ? setDateWithZeroTime(dto.nextServiceDate)
              : undefined,
            note: dto.note,
            serviceImageUrl: dto.serviceImageUrl,
            rating: dto.rating,
            supplier: dto.supplierId
              ? {
                  connect: {
                    id: dto.supplierId,
                  },
                }
              : undefined,
            appliances: dto.applianceIds
              ? {
                  connect: dto.applianceIds.map((id) => ({ id })),
                }
              : undefined,
            assets: dto.assetIds
              ? {
                  connect: dto.assetIds.map((id) => ({ id })),
                }
              : undefined,
            customFields: dto.customFields,
            fieldGroups: {
              connect: fieldGroups.map((fieldGroupId) => ({
                id: fieldGroupId,
              })),
            },
          },
          select: this.selectArgs,
        });
        this.logger.log('The service record created successfully');
        await this.prisma.history.create({
          data: {
            action: HistoryActions.CREATED,
            changeInTable: ChangesOcccuredIn.SERVICE,
            date: new Date(),
            entityId: createdService.id,
            log: {
              userId: user.id,
              name: user.name,
              serviceId: createdService.id,
            },
          },
        });
        this.logger.log("History for 'create' created successfully");
        return createdService;
      } catch (error) {
        if (
          error instanceof Prisma.PrismaClientKnownRequestError &&
          error?.code === 'P2025'
        ) {
          checkErrorAndThrowNotFoundError(error);
        }
        this.logger.error(
          `Failed to create a service record: ${error.message}`,
        );
        throw error;
      }
    } else {
      this.logger.error(
        'At least provide one entity ID to connect it with service record',
      );
      throw new BadRequestException('Ensure to provide at least one entity');
    }
  }

  /**
   * Deletes a service record.
   *
   * @param {string} serviceId - The ID of the service to be deleted.
   * @param {User} user - The user who is deleting the service.
   * @returns {Promise<boolean>} - A boolean indicating whether the deletion was successful.
   * @throws {NotFoundException} - If the service record with the given ID is not found.
   */
  async deleteService(serviceId: string, user: User): Promise<boolean> {
    const serviceTransaction = this.prisma.$transaction(async (prisma) => {
      const service = await prisma.service.findFirst({
        where: {
          id: serviceId,
          isDeleted: false,
        },
        select: this.selectArgs,
      });
      if (!service) {
        this.logger.error('The service record not found with given service ID');
        throw new NotFoundException(SERVICE_NOT_FOUND);
      }
      await prisma.service.update({
        where: {
          id: serviceId,
        },
        data: {
          isDeleted: true,
        },
      });
      await disconnectFieldGroups(prisma, 'service', serviceId, service);
      this.logger.log(`Asset disconnected from all field groups`);
      await prisma.history.create({
        data: {
          action: HistoryActions.DELETED,
          changeInTable: ChangesOcccuredIn.SERVICE,
          date: new Date(),
          entityId: service.id,
          log: {
            userId: user.id,
            name: user.name,
            serviceId: service.id,
          },
        },
      });
      this.logger.log("History for 'delete' created successfully");
      return true;
    });
    return serviceTransaction;
  }

  /**
   * Updates an existing service record.
   *
   * @param {ServiceRequestDto} dto - The data transfer object containing updated service details.
   * @param {string} serviceId - The ID of the service to be updated.
   * @param {User} user - The user who is updating the service.
   * @returns {Promise<GetServiceResponseDto>} - The updated service record.
   * @throws {ConflictException} - If a service record with the same date or next service date already exists.
   * @throws {NotFoundException} - If the service record with the given ID is not found.
   */
  async updateService(
    dto: ServiceRequestDto,
    serviceId: string,
    user: User,
    isRenew: string,
  ): Promise<GetServiceResponseDto> {
    if (dto.assetIds?.length > 0 || dto.applianceIds?.length > 0) {
      const entityIds = dto.assetIds ? dto.assetIds : dto.applianceIds;
      await Promise.all(
        entityIds.map(async (entity) => {
          const data = await this.prisma[
            dto.type.toLocaleLowerCase()
          ].findFirst({
            where: {
              id: entity,
              isDeleted: false,
            },
          });
          if (!data) {
            this.logger.error(
              'If any of the entities provided are invalid, please double-check',
            );
            throw new NotFoundException('Please provide valid entity ids');
          }
        }),
      );
      try {
        const service = await this.prisma.service.findFirst({
          where: {
            id: serviceId,
            isDeleted: false,
          },
          select: this.selectArgs,
        });
        if (!service) {
          this.logger.error(
            'The service record not found with specified service ID',
          );
          throw new NotFoundException(SERVICE_NOT_FOUND);
        }
        const sameDateServices = await this.prisma.service.findFirst({
          where: {
            id: {
              not: serviceId,
            },
            date: dto.date,
            type: dto.type,
            assets: {
              every: {
                id: {
                  in: dto.assetIds,
                },
              },
            },
            appliances: {
              every: {
                id: {
                  in: dto.applianceIds,
                },
              },
            },
          },
        });
        if (sameDateServices) {
          this.logger.error(
            'The service record with provided date already exists',
          );
          throw new ConflictException(SERVICE_DATE_EXIST);
        }

        const serviceTransaction = await this.prisma.$transaction(
          async (prisma) => {
            await updateFieldGroups(prisma, 'service', serviceId, service, dto);
            const updatedService = await prisma.service.update({
              where: {
                id: serviceId,
              },
              data: {
                name: dto.name,
                type: dto.type,
                contactName: dto.contactName,
                contactNumber: dto.contactNumber,
                cost: dto.cost,
                date: setDateWithZeroTime(dto.date),
                nextServiceDate: dto.nextServiceDate
                  ? setDateWithZeroTime(dto.nextServiceDate)
                  : undefined,
                note: dto.note,
                serviceImageUrl: dto.serviceImageUrl,
                rating: dto.rating,
                supplier: dto.supplierId
                  ? {
                      connect: {
                        id: dto.supplierId,
                      },
                    }
                  : undefined,
                appliances: dto.applianceIds
                  ? {
                      connect: dto.applianceIds.map((id) => ({ id })),
                    }
                  : undefined,
                assets: dto.assetIds
                  ? {
                      connect: dto.assetIds.map((id) => ({ id })),
                    }
                  : undefined,
                customFields: dto.customFields,
              },
              select: this.selectArgs,
            });
            this.logger.log('The service record updated with provided data');
            await prisma.history.create({
              data: {
                action:
                  isRenew === 'renew'
                    ? HistoryActions.RENEWED
                    : HistoryActions.UPDATED,
                changeInTable: ChangesOcccuredIn.SERVICE,
                date: new Date(),
                entityId: service.id,
                log: {
                  userId: user.id,
                  name: user.name,
                  serviceId: service.id,
                  updatedFields: getUpdatedFields(service, updatedService),
                },
              },
            });
            this.logger.log("History for 'update' created successfully");
            return updatedService;
          },
        );
        return serviceTransaction;
      } catch (error) {
        if (
          error instanceof Prisma.PrismaClientKnownRequestError &&
          error?.code === 'P2025'
        ) {
          checkErrorAndThrowNotFoundError(error);
        }
        this.logger.error(`Failed to update service record: ${error.message}`);
        throw error;
      }
    } else {
      this.logger.error(
        'At least provide one entity ID to connect it with service record',
      );
      throw new BadRequestException('Ensure to provide at least one entity');
    }
  }

  /**
   * Retrieves a service record by its ID.
   *
   * @param {string} serviceId - The ID of the service to retrieve.
   * @returns {Promise<GetServiceResponseDto>} - The service record.
   * @throws {NotFoundException} - If the service record with the given ID is not found.
   */
  async getServiceById(serviceId: string): Promise<GetServiceResponseDto> {
    const service = await this.prisma.service.findFirst({
      where: {
        id: serviceId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });
    if (!service) {
      this.logger.error('The service not found with the given ID');
      throw new NotFoundException(SERVICE_NOT_FOUND);
    }
    return service;
  }

  /**
   * Retrieves all service records with optional pagination and search.
   *
   * @param {GetAllQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetAllServicesResponseDto>} - The paginated list of service records and total count.
   */
  async getAllServices(
    dto: GetAllQueryParamsDto,
  ): Promise<GetAllServicesResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };

    const whereOptions: Prisma.ServiceWhereInput = {
      isDeleted: false,
      ...(dto?.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                supplier: {
                  name: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              },
              {
                contactName: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : {}),
    };
    const [services, count] = await this.prisma.$transaction([
      this.prisma.service.findMany({
        where: whereOptions,
        orderBy,
        take: limit,
        skip,
        select: this.selectArgs,
      }),
      this.prisma.service.count({
        where: whereOptions,
        orderBy,
        take: limit,
        skip,
      }),
    ]);
    this.logger.log('All services fetched successfully');

    return {
      services,
      count,
    };
  }

  /**
   * Retrieves the history of a service record by its ID.
   *
   * @param {string} serviceId - The ID of the service to retrieve history for.
   * @param {GetAllQueryParamsDto} dto - The data transfer object containing query parameters.
   * @returns {Promise<GetEntityHistoryResponse>} - The history records and total count.
   * @throws {NotFoundException} - If the service record with the given ID is not found.
   */
  async getServiceHistory(
    serviceId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const service = await this.prisma.service.findFirst({
      where: {
        id: serviceId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });
    if (!service) {
      this.logger.error('The service not found with the given ID');
      throw new NotFoundException(SERVICE_NOT_FOUND);
    }
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };
    const [serviceHistory, count] = await this.prisma.$transaction([
      this.prisma.history.findMany({
        where: {
          entityId: serviceId,
        },
        select: {
          action: true,
          date: true,
          log: true,
        },
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.history.count({
        where: {
          entityId: serviceId,
        },
        orderBy,
        take: limit,
        skip,
      }),
    ]);
    this.logger.log('The service history fetched successfully');
    return { history: serviceHistory, count };
  }

  /**
   * Fetches services related to a given entity ID, which could be either an asset or an appliance.
   *
   * @param {ServiceType} entityType - The type of entity, either 'Asset' or 'Appliance'.
   * @param {string} entityId - The ID of the entity to fetch related services for.
   * @returns {Promise<GetServiceResponseDto[]>} - A promise that resolves to an array of service response DTOs.
   * @throws {NotFoundException} - If the entity is not found or is marked as deleted.
   */
  async getServiceByEntityId(
    entityType: ServiceType,
    entityId: string,
  ): Promise<GetServiceResponseDto[]> {
    const entity = await this.prisma[entityType.toLowerCase()].findFirst({
      where: {
        id: entityId,
        isDeleted: false,
      },
    });
    if (!entity) {
      this.logger.error('The entity is not available or deleted');
      throw new NotFoundException(ENTITY_NOT_FOUND);
    }
    const services = await this.prisma.service.findMany({
      where: {
        isDeleted: false,
        OR: [
          {
            appliances: {
              some: {
                id: entityId,
              },
            },
          },
          {
            assets: {
              some: {
                id: entityId,
              },
            },
          },
        ],
      },
      select: this.selectArgs,
    });
    return services;
  }
}
