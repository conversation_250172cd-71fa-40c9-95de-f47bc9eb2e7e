import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseEnumPipe,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import { ResourceService } from './service.service';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
  ApiQuery,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  ENTITY_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { GetServiceResponseDto, ServiceRequestDto } from './dto/service-dto';
import { Request } from 'express';
import { SERVICE_NOT_FOUND } from 'src/constants';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { ServiceType } from '@prisma-clients/asset-management-backend';

@Controller('services')
@ApiTags('Service')
export class ServiceController {
  constructor(private readonly resourceService: ResourceService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Service record created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a service record',
    summary: 'Create a service record',
  })
  @ApiExtraModels(HTTPResponseDto<GetServiceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.SERVICE))
  async createService(
    @Body() dto: ServiceRequestDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetServiceResponseDto>> {
    const { user } = request;
    const createdService = await this.resourceService.createService(dto, user);
    return {
      data: createdService,
      message: 'The service record created successfully',
      statusCode: HttpStatus.CREATED,
    };
  }

  @Delete(':serviceId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted a service record',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetServiceResponseDto>),
    },
  })
  @ApiParam({
    name: 'serviceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter serviceId to delete service data',
  })
  @ApiNotFoundResponse({
    description: 'Service with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: SERVICE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete a service record',
    summary: 'Delete a service record',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.SERVICE))
  async deleteService(
    @Param('serviceId', new ParseUUIDPipe()) serviceId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const deletedService = await this.resourceService.deleteService(
      serviceId,
      user,
    );
    return {
      data: deletedService,
      message: 'The service record deleted successfully',
      statusCode: HttpStatus.OK,
    };
  }

  @Put(':serviceId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated a service record',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetServiceResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Service with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: SERVICE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the service details',
    summary: 'Update service record',
  })
  @ApiParam({
    name: 'serviceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter service Id to update',
  })
  @ApiQuery({
    name: 'isRenew',
    type: 'string',
    description: 'Indicates if the serice is being renewed',
  })
  @ApiExtraModels(HTTPResponseDto<GetServiceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.SERVICE))
  async updateService(
    @Body() dto: ServiceRequestDto,
    @Param('serviceId', new ParseUUIDPipe()) serviceId: string,
    @Req() request: Request,
    @Query('isRenew') isRenew: string,
  ): Promise<HTTPResponseDto<GetServiceResponseDto>> {
    const { user } = request;
    const updatedService = await this.resourceService.updateService(
      dto,
      serviceId,
      user,
      isRenew,
    );
    return {
      data: updatedService,
      message: 'The service record updated successfully',
      statusCode: HttpStatus.OK,
    };
  }

  @Get(':serviceId')
  @ApiOperation({
    summary: 'API to get the service record details',
    description: 'API to get the service record by providing service Id',
  })
  @ApiFoundResponse({
    description: 'Successfully fetched the service record',
  })
  @ApiNotFoundResponse({
    description: SERVICE_NOT_FOUND,
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: SERVICE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    description: 'Service Id',
    name: 'serviceId',
    required: true,
    example: '0334f4f3-48e6-42e6-9c5d-168c62bc7295',
  })
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SERVICE))
  async getServiceById(
    @Param('serviceId', new ParseUUIDPipe()) serviceId: string,
  ): Promise<HTTPResponseDto<GetServiceResponseDto>> {
    const service = await this.resourceService.getServiceById(serviceId);
    return {
      data: service,
      message: 'Successfully fetched the service record',
      statusCode: HttpStatus.FOUND,
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all services',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetServiceResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all services',
    summary: 'Fetches all services',
  })
  @ApiExtraModels(GetAllResponseDto<GetServiceResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SERVICE))
  async getAllServices(
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetServiceResponseDto[]>> {
    const data = await this.resourceService.getAllServices(dto);
    return {
      data: data.services,
      message: 'Successfully fetched all services',
      statusCode: HttpStatus.OK,
      count: data.count,
    };
  }

  @Get('/history/:serviceId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all service histories',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetServiceResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all service history',
    summary: 'Fetches all service history',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SERVICE))
  async getServiceHistory(
    @Param('serviceId', new ParseUUIDPipe()) serviceId: string,
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const data = await this.resourceService.getServiceHistory(serviceId, dto);
    return {
      data: data.history,
      count: data.count,
      message: 'Successfully fetched the all history of service',
      statusCode: HttpStatus.OK,
    };
  }

  @Get('/entity/:entityType/:entityId')
  @ApiOperation({
    summary: 'API to get the service record details',
    description: 'API to get the service record by providing entity Id',
  })
  @ApiOkResponse({
    description: 'Successfully fetched the service records',
  })
  @ApiNotFoundResponse({
    description: ENTITY_NOT_FOUND,
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ENTITY_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    description: 'Entity Id',
    name: 'entityId',
    required: true,
    example: '0334f4f3-48e6-42e6-9c5d-168c62bc7295',
  })
  @ApiParam({
    description: 'Category type',
    name: 'categoryType',
    required: true,
    example: ServiceType.APPLIANCE,
    enum: ServiceType,
  })
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.SERVICE))
  async getServiceByEntityId(
    @Param('entityType', new ParseEnumPipe(ServiceType))
    entityType: ServiceType,
    @Param('entityId', new ParseUUIDPipe()) entityId: string,
  ): Promise<HTTPResponseDto<GetServiceResponseDto[]>> {
    const services = await this.resourceService.getServiceByEntityId(
      entityType,
      entityId,
    );
    return {
      message: 'All related services fetched successfully',
      statusCode: HttpStatus.OK,
      data: services,
    };
  }
}
