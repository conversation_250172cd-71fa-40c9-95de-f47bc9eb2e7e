import { ApiProperty } from '@nestjs/swagger';
import {
  ServiceType,
  serviceRating,
} from '@prisma-clients/asset-management-backend';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
} from 'class-validator';
import { MinMax } from 'src/common/custom-validators';
import { JsonValue } from 'types';

export class ServiceRequestDto {
  @ApiProperty({
    description: 'Service name',
    type: String,
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Service date',
    type: Date,
    required: true,
  })
  date: Date;

  @ApiProperty({
    description: 'Next service date',
    type: Date,
    required: false,
  })
  @IsOptional()
  @MinMax('date')
  nextServiceDate?: Date;

  @ApiProperty({
    description: 'Note about the service details',
    type: String,
    required: false,
    example: 'Note about the service details',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  note?: string;

  @ApiProperty({
    description: 'Contact Name of the servicer',
    type: String,
    required: false,
    example: 'John',
  })
  @IsOptional()
  @IsString()
  contactName?: string;

  @ApiProperty({
    description: 'Contact Number of the servicer',
    type: String,
    required: false,
    example: '6363636363',
  })
  @IsOptional()
  @IsString()
  contactNumber?: string;

  @ApiProperty({
    description: 'Cost of the service',
    type: Number,
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  cost?: number;

  @ApiProperty({
    description: 'Rating for the service',
    enum: serviceRating,
    example: serviceRating.GOOD,
    required: false,
  })
  @IsOptional()
  @IsEnum(serviceRating)
  rating?: serviceRating;

  @ApiProperty({
    description: 'Id of the supplier',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  supplierId?: string;

  @ApiProperty({
    description: 'Images associated with the service',
    type: [String],
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsString({ each: true, message: 'Images Url needs to be string' })
  serviceImageUrl?: string[];

  @ApiProperty({
    description: 'Service Image Url',
    enum: ServiceType,
    example: ServiceType.ASSET,
    required: true,
  })
  @IsEnum(ServiceType)
  type: ServiceType;

  @ApiProperty({
    description: 'The appliance IDs to connect service record',
    type: [String],
    example: [
      '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    ],
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { each: true, message: 'Each applianceId must be a valid UUID' })
  applianceIds?: string[];

  @ApiProperty({
    description: 'The asset IDs to connect service record',
    type: [String],
    example: [
      '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    ],
    required: false,
  })
  @IsOptional()
  @IsUUID('4', { each: true, message: 'Each assetId must be a valid UUID' })
  assetIds?: string[];

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class GetServiceResponseDto {
  @ApiProperty({
    description: 'Service record id',
    type: String,
  })
  id: string;

  @ApiProperty({
    description: 'Service name',
  })
  name: string;

  @ApiProperty({
    description: 'Service date',
    type: Date,
  })
  date: Date;

  @ApiProperty({
    description: 'Next service date',
    type: Date,
  })
  nextServiceDate: Date;

  @ApiProperty({
    description: 'Note about the service details',
    type: String,
  })
  note?: string;

  @ApiProperty({
    description: 'Contact Name of the servicer',
    type: String,
  })
  contactName?: string;

  @ApiProperty({
    description: 'Contact Number of the servicer',
    type: String,
  })
  contactNumber?: string;

  @ApiProperty({
    description: 'Cost of the service',
    type: Number,
  })
  cost?: number;

  @ApiProperty({
    description: 'Rating for the service',
    enum: serviceRating,
  })
  rating?: serviceRating;

  @ApiProperty({
    description: 'Id of the supplier',
    type: 'uuid',
  })
  supplierId?: string;

  @ApiProperty({
    description: 'Images associated with the service',
    type: [String],
  })
  serviceImageUrl?: string[];

  @ApiProperty({
    description: 'entity type that involved the service',
    type: String,
  })
  type?: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  customFields?: JsonValue;
}

export class GetAllServicesResponseDto {
  @ApiProperty({
    description: 'All Services details',
    type: 'array',
  })
  services: GetServiceResponseDto[];

  @ApiProperty({
    description: 'Total Services count',
    type: Number,
  })
  count: number;
}
