import {
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
} from '@nestjs/common';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { PurchaseResponseDto } from './dto/purchase.dto';
import { PurchaseService } from './purchase.service';

@Controller('purchase')
export class PurchaseController {
  constructor(private readonly purchaseServic: PurchaseService) {}

  @Get('/:purchaseId')
  async getPurchaseRecordById(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const purchaseRecord =
      await this.purchaseServic.getEntityPurchaseDetailsById(purchaseId);
    return {
      message: 'Purchase record fetched successfully',
      data: purchaseRecord,
      statusCode: HttpStatus.OK,
    };
  }
}
