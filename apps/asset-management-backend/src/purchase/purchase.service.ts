import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from './dto/purchase.dto';
import {
  INVALID_QUANTITY,
  INVALID_UUID_FORMAT,
  ORDER_NUMBER_ALREADY_EXISTS,
} from 'src/constants/message-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  getUpdatedFields,
  setDateWithZeroTime,
  upperSnakeCaseToCamelCase,
} from 'src/utility';
import { isUUID } from 'class-validator';
import { User } from 'types';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class PurchaseService {
  private logger = new Logger('PurchaseService');
  constructor(private readonly prisma: PrismaService) {}

  private selectArgs = {
    id: true,
    orderNumber: true,
    purchaseCost: true,
    currency: true,
    quantity: true,
    supplier: {
      select: {
        id: true,
        name: true,
      },
    },
    purchaseDate: true,
    purchasedBy: {
      select: {
        id: true,
        name: true,
      },
    },
    typeOfPurchase: true,
  };

  /**
   * An injectable service method that create a record of purchase details with the given data
   * and updates the quantity of the entity if already exists.
   *
   * @param {CreatePurchaseDto} createPurchaseDto - The data for creating the purchase.
   * @param {CreatePurchaseDto['entityId']} entityId - The ID of the entity related to the purchase.
   * @param {Request} request - The HTTP request object.
   * @returns {Promise<PurchaseResponseDto>} - A Promise that resolves to the created Purchase details.
   * @throws {ConflictException} - Thrown if the order number already exists.
   * @throws {NotFoundException} - Thrown if the user does not exist.
   */
  async createPurchase(
    typeOfPurchase: TypeOfCategory,
    createPurchaseDto: CreatePurchaseDto,
    entityId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    try {
      if (!isUUID(entityId)) {
        this.logger.log(`Invalid UUID format for entityId: ${entityId}`);
        throw new BadRequestException(INVALID_UUID_FORMAT);
      }

      if (
        typeOfPurchase !== TypeOfCategory.CONSUMABLE &&
        !createPurchaseDto.quantity
      )
        throw new BadRequestException(INVALID_QUANTITY);

      const entityType: string = upperSnakeCaseToCamelCase(typeOfPurchase);

      const purchaseTransaction: PurchaseResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const orderNumberExist: number = await prisma.purchase.count({
            where: {
              OR: [
                createPurchaseDto.orderNumber
                  ? {
                      orderNumber: {
                        equals: createPurchaseDto.orderNumber,
                      },
                    }
                  : undefined,
              ],
            },
          });

          if (orderNumberExist) {
            this.logger.log(
              `Order number ${createPurchaseDto.orderNumber} already exists`,
            );
            throw new ConflictException(ORDER_NUMBER_ALREADY_EXISTS);
          }

          const entityCount: number = await prisma[entityType].count({
            where: { id: entityId },
          });

          if (entityCount && createPurchaseDto.quantity) {
            if (
              typeOfPurchase === TypeOfCategory.CONSUMABLE ||
              typeOfPurchase === TypeOfCategory.SOFTWARE_LICENSE ||
              typeOfPurchase === TypeOfCategory.ACCESSORY
            ) {
              await prisma[entityType].update({
                where: { id: entityId },
                data: {
                  totalQuantity: {
                    increment: createPurchaseDto.quantity,
                  },
                  availableQuantity: {
                    increment: createPurchaseDto.quantity,
                  },
                },
              });
              this.logger.log(
                'Updated total quantity and available quantity of the entity',
              );
            }
          }
          return this.createPurchaseRecord(
            typeOfPurchase,
            createPurchaseDto,
            entityId,
            user,
          );
        });

      return purchaseTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create purchase details: ${error}`);
      throw error;
    }
  }

  async createPurchaseRecord(
    typeOfPurchase: TypeOfCategory,
    createPurchaseDto: CreatePurchaseDto,
    entityId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    return await this.prisma.$transaction(async (prisma) => {
      const purchaseDetails: PurchaseResponseDto = await prisma.purchase.create(
        {
          data: {
            typeOfPurchase: typeOfPurchase,
            entityId: entityId,
            orderNumber: createPurchaseDto.orderNumber
              ? createPurchaseDto.orderNumber
              : null,
            purchaseCost: createPurchaseDto.purchaseCost,
            currency: createPurchaseDto.currency,
            quantity: createPurchaseDto.quantity,
            supplier: createPurchaseDto.supplierId
              ? {
                  connect: {
                    id: createPurchaseDto.supplierId,
                  },
                }
              : undefined,
            purchaseDate: createPurchaseDto.purchaseDate
              ? setDateWithZeroTime(createPurchaseDto.purchaseDate)
              : null,
            purchasedBy: createPurchaseDto.purchasedById
              ? {
                  connect: {
                    id: createPurchaseDto.purchasedById,
                  },
                }
              : undefined,
          },
          select: this.selectArgs,
        },
      );

      this.logger.log(
        `Purchase details created for order number ${purchaseDetails.orderNumber}`,
      );

      // history for purchase
      await prisma.history.create({
        data: {
          changeInTable: ChangesOcccuredIn.PURCHASE,
          action: HistoryActions.CREATED,
          date: new Date(),
          entityId: entityId,
          log: {
            userId: user.id,
            name: user.name,
            purchaseId: purchaseDetails.id,
          },
        },
      });

      this.logger.log(
        `History for creating purchase details having id ${purchaseDetails.id} created`,
      );

      return purchaseDetails;
    });
  }

  /**
   * An injectable service method that fetches purchase details for the given entity ID.
   *
   * @param {string} entityId - The ID of the entity to fetch purchase details for.
   * @param {GetAllQueryParamsDto} queryParams - The query parameters for pagination and sorting.
   * @returns {Promise<PurchaseResponseDto[]>} A Promise that resolves to an array of Purchase details.
   * @throws {BadRequestException} Throws a BadRequestException if there's an error parsing the entity ID.
   */
  async getEntityPurchaseDetails(
    entityId: string,
    queryParams?: GetAllQueryParamsDto,
  ): Promise<GetAllPurchasesResponse> {
    if (!isUUID(entityId)) {
      this.logger.log(`Invalid UUID format for entityId: ${entityId}`);
      throw new BadRequestException(INVALID_UUID_FORMAT);
    }

    const count = await this.prisma.purchase.count({
      where: { entityId },
    });

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    // TODO: Search functionality to be implemented

    const purchaseDetails = await this.prisma.purchase.findMany({
      where: { entityId },
      select: this.selectArgs,
      orderBy,
      take: limit,
      skip,
    });

    this.logger.log(
      `Successfully fetched purchase details for entityId: ${entityId}`,
    );

    return { purchaseDetails, count };
  }

  /**
   * Updates a purchase record with the provided data.
   * @param {UpdatePurchaseDto} updatePurchaseDto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @throws {NotFoundException} Throws NotFoundException if the purchase record with the specified ID is not found.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    updatePurchaseDto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    entityId?: string,
  ): Promise<PurchaseResponseDto> {
    const purchaseData = await this.prisma.purchase.findFirst({
      where: {
        id: purchaseId,
      },
      select: this.selectArgs,
    });
    if (!purchaseData) {
      this.logger.error(
        `The purchase details not found with the specified ID: ${purchaseId}`,
      );
      throw new NotFoundException('Purchase details not found');
    }

    if (
      purchaseData.typeOfPurchase !== TypeOfCategory.CONSUMABLE &&
      !updatePurchaseDto.quantity
    )
      throw new BadRequestException(INVALID_QUANTITY);

    try {
      const purchaseTransaction = await this.prisma.$transaction(
        async (prisma) => {
          const updatedPurchaseData = await prisma.purchase.update({
            where: {
              id: purchaseId,
            },
            data: {
              orderNumber: updatePurchaseDto.orderNumber,
              quantity: updatePurchaseDto.quantity,
              purchaseDate: updatePurchaseDto.purchaseDate
                ? setDateWithZeroTime(updatePurchaseDto.purchaseDate)
                : null,
              purchaseCost: updatePurchaseDto.purchaseCost,
              currency: updatePurchaseDto.currency,
              supplier: updatePurchaseDto.supplierId
                ? {
                    connect: {
                      id: updatePurchaseDto.supplierId,
                    },
                  }
                : undefined,
              purchasedBy: updatePurchaseDto.purchasedById
                ? {
                    connect: {
                      id: updatePurchaseDto.purchasedById,
                    },
                  }
                : undefined,
            },
            select: this.selectArgs,
          });
          this.logger.log('The purchase detials updated');
          await prisma.history.create({
            data: {
              action: HistoryActions.UPDATED,
              changeInTable: ChangesOcccuredIn.PURCHASE,
              entityId: entityId,
              date: new Date(),
              log: {
                userId: user.id,
                name: user.name,
                purchaseId: entityId ?? purchaseId,
                updatedFields: getUpdatedFields(
                  purchaseData,
                  updatedPurchaseData,
                ),
              },
            },
          });
          this.logger.log(
            `The history for purchase 'update' created, purchase ID: ${purchaseId}`,
          );
          const entityType = upperSnakeCaseToCamelCase(
            updatedPurchaseData.typeOfPurchase,
          );
          if (entityType !== 'asset' && entityType !== 'appliance') {
            await this.prisma[entityType].update({
              where: {
                id: entityId,
              },
              data:
                updatePurchaseDto.quantity > purchaseData.quantity
                  ? {
                      totalQuantity: {
                        increment:
                          updatePurchaseDto.quantity - purchaseData.quantity,
                      },
                      availableQuantity: {
                        increment:
                          updatePurchaseDto.quantity - purchaseData.quantity,
                      },
                    }
                  : {
                      totalQuantity: {
                        decrement:
                          purchaseData.quantity - updatePurchaseDto.quantity,
                      },
                      availableQuantity: {
                        decrement:
                          purchaseData.quantity - updatePurchaseDto.quantity,
                      },
                    },
            });
          }
          return updatedPurchaseData;
        },
      );
      return purchaseTransaction;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update purchase details: ${error.message}`);
      throw error;
    }
  }

  async getEntityPurchaseDetailsById(
    purchaseId: string,
  ): Promise<PurchaseResponseDto> {
    const purchaseDetails = await this.prisma.purchase.findFirst({
      where: { id: purchaseId },
      select: { ...this.selectArgs, entityId: true },
    });
    if (!purchaseDetails) {
      throw new NotFoundException('Purchase record not found');
    }
    this.logger.log(
      `Successfully fetched purchase details for entityId: ${purchaseId}`,
    );
    return purchaseDetails;
  }
}
