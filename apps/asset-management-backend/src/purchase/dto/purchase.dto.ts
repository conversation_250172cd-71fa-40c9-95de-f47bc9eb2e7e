import { ApiProperty } from '@nestjs/swagger';
import { Currency } from '@prisma-clients/asset-management-backend';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
  Min,
} from 'class-validator';

export class CreatePurchaseDto {
  @ApiProperty({
    description: 'Order number of the purchase',
    type: 'string',
    example: 'Order-123',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderNumber?: string;

  @ApiProperty({
    description: 'Cost of the purchased entity/s',
    type: 'number',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  purchaseCost?: number;

  @ApiProperty({
    description: 'Currency in which purchase is done',
    enum: Currency,
    example: Currency.USD,
    required: false,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiProperty({
    description: 'Quantity of the purchased entity/s',
    type: 'number',
    example: 10,
    required: true,
  })
  @Type(() => Number)
  @Min(0)
  quantity?: number;

  @ApiProperty({
    description: 'Id of the supplier',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  supplierId?: string;

  @ApiProperty({
    description: 'Date of purchase',
    type: Date,
    example: '2024-01-13T14:32:02.773Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  purchaseDate?: Date;

  @ApiProperty({
    description: 'Id of the user who purchased the entity/s',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  purchasedById?: string;
}

export class PurchaseResponseDto {
  @ApiProperty({
    description: 'Id of the purchase record',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
  })
  id: string;

  @ApiProperty({
    description: 'Order number of the purchase',
    type: 'string',
    example: 'ORDER-123',
  })
  orderNumber: string;

  @ApiProperty({
    description: 'Cost of the purchased entity/s',
    type: 'number',
    example: 1000,
  })
  purchaseCost: number;

  @ApiProperty({
    description: 'Currency in which purchase is done',
    enum: Currency,
    example: Currency.USD,
  })
  currency: Currency;

  @ApiProperty({
    description: 'Quantity of the purchased entity/s',
    type: 'number',
    example: 10,
  })
  quantity: number;

  @ApiProperty({
    description: 'Supplier details',
    type: 'object',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Supplier 1 ',
    },
  })
  supplier: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Date of purchase',
    type: Date,
    example: '2024-01-13T14:32:02.773Z',
  })
  purchaseDate: Date;

  @ApiProperty({
    description: 'Id of the user who purchased the entity/s',
    type: 'object',
    example: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'John',
    },
  })
  purchasedBy: {
    id: string;
    name: string;
  };
}

export class GetAllPurchasesResponse {
  @ApiProperty({
    description: 'Purchase details for the specified entity id',
    type: PurchaseResponseDto,
    isArray: true,
  })
  purchaseDetails: PurchaseResponseDto[];

  @ApiProperty({
    description: 'Total number of purchases',
    type: 'number',
    example: 10,
  })
  count: number;
}

export class UpdatePurchaseDto extends CreatePurchaseDto {}
