import { Test, TestingModule } from '@nestjs/testing';
import { PurchaseService } from './purchase.service';
import {
  ChangesOcccuredIn,
  History,
  HistoryActions,
  Purchase,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { CreatePurchaseDto, PurchaseResponseDto } from './dto/purchase.dto';
import { Request } from 'express';
import { isUUID } from 'class-validator';
import { BadRequestException, ConflictException } from '@nestjs/common';
import {
  INVALID_UUID_FORMAT,
  ORDER_NUMBER_ALREADY_EXISTS,
} from '../constants/message-constants';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('PurchaseService', () => {
  let service: PurchaseService;
  let prismaService: PrismaService;

  const purchaseDto: CreatePurchaseDto = {
    orderNumber: 'Order-1',
    purchaseCost: 1000,
    currency: 'INR',
    quantity: 10,
    supplierId: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    purchaseDate: new Date(),
    purchasedById: 'cdcf1c26-cfaf-41af-be67-feddbc056c28',
  };

  const purchaseDetails: PurchaseResponseDto = {
    id: '387ad454-5922-48e0-8091-cac8fe2a9169',
    orderNumber: 'Order-1',
    purchaseCost: 1000,
    currency: 'INR',
    quantity: 10,
    supplier: {
      id: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
      name: 'Supplier-1',
    },
    purchaseDate: new Date(),
    purchasedBy: {
      id: 'cdcf1c26-cfaf-41af-be67-feddbc056c28',
      name: 'John',
    },
  };

  const history: History = {
    id: 'f08cc113-dabd-4076-9d20-5d88e7261be2',
    date: new Date(),
    action: HistoryActions.CREATED,
    changeInTable: ChangesOcccuredIn.CONSUMABLE,
    entityId: purchaseDetails.id,
    log: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const request = {
    user: {
      id: 'cdcf1c26-cfaf-41af-be67-feddbc056c28',
      name: 'John',
    },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<PurchaseService>(PurchaseService);
    prismaService = service['prisma'];

    expect(isUUID(purchaseDetails.id)).toBe(true);
    expect(isUUID(history.id)).toBe(true);
    expect(isUUID(request.user.id)).toBe(true);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createPurchase', () => {
    it('should create purchase details with the passed data (dto)', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(purchaseDetails);

      const result = await service.createPurchase(
        TypeOfCategory.CONSUMABLE,
        purchaseDto,
        purchaseDetails.id,
        request.user,
      );

      expect(result).toEqual(purchaseDetails);
    });

    it('should create purchase details and update the totalQuantity of the entity if already exist', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(purchaseDetails);

      const result = await service.createPurchase(
        TypeOfCategory.CONSUMABLE,
        purchaseDto,
        purchaseDetails.id,
        request.user,
      );

      expect(result).toEqual(purchaseDetails);
    });

    it('should throw a conflict exception if the order number already exist', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(new ConflictException(ORDER_NUMBER_ALREADY_EXISTS));

      try {
        await service.createPurchase(
          TypeOfCategory.CONSUMABLE,
          purchaseDto,
          purchaseDetails.id,
          request.user,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(ConflictException);
        expect(error.status).toEqual(409);
        expect(error.message).toEqual(ORDER_NUMBER_ALREADY_EXISTS);
      }
    });

    it('should throw prisma known exception if the user is not found', async () => {
      jest.spyOn(prismaService, '$transaction').mockRejectedValue(
        new PrismaClientKnownRequestError('User not found', {
          code: 'P2025',
          clientVersion: 'none',
        }),
      );

      try {
        await service.createPurchase(
          TypeOfCategory.CONSUMABLE,
          purchaseDto,
          purchaseDetails.id,
          request.user,
        );
      } catch (error) {
        expect(error).toBeInstanceOf(PrismaClientKnownRequestError);
        expect(error.code).toEqual('P2025');
        expect(error.message).toEqual('User not found');
      }
    });
  });

  describe('getEntityPurchaseDetails', () => {
    it('should return an array of all purchases of the specified entity Id', async () => {
      jest.spyOn(prismaService.purchase, 'count').mockResolvedValue(1);
      jest
        .spyOn(prismaService.purchase, 'findMany')
        .mockResolvedValue([purchaseDetails as unknown as Purchase]);

      const result = await service.getEntityPurchaseDetails(purchaseDetails.id);

      expect(result.purchaseDetails).toEqual([purchaseDetails]);
      expect(result.count).toEqual(1);
    });

    it('should throw a bad request exception if invalid entity Id is passed', async () => {
      jest
        .spyOn(prismaService.purchase, 'findMany')
        .mockRejectedValue(new BadRequestException(INVALID_UUID_FORMAT));

      try {
        await service.getEntityPurchaseDetails('invalid-id');
      } catch (error) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.status).toEqual(400);
        expect(error.message).toEqual(INVALID_UUID_FORMAT);
      }
    });
  });
});
