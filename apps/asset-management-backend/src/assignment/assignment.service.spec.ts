import { Test, TestingModule } from '@nestjs/testing';
import { AssignmentService } from './assignment.service';
import { AppModule } from 'src/app.module';

describe('AssignmentService', () => {
  let service: AssignmentService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<AssignmentService>(AssignmentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
