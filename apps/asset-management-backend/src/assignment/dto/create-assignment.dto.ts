import {
  IsString,
  IsOptional,
  IsDateString,
  IsUUID,
  IsNotEmpty,
  IsArray,
  IsEnum,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { assignmentEntityDto } from 'src/common/dto/entity.dto';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';

export class CreateAssignmentDto {
  @ApiProperty({
    description: 'Date of the Assignment',
    example: '2024-01-15T10:30:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  date: Date;

  @ApiProperty({
    description: 'User ID',
    example: '28c4f6ac-8402-4f1f-961d-8abf4222a837',
    required: true,
  })
  @IsOptional()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Entity ID',
    example: 'd4c1463f-6236-4edb-907b-dd1a5c0dadad',
    required: false,
  })
  @IsNotEmpty()
  @IsUUID()
  entityId: string;

  @ApiProperty({
    description: 'Additional information for assignment',
    example: 'This assignment has some conditions',
    required: false,
  })
  @IsOptional()
  @IsString()
  note: string;

  @ApiProperty({
    description: 'status of the Asset',
    type: 'string',
    example: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  assetStausId: string;

  @ApiProperty({
    description: 'Notify user ID',
    type: 'string',
    example: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
    required: false,
  })
  @IsOptional()
  @IsArray()
  notifyUser?: { id: string; email: string }[];

  @ApiProperty({
    description: 'Resource tag',
    type: 'string',
    required: false,
  })
  @IsOptional()
  tag?: string;

  @ApiProperty({
    description: 'Asset Id',
    type: 'string',
    example: '3e1ae358-68c8-4b70-b8b4-944f1481b60a',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  assetId: string;
}

export class GetAllAssignmentDetailsDto {
  @ApiProperty({
    name: 'typeOfEntity',
    enum: TypeOfCategory,
    example: TypeOfCategory.ACCESSORY,
    description: 'Enter the type of category of assignment',
    required: true,
  })
  @IsEnum(TypeOfCategory)
  typeOfEntity: TypeOfCategory;
}

export class AssignmentResponseDto {
  @ApiProperty({ example: '999fc45f-bb45-4de5-9252-047b9ba155e9' })
  id: string;

  @ApiProperty({ example: 'accessory' })
  typeOfAssignment: TypeOfCategory;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  date: Date;

  @ApiProperty({
    description: 'User details',
    example: {
      id: '28c4f6ac-8402-4f1f-961d-8abf4222a837',
      name: 'john_doe',
      email: '<EMAIL>',
    },
  })
  user: {
    id: string;
    name: string;
    email: string;
  };

  @ApiProperty({ example: 'd4c1463f-6236-4edb-907b-dd1a5c0dadad' })
  entityId: string;

  @ApiProperty({
    description: 'Whether the assignment is pending',
    example: true,
  })
  isPending: boolean;

  @ApiProperty({ example: 'This assignment has some conditions' })
  note: string;

  @ApiProperty({
    description: 'entity details of the assignment',
  })
  entityDetails?: assignmentEntityDto;

  @ApiProperty({
    description: 'Notify user data',
  })
  notifyUser: { id: string; name: string; email: string }[];

  @ApiProperty({ example: '2024-01-01T10:30:00Z' })
  updatedAt?: Date;

  @ApiProperty({
    example: {
      id: '28c4f6ac-8402-4f1f-961d-8abf4222a837',
      name: 'john_doe',
    },
  })
  asset: {
    id: string;
    assetName: string;
  };
}
