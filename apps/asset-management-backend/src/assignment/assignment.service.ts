import {
  ForbiddenException,
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';
import {
  AssignmentResponseDto,
  CreateAssignmentDto,
} from './dto/create-assignment.dto';
import { Prisma } from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  upperSnakeCaseToCamelCase,
} from '../utility';
import { ASSIGNMENT_NOT_FOUND } from '../constants/message-constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { User } from '../../types';
import { AwsService } from 'src/aws/aws.service';
import { PrismaService } from 'src/prisma/prisma.service';
@Injectable()
export class AssignmentService {
  private logger = new Logger('AssignmentService');
  private selectArgs = {
    id: true,
    typeOfAssignment: true,
    date: true,
    user: {
      select: {
        id: true,
        name: true,
        email: true,
      },
    },
    note: true,
    entityId: true,
    isPending: true,
    notifyUser: {
      select: {
        id: true,
        name: true,
        email: true,
      },
    },
    asset: {
      select: {
        id: true,
        assetName: true,
      },
    },
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  /**
   * @async
   * @function
   * @name assign
   * @description Assigns a new assignment based on the provided assignment data.
   * @param {CreateAssignmentDto} assignmentDto - Data for creating a new assignment.
   * @param {User} user - User object representing the entity performing the assignment.
   * @returns {Promise<AssignmentResponseDto>} - Newly assigned assignment.
   * @throws {BadRequestException} - If the total quantity for the assigned entity is zero.
   * @throws {Error} - If any other error occurs during the assignment process.
   */
  async assign(
    typeOfAssignment: TypeOfCategory,
    assignmentDto: CreateAssignmentDto,
    user: User,
  ): Promise<AssignmentResponseDto> {
    try {
      const assignmentTransaction = await this.prisma.$transaction(
        async (prisma) => {
          // Fetch totalQuantity info from the entity
          const entityType = upperSnakeCaseToCamelCase(typeOfAssignment);
          const entityExists = await prisma[entityType].findFirst({
            where: {
              id: assignmentDto.entityId,
            },
          });

          if (!entityExists) {
            this.logger.log(
              `Entity with id ${assignmentDto.entityId} not found`,
            );

            throw new NotFoundException(
              `Entity with id ${assignmentDto.entityId} not found`,
            );
          }

          if (entityExists.availableQuantity <= 0) {
            this.logger.log(`${entityType} not available`);
            throw new NotAcceptableException(`${entityType} not available`);
          }
          // Updating an entity record in the database by decrementing the totalQuantity while assigning
          await prisma[entityType].update({
            where: {
              id: assignmentDto.entityId,
            },
            data: {
              availableQuantity: { decrement: 1 },
            },
          });

          this.logger.log(
            `Available quantity Updated for ${entityType} with id:${assignmentDto.entityId}`,
          );

          return this.createAssignmentRecord(
            typeOfAssignment,
            assignmentDto,
            user,
          );
        },
      );
      return assignmentTransaction;
    } catch (error) {
      this.logger.error(`Error while creating Assignment`);
      if (
        error.code === 'P2025' &&
        error instanceof Prisma.PrismaClientKnownRequestError
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      throw error;
    }
  }

  /**
   * Creates a new assignment record.
   * @param {TypeOfCategory} typeOfAssignment - The type of assignment category.
   * @param {CreateAssignmentDto} assignmentDto - The data transfer object containing assignment details.
   * @param {User} user - The user performing the assignment.
   * @returns {Promise<AssignmentResponseDto>} A promise that resolves to the created assignment response DTO.
   */
  async createAssignmentRecord(
    typeOfAssignment: TypeOfCategory,
    assignmentDto: CreateAssignmentDto,
    user: User,
  ): Promise<AssignmentResponseDto> {
    return await this.prisma.$transaction(async (prisma) => {
      // Prepare notify users data
      const notifyUserIds =
        assignmentDto.notifyUser && assignmentDto.notifyUser.length > 0
          ? assignmentDto.notifyUser.map((user) => ({ id: user.id }))
          : undefined;

      // Prepare user connection if userId exists
      const userConnection = assignmentDto.userId
        ? { connect: { id: assignmentDto.userId } }
        : undefined;

      // Prepare asset connection if asset information is provided in assignmentDto
      const assetConnection = assignmentDto.assetId
        ? { connect: { id: assignmentDto.assetId } }
        : undefined;

      const assignment: AssignmentResponseDto = await prisma.assignment.create({
        data: {
          typeOfAssignment: typeOfAssignment,
          date: new Date(assignmentDto.date),
          user: userConnection,
          entityId: assignmentDto.entityId,
          isPending: typeOfAssignment === TypeOfCategory.ASSET,
          note: assignmentDto.note,
          notifyUser: {
            connect: notifyUserIds,
          },
          asset: assetConnection,
        },
        select: this.selectArgs,
      });

      this.logger.log(`Assignment Created with id:${assignment.id}`);

      // Create history entry for this assignment
      await prisma.history.create({
        data: {
          changeInTable: ChangesOcccuredIn.ASSIGNMENT,
          action:
            typeOfAssignment === TypeOfCategory.ASSET
              ? HistoryActions.PENDING
              : HistoryActions.ASSIGNED,
          entityId: assignment.entityId,
          date: new Date(),
          log: {
            userId: user.id,
            name: user.name,
            assignmentDetails: {
              id: assignment.id,
              assignedTo: assignment.user?.name,
            },
          },
        },
      });

      this.logger.log(
        `Assignment history created for assignment with id ${assignment.id}`,
      );

      return assignment;
    });
  }

  /**
   * Acknowledges an assignment.
   * @param {string} assignmentId - The ID of the assignment to acknowledge.
   * @param {User} user - The user acknowledging the assignment.
   * @returns {Promise<AssignmentResponseDto>} A promise that resolves with the updated assignment response DTO.
   * @throws {ForbiddenException} Throws an error if the user is not allowed to perform the operation.
   */
  async acknowledgeAssignment(
    assignmentId: string,
    user: User,
  ): Promise<AssignmentResponseDto> {
    const assignmentExists: number = await this.prisma.assignment.count({
      where: {
        id: assignmentId,
        isPending: true,
        userId: user.id,
      },
    });

    if (!assignmentExists) {
      throw new ForbiddenException(
        'You are not not allowed to perform this operation',
      );
    }

    const assignmentDetails: AssignmentResponseDto =
      await this.prisma.assignment.update({
        where: {
          id: assignmentId,
        },
        data: {
          isPending: false,
        },
        select: this.selectArgs,
      });

    await this.prisma.history.create({
      data: {
        changeInTable: ChangesOcccuredIn.ASSIGNMENT,
        action: HistoryActions.ASSIGNED,
        entityId: assignmentDetails.entityId,
        date: new Date(),
        log: {
          userId: assignmentDetails.user.id,
          name: assignmentDetails.user.name,
          assignmentDetails: {
            id: assignmentDetails.id,
            assignedTo: assignmentDetails.user.name,
          },
        },
      },
    });

    await this.awsService.sendAcknowledgedEmail(assignmentDetails);

    this.logger.log(
      `Assignment history created for assignment with id ${assignmentDetails.id}`,
    );

    return assignmentDetails;
  }

  /**
   * Retrieves assignment acknowledgement details.
   * @param {string} assignmentId - The ID of the assignment.
   * @param {User} user - The user requesting the details.
   * @returns {Promise<Object>} A promise that resolves with the assignment acknowledgement details.
   * @throws {NotFoundException} Throws an error if the assignment is not found.
   */
  async getAssignmentAcknowledgementDetails(
    assignmentId: string,
    user: User,
  ): Promise<AssignmentResponseDto> {
    const transaction = await this.prisma.$transaction(async (prisma) => {
      const assignmentDetails = await prisma.assignment.findFirst({
        where: {
          id: assignmentId,
          userId: {
            equals: user.id,
          },
          isPending: true,
        },
        select: this.selectArgs,
      });

      if (!assignmentDetails) {
        throw new NotFoundException(ASSIGNMENT_NOT_FOUND);
      }

      const entityType = upperSnakeCaseToCamelCase(
        assignmentDetails.typeOfAssignment,
      );

      const entityDetails = await prisma[entityType].findUnique({
        where: {
          id: assignmentDetails.entityId,
        },
      });

      if (!entityDetails) {
        throw new NotFoundException('Entity not found');
      }

      return { ...assignmentDetails, entityDetails: entityDetails };
    });

    return transaction;
  }

  /**
   * Finds an assignment by its ID.
   * @param {string} assignmentId - The ID of the assignment to find.
   * @returns {Promise<Object>} A promise that resolves with the assignment details.
   * @throws {NotFoundException} Throws an error if the assignment is not found.
   */
  async findAssignmentById(
    assignmentId: string,
  ): Promise<AssignmentResponseDto> {
    const getAssignmentTransaction = await this.prisma.$transaction(
      async (prisma) => {
        const assignmentDetails = await prisma.assignment.findUnique({
          where: {
            id: assignmentId,
          },
          select: this.selectArgs,
        });

        if (!assignmentDetails) {
          throw new NotFoundException(ASSIGNMENT_NOT_FOUND);
        }

        const entityType = upperSnakeCaseToCamelCase(
          assignmentDetails.typeOfAssignment,
        );

        const entityDetails = await prisma[entityType].findUnique({
          where: {
            id: assignmentDetails.entityId,
          },
        });

        if (!entityDetails) {
          throw new NotFoundException('Entity not found');
        }

        return {
          ...assignmentDetails,
          entityDetails: entityDetails,
        };
      },
    );

    return getAssignmentTransaction;
  }

  /**
   * @method
   * @name findAll
   * @description Retrieves all assignments of a specific entity type.
   * @param {TypeOfCategory} typeOfEntity - Type of entity for which assignments should be retrieved.
   * @returns {Promise<AssignmentResponseDto[]>} - List of assignments.
   * @throws {Error} - If any error occurs during the retrieval process.
   */
  async findAll(
    typeOfEntity: TypeOfCategory,
  ): Promise<AssignmentResponseDto[]> {
    // Find and return assignments for the specified entity type
    const assignments: AssignmentResponseDto[] =
      await this.prisma.assignment.findMany({
        where: { typeOfAssignment: typeOfEntity },
        select: { ...this.selectArgs, updatedAt: true },
      });
    this.logger.log(`Successfully fetched all ${typeOfEntity} assignments`);

    return assignments;
  }

  /**
   * @method
   * @name findAllByEntityId
   * @description Retrieves all assignments of a specific entity Id.
   * @param {string} entityId - Entity Id for which assignments should be retrieved.
   * @returns {Promise<AssignmentResponseDto[]>} - List of assignments.
   * @throws {Error} - If any error occurs during the retrieval process.
   */
  async findAllByEntityId(entityId: string): Promise<AssignmentResponseDto[]> {
    // Find and return assignments for the specified entity type
    const assignments: AssignmentResponseDto[] =
      await this.prisma.assignment.findMany({
        where: { entityId },
        select: this.selectArgs,
      });
    this.logger.log(`Successfully fetched all ${entityId}`);

    return assignments;
  }

  /**
   * @async
   * @function
   * @name unassign
   * @description Unassigns an assignment by ID.
   * @param {string} id - ID of the assignment to be unassigned.
   * @param {User} user - User object representing the entity performing the unassignment.
   * @returns {Promise<boolean>} - True if the assignment is successfully unassigned.
   * @throws {NotFoundException} - If the assignment with the given ID is not found.
   * @throws {Error} - If any other error occurs during the unassignment process.
   */

  async unAssign(id: string, user: User): Promise<boolean> {
    // Check if the assignment with the given ID exists
    const assignment = await this.prisma.assignment.findUnique({
      where: { id },
      select: {
        typeOfAssignment: true,
        entityId: true,
        userId: true,
      },
    });

    if (!assignment) {
      this.logger.log(`${ASSIGNMENT_NOT_FOUND} for id: ${id}`);
      throw new NotFoundException(ASSIGNMENT_NOT_FOUND);
    }

    const entityType = upperSnakeCaseToCamelCase(assignment.typeOfAssignment);

    this.deleteAssignmentRecord(id, user);

    // Update totalQuantity info from the entity
    await this.prisma[entityType].update({
      where: {
        id: assignment.entityId,
      },
      data: {
        availableQuantity: { increment: 1 },
      },
    });

    this.logger.log(
      `TotalQuantity Updated for ${entityType} with id:${assignment.entityId}`,
    );

    return true;
  }

  async deleteAssignmentRecord(id: string, user: User): Promise<void> {
    try {
      return await this.prisma.$transaction(async (prisma) => {
        // Delete the assignment
        const deletedAssignment = await prisma.assignment.delete({
          where: { id },
          select: this.selectArgs,
        });

        this.logger.log(`Successfully deleted assignment of id ${id}`);

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ASSIGNMENT,
            action: HistoryActions.UNASSIGNED,
            date: new Date(),
            entityId: deletedAssignment.entityId,
            log: {
              userId: user.id,
              name: user.name,
              assignmentDetails: {
                id: deletedAssignment.id,
                assignedTo: deletedAssignment.user?.name,
              },
            },
          },
        });

        this.logger.log(
          `Assignment history created for assignment with id ${id}`,
        );
      });
    } catch (error) {
      this.logger.error(`Error while unassigning`);

      if (
        error.code === 'P2025' &&
        error instanceof Prisma.PrismaClientKnownRequestError
      ) {
        throw new NotFoundException(ASSIGNMENT_NOT_FOUND);
      }

      throw error;
    }
  }
}
