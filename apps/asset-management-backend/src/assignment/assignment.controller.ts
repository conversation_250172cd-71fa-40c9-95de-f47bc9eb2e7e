import {
  <PERSON>,
  Get,
  HttpStatus,
  <PERSON><PERSON>,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  Req,
} from '@nestjs/common';
import { AssignmentService } from './assignment.service';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import {
  AssignmentResponseDto,
  GetAllAssignmentDetailsDto,
} from './dto/create-assignment.dto';
import {
  ASSIGNMENT_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { CheckPolicies } from 'src/decorators';
import { <PERSON>Handler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Assignment')
@Controller('assignment')
export class AssignmentController {
  private logger = new Logger('AssetController');
  constructor(private readonly assignmentService: AssignmentService) {}

  @Post('acknowledge/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully acknowledged assignment',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api is for user to acknowledge the assignment',
    summary: 'Acknowledge the assigned asset',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter assignment Id to be acknowledged',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  async assetAcknowledgement(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    const { user } = request;
    const assignmentDetails =
      await this.assignmentService.acknowledgeAssignment(assignmentId, user);

    return {
      statusCode: HttpStatus.OK,
      data: assignmentDetails,
      message: 'Successfully acknowledged assignment',
    };
  }

  @Get('acknowledge/:assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched assignment details',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Assignment with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSIGNMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch assignment details for acknowledgement with specified Id (checks if the logged in user is the assigned user)',
    summary: 'Fetches assignment details for acknowledgement with given Id',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter assignment Id to fetch details',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  async getAssetAcknowledgementDetails(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    const { user } = request;
    const assignmentDetails =
      await this.assignmentService.getAssignmentAcknowledgementDetails(
        assignmentId,
        user,
      );

    return {
      statusCode: HttpStatus.OK,
      data: assignmentDetails,
      message: 'Successfully fetched assignment details',
    };
  }

  @Get(':assignmentId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched asset',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Assignment with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSIGNMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch assignment with specified Id',
    summary: 'Fetches assignment with given Id',
  })
  @ApiParam({
    name: 'assignmentId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter assignment Id to fetch details',
  })
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  async getAssignmentDetails(
    @Param('assignmentId', new ParseUUIDPipe()) assignmentId: string,
  ): Promise<HTTPResponseDto<AssignmentResponseDto>> {
    const assignmentDetails =
      await this.assignmentService.findAssignmentById(assignmentId);
    return {
      statusCode: HttpStatus.OK,
      data: assignmentDetails,
      message: 'Successfully fetched assignment details',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description:
      'Successfully fetched all the assignments for the given entity',
    schema: { $ref: getSchemaPath(HTTPResponseDto<AssignmentResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Assignments for the type of category not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSIGNMENT_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all assignments for the given category',
    summary: 'Fetches all assignments for a given category',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET))
  @ApiExtraModels(HTTPResponseDto<AssignmentResponseDto>)
  async getAllAssignmentsByEntityType(
    @Query() dto: GetAllAssignmentDetailsDto,
  ): Promise<HTTPResponseDto<AssignmentResponseDto[]>> {
    const assignments = await this.assignmentService.findAll(
      TypeOfCategory[dto.typeOfEntity],
    );

    return {
      statusCode: HttpStatus.OK,
      data: assignments,
      message: 'Successfully fetched assignment details',
    };
  }
}
