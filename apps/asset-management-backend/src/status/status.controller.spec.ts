import { Test, TestingModule } from '@nestjs/testing';
import { StatusController } from './status.controller';
import { StatusService } from './status.service';
import { Request } from 'express';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import {
  AssetStatusRequestDto,
  SelectedStatusResponseDto,
} from './dto/status.dto';

import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { User } from 'types';
import { AppModule } from 'src/app.module';

describe('StatusController', () => {
  let controller: StatusController;
  let service: StatusService;

  const user: User = {
    id: 'user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        status: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<StatusController>(StatusController);
    service = module.get<StatusService>(StatusService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create status successfully', async () => {
      const createAssetStatusDto: AssetStatusRequestDto = {
        name: 'Active',
        color: '#00FF00',
        note: 'Status Active',
      };

      const expectedResponse: HTTPResponseDto<SelectedStatusResponseDto> = {
        statusCode: HttpStatus.CREATED,
        data: {
          id: '073c2022-a405-456a-9d33-844e2c39ac58',
          name: 'Active',
          color: '#00FF00',
          note: 'Status Active',
          assets: [],
        },
        message: 'Status created successfully',
      };

      jest
        .spyOn(service, 'createStatus')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.create(
        createAssetStatusDto,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle conflict when status already exists', async () => {
      const createAssetStatusDto: AssetStatusRequestDto = {
        name: 'Active',
        color: '#00FF00',
        note: 'Status Active',
      };

      jest
        .spyOn(service, 'createStatus')
        .mockRejectedValue(
          new ConflictException('Status with the same name already exists'),
        );

      await expect(
        controller.create(createAssetStatusDto, user as unknown as Request),
      ).rejects.toThrowError(ConflictException);
    });
  });

  describe('findAll', () => {
    it('should get all statuses successfully', async () => {
      const dto: GetAllQueryParamsDto = {
        page: 1,
        limit: 10,
        sortBy: SORT_BY.CREATED_AT,
        sortOrder: SORT_ORDER.DESC,
        searchInput: 'example',
      };

      const expectedResponse: GetAllResponseDto<SelectedStatusResponseDto[]> = {
        statusCode: HttpStatus.OK,
        data: [
          {
            id: '073c2022-a405-456a-9d33-844e2c39ac58',
            name: 'Active',
            color: '#00FF00',
            note: 'Status Active',
            assets: [],
          },
        ],
        count: 1,
        message: 'Statuses retrieved successfully',
      };

      jest.spyOn(service, 'getAllStatus').mockResolvedValue(expectedResponse);

      const result = await controller.findAll(dto);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle internal server error', async () => {
      const dto: GetAllQueryParamsDto = {
        page: 1,
        limit: 10,
        sortBy: SORT_BY.CREATED_AT,
        sortOrder: SORT_ORDER.ASC,
        searchInput: 'example',
      };

      const errorResponse: InternalServerErrorException =
        new InternalServerErrorException('Internal Server Error');

      jest.spyOn(service, 'getAllStatus').mockRejectedValue(errorResponse);

      await expect(controller.findAll(dto)).rejects.toThrowError(errorResponse);
    });
  });

  describe('findOne', () => {
    it('should get status by ID successfully', async () => {
      const id = '073c2022-a405-456a-9d33-844e2c39ac58';

      const expectedResponse: HTTPResponseDto<SelectedStatusResponseDto> = {
        statusCode: HttpStatus.OK,
        data: {
          id,
          name: 'Active',
          color: '#00FF00',
          note: 'Status Active',
          assets: [],
        },
        message: 'Status retrieved successfully',
      };

      jest.spyOn(service, 'getStatus').mockResolvedValue(expectedResponse.data);

      const result = await controller.findOne(id);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when status is not found', async () => {
      const id = 'nonExistentId';

      jest
        .spyOn(service, 'getStatus')
        .mockRejectedValue(
          new NotFoundException('No status found for the provided ID'),
        );
      await expect(controller.findOne(id)).rejects.toThrowError(
        NotFoundException,
      );
    });

    it('should handle bad request with validation errors', async () => {
      const id = 'invalidId';

      jest
        .spyOn(service, 'getStatus')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );
      await expect(controller.findOne(id)).rejects.toThrowError(
        BadRequestException,
      );
    });
  });
  describe('update', () => {
    it('should update status successfully', async () => {
      const id = '073c2022-a405-456a-9d33-844e2c39ac58';
      const updateAssetStatusDto: AssetStatusRequestDto = {
        name: 'Updated Active',
        color: '#0000FF',
        note: 'Updated Status Active',
      };

      const expectedResponse: HTTPResponseDto<SelectedStatusResponseDto> = {
        statusCode: HttpStatus.OK,
        data: {
          id,
          name: 'Updated Active',
          color: '#0000FF',
          note: 'Updated Status Active',
          assets: [],
        },
        message: 'Status updated successfully',
      };

      jest
        .spyOn(service, 'updateStatus')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.update(
        id,
        updateAssetStatusDto,
        user as unknown as Request,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when updating non-existent status', async () => {
      const id = 'nonExistentId';
      const updateAssetStatusDto: AssetStatusRequestDto = {
        name: 'Updated Active',
        color: '#0000FF',
        note: 'Updated Status Active',
      };

      jest
        .spyOn(service, 'updateStatus')
        .mockRejectedValue(
          new NotFoundException('No status found for the provided ID'),
        );

      await expect(
        controller.update(id, updateAssetStatusDto, user as unknown as Request),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors during update', async () => {
      const id = 'invalidId';
      const updateAssetStatusDto: AssetStatusRequestDto = {
        name: 'Updated Active',
        color: '#0000FF',
        note: 'Updated Status Active',
      };

      jest
        .spyOn(service, 'updateStatus')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.update(id, updateAssetStatusDto, user as unknown as Request),
      ).rejects.toThrowError(BadRequestException);
    });
  });

  describe('remove', () => {
    it('should delete status successfully', async () => {
      const id = '073c2022-a405-456a-9d33-844e2c39ac58';
      const expectedResponse: HTTPResponseDto<boolean> = {
        statusCode: HttpStatus.OK,
        data: true,
        message: 'Status deleted successfully',
      };

      jest.spyOn(service, 'deleteStatus').mockResolvedValue(true);

      const result = await controller.remove(id, user as unknown as Request);

      expect(result).toEqual(expectedResponse);
    });

    it('should handle not found error when deleting non-existent status', async () => {
      const id = 'nonExistentId';

      jest
        .spyOn(service, 'deleteStatus')
        .mockRejectedValue(
          new NotFoundException('No status found for the provided ID'),
        );

      await expect(
        controller.remove(id, user as unknown as Request),
      ).rejects.toThrowError(NotFoundException);
    });

    it('should handle bad request with validation errors during delete', async () => {
      const id = 'invalidId';

      jest
        .spyOn(service, 'deleteStatus')
        .mockRejectedValue(
          new BadRequestException('Validation failed (uuid is expected)'),
        );

      await expect(
        controller.remove(id, user as unknown as Request),
      ).rejects.toThrowError(BadRequestException);
    });
  });
});
