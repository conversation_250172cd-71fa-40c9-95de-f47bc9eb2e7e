import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  AssetStatusRequestDto,
  SelectedStatusResponseDto,
  StatusResponseDto,
} from './dto/status.dto';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';

import { getUpdatedFields } from 'src/utility';
import { GetStatusFilterResponseDto } from './dto/status.dto';
import { User } from 'types';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class StatusService {
  private logger = new Logger('StatusService');
  private selectArgs = {
    id: true,
    name: true,
    assets: true,
    color: true,
    note: true,
  };
  constructor(private readonly prisma: PrismaService) {}
  async createStatus(
    dto: AssetStatusRequestDto,
    user: User,
  ): Promise<SelectedStatusResponseDto> {
    const statusTransaction = await this.prisma.$transaction(async (prisma) => {
      const existingStatus: StatusResponseDto =
        await prisma.assetStatus.findFirst({
          where: {
            OR: [
              { name: { equals: dto.name, mode: 'insensitive' } },
              { color: { equals: dto.color, mode: 'insensitive' } },
            ],
            isDeleted: false,
          },
        });

      if (existingStatus) {
        if (existingStatus.name?.toLowerCase() === dto.name?.toLowerCase()) {
          this.logger.log('Status with the same name already exists');
          throw new ConflictException(
            'Status with the same name already exists',
          );
        } else {
          this.logger.log('Status with the same color already exists');
          throw new ConflictException(
            'Status with the same color already exists',
          );
        }
      }

      const createdStatus: SelectedStatusResponseDto =
        await prisma.assetStatus.create({
          data: {
            name: dto.name,
            color: dto.color,
            note: dto.note,
          },
          select: this.selectArgs,
        });

      await this.prisma.history.create({
        data: {
          action: HistoryActions.CREATED,
          changeInTable: ChangesOcccuredIn.ASSET_STATUS,
          date: new Date(),
          entityId: createdStatus?.id,
          log: {
            userId: user.id,
            name: user.name,
            statusId: createdStatus?.id,
          },
        },
      });
      return createdStatus;
    });
    this.logger.log('Status created successfully');
    return statusTransaction;
  }

  async getAllStatus(
    dto?: GetAllQueryParamsDto,
  ): Promise<GetStatusFilterResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };

    const options: Prisma.AssetStatusFindManyArgs = {
      where: {
        name: dto?.searchInput
          ? {
              contains: dto?.searchInput,
              mode: 'insensitive',
            }
          : undefined,
        isDeleted: false,
      },
      select: this.selectArgs,
      orderBy,
      take: limit,
      skip,
    };

    const count: number = await this.prisma.assetStatus.count({
      where: options.where,
    });

    const status: StatusResponseDto[] =
      await this.prisma.assetStatus.findMany(options);

    this.logger.log('Statuses retrieved successfully');
    return {
      data: status,
      count,
    };
  }

  async getStatus(id: string): Promise<SelectedStatusResponseDto> {
    const status: SelectedStatusResponseDto =
      await this.prisma.assetStatus.findFirst({
        where: {
          id: id,
          isDeleted: false,
        },
        select: this.selectArgs,
      });

    if (!status) {
      this.logger.log('No status found for the provided ID');
      throw new NotFoundException('No status found for the provided ID');
    }

    this.logger.log('Status retreived successfully');

    return status;
  }

  async updateStatus(
    id: string,
    updateStatusDto: AssetStatusRequestDto,
    user: User,
  ): Promise<SelectedStatusResponseDto> {
    const statusTransaction: SelectedStatusResponseDto =
      await this.prisma.$transaction(async (prisma) => {
        const existingStatus: StatusResponseDto =
          await prisma.assetStatus.findUnique({
            where: {
              id: id,
            },
          });

        if (!existingStatus || existingStatus.isDeleted) {
          this.logger.log('No status found for the provided ID');
          throw new NotFoundException('No status found for the provided ID');
        }

        const duplicateStatus: StatusResponseDto | null =
          await prisma.assetStatus.findFirst({
            where: {
              id: { not: id },
              OR: [
                { name: { equals: updateStatusDto.name, mode: 'insensitive' } },
                {
                  color: { equals: updateStatusDto.color, mode: 'insensitive' },
                },
              ],
              isDeleted: false,
            },
          });
        if (duplicateStatus) {
          if (
            duplicateStatus.name?.toLowerCase() ===
            updateStatusDto.name?.toLowerCase()
          ) {
            this.logger.log('Status with the same name already exists');
            throw new ConflictException(
              'Status with the same name already exists',
            );
          } else {
            this.logger.log('Status with the same color already exists');
            throw new ConflictException(
              'Status with the same color already exists',
            );
          }
        }

        const updatedStatus: SelectedStatusResponseDto =
          await prisma.assetStatus.update({
            where: {
              id: id,
            },
            data: updateStatusDto,
            select: this.selectArgs,
          });

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ASSET_STATUS,
            action: HistoryActions.UPDATED,
            date: new Date(),
            entityId: existingStatus?.id,
            log: {
              userId: user.id,
              userName: user.name,
              updatedStatusId: existingStatus?.id,
              updatedFields: getUpdatedFields(existingStatus, updateStatusDto),
            },
          },
        });

        this.logger.log('Status updated successfully');

        return updatedStatus;
      });

    return statusTransaction;
  }

  async deleteStatus(id: string, user: User): Promise<boolean> {
    const statusTransaction: boolean = await this.prisma.$transaction(
      async (prisma) => {
        const status: StatusResponseDto = await prisma.assetStatus.findUnique({
          where: {
            id: id,
          },
        });

        if (!status || status.isDeleted) {
          this.logger.log('No status found for the provided ID');
          throw new NotFoundException('No status found for the provided ID');
        }

        await prisma.assetStatus.update({
          where: {
            id: id,
          },
          data: {
            isDeleted: true,
          },
        });
        this.logger.log('Status deleted successfully');

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.ASSET_STATUS,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: status?.id,
            log: {
              userId: user.id,
              userName: user.name,
              deletedStatusId: status?.id,
            },
          },
        });
        this.logger.log("History for 'delete' created sucessfully");

        return true;
      },
    );

    return statusTransaction;
  }
}
