import { Test, TestingModule } from '@nestjs/testing';
import { StatusService } from './status.service';
import { ConflictException, NotFoundException } from '@nestjs/common';
import {
  AssetStatusRequestDto,
  SelectedStatusResponseDto,
} from './dto/status.dto';
import { AssetStatus } from '@prisma-clients/asset-management-backend';
import { User } from 'types';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('StatusService', () => {
  let service: StatusService;
  let prismaService: PrismaService;

  const dto: AssetStatusRequestDto = {
    name: 'Active',
    color: 'red',
    note: 'Status active',
  };

  const selectedStatus: SelectedStatusResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Active',
    color: '#00FF00',
    note: 'Status Active',
    assets: [],
  };
  const user: User = {
    id: 'user-id',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        status: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<StatusService>(StatusService);
    prismaService = service['prisma'];
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createStatus', () => {
    it('should create a status when not exists', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(selectedStatus);

      const result = await service.createStatus(dto, user);

      expect(result).toEqual(selectedStatus);
    });

    it('should throw conflict exception when status already exists', () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new ConflictException('Status with the same name already exists'),
        );

      expect(
        async () => await service.createStatus(dto, user),
      ).rejects.toThrowError(
        new ConflictException('Status with the same name already exists'),
      );
    });

    it('should throw ConflictException on color conflict', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new ConflictException('Status with the same color already exists'),
        );

      await expect(async () => {
        await service.createStatus(dto, user);
      }).rejects.toThrowError(
        new ConflictException('Status with the same color already exists'),
      );
    });
  });

  describe('getAllStatus', () => {
    it('should retrieve statuses based on query parameters', async () => {
      const mockStatuses: SelectedStatusResponseDto[] = [
        {
          id: '073c2022-a405-456a-9d33-844e2c39ac58',
          name: 'Active',
          color: '#00FF00',
          note: 'Status Active',
          assets: [],
        },
        {
          id: '0de54f23-a405-456a-9d33-844e2c39ac58',
          name: 'Out of office',
          color: 'pink',
          note: 'Status Out of office',
          assets: [],
        },
      ];

      const count = mockStatuses.length;

      jest.spyOn(prismaService.assetStatus, 'count').mockResolvedValue(count);
      jest
        .spyOn(prismaService.assetStatus, 'findMany')
        .mockResolvedValue(mockStatuses as AssetStatus[]);

      const result = await service.getAllStatus();

      expect(result).toEqual({
        data: mockStatuses,
        count,
      });
    });
  });

  describe('getStatus', () => {
    it('should get status successfully', async () => {
      const id = 'exampleId';
      const expectedStatus: SelectedStatusResponseDto = {
        id,
        name: 'Example Status',
        color: '#123456',
        note: 'Example Note',
        assets: [],
      };

      jest
        .spyOn(prismaService.assetStatus, 'findFirst')
        .mockResolvedValue(expectedStatus as AssetStatus);

      const result = await service.getStatus(id);

      expect(result).toEqual({
        id,
        name: 'Example Status',
        color: '#123456',
        note: 'Example Note',
        assets: [],
      });
    });

    it('should throw NotFoundException when status is not found', async () => {
      const nonExistentId = 'nonExistentId';

      jest
        .spyOn(prismaService.assetStatus, 'findFirst')
        .mockResolvedValue(null);

      await expect(service.getStatus(nonExistentId)).rejects.toThrowError(
        new NotFoundException('No status found for the provided ID'),
      );
    });
  });
  describe('updateStatus', () => {
    const id = 'exampleId';
    const updateStatusDto: AssetStatusRequestDto = {
      name: 'Updated Status',
      color: '#FFFFFF',
      note: 'Updated Note',
    };

    const updatedStatus = {
      id,
      name: 'Updated Status',
      color: '#FFFFFF',
      note: 'Updated Note',
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      assets: [],
    };

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should update status successfully', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(updatedStatus);

      const result = await service.updateStatus(id, updateStatusDto, user);

      expect(result).toEqual(updatedStatus);
    });

    it('should throw NotFoundException when status is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new NotFoundException('No status found for the provided ID'),
        );

      await expect(
        service.updateStatus(id, updateStatusDto, user),
      ).rejects.toThrowError(
        new NotFoundException('No status found for the provided ID'),
      );
    });
  });

  describe('deleteStatus', () => {
    const id = 'exampleId';

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('should delete status successfully', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(true);

      const result = await service.deleteStatus(id, user);

      expect(result).toBe(true);
    });

    it('should throw NotFoundException when status is not found', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockRejectedValue(
          new NotFoundException('No status found for the provided ID'),
        );

      await expect(service.deleteStatus(id, user)).rejects.toThrowError(
        new NotFoundException('No status found for the provided ID'),
      );
    });
  });
});
