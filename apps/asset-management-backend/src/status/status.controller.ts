import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  HttpStatus,
  ParseUUIDPipe,
  Logger,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { StatusService } from './status.service';
import { SelectedStatusResponseDto } from './dto/status.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { AssetStatusRequestDto } from './dto/status.dto';
import { Request } from 'express';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Asset-Status')
@Controller('asset-status')
@UseGuards(PermissionGuard)
export class StatusController {
  private logger = new Logger('StatusController');
  constructor(private readonly statusService: StatusService) {}

  @ApiOperation({
    summary: 'Create New Status',
    description:
      'Endpoint for creating a new status. A new status can be created, provided that no status with the same name or color already exists. If a status with the same name or color is found, the operation will fail, and an appropriate error message will be returned.',
  })
  @ApiCreatedResponse({
    description: 'Successfully created status response',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SelectedStatusResponseDto>),
    },
  })
  @ApiConflictResponse({
    description: 'Error: Conflict',
    schema: {
      example: {
        statusCode: 409,
        message: 'Status with the same name already exists',
        error: 'Conflict',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Error: Bad Request',
    schema: {
      example: {
        statusCode: 400,
        message: ['color must be a string'],
        error: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Error: Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<SelectedStatusResponseDto>)
  @Post()
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.STATUS))
  async create(
    @Body() createAssetStatusDto: AssetStatusRequestDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<SelectedStatusResponseDto>> {
    const { user } = req;
    this.logger.log('API to create new asset status');
    const data = await this.statusService.createStatus(
      createAssetStatusDto,
      user,
    );
    return {
      statusCode: HttpStatus.CREATED,
      data,
      message: 'Status created successfully',
    };
  }

  @ApiOperation({
    summary: 'Get List of All Statuses',
    description: 'Endpoint to retrieve a list of all statuses in the system.',
  })
  @ApiOkResponse({
    description: 'Successfully retreived status response',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<SelectedStatusResponseDto[]>),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Error: Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetAllResponseDto<SelectedStatusResponseDto[]>)
  @Get()
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.STATUS))
  async findAll(
    @Query() dto?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<SelectedStatusResponseDto[]>> {
    this.logger.log('API to get all asset status');
    const statuses = await this.statusService.getAllStatus(dto);

    return {
      statusCode: HttpStatus.OK,
      data: statuses.data,
      count: statuses.count,
      message: 'Statuses retrieved successfully',
    };
  }

  @ApiOperation({
    summary: 'Get Status by ID',
    description:
      'Endpoint to retrieve the details of a specific status identified by its unique ID.',
  })
  @ApiInternalServerErrorResponse({
    description: 'Error: Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'No status found for the provided ID',
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Error: Bad Request',
    schema: {
      example: {
        statusCode: 400,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully retreive status',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SelectedStatusResponseDto>),
    },
  })
  @ApiParam({
    name: 'statusId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Retreive status by Id',
  })
  @ApiBearerAuth('access-token')
  @ApiExtraModels(HTTPResponseDto<SelectedStatusResponseDto>)
  @Get(':statusId')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.STATUS))
  async findOne(
    @Param('statusId', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<SelectedStatusResponseDto>> {
    this.logger.log('API to get asset status by id');
    const data = await this.statusService.getStatus(id);

    return {
      statusCode: HttpStatus.OK,
      data,
      message: 'Status retrieved successfully',
    };
  }

  @ApiOperation({
    summary: 'Update Status by ID',
    description:
      'Endpoint to modify the details of a specific status identified by its unique ID.',
  })
  @ApiOkResponse({
    description: 'Successfully updated status',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<SelectedStatusResponseDto>),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Error: Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'No status found for the provided ID',
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Error: Bad Request',
    schema: {
      example: {
        statusCode: 400,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request',
      },
    },
  })
  @ApiParam({
    name: 'statusId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter status Id to update',
  })
  @ApiExtraModels(HTTPResponseDto<SelectedStatusResponseDto>)
  @ApiBearerAuth('access-token')
  @Put(':statusId')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.STATUS))
  async update(
    @Param('statusId', new ParseUUIDPipe()) id: string,
    @Body() updateAssetStatusDto: AssetStatusRequestDto,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<SelectedStatusResponseDto>> {
    const { user } = req;
    this.logger.log('API to update asset status');
    const data = await this.statusService.updateStatus(
      id,
      updateAssetStatusDto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data,
      message: 'Status updated successfully',
    };
  }

  @ApiOperation({
    summary: 'Delete Status by ID',
    description:
      'Endpoint to soft delete a status based on its unique identifier.',
  })
  @ApiOkResponse({
    description: 'Successfully deleted status',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<boolean>),
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'No status found for the provided ID',
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Error: Bad Request',
    schema: {
      example: {
        statusCode: 400,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Error: Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'statusId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter status Id to delete',
  })
  @ApiBearerAuth('access-token')
  @Delete(':statusId')
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.STATUS))
  async remove(
    @Param('statusId', new ParseUUIDPipe()) id: string,
    @Req() req: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = req;
    this.logger.log('API to delete asset status');
    const isDeleted = await this.statusService.deleteStatus(id, user);
    return {
      statusCode: HttpStatus.OK,
      data: isDeleted,
      message: 'Status deleted successfully',
    };
  }
}
