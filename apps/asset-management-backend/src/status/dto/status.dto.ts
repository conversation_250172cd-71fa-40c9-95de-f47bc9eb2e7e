import { ApiProperty } from '@nestjs/swagger';
import { Asset } from '@prisma-clients/asset-management-backend';
import {
  IsOptional,
  IsString,
  IsArray,
  IsBoolean,
  IsDate,
  IsUUID,
} from 'class-validator';

// DTO for handling post and update requests
export class AssetStatusRequestDto {
  @ApiProperty({
    description: 'The name of the asset status',
    type: String,
    example: 'Active',
    required: false,
  })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'The color associated with the asset status',
    type: String,
    example: '#00FF00',
    required: false,
  })
  @IsString()
  @IsOptional()
  color: string;

  @ApiProperty({
    description: 'Additional notes for the asset status',
    type: String,
    example: 'This is a sample note.',
    required: false,
  })
  @IsOptional()
  @IsString()
  note: string;
}

export class StatusResponseDto {
  @ApiProperty({
    description: 'The unique identifier for the status',
    example: '073c2022-a405-456a-9d33-844e2c39ac58',
    type: 'uuid',
    required: true,
  })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the status',
    example: 'Active',
    required: true,
  })
  @IsString()
  name: string | null;

  @ApiProperty({
    description: 'The color associated with the status',
    example: '#00FF00',
    required: true,
  })
  @IsString()
  color: string | null;

  @ApiProperty({
    description: 'Additional notes about the status',
    example: 'Status Active',
    required: true,
  })
  @IsString()
  note: string | null;

  @ApiProperty({
    description: 'The date and time when the status was created',
    example: '2022-01-01T12:34:56Z',
    type: Date,
    required: true,
  })
  @IsOptional()
  @IsDate()
  createdAt?: Date;

  @ApiProperty({
    description: 'The date and time when the status was last updated',
    example: '2022-01-02T14:45:30Z',
    type: Date,
    required: true,
  })
  @IsOptional()
  @IsDate()
  updatedAt?: Date;

  @ApiProperty({
    description: 'Flag indicating whether the status is deleted',
    example: false,
    required: true,
  })
  @IsOptional()
  @IsBoolean()
  isDeleted?: boolean;

  @ApiProperty({
    description: 'Array of assets associated with the status',
    example: [],
    required: true,
    isArray: true,
  })
  @IsArray()
  assets?: Asset[];
}

//response dto for create,get by id, and update
export class SelectedStatusResponseDto {
  @ApiProperty({
    description: 'The unique identifier for the status',
    example: '073c2022-a405-456a-9d33-844e2c39ac58',
    type: 'uuid',
    required: true,
  })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'The name of the status',
    example: 'Active',
    required: true,
  })
  @IsString()
  name: string | null;

  @ApiProperty({
    description: 'The color associated with the status',
    example: '#00FF00',
    required: true,
  })
  @IsString()
  color: string | null;

  @ApiProperty({
    description: 'Additional notes about the status',
    example: 'Status Active',
    required: true,
  })
  @IsString()
  note: string | null;

  @ApiProperty({
    description: 'Array of assets associated with the status',
    example: [],
    required: true,
    isArray: true,
  })
  @IsArray()
  assets?: Asset[];
}

//response for get all status
export class GetStatusFilterResponseDto {
  @ApiProperty({
    description: 'The data payload of the response.',
  })
  data: SelectedStatusResponseDto[];
  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}
