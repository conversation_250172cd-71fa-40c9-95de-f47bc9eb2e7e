import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class ManufacturerDto {
  @ApiProperty({
    description: 'Manufacturer name',
    type: 'string',
    example: 'Cosmic Creations',
    required: false,
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: 'Contact Name of manufacturer',
    type: 'string',
    example: 'Stella Stardust',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactName: string;

  @ApiProperty({
    description: 'Email of manufacturer',
    type: 'string',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  contactEmail: string;

  @ApiProperty({
    description: 'Contact number of manufacturer',
    type: 'string',
    example: '9876543210',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPhoneNumber: string;

  @ApiProperty({
    description: 'URL for manufacturer image profile',
    type: 'string',
    example: 'https://cosmiccreations.com/logo.jpg',
    required: false,
  })
  @IsOptional()
  manufacturerImageUrl: string;

  @ApiProperty({
    description: 'Note for manufacturer',
    type: 'string',
    example: 'Exploring the universe of innovative manufacturing.',
    required: false,
  })
  @IsString()
  @IsOptional()
  note: string;
}
export class ManufacturerResponseDto {
  @ApiProperty({
    description: 'Manufacturer Id',
    type: 'string',
    example: '0488e761-7341-48a4-8513-8fe2ede7c6e7',
    required: false,
  })
  @IsString()
  @IsOptional()
  id: string;

  @ApiProperty({
    description: 'Manufacturer name',
    type: 'string',
    example: 'Cosmic Creations',
    required: false,
  })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({
    description: 'Contact Name of manufacturer',
    type: 'string',
    example: 'Stella Stardust',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactName: string;

  @ApiProperty({
    description: 'Email of manufacturer',
    type: 'string',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  contactEmail: string;

  @ApiProperty({
    description: 'Contact number of manufacturer',
    type: 'string',
    example: '9876543210',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPhoneNumber: string;

  @ApiProperty({
    description: 'URL for manufacturer image profile',
    type: 'string',
    example: 'https://cosmiccreations.com/logo.jpg',
    required: false,
  })
  @IsOptional()
  manufacturerImageUrl: string;

  @ApiProperty({
    description: 'Note for manufacturer',
    type: 'string',
    example: 'Exploring the universe of innovative manufacturing.',
    required: false,
  })
  @IsString()
  @IsOptional()
  note: string;
}

export class GetAllManufacturersFilterResponseDto {
  @ApiProperty({
    description: 'List of manufacturer',
    type: ManufacturerResponseDto,
    isArray: true,
  })
  manufacturers: ManufacturerResponseDto[];

  @ApiProperty({
    description: 'Total count of manufacturer',
    type: 'number',
    example: 10,
  })
  count: number;
}
