import { Test, TestingModule } from '@nestjs/testing';
import { ManufacturerController } from './manufacturer.controller';
import {
  BadRequestException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { Manufacturer } from '@prisma-clients/asset-management-backend';
import { ManufacturerService } from './manufacturer.service';
import { ManufacturerResponseDto } from './dto/manufacturer.dto';
import { ManufacturerMessageConsts } from 'src/constants/manufacturer-constants';
import { Request } from 'express';
import { AppModule } from 'src/app.module';

describe('ManufacturerController', () => {
  let controller: ManufacturerController;
  let mockManufacturerService: ManufacturerService;

  const mockManufacturerData: Manufacturer = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Company Test',
    contactName: '<PERSON>',
    contactPhoneNumber: '1122334455',
    contactEmail: '<EMAIL>',
    manufacturerImageUrl: 'data:image/jpeghuAe',
    note: 'Sent product catalog, interested in pricing details.',
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  const mockManufacturerResponseData: ManufacturerResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Company Test',
    contactName: 'Bob Johnson',
    contactPhoneNumber: '1122334455',
    contactEmail: '<EMAIL>',
    manufacturerImageUrl: 'data:image/jpeghuAe',
    note: 'Sent product catalog, interested in pricing details.',
  };

  // Mocking the request object
  const mockRequest: Partial<Request> = {
    user: {
      id: 'bb0bae90-c9c0-48ee-bd62-071b8d42ba0e',
      email: '<EMAIL>',
      name: 'Mock Name',
      role: {
        id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
        name: 'admin',
        permissions: {
          manufacturers: ['read', 'create', 'update', 'delete'],
        },
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<ManufacturerController>(ManufacturerController);
    mockManufacturerService =
      module.get<ManufacturerService>(ManufacturerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /** TEST: create new manufacturer  */
  describe('createManufacturer', () => {
    /** API should return a valid status code, valid success message and data */
    it('should return a statusCode of 201, valid success message and data ', async () => {
      jest
        .spyOn(mockManufacturerService, 'create')
        .mockImplementation(() =>
          Promise.resolve(mockManufacturerResponseData),
        );

      const result = await controller.create(
        mockManufacturerData,
        mockRequest as Request,
      );

      expect(result.statusCode).toBe(HttpStatus.CREATED);
      expect(result.message).toBe(
        ManufacturerMessageConsts.successCreateManufacturer,
      );
      expect(result.data).toEqual(mockManufacturerResponseData);
    });
  });

  /** TEST: get manufacturer as per given id  */
  describe('getManufacturer', () => {
    /** API should return a valid status code, success message and valid data */
    it('should return a statusCode of 200, success message and valid data ', async () => {
      jest
        .spyOn(mockManufacturerService, 'findOne')
        .mockImplementation(() =>
          Promise.resolve(mockManufacturerResponseData),
        );
      const result = await controller.findOne(mockManufacturerData.id);
      expect(result.statusCode).toBe(HttpStatus.OK);
      expect(result.message).toBe(
        `${ManufacturerMessageConsts.successGetManufacturer} having id ${mockManufacturerData.id}`,
      );
      expect(result.data).toEqual(mockManufacturerResponseData);
    });

    /** API should return NotFoundException while trying to get details of invalid ID*/
    it('should return NotFoundException while trying to get details of non-existing manufacturer', async () => {
      jest
        .spyOn(mockManufacturerService, 'findOne')
        /** id is invalid, exception will throw from controller layer */
        .mockImplementation(jest.fn());

      await expect(
        controller.findOne(
          /** invalid Id */
          '111z1111-z111-111z-1z11-111z1z11zz11',
        ),
      ).rejects.toThrow(
        new NotFoundException(ManufacturerMessageConsts.notFoundManufacturer),
      );
    });
  });

  /** TEST: get all manufacturers  */
  describe('getAllManufacturer', () => {
    /** API should return a valid status code, success message and valid data */
    it('should return a statusCode of 200, success message and valid data', async () => {
      jest.spyOn(mockManufacturerService, 'findAll').mockResolvedValue(
        Promise.resolve({
          manufacturers: [mockManufacturerResponseData],
          count: 1,
        }),
      );

      const result = await controller.findAll();

      expect(result.statusCode === HttpStatus.OK);
      expect(result.message).toBe(
        ManufacturerMessageConsts.successGetAllManufacturers,
      );
      expect(result.data).toEqual([mockManufacturerResponseData]);
    });

    /** API should not return list of existing manufacturers having values of invalid data types */
    it('should not return list of existing manufacturers having values of invalid data types', async () => {
      jest.spyOn(mockManufacturerService, 'findAll').mockResolvedValue(
        Promise.resolve({
          manufacturers: [mockManufacturerResponseData],
          count: 1,
        }),
      );

      const result = await controller.findAll();

      expect(result.data).toEqual(
        expect.arrayContaining([
          expect.not.objectContaining({
            id: expect.any(BigInt),
            name: expect.any(Number),
            note: expect.any(Boolean),
            contactName: expect.any(Object),
            contactEmail: expect.any(Symbol),
            contactPhoneNumber: expect.any(Boolean),
            manufacturerImageUrl: expect.any(Object),
          }),
        ]),
      );
    });
  });

  /** TEST: to update existing manufacturer  */
  describe('updateManufacturer', () => {
    /** API should return a valid status code, valid success message and valid data */
    it('should return a statusCode of 200, valid success message and valid data', async () => {
      jest
        .spyOn(mockManufacturerService, 'update')
        .mockResolvedValue(Promise.resolve(mockManufacturerResponseData));

      const result = await controller.update(
        mockManufacturerData.id,
        mockManufacturerData,
        mockRequest as Request,
      );

      expect(result.statusCode).toBe(HttpStatus.OK);
      expect(result.message).toBe(
        ManufacturerMessageConsts.successUpdateManufacturer,
      );
      expect(result.data).toEqual(mockManufacturerResponseData);
    });

    /** API should return BadRequestException while trying update details of  invalid ID /  non-existing manufacturer*/
    it('should return BadRequestException while update details of non-existing manufacturer', async () => {
      jest
        .spyOn(mockManufacturerService, 'update')
        /** id is invalid, exception will throw from controller layer*/
        .mockImplementation(jest.fn());

      await expect(
        controller.update(
          /** passing invalid id */
          '111z1111-z111-111z-1z11-111z1z11zz11',
          mockManufacturerData,
          mockRequest as Request,
        ),
      ).rejects.toThrow(
        new BadRequestException(
          ManufacturerMessageConsts.failedUpdateManufacturer,
        ),
      );
    });
  });
});
