import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  GetAllManufacturersFilterResponseDto,
  ManufacturerDto,
  ManufacturerResponseDto,
} from './dto/manufacturer.dto';
import { ManufacturerMessageConsts } from 'src/constants/manufacturer-constants';

import { MANUFACTURER_NOT_FOUND } from 'src/constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';

import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import { User } from 'types';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class ManufacturerService {
  private logger = new Logger('ManufacturerService');

  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  private fieldsToSelect = {
    id: true,
    name: true,
    note: true,
    contactName: true,
    contactEmail: true,
    contactPhoneNumber: true,
    manufacturerImageUrl: true,
    accessories: {
      select: {
        id: true,
      },
    },
    appliances: {
      select: {
        id: true,
      },
    },
    consumables: {
      select: {
        id: true,
      },
    },
    softwareLicenses: {
      select: {
        id: true,
      },
    },
    Policy: {
      select: {
        id: true,
      },
    },
    assetModels: {
      select: {
        id: true,
      },
    },
    Insurance: {
      select: {
        id: true,
      },
    },
  };

  async create(
    createManufacturerDto: ManufacturerDto,
    user: User,
  ): Promise<ManufacturerResponseDto> {
    try {
      const createManufacturerResponse: ManufacturerResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          const manufacturer = await prisma.manufacturer.count({
            where: {
              OR: [
                {
                  name: {
                    equals: createManufacturerDto.name,
                    mode: 'insensitive',
                  },
                },
              ],
              isDeleted: false,
            },
          });
          if (manufacturer) {
            this.logger.error('The manufacturer name already exist');
            throw new ConflictException('The manufacturer name already exist');
          }
          const createdManufacturer: ManufacturerResponseDto =
            await prisma.manufacturer.create({
              data: createManufacturerDto,
              select: this.fieldsToSelect,
            });

          this.logger.log(
            `${ManufacturerMessageConsts.successCreateManufacturer} have id ${createdManufacturer.id}`,
          );

          /** history */
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.MANUFACTURER,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: createdManufacturer.id,
              log: {
                userId: user.id,
                name: user.name,
                manufacturerId: createdManufacturer.id,
              },
            },
          });
          this.logger.log(
            `Successfully created history for a new manufacturer having id ${createdManufacturer.id}`,
          );

          return createdManufacturer;
        });
      return createManufacturerResponse;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (createManufacturerDto.manufacturerImageUrl) {
        this.awsService.deleteFile(createManufacturerDto.manufacturerImageUrl);
        this.logger.log(
          'Manufacturer image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to create manufacturer: ${error}`);
      throw error;
    }
  }

  async findOne(id: string): Promise<ManufacturerResponseDto> {
    const manufacturer: ManufacturerResponseDto =
      await this.prisma.manufacturer.findFirst({
        where: { id, isDeleted: false },
        select: this.fieldsToSelect,
      });
    this.logger.log(
      `${ManufacturerMessageConsts.successGetManufacturer} having id ${id}`,
    );
    return manufacturer;
  }

  async findAll(
    queryFilters?: GetAllQueryParamsDto,
  ): Promise<GetAllManufacturersFilterResponseDto> {
    const page: number | null = queryFilters?.page ? queryFilters.page : null;
    const limit: number | undefined = queryFilters?.limit
      ? queryFilters.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryFilters?.sortBy || 'createdAt']: queryFilters?.sortOrder || 'desc',
    };
    const whereOptions: Prisma.ManufacturerWhereInput =
      queryFilters?.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: queryFilters.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                contactName: {
                  contains: queryFilters.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                contactEmail: {
                  contains: queryFilters.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : undefined;

    const manufacturers: ManufacturerResponseDto[] =
      await this.prisma.manufacturer.findMany({
        where: {
          ...whereOptions,
          isDeleted: false,
        },
        select: this.fieldsToSelect,
        orderBy,
        take: limit,
        skip,
      });
    const count: number = await this.prisma.manufacturer.count({
      where: {
        ...whereOptions,
        isDeleted: false,
      },
    });

    this.logger.log(ManufacturerMessageConsts.successGetAllManufacturers);
    return { manufacturers, count };
  }

  async update(
    id: string,
    manufacturerDto: ManufacturerDto,
    user: User,
  ): Promise<ManufacturerResponseDto> {
    let manufacturer: ManufacturerResponseDto;
    try {
      const updatedManufacturerResponse: ManufacturerResponseDto =
        await this.prisma.$transaction(async (prisma) => {
          /** checking whether manufacturer exists */
          manufacturer = await prisma.manufacturer.findFirst({
            where: { id },
          });

          if (!manufacturer) {
            this.logger.log(
              `${ManufacturerMessageConsts.failedUpdateManufacturer} having id ${id}`,
            );
            throw new NotFoundException(MANUFACTURER_NOT_FOUND);
          }

          /** updating manufacturer */
          const updatedManufacturer: ManufacturerResponseDto =
            await prisma.manufacturer.update({
              where: { id },
              data: manufacturerDto,
              select: this.fieldsToSelect,
            });

          this.logger.log(
            `${ManufacturerMessageConsts.successUpdateManufacturer} having id ${updatedManufacturer.id}`,
          );

          // If update successfully, delete previous image attached with manufacturer
          if (
            updatedManufacturer &&
            manufacturer.manufacturerImageUrl &&
            manufacturer.manufacturerImageUrl !==
              manufacturerDto.manufacturerImageUrl
          ) {
            await this.awsService.deleteFile(manufacturer.manufacturerImageUrl);
            this.logger.log(
              'Manufacturer image uploaded on s3 bucket deleted successfully',
            );
          }

          /** history */
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.MANUFACTURER,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedManufacturer.id,
              log: {
                userId: user.id,
                name: user.name,
                manufacturerId: updatedManufacturer.id,
                updatedFields: getUpdatedFields(
                  manufacturer,
                  updatedManufacturer,
                ),
              },
            },
          });
          this.logger.log(
            `Successfully created history for a updated manufacturer having id ${updatedManufacturer.id}`,
          );

          return updatedManufacturer;
        });
      return updatedManufacturerResponse;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (
        manufacturerDto.manufacturerImageUrl &&
        manufacturer.manufacturerImageUrl !==
          manufacturerDto.manufacturerImageUrl
      ) {
        this.awsService.deleteFile(manufacturerDto.manufacturerImageUrl);
        this.logger.log(
          'Manufacturer image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }

      this.logger.log(`Failed to update manufacturer: ${error}`);
      throw error;
    }
  }

  async delete(id: string, user: User): Promise<boolean> {
    const deletedManufacturerResponse: boolean = await this.prisma.$transaction(
      async (prisma) => {
        /** checking whether manufacturer exists */
        const manufacturer: ManufacturerResponseDto =
          await prisma.manufacturer.findFirst({
            where: { id, isDeleted: false },
          });

        if (!manufacturer) {
          this.logger.log(
            `${ManufacturerMessageConsts.failedDeleteManufacturer} having id ${id}`,
          );
          throw new NotFoundException(MANUFACTURER_NOT_FOUND);
        }

        const consumablesUsingManufacturer = await prisma.consumable.findMany({
          where: { manufacturerId: id, isDeleted: false },
        });

        if (consumablesUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }

        const accessoriesUsingManufacturer = await prisma.accessory.findMany({
          where: { manufacturerId: id, isDeleted: false },
        });

        if (accessoriesUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }

        const licensesUsingManufacturer = await prisma.softwareLicense.findMany(
          {
            where: { manufacturerId: id, isDeleted: false },
          },
        );

        if (licensesUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }

        const appliancesUsingManufacturer = await prisma.appliance.findMany({
          where: { manufacturerId: id, isDeleted: false },
        });

        if (appliancesUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }

        const insuranceUsingManufacturer = await prisma.insurance.findMany({
          where: { companyId: id, isDeleted: false },
        });

        if (insuranceUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }

        const assetModelUsingManufacturer = await prisma.assetModel.findMany({
          where: { manufacturerId: id, isDeleted: false },
        });

        if (assetModelUsingManufacturer.length > 0) {
          throw new BadRequestException(
            ManufacturerMessageConsts.failedDeleteManufacturerInEntites,
          );
        }
        /** updating manufacturer */
        const deletedManufacturer: ManufacturerResponseDto =
          await prisma.manufacturer.update({
            where: { id },
            data: { isDeleted: true },
            select: this.fieldsToSelect,
          });

        this.logger.log(
          `${ManufacturerMessageConsts.successDeleteManufacturer} having id ${deletedManufacturer.id}`,
        );

        // If deleted successfully, delete image attached with manufacturer
        if (manufacturer.manufacturerImageUrl && deletedManufacturer) {
          await this.awsService.deleteFile(manufacturer.manufacturerImageUrl);

          this.logger.log(
            'Manufacturer image uploaded on s3 bucket deleted successfully',
          );
        }

        /** history */
        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.MANUFACTURER,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: deletedManufacturer.id,
            log: {
              userId: user.id,
              name: user.name,
              manufacturerId: deletedManufacturer.id,
            },
          },
        });
        this.logger.log(
          `Successfully created history for a deleted manufacturer having id ${deletedManufacturer.id}`,
        );

        return true;
      },
    );
    return deletedManufacturerResponse;
  }
}
