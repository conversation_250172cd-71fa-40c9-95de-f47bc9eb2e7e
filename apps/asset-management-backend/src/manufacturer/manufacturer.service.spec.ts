import { Test, TestingModule } from '@nestjs/testing';
import { ManufacturerService } from './manufacturer.service';
import { Manufacturer } from '@prisma-clients/asset-management-backend';
import { ManufacturerResponseDto } from './dto/manufacturer.dto';
import { User } from 'types';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('ManufacturerService', () => {
  let service: ManufacturerService;
  let prismaService: PrismaService;

  const mockTestId = '073c2022-a405-456a-9d33-844e2c39ac58';
  const mockManufacturerData: Manufacturer = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Company Test',
    contactName: '<PERSON>',
    contactPhoneNumber: '1122334455',
    contactEmail: '<EMAIL>',
    manufacturerImageUrl: 'data:image/jpeghuAe',
    note: 'Sent product catalog, interested in pricing details.',
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  const mockManufacturerResponseData: ManufacturerResponseDto = {
    id: '073c2022-a405-456a-9d33-844e2c39ac58',
    name: 'Company Test',
    contactName: 'Bob Johnson',
    contactPhoneNumber: '1122334455',
    contactEmail: '<EMAIL>',
    manufacturerImageUrl: 'data:image/jpeghuAe',
    note: 'Sent product catalog, interested in pricing details.',
  };

  const mockUser: User = {
    id: 'bb0bae90-c9c0-48ee-bd62-071b8d42ba0e',
    email: '<EMAIL>',
    name: 'Mock Name',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        manufacturers: ['read', 'create', 'update', 'delete'],
      },
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<ManufacturerService>(ManufacturerService);
    prismaService = service['prisma'];
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  /** TEST: create new manufacturer  */
  describe('createManufacturer', () => {
    it('should return valid message and all required properties of newly created manufacturer', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(() =>
          Promise.resolve(mockManufacturerResponseData),
        );

      const result = await service.create(mockManufacturerData, mockUser);
      expect(result).toEqual(mockManufacturerResponseData);
    });
  });

  /** TEST: get manufacturer as per given id  */
  describe('getManufacturer', () => {
    /** API should return valid message and all required properties of existing manufacturer */
    it('should return all required properties of existing manufacturer', async () => {
      jest
        .spyOn(prismaService.manufacturer, 'findFirst')
        .mockResolvedValue(mockManufacturerData);

      const result = await service.findOne(mockTestId);

      expect(result).toEqual({
        ...mockManufacturerResponseData,
        isDeleted: mockManufacturerData.isDeleted,
        createdAt: mockManufacturerData.createdAt,
        updatedAt: mockManufacturerData.updatedAt,
      });
    });
  });

  /** TEST: get all manufacturers  */
  describe('getAllManufacturer', () => {
    /** API should return valid success message and list of existing manufacturers */
    it('should return valid success message and list of existing manufacturers', async () => {
      jest
        .spyOn(prismaService.manufacturer, 'findMany')
        .mockResolvedValue([mockManufacturerData]);
      jest.spyOn(prismaService.manufacturer, 'count').mockResolvedValue(1);

      const result = await service.findAll();

      expect(result.manufacturers).toEqual([
        {
          ...mockManufacturerResponseData,
          isDeleted: mockManufacturerData.isDeleted,
          createdAt: mockManufacturerData.createdAt,
          updatedAt: mockManufacturerData.updatedAt,
        },
      ]);
      expect(result.count).toEqual(1);
    });
  });

  /** TEST: to update existing manufacturer  */
  describe('updateManufacturer', () => {
    /** API should return valid success message and required properties of updated manufacturer */
    it('should return valid success message and required properties of updated manufacturer', async () => {
      jest
        .spyOn(prismaService, '$transaction')
        .mockImplementation(() =>
          Promise.resolve(mockManufacturerResponseData),
        );

      const result = await service.update(
        mockTestId,
        mockManufacturerData,
        mockUser,
      );

      expect(result).toEqual(mockManufacturerResponseData);
    });
  });
});
