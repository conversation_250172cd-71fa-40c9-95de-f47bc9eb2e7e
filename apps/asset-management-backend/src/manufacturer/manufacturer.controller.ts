import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Logger,
  HttpStatus,
  NotFoundException,
  BadRequestException,
  Delete,
  ParseUUIDPipe,
  Req,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ManufacturerService } from './manufacturer.service';
import {
  ManufacturerDto,
  ManufacturerResponseDto,
} from './dto/manufacturer.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { ManufacturerMessageConsts } from 'src/constants/manufacturer-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { INTERNAL_ERROR } from 'src/constants/message-constants';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Manufacturer')
@ApiBearerAuth('access-token')
@Controller('manufacturer')
@UseGuards(PermissionGuard)
export class ManufacturerController {
  private logger = new Logger('ManufacturerController');
  constructor(private readonly manufacturerService: ManufacturerService) {}

  @ApiOperation({
    summary: 'Create new manufacturer',
    description: 'API for creating new manufacturer',
  })
  @ApiCreatedResponse({
    status: HttpStatus.CREATED,
    description: 'Created manufacturer successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<ManufacturerResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Unable to create manufacturer',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Post()
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.MANUFACTURER))
  async create(
    @Body() createManufacturerDto: ManufacturerDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<ManufacturerResponseDto>> {
    this.logger.log('API for creating a new manufacturer');

    const result = await this.manufacturerService.create(
      createManufacturerDto,
      request.user,
    );

    if (!result) {
      this.logger.log('Failed to create manufacturer');
      throw new BadRequestException(
        ManufacturerMessageConsts.failedCreateManufacturer,
      );
    }

    return {
      statusCode: HttpStatus.CREATED,
      message: ManufacturerMessageConsts.successCreateManufacturer,
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Get list of all manufacturers',
    description: 'API is to retrieve all manufacturers.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Get all manufacturers successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<ManufacturerResponseDto[]>),
    },
  })
  @ApiBadRequestResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Unable to retrieve the list of manufacturers',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Get('/')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.MANUFACTURER))
  async findAll(
    @Query() queryFilters?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<ManufacturerResponseDto[]>> {
    this.logger.log('API to get all manufacturers');

    const { manufacturers, count } =
      await this.manufacturerService.findAll(queryFilters);

    if (!manufacturers) {
      throw new BadRequestException(
        ManufacturerMessageConsts.failedGetAllManufacturer,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      message: ManufacturerMessageConsts.successGetAllManufacturers,
      data: manufacturers,
      count,
    };
  }

  @ApiOperation({
    summary: 'Get manufacturer',
    description:
      'API is to retrieve manufacturer which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Get manufacturer successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<ManufacturerResponseDto>),
    },
  })
  @ApiNotFoundResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'No manufacturer found for the provided ID',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Get(':id')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.MANUFACTURER))
  async findOne(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<ManufacturerResponseDto>> {
    this.logger.log('API to get a manufacturer as per Id passed');

    const result = await this.manufacturerService.findOne(id);
    if (!result) {
      throw new NotFoundException(
        ManufacturerMessageConsts.notFoundManufacturer,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: `${ManufacturerMessageConsts.successGetManufacturer} having id ${id}`,
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Update manufacturer',
    description: 'API updates manufacturer which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Updated manufacturer successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<ManufacturerResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Unable to update manufacturer information',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Put(':id')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.MANUFACTURER))
  async update(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Body() ManufacturerDto: ManufacturerDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<ManufacturerResponseDto>> {
    this.logger.log('API to update an existing manufacturer');

    const result = await this.manufacturerService.update(
      id,
      ManufacturerDto,
      request.user,
    );
    if (!result) {
      throw new BadRequestException(
        ManufacturerMessageConsts.failedUpdateManufacturer,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: ManufacturerMessageConsts.successUpdateManufacturer,
      data: result,
    };
  }

  @ApiOperation({
    summary: 'Delete manufacturer',
    description:
      'This API delete manufacturer which have same id as requested.',
  })
  @ApiOkResponse({
    status: HttpStatus.OK,
    description: 'Deleted manufacturer successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<boolean>),
    },
  })
  @ApiBadRequestResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Unable to delete manufacturer',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Bad Request',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @Delete(':id')
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.MANUFACTURER))
  async delete(
    @Param('id', new ParseUUIDPipe()) id: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    this.logger.log('API to delete an existing manufacturer');

    const result = await this.manufacturerService.delete(id, request.user);
    if (!result) {
      throw new BadRequestException(
        ManufacturerMessageConsts.failedDeleteManufacturer,
      );
    }
    return {
      statusCode: HttpStatus.OK,
      message: ManufacturerMessageConsts.successDeleteManufacturer,
      data: result,
    };
  }
}
