import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from './users.service';
import {
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import {
  EntityDetailsDto,
  GetAllAssignedEntitiesResponseDto,
  UpdateUserRoleDto,
  UserFilterQueryParamsDto,
  UserHistoryResponsePayload,
  UsersResponseDto,
} from './dto/users.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiProduces,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { Request } from 'express';
import {
  INTERNAL_ERROR,
  USER_NOT_FOUND,
} from 'src/constants/message-constants';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
@ApiTags('Users')
@Controller('users')
@UseGuards(PermissionGuard)
export class UsersController {
  private logger = new Logger('UsersController');

  constructor(private readonly usersService: UsersService) {}

  @ApiOperation({
    description: 'This API retrieves a list of all users.',
    summary: 'API for retrieving all users',
  })
  @ApiExtraModels(HTTPResponseDto<UsersResponseDto[]>)
  @ApiOkResponse({
    description: 'Retrieved all users successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<UsersResponseDto[]>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve users',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Get()
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.USER))
  async findAll(
    @Query() queryFilters?: UserFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<UsersResponseDto[]>> {
    this.logger.log('API for retrieving all users');
    const { users, count } = await this.usersService.findAll(queryFilters);
    return {
      message: 'Users retrieved successfully',
      statusCode: HttpStatus.OK,
      data: users,
      count,
    };
  }

  @ApiOperation({
    description: 'This API retrieves a list all assets assigned to user',
    summary: 'API for retrieving user assets list',
  })
  @ApiBearerAuth('access-token')
  @Get('history')
  async history(@Req() req: Request) {
    const { data } = await this.usersService.getAllHistoryOfUser(req.user);
    return {
      message: 'User history retrived successfully',
      statusCode: HttpStatus.OK,
      data,
    };
  }

  @ApiOperation({
    description: 'This API retrieves user by id.',
    summary: 'API for retrieving user by id',
  })
  @ApiOkResponse({
    description: 'Retrieved user successfully',
    status: HttpStatus.OK,
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<UsersResponseDto>),
    },
  })
  @ApiExtraModels(HTTPResponseDto<UsersResponseDto>)
  @ApiBadRequestResponse({
    description: 'Unable to retrieve user by id',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.BAD_REQUEST,
              message: 'Unable to retrieve user by id',
              error: 'Bad request',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'User with given id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: `User not found / already deleted`,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Get(':id')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.USER))
  async findOne(
    @Param('id', new ParseUUIDPipe()) id: string,
  ): Promise<HTTPResponseDto<UsersResponseDto>> {
    this.logger.log('API to get user by id');
    const user = await this.usersService.findOne(id);
    return {
      message: 'User retrived successfully',
      statusCode: HttpStatus.OK,
      data: user,
    };
  }

  @ApiOperation({
    description: 'This API retrieves a list all assets assigned to user',
    summary: 'API for retrieving user assets list',
  })
  @ApiOkResponse({
    description: 'Retrieved all assets assigned to logged in user successfully',
    status: HttpStatus.OK,
    type: HTTPResponseDto<GetAllAssignedEntitiesResponseDto>,
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiBearerAuth('access-token')
  @Get('entities/all')
  async GetAllAssignedEntitiesOfUser(
    @Req() req: Request,
  ): Promise<GetAllResponseDto<EntityDetailsDto[]>> {
    const { data, count } =
      await this.usersService.GetAllAssignedEntitiesOfUser(req.user.id);

    return {
      message: 'User assets retrived successfully',
      statusCode: HttpStatus.OK,
      data: data,
      count: count,
    };
  }

  @ApiBearerAuth('access-token')
  @Get('entities/:userId')
  async GetAllAssignedEntities(
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<GetAllResponseDto<EntityDetailsDto[]>> {
    const { data, count } =
      await this.usersService.GetAllAssignedEntitiesOfUser(userId);

    return {
      message: 'User assignments retrived successfully',
      statusCode: HttpStatus.OK,
      data: data,
      count: count,
    };
  }

  @Put(':userId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated user role',
    schema: { $ref: getSchemaPath(HTTPResponseDto<UsersResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'user not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: USER_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the user role',
    summary: 'Update user role',
  })
  @ApiParam({
    name: 'userId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter user Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.USER))
  async UpdateUserRole(
    @Param('userId', new ParseUUIDPipe()) userId: string,
    @Body() dto: UpdateUserRoleDto,
  ): Promise<HTTPResponseDto<UsersResponseDto>> {
    const user = await this.usersService.updateUserRole(userId, dto);

    return {
      message: 'User updated successfully',
      statusCode: HttpStatus.OK,
      data: user,
    };
  }

  @ApiBearerAuth('access-token')
  @Get('history/:userId')
  async UserHistory(
    @Param('userId', new ParseUUIDPipe()) userId: string,
  ): Promise<HTTPResponseDto<UserHistoryResponsePayload[]>> {
    const result = await this.usersService.getHistoryOfUser(userId);
    const data = result.data;

    return {
      message: 'User history retrieved successfully',
      statusCode: HttpStatus.OK,
      data,
    };
  }

  @ApiBearerAuth('access-token')
  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './uploads',
        filename(req, file, callback) {
          const randomName = Array(32)
            .fill(null)
            .map(() => Math.round(Math.random() * 16).toString(16))
            .join('');
          callback(null, `${file.originalname}_${randomName}`);
        },
      }),
    }),
  )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    required: true,
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiProduces('multipart/form-data')
  @ApiOkResponse({
    description: 'Successfully uploaded file',
    schema: { $ref: getSchemaPath(HTTPResponseDto<{ fileName: string }>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    content: {
      'multipart/form-data': {
        example: {
          statusCode: HttpStatus.UNAUTHORIZED,
          message: 'Unauthorized',
        },
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid file',
    content: {
      'multipart/form-data': {
        examples: {
          example1: {
            value: {
              statuscode: HttpStatus.BAD_REQUEST,
              message: 'file type should be csv',
              error: 'Bad Request',
            },
            summary: 'Invalid file type',
          },
          example2: {
            value: {
              statuscode: HttpStatus.BAD_REQUEST,
              message:
                'Missing required headers: Name, Email, EmployeeId, PhoneNumber',
              error: 'Bad Request',
            },
            summary: 'Invalid file header',
          },
          example3: {
            value: {
              statuscode: HttpStatus.BAD_REQUEST,
              message:
                'No file was uploaded. Please attach a CSV file and try again.',
              error: 'Bad Request',
            },
            summary: 'Empty file',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    content: {
      'multipart/form-data': {
        example: {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: INTERNAL_ERROR,
          error: 'Internal Server Error',
        },
      },
    },
  })
  async uploadUserData(
    @UploadedFile() file: Express.Multer.File,
  ): Promise<HTTPResponseDto<{ fileName: string }>> {
    await this.usersService.uploadUserData(file);
    return {
      statusCode: HttpStatus.OK,
      data: { fileName: file.filename.split('_')[0] },
      message: 'successfully uploaded the file',
    };
  }
}
