import { ApiProperty } from '@nestjs/swagger';
import {
  ChangesOcccuredIn,
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { JsonValue, Role } from 'types';

export class UsersResponseDto {
  @ApiProperty({
    description: 'User Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'User name',
    type: 'string',
    example: 'John Do<PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Email of the user',
    type: 'string',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Role of the user',
    type: 'object',
    example: {
      id: 'cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca',
      name: 'Admin',
      permissions: {
        assets: ['read', 'create'],
        suppliers: ['read'],
      },
    },
  })
  role: Role;

  @ApiProperty({
    description: 'Phone number of the user',
    type: 'string',
    example: '+************',
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Employee Id',
    example: 'CC357',
  })
  employeeId: string;

  @ApiProperty({
    description: "User's department details",
    type: 'object',
    example: {
      id: 'cf1c4e5b-83bb-48fb-937e-e8a5c3e95bca',
      name: 'Web Development',
    },
  })
  department: {
    id: string;
    name: string;
  };
}

export class GetAllUsersResponseDto {
  @ApiProperty({
    description: 'List of Users',
    type: UsersResponseDto,
    isArray: true,
  })
  users: UsersResponseDto[];

  @ApiProperty({
    description: 'Total count of users',
    type: 'number',
    example: 10,
  })
  count: number;
}

export interface IGetAllAssignedEntities {
  id: string;
  name: string;
  entityType: TypeOfCategory;
  assignedAt: Date;
  modelNumber?: string;
  entityImageUrl?: string;
  assetTag?: string;
  serialNumber?: string;
  productKey?: string;
  expiryDate?: Date;
  modelName?: string;
  assetName?: string;
  count?: number;
  isPending?: boolean;
  userId?: string;
  location?: string;
  assignmentId?: string;
  assignedBy?: string;
}

export class EntityDetailsDto implements IGetAllAssignedEntities {
  constructor(dto?: IGetAllAssignedEntities) {
    if (dto) {
      this.id = dto.id;
      this.name = dto.name;
      this.entityType = dto.entityType;
      this.assignedAt = dto.assignedAt;
      this.modelNumber = dto?.modelNumber;
      this.entityImageUrl = dto?.entityImageUrl;
      this.assetTag = dto?.assetTag;
      this.assetName = dto?.assetName;
      this.serialNumber = dto?.serialNumber;
      this.productKey = dto?.productKey;
      this.expiryDate = dto?.expiryDate;
      this.modelName = dto?.modelName;
      this.isPending = dto?.isPending;
      this.userId = dto?.userId;
      this.location = dto?.location;
      this.count = dto?.count;
      this.assignmentId = dto?.assignmentId;
      this.assignedBy = dto.assignedBy;
    }
  }
  @ApiProperty()
  id: string;
  @ApiProperty()
  name: string;
  @ApiProperty()
  modelNumber: string;
  @ApiProperty()
  entityImageUrl: string;
  @ApiProperty()
  entityType: TypeOfCategory;
  @ApiProperty()
  assignedAt: Date;
  @ApiProperty()
  assetTag?: string;
  @ApiProperty()
  serialNumber?: string;
  @ApiProperty()
  productKey?: string;
  @ApiProperty()
  expiryDate?: Date;
  @ApiProperty()
  modelName?: string;
  @ApiProperty()
  assetName?: string;
  @ApiProperty()
  isPending?: boolean;
  @ApiProperty()
  userId?: string;
  @ApiProperty()
  location?: string;
  @ApiProperty()
  assignmentId?: string;
  @ApiProperty()
  count?: number;
  @ApiProperty()
  assignedBy?: string;

  static getEntityDetailsObject(entityDetails: IGetAllAssignedEntities) {
    return new EntityDetailsDto(getIEntityDetails(entityDetails));
  }
}

function getIEntityDetails(entityDetails): IGetAllAssignedEntities {
  const imageUrlField = Object.keys(entityDetails).find(
    (key) =>
      typeof entityDetails[key] === 'string' &&
      key.toLowerCase().includes(`imageurl`),
  );
  return {
    ...entityDetails,
    entityImageUrl: imageUrlField ? entityDetails[imageUrlField] : '',
    modelName:
      entityDetails.entityType === TypeOfCategory.ASSET
        ? entityDetails?.assetModel?.modelName ?? ''
        : undefined,
    expiryDate:
      entityDetails.entityType === TypeOfCategory.SOFTWARE_LICENSE
        ? entityDetails?.expiryDate ?? ''
        : undefined,
  };
}

export class GetAllAssignedEntitiesResponseDto {
  @ApiProperty({
    type: [EntityDetailsDto],
    description: 'List of assets',
    isArray: true,
  })
  @ValidateNested({ each: true })
  @Type(() => EntityDetailsDto)
  data: EntityDetailsDto[];
  @ApiProperty({
    type: Number,
    description: 'Total count of appliance',
  })
  count: number;
}

export class UserFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'role name',
    type: 'string',
    example: 'admin',
    required: false,
  })
  @IsOptional()
  @IsString()
  role: string;
}
export class UserHistoryResponsePayload {
  @ApiProperty({
    description: 'Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;
  @ApiProperty({
    description: 'Action performed on the entity',
    enum: HistoryActions,
    example: HistoryActions.CREATED,
  })
  action: HistoryActions;

  @ApiProperty({
    description: 'Date of the action',
    type: 'date',
    example: '2021-01-01T00:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Log of the action',
    type: 'object',
    example: {
      action: HistoryActions.CREATED,
      date: '2021-01-01T00:00:00.000Z',
      entity: TypeOfCategory.CONSUMABLE,
    },
  })
  log: JsonValue;

  @ApiProperty({
    description: 'changes occurred table',
    enum: ChangesOcccuredIn,
    example: ChangesOcccuredIn.ACCESSORY,
  })
  changeInTable: ChangesOcccuredIn;
  @ApiProperty({
    description: 'ID of the associated entity',
    type: 'string',
  })
  entityId: string;
  @ApiProperty({
    description: 'Name of the associated entity',
    type: 'string',
  })
  entityName?: string;
}

export class UserHistoryListResponse {
  @ApiProperty({
    type: [UserHistoryResponsePayload],
    description: 'List of history of user',
    isArray: true,
  })
  data: UserHistoryResponsePayload[];
}

export class UpdateUserRoleDto {
  @ApiProperty({
    description: 'Id of the Role',
    type: 'string',
    example: 'ff84d492-d03f-4533-9fd0-2f7da38fbd2b',
  })
  @IsUUID()
  roleId: string;
}
export class UserResourceDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  employeeId: string;

  @IsString()
  phoneNumber: string;

  roleId: string;
}
