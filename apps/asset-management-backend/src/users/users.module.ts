import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { AssignmentService } from 'src/assignment/assignment.service';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [UsersController],
  providers: [UsersService, AssignmentService],
  exports: [UsersService],
})
export class UsersModule {}
