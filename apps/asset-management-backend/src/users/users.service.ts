import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { USER_NOT_FOUND } from 'src/constants/message-constants';
import { validateOrReject } from 'class-validator';
import * as fs from 'fs';
import * as csv from 'fast-csv';
import {
  EntityDetailsDto,
  GetAllAssignedEntitiesResponseDto,
  UsersResponseDto,
  UserFilterQueryParamsDto,
  UserHistoryListResponse,
  UpdateUserRoleDto,
  UserResourceDto,
} from './dto/users.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  Prisma,
  Role,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { AssignmentService } from 'src/assignment/assignment.service';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { User } from 'types';

@Injectable()
export class UsersService {
  private logger = new Logger('UsersService');

  private selectArgs = {
    id: true,
    name: true,
    email: true,
    employeeId: true,
    phoneNumber: true,
    department: { select: { id: true, name: true } },
    role: {
      select: {
        id: true,
        name: true,
        permissions: true,
      },
    },
  };
  constructor(
    private prisma: PrismaService,
    private assignmentService: AssignmentService,
  ) {}

  /**
   * Retrieves a list of users based on the provided query filters.
   * @param queryFilters - Optional query filters to apply.
   * @returns A promise that resolves to an object containing the retrieved users and a count of total users.
   */
  async findAll(queryFilters?: UserFilterQueryParamsDto) {
    const page: number | null = queryFilters?.page ? queryFilters.page : null;
    const limit: number | undefined = queryFilters?.limit
      ? queryFilters.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryFilters?.sortBy || 'createdAt']: queryFilters?.sortOrder || 'desc',
    };
    const whereOptions: Prisma.UserWhereInput = queryFilters?.searchInput
      ? {
          OR: [
            {
              name: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
            {
              email: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
            {
              employeeId: {
                contains: queryFilters.searchInput,
                mode: 'insensitive',
              },
            },
          ],
        }
      : undefined;

    const roleFilter: Prisma.UserWhereInput = queryFilters?.role
      ? {
          role: {
            name: {
              equals: queryFilters.role,
              mode: 'insensitive',
            },
          },
        }
      : {};

    const users = await this.prisma.user.findMany({
      where: {
        ...whereOptions,
        ...roleFilter,
        isDeleted: false,
      },
      select: this.selectArgs,
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.user.count({
      where: {
        ...whereOptions,
        ...roleFilter,
        isDeleted: false,
      },
    });
    this.logger.log('Users retrieved successfully');
    return { users, count };
  }

  /**
   * Retrieves a single user by their ID.
   * @param id - The ID of the user to retrieve.
   * @returns A promise that resolves to a UserResponseDto object if the user is found, otherwise throws a NotFoundException.
   */
  async findOne(id: string): Promise<UsersResponseDto> {
    const user = await this.prisma.user.findFirst({
      where: { id },
      select: this.selectArgs,
    });

    if (!user) {
      this.logger.log(`User with ID: ${id} not found`);
      throw new NotFoundException(USER_NOT_FOUND);
    }
    this.logger.log(`User with id ${user.id} retrieved successfully`);

    return user;
  }

  /**
   * Retrieves all entities assigned to a user with additional information including counts.
   * @param userId - The unique identifier of the user.
   * @param filters - Additional filters for the query
   * @returns A Promise that resolves to an object containing the data and count of assigned entities.
   */
  async GetAllAssignedEntitiesOfUser(
    userId: string,
  ): Promise<GetAllAssignedEntitiesResponseDto> {
    try {
      const [listOfEntity, count] = await this.prisma.$transaction([
        this.prisma.assignment.findMany({
          where: {
            userId,
          },
        }),
        this.prisma.assignment.count({
          where: {
            userId,
          },
        }),
      ]);

      const entityMap = listOfEntity.reduce(
        (ac, cv) => {
          if (ac.has(cv.typeOfAssignment)) {
            const existingEntity = ac
              .get(cv.typeOfAssignment)
              .find((entity) => entity.entityId === cv.entityId);

            if (existingEntity) {
              existingEntity.count += 1;
            } else {
              ac.get(cv.typeOfAssignment).push({
                id: cv.id,
                entityId: cv.entityId,
                assignedAt: cv.createdAt,
                count: 1,
                isPending: cv.isPending,
                userId: cv.userId,
              });
            }
          } else {
            ac.set(cv.typeOfAssignment, [
              {
                id: cv.id,
                entityId: cv.entityId,
                assignedAt: cv.createdAt,
                count: 1,
                isPending: cv.isPending,
                userId: cv.userId,
              },
            ]);
          }
          return ac;
        },
        new Map<
          TypeOfCategory,
          {
            id: string;
            entityId: string;
            assignedAt: Date;
            count: number;
            isPending: boolean;
            userId: string;
          }[]
        >(),
      );

      const results = (
        await Promise.all(
          Array.from(entityMap, async ([name, value]) => {
            const mapObj = value.map((item) => item.entityId);

            const entities = await this.prisma[
              name
                .toLowerCase()
                .replace(/_./g, (match) => match.charAt(1).toUpperCase())
            ].findMany({
              where: {
                id: {
                  in: mapObj,
                },
                isDeleted: false,
              },
              include:
                name === TypeOfCategory.ASSET
                  ? {
                      assetModel: {
                        select: {
                          modelName: true,
                        },
                      },
                    }
                  : undefined,
            });

            const entityWithAssignmentDetails = entities.map(
              (entity: EntityDetailsDto) => {
                const assignment = value.find(
                  (item) => item.entityId === entity.id,
                );
                return {
                  ...entity,
                  entityType: name,
                  assignedAt: assignment.assignedAt,
                  count: assignment.count,
                  isPending: assignment.isPending,
                  userId: assignment.userId,
                  assignmentId: assignment.id,
                };
              },
            );

            return entityWithAssignmentDetails;
          }),
        )
      ).flat();

      const entityResults = results.map((result) =>
        EntityDetailsDto.getEntityDetailsObject(result),
      );

      entityResults.sort((a, b) => {
        if (a.isPending === b.isPending) {
          return 0;
        } else if (a.isPending) {
          return -1;
        } else {
          return 1;
        }
      });

      return {
        data: entityResults,
        count: count,
      };
    } catch (error) {
      this.logger.error('Failed to fetch all entities assigned to user');
      throw error;
    }
  }

  /**
   * Retrieves the entity name based on the provided changeInTable value.
   * @param changeInTable - The value representing the change in the table.
   * @param entityId - The ID of the entity.
   * @returns The entity name.
   */
  private async getEntityName(
    changeInTable: string,
    entityId: string | null,
  ): Promise<string> {
    if (entityId === null) {
      return '';
    }
    const emptyStringValues = [
      'SERVICE',
      'CUSTOM_FIELD',
      'FIELDGROUP',
      'TRANSFER',
      'GLOBAL_NOTIFICATION',
    ];

    if (emptyStringValues.includes(changeInTable)) {
      return '';
    }
    if (changeInTable === 'ASSIGNMENT') {
      const assignment = await this.prisma.assignment.findFirst({
        where: { entityId },
      });
      if (assignment) {
        const assignmentEntity =
          await this.assignmentService.findAssignmentById(assignment.id);
        if (assignment.typeOfAssignment === 'ASSET') {
          return assignmentEntity.entityDetails?.assetName || '';
        } else {
          return assignmentEntity.entityDetails.name || '';
        }
      }
      return '';
    }

    if (changeInTable === 'VENDOR_EVALUATION') {
      const vendorEntity = await this.prisma.vendorEvaluation.findFirst({
        where: {
          id: entityId,
        },
        include: {
          supplier: {
            select: {
              name: true,
            },
          },
        },
      });
      return vendorEntity.supplier.name || '';
    }

    const entity = await this.prisma[
      changeInTable
        .toLowerCase()
        .replace(/_./g, (match) => match.charAt(1).toUpperCase())
    ].findFirst({
      where: { id: entityId },
    });
    return entity?.assetName || entity?.modelName || entity?.name || '';
  }
  /**
   * Retrieves all history of a user including the corresponding entity names.
   * @param queryFilters - Optional query filters for pagination and sorting.
   * @returns A promise that resolves to user history data with entity names.
   */
  async getAllHistoryOfUser(
    user: User,
    queryFilters?: GetAllQueryParamsDto,
  ): Promise<UserHistoryListResponse> {
    try {
      const page: number | null = queryFilters?.page;
      const limit: number | undefined = queryFilters?.limit;

      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = [
        {
          [queryFilters?.sortBy || 'createdAt']:
            queryFilters?.sortOrder || 'desc',
        },
        {
          [queryFilters?.sortBy || 'updatedAt']:
            queryFilters?.sortOrder || 'desc',
        },
      ];

      const history = await this.prisma.history.findMany({
        orderBy,
        take: limit,
        skip,
      });

      const userHistory = history.filter((item) => {
        return JSON.stringify(item.log).includes(user.id);
      });

      const formattedHistory = await Promise.all(
        userHistory.map(async (item) => {
          const entityName = await this.getEntityName(
            item.changeInTable,
            item.entityId,
          );

          return { entityName, ...item };
        }),
      );
      this.logger.log(`Fetched history for User`);
      return {
        data: formattedHistory,
      };
    } catch (error) {
      this.logger.error('Failed to fetch history of user');
      throw error;
    }
  }

  /**
   * Updates the role of a user based on the provided user ID.
   * @param id - The ID of the user whose role needs to be updated.
   * @param dto - The dto ontaining the new role ID.
   * @returns - A promise that resolves to the updated user details.
   */
  async updateUserRole(
    id: string,
    dto: UpdateUserRoleDto,
  ): Promise<UsersResponseDto> {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          id,
          isDeleted: false,
        },
      });
      if (!user) {
        this.logger.log(`User with ID: ${id} not found`);
        throw new NotFoundException(USER_NOT_FOUND);
      }

      const updatedUser = await this.prisma.user.update({
        where: {
          id,
        },
        data: {
          role: dto.roleId
            ? {
                connect: {
                  id: dto.roleId,
                },
              }
            : undefined,
        },
        select: this.selectArgs,
      });
      return updatedUser;
    } catch (error) {
      this.logger.log(`Failed to update user: ${error}`);
      throw error;
    }
  }

  async getHistoryOfUser(
    userId: string,
    queryFilters?: GetAllQueryParamsDto,
  ): Promise<UserHistoryListResponse> {
    try {
      const page: number | null = queryFilters?.page;
      const limit: number | undefined = queryFilters?.limit;

      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = [
        {
          [queryFilters?.sortBy || 'createdAt']:
            queryFilters?.sortOrder || 'desc',
        },
        {
          [queryFilters?.sortBy || 'updatedAt']:
            queryFilters?.sortOrder || 'desc',
        },
      ];

      const history = await this.prisma.history.findMany({
        orderBy,
        take: limit,
        skip,
      });

      const userHistory = history.filter((item) => {
        return JSON.stringify(item.log).includes(userId);
      });

      const formattedHistory = await Promise.all(
        userHistory.map(async (item) => {
          const entityName = await this.getEntityName(
            item.changeInTable,
            item.entityId,
          );

          return { entityName, ...item };
        }),
      );

      this.logger.log(`Fetched history for User`);
      return {
        data: formattedHistory,
      };
    } catch (error) {
      this.logger.error('Failed to fetch history of user');
      throw error;
    }
  }

  async uploadUserData(file: Express.Multer.File): Promise<void> {
    let batchData: UserResourceDto[] = [];
    const requiredHeaders = ['Name', 'Email', 'EmployeeId', 'PhoneNumber'];
    const batchSize = 500;
    let batchCount = 0;

    // Validate file existence and type upfront
    if (!file || !file.originalname.toLocaleLowerCase().includes('.csv')) {
      if (file) {
        fs.unlinkSync(file.path); // Ensure file cleanup
      }
      throw new BadRequestException(
        'Invalid file or file type. Please upload a CSV file.',
      );
    }

    const role: Role = await this.prisma.role.findFirst({
      where: { name: 'employee' },
    });

    await new Promise<void>((resolve, reject) => {
      const stream = fs.createReadStream(file.path);
      const csvStream = csv.parse({ headers: true });

      // Stream error handling for file read errors
      stream.on('error', (error) => {
        this.logger.error(`File read error: ${error.message}`);
        reject(
          new InternalServerErrorException('Error reading the uploaded file'),
        );
      });

      csvStream
        .on('headers', (headers) => {
          // Check for missing required headers
          const missingHeaders = requiredHeaders.filter(
            (header) => !headers.includes(header),
          );

          if (missingHeaders.length > 0) {
            csvStream.destroy(); // Stop further processing
            return reject(
              new BadRequestException(
                `Missing required headers: ${missingHeaders.join(', ')}`,
              ),
            );
          }

          // Check for duplicate headers
          const headerSet = new Set(headers);
          if (headerSet.size !== headers.length) {
            const duplicates = headers.filter(
              (item, index) => headers.indexOf(item) !== index,
            );
            csvStream.destroy(); // Stop further processing
            return reject(
              new BadRequestException(
                `Duplicate column names found: ${duplicates.join(', ')}`,
              ),
            );
          }
        })
        .on('data', async (row) => {
          const user = {
            name: row.Name,
            email: row.Email,
            employeeId: row.EmployeeId,
            phoneNumber: row.PhoneNumber,
            roleId: role?.id,
          };

          try {
            await validateOrReject(user); // Validate row data
          } catch (error) {
            this.logger.error('Invalid row encountered, skipping: ', row);
            return; // Skip invalid row but continue processing
          }

          batchData.push(user);
          if (batchData.length >= batchSize) {
            csvStream.pause(); // Pause processing while handling batch

            try {
              await this.processBatchWithTransaction(batchData);
              batchData = []; // Reset batch data after successful insertion
              batchCount++;
              this.logger.log(`Batch ${batchCount} inserted successfully.`);
            } catch (error) {
              this.logger.error('Batch processing failed: ', error.message);
              return reject(
                new InternalServerErrorException('Error processing batch data'),
              );
            }

            csvStream.resume(); // Resume processing after batch completion
          }
        })
        .on('end', async () => {
          if (batchData.length > 0) {
            try {
              await this.processBatchWithTransaction(batchData);
              this.logger.log('Final batch inserted successfully.');
            } catch (error) {
              this.logger.error(
                'Final batch processing error: ',
                error.message,
              );
              return reject(
                new InternalServerErrorException(
                  'Error processing final batch',
                ),
              );
            }
          }
          resolve();
        })
        .on('error', (error) => {
          this.logger.error(`CSV stream error: ${error.message}`);
          reject(
            new InternalServerErrorException('Error during CSV processing'),
          );
        });

      stream.pipe(csvStream);
    }).finally(() => {
      if (file) {
        batchData = [];
        fs.unlinkSync(file.path); // Ensure file cleanup
      }
    });
  }

  private async processBatchWithTransaction(batch: UserResourceDto[]) {
    // Prisma transaction to ensure atomicity across the entire batch
    await this.prisma.$transaction(async () => {
      await this.prisma.user.createMany({
        data: batch,
        skipDuplicates: true, // Skip duplicates based on unique constraints
      });
    });
  }
}
