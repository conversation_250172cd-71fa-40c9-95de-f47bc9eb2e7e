import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { User } from '@prisma-clients/asset-management-backend';
import { NotFoundException } from '@nestjs/common';
import { USER_NOT_FOUND } from 'src/constants/message-constants';
import { UsersResponseDto } from './dto/users.dto';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('UsersService', () => {
  let service: UsersService;
  let prismaService: PrismaService;
  const mockUserId = '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92';
  const invalidUserId = '59f4e70ee-61ca-40fa-bd0a-9e67df3a8c92';
  const mockUserResponeData: UsersResponseDto = {
    id: mockUserId,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        assets: ['read'],
      },
    },
    employeeId: 'CC500',
    phoneNumber: '91234567890',
    department: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'Development',
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<UsersService>(UsersService);
    prismaService = service['prisma'];
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllusers', () => {
    it('should return list of existing users', async () => {
      jest
        .spyOn(prismaService.user, 'findMany')
        .mockResolvedValue([mockUserResponeData as unknown as User]);
      jest.spyOn(prismaService.user, 'count').mockResolvedValue(1);

      const result = await service.findAll();
      expect(result.users).toEqual([mockUserResponeData]);
      expect(result.count).toEqual(1);
    });
  });

  describe('getUser', () => {
    it('should retrieve a user by their ID', async () => {
      jest
        .spyOn(prismaService.user, 'findFirst')
        .mockResolvedValue(mockUserResponeData as unknown as User);

      const result = await service.findOne(mockUserId);
      expect(result).toEqual(mockUserResponeData);
    });

    it('should handle invalid user IDs', async () => {
      jest.spyOn(prismaService.user, 'findFirst').mockResolvedValue(null);

      await expect(
        async () => await service.findOne(invalidUserId),
      ).rejects.toThrowError(new NotFoundException(USER_NOT_FOUND));
    });
  });
});
