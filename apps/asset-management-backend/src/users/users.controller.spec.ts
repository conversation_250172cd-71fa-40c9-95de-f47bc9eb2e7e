import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { HttpStatus, NotFoundException } from '@nestjs/common';
import { USER_NOT_FOUND } from 'src/constants/message-constants';
import { UsersResponseDto } from './dto/users.dto';
import {
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { AppModule } from 'src/app.module';

describe('UsersController', () => {
  let controller: UsersController;
  let mockUserService: UsersService;

  const mockUserId = '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92';
  const invalidUserId = '59f4e70-61ca-40fa-bd0a-9e67df3a8c92';
  const mockUserResponeData: UsersResponseDto = {
    id: mockUserId,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'admin',
      permissions: {
        assets: ['read'],
      },
    },
    employeeId: 'CC500',
    phoneNumber: '91234567890',
    department: {
      id: '59f4e70d-61ca-40fa-bd0a-9e67df3a8c92v',
      name: 'Development',
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    mockUserService = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllUsers', () => {
    it('it should retrieve all users successfully', async () => {
      const expectedResponse: GetAllResponseDto<UsersResponseDto[]> = {
        statusCode: HttpStatus.OK,
        data: [mockUserResponeData],
        count: 1,
        message: 'Users retrieved successfully',
      };

      jest.spyOn(mockUserService, 'findAll').mockResolvedValue(
        Promise.resolve({
          users: expectedResponse.data,
          count: expectedResponse.count,
        }),
      );

      const result = await controller.findAll();

      expect(result).toEqual(expectedResponse);
    });
  });
  describe('getUser', () => {
    it('it should retrieve user by ID successfully', async () => {
      const expectedResponse: HTTPResponseDto<UsersResponseDto> = {
        statusCode: HttpStatus.OK,
        data: mockUserResponeData,
        message: 'User retrived successfully',
      };

      jest
        .spyOn(mockUserService, 'findOne')
        .mockResolvedValue(expectedResponse.data);

      const result = await controller.findOne(mockUserId);

      expect(result).toEqual(expectedResponse);
    });

    it('it should handle not found error when user is not found', async () => {
      jest
        .spyOn(mockUserService, 'findOne')
        .mockRejectedValue(new NotFoundException(USER_NOT_FOUND));
      await expect(controller.findOne(invalidUserId)).rejects.toThrowError(
        NotFoundException,
      );
    });
  });
});
