import { HttpStatus } from '@nestjs/common';

export interface AssetModelDetails {
  id: string;
  modelName: string;
  modelNumber: string;
  note: string;
  assetModelImageUrl: string;
  category: {
    id: string;
    name: string;
  };
  manufacturer: {
    id: string;
    name: string;
  };
}

export interface AssetModelDetailsResponse {
  statusCode: HttpStatus;
  data: AssetModelDetails;
  message: string;
}

export interface AssetModelGetAllDetails {
  id: string;
  modelName: string;
  modelNumber: string;
  note: string;
  assetModelImage: string;
  category: {
    id: string;
    name: string;
  };
  manufacturer: {
    id: string;
    name: string;
  };
  totalAssets: number;
}

export interface AssetModelGetAllDetailsResponse {
  id: string;
  modelName: string;
  modelNumber: string;
  note: string;
  assetModelImageUrl: string;
  category: {
    id: string;
    name: string;
  };
  manufacturer: {
    id: string;
    name: string;
  };
  totalAssets: number;
}

export interface GetAllAssetModelsResponse {
  data: AssetModelGetAllDetailsResponse[];
  totalCount: number;
}

export interface AssetModelGetAllResponse {
  statusCode: HttpStatus;
  data: AssetModelGetAllDetailsResponse[];
  count: number;
  message: string;
}

export interface GetDetailsByAssetModelId {
  id: string;
  modelName: string;
  modelNumber: string;
  note: string;
  assetModelImageUrl: string;
  category: {
    id: string;
    name: string;
  };
  manufacturer: {
    id: string;
    name: string;
  };
  assets: {
    assetStatus: {
      id: string;
      name: string;
      color: string;
    };
    id: string;
    assetTag: string;
    serialNumber: string;
    assetImageUrl: string;
    assetName: string;
  }[];
}

export interface DeleteAssetModelResponse {
  statusCode: HttpStatus;
  data: boolean;
  message: string;
}
