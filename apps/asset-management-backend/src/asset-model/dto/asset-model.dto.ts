import {
  IsOptional,
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { HttpStatus } from '@nestjs/common';
export class CreateAssetModelDto {
  @ApiProperty({
    description: 'The name of the asset model',
    type: 'string',
    example: 'Dell i5 core',
    required: true,
  })
  @IsString()
  modelName: string;

  @ApiProperty({
    description: 'The model number of the asset model',
    type: 'string',
    example: '12334',
    required: false,
  })
  @IsString()
  @IsOptional()
  modelNumber: string;

  @ApiProperty({
    description: 'Additional notes about the asset model',
    example: 'This is a sample asset model',
    type: 'string',
    required: false,
  })
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'URL of the image associated with the asset model',
    example: 'https://example.com/image.jpg',
    type: 'string',
    required: false,
  })
  @IsString()
  @IsOptional()
  assetModelImageUrl: string;

  @ApiProperty({
    description: 'ID of the category to which the asset model belongs',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  categoryId: string;

  @ApiProperty({
    description: 'ID of the manufacturer of the asset model',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
    type: 'string',
  })
  @IsOptional()
  @IsUUID()
  manufacturerId: string;
}

export class AssetModelDto {
  @ApiProperty({
    description: 'The unique identifier of the asset model',
    example: '93cf517a-a745-4c8d-b561-2336966e2d6b',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the asset model',
    example: 'Apple',
  })
  modelName: string;

  @ApiProperty({
    description: 'The model number of the asset model',
    example: '12345',
  })
  modelNumber: string;

  @ApiProperty({
    description: 'Additional notes about the asset model',
    example: 'Hello',
  })
  note: string;

  @ApiProperty({
    description: 'URL of the image associated with the asset model',
    example: 'demo.jpg',
  })
  assetModelImageUrl: string;

  @ApiProperty({
    description:
      'ID of the category to which the asset model belongs (nullable)',
    example: '93cf517a-a745-4c8d-b561-2336966e2d6b',
    required: false,
  })
  categoryId?: string;

  @ApiProperty({
    description: 'ID of the manufacturer of the asset model (nullable)',
    example: '93cf517a-a745-4c8d-b561-2336966e2d6b',
    required: false,
  })
  manufacturerId?: string;
}

export class CreatedAssetModelResponseDto {
  @ApiProperty({ description: 'Status code', type: 'number', example: 201 })
  statusCode: HttpStatus.CREATED;
  @ApiProperty({ description: 'Asset Model Data', type: AssetModelDto })
  data: AssetModelDto;
  @ApiProperty({
    description: 'Success message',
    type: 'string',
    example: 'Successfully created asset model',
  })
  message: 'Successfully created asset model';
}

export class UpdatedAssetModelResponseDto {
  @ApiProperty({ description: 'Status code', type: 'number', example: 200 })
  statusCode: HttpStatus.OK;
  @ApiProperty({ description: 'Asset Model Data', type: AssetModelDto })
  data: AssetModelDto;
  @ApiProperty({
    description: 'Success message',
    type: 'string',
    example: 'Successfully updated asset model',
  })
  message: 'Successfully updated asset model';
}
export enum AssetModelSortBy {
  createdAt = 'createdAt',
  updatedAt = 'updatedAt',
  modelName = 'modelName',
  modelNumber = 'modelNumber',
}

export enum SortOrder {
  asc = 'asc',
  desc = 'desc',
}
export class GetAllAssetModelFilters {
  @ApiProperty({
    description: 'Search for filtering asset models.',
    example: 'Dell',
    required: false,
  })
  @IsString()
  @IsOptional()
  searchInput: string;

  @ApiProperty({
    description: 'List of asset model IDs separated by commas.',
    example:
      '93cf517a-a745-4c8d-b561-2336966e2d6b,74e517bc-7a45-4c8d-b561-2336966e2d6c',
    required: false,
  })
  @IsOptional()
  @IsUUID('all', { each: true })
  @Type(() => String)
  @Transform(({ value }) => value.toString().split(',').map(String))
  assetModelIds?: string[];

  @ApiProperty({
    description: 'Page specified by user',
    type: 'number',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page: number;

  @ApiProperty({
    description: 'Data per page specified by user',
    type: 'number',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit: number;

  @ApiProperty({
    description: 'Sorting field for asset models.',
    enum: AssetModelSortBy,
    required: false,
  })
  @IsEnum(AssetModelSortBy)
  @IsOptional()
  sortBy?: AssetModelSortBy;

  @ApiProperty({
    description: 'Sorting order for asset models.',
    enum: SortOrder,
    required: false,
  })
  @IsEnum(SortOrder)
  @IsOptional()
  sortOrder?: SortOrder;
}

export class GetAllAssetModelsDetailsDto {
  @ApiProperty({
    description: 'The unique identifier of the asset model.',
    example: '93cf517a-a745-4c8d-b561-2336966e2d6b',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the asset model.',
    example: 'Example Model',
  })
  modelName: string;

  @ApiProperty({
    description: 'The model number of the asset model.',
    example: '12345',
  })
  modelNumber: string;

  @ApiProperty({
    description: 'Additional notes about the asset model.',
    example: 'This is a sample asset model.',
  })
  note: string;

  @ApiProperty({
    description: 'URL of the image associated with the asset model.',
    example: 'https://example.com/image.jpg',
  })
  assetModelImageUrl: string;

  @ApiProperty({
    description: 'Details of the category to which the asset model belongs.',
    type: 'object',
    properties: {
      id: {
        type: 'string',
        example: '93cf517a-a745-4c8d-b561-2336966e2d6f',
      },
      name: {
        type: 'string',
        example: 'Laptop',
      },
    },
  })
  category: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description:
      'Details of the manufacturer to which the asset model belongs.',
    type: 'object',
    properties: {
      id: {
        type: 'string',
        example: '93cf517a-a745-4c8d-b561-2336966e2d6a',
      },
      name: {
        type: 'string',
        example: 'APPLE',
      },
    },
  })
  manufacturer: {
    id: string;
    name: string;
  };

  @ApiProperty({
    description: 'Total number of assets associated with the asset model.',
    example: 10,
  })
  totalAssets: number;
}

export class GetAllAssetModelsDetailResponse {
  @ApiProperty({ description: 'Status code', type: 'number', example: 200 })
  statusCode: HttpStatus.OK;
  @ApiProperty({
    description: 'Asset Model Data',
    type: GetAllAssetModelsDetailsDto,
    isArray: true,
  })
  data: GetAllAssetModelsDetailsDto[];
  @ApiProperty({
    description: 'Success message',
    type: 'string',
    example: 'Successfully retrieved all asset models',
  })
  message: 'Successfully retrieved all assets models';
}

class AssetStatus {
  @ApiProperty({
    description: 'The unique identifier of the asset status',
    example: 'b6ff585a-03c8-40d3-a22c-521069d24a9b',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the asset status',
    example: 'IN',
  })
  name: string;

  @ApiProperty({
    description: 'The color associated with the asset status',
    example: 'red',
  })
  color: string;
}

class Asset {
  @ApiProperty({
    description: 'The unique identifier of the asset',
    example: '84279b87-4227-4dcc-9b50-29f3943d68b4',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the asset',
    example: 'Dell',
  })
  assetName: string | null;

  @ApiProperty({
    description: 'The URL of the asset image',
    example: 'demo.jpg',
  })
  assetImageUrl: string | null;

  @ApiProperty({
    description: 'The tag associated with the asset',
    example: 'dell',
  })
  assetTag: string;

  @ApiProperty({
    description: 'The serial number of the asset',
    example: '123',
  })
  serialNumber: string;

  @ApiProperty({ type: AssetStatus })
  assetStatus: AssetStatus;
}

export class GetByAssetModelIdDto extends AssetModelDto {
  @ApiProperty({ type: [Asset] })
  assets: Asset[];
}

export class GetByAssetModelIdResponseDto {
  @ApiProperty({ description: 'Status code', type: 'number', example: 200 })
  statusCode: HttpStatus.OK;
  @ApiProperty({ description: 'Asset Model Data', type: GetByAssetModelIdDto })
  data: GetByAssetModelIdDto;
  @ApiProperty({
    description: 'Success message',
    type: 'string',
    example: 'Successfully retrieved asset model by id',
  })
  message: 'Successfully retrieved asset model by id';
}
