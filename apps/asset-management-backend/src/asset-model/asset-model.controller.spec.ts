import { Test, TestingModule } from '@nestjs/testing';
import { AssetModelController } from './asset-model.controller';
import { AppModule } from 'src/app.module';

describe('AssetModelController', () => {
  let controller: AssetModelController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<AssetModelController>(AssetModelController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
