import { Module } from '@nestjs/common';
import { AssetModelService } from './asset-model.service';
import { AssetModelController } from './asset-model.controller';
import { AbilityModule } from 'src/ability/ability.module';

@Module({
  imports: [AbilityModule],
  controllers: [AssetModelController],
  providers: [AssetModelService],
  exports: [AssetModelService],
})
export class AssetModelModule {}
