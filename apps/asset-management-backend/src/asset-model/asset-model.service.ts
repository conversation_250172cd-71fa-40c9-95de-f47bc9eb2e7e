import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  AssetModelDto,
  AssetModelSortBy,
  CreateAssetModelDto,
  GetAllAssetModelFilters,
  SortOrder,
} from './dto/asset-model.dto';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import {
  AssetModelDetails,
  GetAllAssetModelsResponse,
  GetDetailsByAssetModelId,
} from './interface/asset-model.interface';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from '../utility';
import {
  ASSET_MODEL_NAME_EXISTS,
  ASSET_MODEL_NOT_FOUND,
} from '../constants/message-constants';
import { User } from 'types';
import { PrismaService } from 'src/prisma/prisma.service';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class AssetModelService {
  private logger = new Logger('AssetModelService');
  private selectArgs = {
    id: true,
    modelName: true,
    modelNumber: true,
    note: true,
    assetModelImageUrl: true,
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
  };
  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}

  /**
   * This method creates a assetModel based on the provided DTO if not already exists.
   * Also creates a record in history table for the created assetModel.
   *
   * @param {CreateAssetModelDto} dto The DTO (Data Transfer Object) containing assetModel details.
   * @returns {Promise<AssetModelDetails>} A promise that resolves to the details of the created asset model.
   * @throws ConflictException if a model name with the same name already exists.
   * @throws NotFoundException if the manufacturer, or category with the specified IDs are not found.
   */

  async createAssetModel(
    dto: CreateAssetModelDto,
    user: User,
  ): Promise<AssetModelDetails> {
    try {
      const transaction: AssetModelDetails = await this.prisma.$transaction(
        async (prisma) => {
          // Check if model name already exists
          const existingName: number = await prisma.assetModel.count({
            where: {
              modelName: dto.modelName,
              isDeleted: false,
            },
          });
          if (existingName) {
            this.logger.log(`Asset Model ${dto.modelName} Name already exists`);
            throw new ConflictException(ASSET_MODEL_NAME_EXISTS);
          }

          const assetModel: AssetModelDetails = await prisma.assetModel.create({
            data: {
              modelName: dto.modelName,
              modelNumber: dto.modelNumber,
              note: dto.note,
              assetModelImageUrl: dto.assetModelImageUrl,
              category: dto.categoryId
                ? {
                    connect: {
                      id: dto.categoryId,
                    },
                  }
                : undefined,
              manufacturer: dto.manufacturerId
                ? {
                    connect: {
                      id: dto.manufacturerId,
                    },
                  }
                : undefined,
            },
            select: this.selectArgs,
          });

          this.logger.log(
            `Successfully created assetModel with id:${assetModel.id}`,
          );
          // Capture log(history)
          await prisma.history.create({
            data: {
              date: new Date(),
              changeInTable: ChangesOcccuredIn.ASSET_MODEL,
              action: HistoryActions.CREATED,
              entityId: assetModel?.id,
              log: {
                userId: user.id,
                name: user.name,
                assetModelId: assetModel?.id,
              },
            },
          });
          this.logger.log(
            `Successfully created history for newly created asset model with id:${assetModel.id}`,
          );
          return assetModel;
        },
      );
      return transaction;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (dto.assetModelImageUrl) {
        this.awsService.deleteFile(dto.assetModelImageUrl);
        this.logger.log(
          'Asset-Model image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        this.logger.log(error);
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to create asset model :${error}`);
      throw error;
    }
  }

  /**
   * This method retrieves a paginated list of assetModels based on optional query parameters.
   * It applies pagination, sorting, and search functionality if provided in the query parameters.
   * @param {GetAllAssetModelFilters} dto - Filters to apply to the query.
   * @returns {Promise<AssetModelGetAllResponseWithCount>} - Response containing the asset models and total count.
   */
  async getAllAssetModels(
    dto?: GetAllAssetModelFilters,
  ): Promise<GetAllAssetModelsResponse> {
    try {
      // Pagination
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | undefined = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      // Sorting
      const orderBy = {
        [dto?.sortBy || AssetModelSortBy.createdAt]:
          dto?.sortOrder || SortOrder.desc,
      };
      const where: Prisma.AssetModelWhereInput = {
        isDeleted: false,
        id: {
          in: dto?.assetModelIds,
        },
        ...(dto?.searchInput
          ? {
              OR: [
                {
                  modelName: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  modelNumber: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
                {
                  category: {
                    name: {
                      contains: dto.searchInput,
                      mode: 'insensitive',
                    },
                  },
                },
                {
                  manufacturer: {
                    name: {
                      contains: dto.searchInput,
                      mode: 'insensitive',
                    },
                  },
                },
              ],
            }
          : {}),
      };
      const select: Prisma.AssetModelSelect = {
        ...this.selectArgs,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        manufacturer: {
          select: {
            id: true,
            name: true,
          },
        },
        assets: {
          where: {
            isDeleted: false,
          },
        },
      };

      const retrieveAllAssetModels = await this.prisma.assetModel.findMany({
        orderBy,
        take: limit,
        skip,
        where,
        select,
      });

      // Calculate total assets for each AssetModel record and format the response
      const formatedResponse = retrieveAllAssetModels?.map((assetModel) => ({
        id: assetModel.id,
        modelName: assetModel?.modelName,
        modelNumber: assetModel?.modelNumber,
        note: assetModel?.note,
        assetModelImageUrl: assetModel?.assetModelImageUrl,
        category: assetModel?.category
          ? {
              id: assetModel.category.id,
              name: assetModel.category.name,
            }
          : null,
        manufacturer: assetModel?.manufacturer
          ? {
              id: assetModel.manufacturer.id,
              name: assetModel.manufacturer.name,
            }
          : null,
        totalAssets: assetModel?.assets?.length,
      }));

      const totalCount = await this.prisma.assetModel.count({ where });
      this.logger.log(`Successfully retrieved all asset models`);

      return {
        data: formatedResponse,
        totalCount,
      };
    } catch (error) {
      this.logger.log(`Failed to fetch asset models: ${error}`);
      throw error;
    }
  }

  /**
   * This method retrives assetModel with the specified ID that is not deleted.
   *
   * @param assetModelId - The unique identifier of the asset model.
   * @returns A promise that resolves to the detailed response for the asset model with associated assets and assignments.
   * @throws {NotFoundException} Throws an exception if the asset model is not found.
   */
  async getAssetModelById(
    assetModelId: string,
  ): Promise<GetDetailsByAssetModelId> {
    const assetModel: GetDetailsByAssetModelId =
      await this.prisma.assetModel.findFirst({
        where: {
          id: assetModelId,
          isDeleted: false,
        },
        select: {
          ...this.selectArgs,
          assets: {
            where: {
              isDeleted: false,
            },
            select: {
              id: true,
              assetName: true,
              assetImageUrl: true,
              assetTag: true,
              serialNumber: true,
              assetStatus: {
                select: {
                  id: true,
                  name: true,
                  color: true,
                },
              },
              endOfLife: true,
              location: true,
              assetModelId: true,
              warranty: true,
            },
          },
        },
      });
    if (!assetModel) {
      this.logger.log(`Asset Model ${assetModelId} not found`);
      throw new NotFoundException(ASSET_MODEL_NOT_FOUND);
    }
    return assetModel;
  }

  /**
   * This method verifies the existence of the asset model
   * by its ID and updates the asset model with the provided data.
   * @param {string} assetModelId The ID of the asset model to update.
   * @param dto The DTO containing updated asset model details.
   * @returns {Promise<AssetModelDetails>} A promise that resolves to the details of the updated asset model.
   * @throws NotFoundException if the manufacturer, or category with the specified IDs are not found.
   */
  async updateAssetModel(
    assetModelId: string,
    dto: CreateAssetModelDto,
    user: User,
  ): Promise<AssetModelDetails> {
    let assetModel: AssetModelDto;
    try {
      const transaction: AssetModelDetails = await this.prisma.$transaction(
        async (prisma) => {
          assetModel = await prisma.assetModel.findFirst({
            where: {
              id: assetModelId,
              isDeleted: false,
            },
            select: this.selectArgs,
          });
          if (!assetModel) {
            this.logger.log(`Asset Model ${assetModelId} not found`);
            throw new NotFoundException(ASSET_MODEL_NOT_FOUND);
          }

          const updatedAssetModel: AssetModelDetails =
            await prisma.assetModel.update({
              where: {
                id: assetModelId,
              },
              data: {
                modelName: dto.modelName,
                modelNumber: dto.modelNumber,
                note: dto.note,
                assetModelImageUrl: dto.assetModelImageUrl,
                category: dto.categoryId
                  ? {
                      connect: {
                        id: dto.categoryId,
                      },
                    }
                  : undefined,
                manufacturer: dto.manufacturerId
                  ? {
                      connect: {
                        id: dto.manufacturerId,
                      },
                    }
                  : undefined,
              },

              select: this.selectArgs,
            });

          this.logger.log(
            `Successfully updated asset model with id:${assetModelId}`,
          );

          // If update successfully, delete previous image attached with asset-model
          if (
            updatedAssetModel &&
            assetModel.assetModelImageUrl &&
            assetModel.assetModelImageUrl !== dto.assetModelImageUrl
          ) {
            await this.awsService.deleteFile(assetModel.assetModelImageUrl);
            this.logger.log(
              'Asset-Model image uploaded on s3 bucket deleted successfully',
            );
          }

          // capture logs(history)

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.ASSET_MODEL,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedAssetModel.id,
              log: {
                userId: user.id,
                name: user.name,
                assetModelName: updatedAssetModel.modelName,
                updatedFields: getUpdatedFields(assetModel, updatedAssetModel),
              },
            },
          });

          this.logger.log(
            `Successfully created history for updated asset model with id:${updatedAssetModel.id}`,
          );
          return updatedAssetModel;
        },
      );
      return transaction;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (
        dto.assetModelImageUrl &&
        assetModel.assetModelImageUrl !== dto.assetModelImageUrl
      ) {
        this.awsService.deleteFile(dto.assetModelImageUrl);
        this.logger.log(
          'Asset-Model image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.log(`Failed to update asset model: ${error}`);
      throw error;
    }
  }

  /**
   * Deletes asset model by marking them as deleted in the database.
   *
   * @param {string} assetModelId - The asset model ID to be deleted.
   * @returns  {Promise<AssetModel>}A promise that resolves to the batch payload indicating the number of asset model deleted.
   * @throws {NotFoundException} Throws a not found exception if the asset model with the specified ID are not found.
   * @throws {Error} Throws an error if the deletion process fails.
   */
  async deleteAssetModel(assetModelId: string, user: User): Promise<boolean> {
    try {
      const transaction: boolean = await this.prisma.$transaction(
        async (prisma) => {
          const assetModel = await prisma.assetModel.findFirst({
            where: {
              id: assetModelId,
              isDeleted: false,
            },
          });
          if (!assetModel) {
            this.logger.log(`Asset Model ${assetModelId} not found`);
            throw new NotFoundException(ASSET_MODEL_NOT_FOUND);
          }
          const deletedAssetModel = await prisma.assetModel.update({
            where: {
              id: assetModelId,
            },
            data: {
              isDeleted: true,
            },
          });
          this.logger.log(
            `Successfully deleted asset model with id:${deletedAssetModel.id}`,
          );

          // If accessory deleted successfully, delete image attached with asset-model.
          if (assetModel.assetModelImageUrl && deletedAssetModel) {
            await this.awsService.deleteFile(assetModel.assetModelImageUrl);

            this.logger.log(
              'Asset-Model image uploaded on s3 bucket deleted successfully',
            );
          }

          // Capture log(history)
          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.ASSET_MODEL,
              action: HistoryActions.DELETED,
              date: new Date(),
              entityId: deletedAssetModel.id,
              log: {
                userId: user.id,
                name: user.name,
                assetModelId: deletedAssetModel.id,
              },
            },
          });
          this.logger.log(
            `Successfully created history for deleted asset models with id:${deletedAssetModel.id}`,
          );
          return true;
        },
      );
      return transaction;
    } catch (error) {
      this.logger.log(`Failed to delete asset model: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves the history of an asset model based on its ID and specified query parameters.
   *
   * @param {string} assetModelId - The ID of the asset model for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If asset model with the provided ID is not found.
   */
  async getAssetModelHistory(
    assetModelId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const assetModel = await this.prisma.assetModel.count({
      where: {
        id: assetModelId,
        isDeleted: false,
      },
    });

    if (!assetModel) {
      this.logger.log(`Asset Model with id ${assetModelId} not found`);
      throw new NotFoundException(ASSET_MODEL_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    const history = await this.prisma.history.findMany({
      where: {
        entityId: assetModelId,
      },
      select: {
        action: true,
        date: true,
        log: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: assetModelId,
      },
    });

    this.logger.log(`Fetched history for asset model with id ${assetModelId}`);

    return { history, count };
  }
}
