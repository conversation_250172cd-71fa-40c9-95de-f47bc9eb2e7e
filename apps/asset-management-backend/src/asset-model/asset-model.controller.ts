import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  HttpStatus,
  Logger,
  ParseUUIDPipe,
  Put,
  Req,
  UseGuards,
} from '@nestjs/common';
import { AssetModelService } from './asset-model.service';
import {
  CreateAssetModelDto,
  CreatedAssetModelResponseDto,
  GetAllAssetModelFilters,
  GetAllAssetModelsDetailResponse,
  GetByAssetModelIdResponseDto,
  UpdatedAssetModelResponseDto,
} from './dto/asset-model.dto';
import {
  AssetModelDetailsResponse,
  AssetModelGetAllResponse,
  DeleteAssetModelResponse,
} from './interface/asset-model.interface';
import {
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  ASSET_MODEL_NAME_EXISTS,
  ASSET_MODEL_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';
import { Request } from 'express';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
} from 'src/common/http/response.dto';
import { PurchaseResponseDto } from 'src/purchase/dto/purchase.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Asset Model')
@Controller('asset-model')
@UseGuards(PermissionGuard)
export class AssetModelController {
  private logger = new Logger('AssetModelController');

  constructor(private readonly assetModelService: AssetModelService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiExtraModels(CreatedAssetModelResponseDto)
  @ApiCreatedResponse({
    description: 'Successfully created asset model',
    schema: {
      $ref: getSchemaPath(CreatedAssetModelResponseDto),
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: ASSET_MODEL_NAME_EXISTS,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to create a new asset model if same model name not exists',
    summary: 'Create a asset model',
  })
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ASSET_MODEL))
  async createAssetModel(
    @Body() dto: CreateAssetModelDto,
    @Req() req: Request,
  ): Promise<AssetModelDetailsResponse> {
    this.logger.log('API to create an asset model');
    const { user } = req;
    const result = await this.assetModelService.createAssetModel(dto, user);
    return {
      statusCode: HttpStatus.CREATED,
      data: result,
      message: 'Successfully created asset model',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetAllAssetModelsDetailResponse)
  @ApiOkResponse({
    description: 'Successfully fetched all asset models',
    schema: {
      $ref: getSchemaPath(GetAllAssetModelsDetailResponse),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch all asset models with an optional quary parameter to search by name',
    summary: 'Fetches all asset models',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET_MODEL))
  async getAllAssetModels(
    @Query() dto?: GetAllAssetModelFilters,
  ): Promise<AssetModelGetAllResponse> {
    this.logger.log('API to fetch all asset models');
    const result = await this.assetModelService.getAllAssetModels(dto);
    return {
      statusCode: HttpStatus.OK,
      data: result.data,
      count: result.totalCount,
      message: 'Successfully retrieved asset models',
    };
  }

  @Get(':assetModelId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(GetByAssetModelIdResponseDto)
  @ApiOkResponse({
    description: 'Successfully retrieved asset model by id',
    schema: {
      $ref: getSchemaPath(GetByAssetModelIdResponseDto),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Asset model with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_MODEL_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description:
      'This API allows to fetch detail of asset model with specified Id',
    summary: 'Fetches asset model with given Id',
  })
  @ApiParam({
    name: 'assetModelId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter assetModel Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET_MODEL))
  async getAssetModelById(
    @Param('assetModelId', new ParseUUIDPipe()) assetModelId: string,
  ): Promise<GetByAssetModelIdResponseDto> {
    this.logger.log('API for retrieving asset model by ID');
    const result = await this.assetModelService.getAssetModelById(assetModelId);
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Successfully retrieved asset model by id',
    };
  }

  @Put('/:assetModelId')
  @ApiBearerAuth('access-token')
  @ApiExtraModels(UpdatedAssetModelResponseDto)
  @ApiOkResponse({
    description: 'Successfully updated asset model',
    schema: { $ref: getSchemaPath(UpdatedAssetModelResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Asset model with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_MODEL_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the asset model',
    summary: 'Update asset model',
  })
  @ApiParam({
    name: 'assetModelId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset model Id to update',
  })
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ASSET_MODEL))
  async updateAssetModel(
    @Param('assetModelId', new ParseUUIDPipe()) assetModelId: string,
    @Body() dto: CreateAssetModelDto,
    @Req() req: Request,
  ): Promise<AssetModelDetailsResponse> {
    this.logger.log('API to update the asset model');
    const { user } = req;
    const result = await this.assetModelService.updateAssetModel(
      assetModelId,
      dto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Successfully updated asset model',
    };
  }

  @Delete('/:assetModelId')
  @ApiBearerAuth('access-token')
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Asset model with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ASSET_MODEL_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to delete the asset model',
    summary: 'Delete asset model',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.ASSET_MODEL))
  async deleteAssetModels(
    @Param('assetModelId', new ParseUUIDPipe()) assetModels: string,
    @Req() req: Request,
  ): Promise<DeleteAssetModelResponse> {
    this.logger.log('API to delete an asset model');
    const { user } = req;
    const result = await this.assetModelService.deleteAssetModel(
      assetModels,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: result,
      message: 'Successfully deleted asset model',
    };
  }

  @Get('history/:assetModelId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched asset model history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'assetModelId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter asset model Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch asset model history',
    summary: 'Fetch asset model history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ASSET_MODEL))
  async getAssetModelHistory(
    @Param('assetModelId', new ParseUUIDPipe()) assetModelId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ) {
    const { history, count } =
      await this.assetModelService.getAssetModelHistory(
        assetModelId,
        queryParams,
      );
    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'Successfully retrieved asset model history',
    };
  }
}
