import { Module } from '@nestjs/common';
import { ApplianceController } from './appliance.controller';
import { ApplianceService } from './appliance.service';
import { PurchaseService } from 'src/purchase/purchase.service';
import { DocumentService } from 'src/document/document.service';
import { AbilityModule } from 'src/ability/ability.module';
@Module({
  imports: [AbilityModule],
  controllers: [ApplianceController],
  providers: [ApplianceService, PurchaseService, DocumentService],
})
export class ApplianceModule {}
