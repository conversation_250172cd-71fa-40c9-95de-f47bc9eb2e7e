import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  ApplianceData,
  ApplianceReqDto,
  AppliancesFilterQueryParamsDto,
  CreateApplianceReqDto,
  GetAppliancesPaginatedResponseDto,
} from './dto/appliance.dto';
import { APPLIANCE_NOT_FOUND } from 'src/constants';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import {
  checkErrorAndThrowNotFoundError,
  disconnectFieldGroups,
  getCustomFieldsData,
  getUpdatedFields,
  jsonToSheet,
  updateFieldGroups,
} from 'src/utility';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { User } from 'types';
import {
  CreatePurchaseDto,
  GetAllPurchasesResponse,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import { PurchaseService } from 'src/purchase/purchase.service';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { AwsService } from 'src/aws/aws.service';
import { DocumentService } from 'src/document/document.service';

@Injectable()
export class ApplianceService {
  private logger = new Logger('Appliance');
  private includeArgs = {
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
  };
  private selectArgs = {
    id: true,
    name: true,
    category: {
      select: {
        id: true,
        name: true,
      },
    },
    manufacturer: {
      select: {
        id: true,
        name: true,
      },
    },
    location: {
      select: {
        id: true,
        name: true,
      },
    },
    modelNumber: true,
    applianceImageUrl: true,
    totalQuantity: true,
    note: true,
    customFields: true,
    services: true,
  };
  constructor(
    private readonly prisma: PrismaService,
    private readonly purchaseService: PurchaseService,
    private readonly awsService: AwsService,
    private readonly documentService: DocumentService,
  ) {}
  async createAppliance(
    createApplianceReqDto: CreateApplianceReqDto,
    user: User,
  ): Promise<ApplianceData> {
    try {
      const {
        name,
        location,
        modelNumber,
        applianceImageUrl,
        note,
        categoryId,
        manufacturerId,
        purchaseInfo,
      } = createApplianceReqDto;

      const applianceTrnsaction: ApplianceData = await this.prisma.$transaction(
        async (prisma) => {
          const fieldGroups: string[] = createApplianceReqDto.customFields
            ? createApplianceReqDto.customFields['fieldgroups']
            : [];

          const appliance = await prisma.appliance.create({
            data: {
              name: name,
              location: location
                ? {
                    connect: {
                      id: location,
                    },
                  }
                : undefined,
              modelNumber: modelNumber,
              applianceImageUrl: applianceImageUrl,
              totalQuantity: purchaseInfo.quantity,
              note: note,
              customFields: createApplianceReqDto.customFields,
              fieldGroups: {
                connect: fieldGroups.map((fieldGroupId) => ({
                  id: fieldGroupId,
                })),
              },
              category: categoryId
                ? {
                    connect: {
                      id: categoryId,
                    },
                  }
                : undefined,
              manufacturer: manufacturerId
                ? {
                    connect: {
                      id: manufacturerId,
                    },
                  }
                : undefined,
            },
            include: {
              category: true,
              manufacturer: true,
              location: true,
            },
          });

          if (purchaseInfo) {
            await this.createAppliancePurchaseDetails(
              purchaseInfo,
              appliance.id,
              user,
            );
            this.logger.log(
              `Purchase created for appliance having id: ${appliance.id}`,
            );
          }

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.APPLIANCE,
              action: HistoryActions.CREATED,
              date: new Date(),
              entityId: appliance.id,
              log: {
                userId: user.id,
                name: user.name,
                applianceId: appliance.id,
              },
            },
          });

          return new ApplianceData(appliance);
        },
      );
      return applianceTrnsaction;
    } catch (error) {
      this.logger.log(`Error while creating Accessory`);

      // If something went wrong, delete new uploaded Image.
      if (createApplianceReqDto.applianceImageUrl) {
        await this.awsService.deleteFile(
          createApplianceReqDto.applianceImageUrl,
        );
        this.logger.log(
          'Appliance image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      throw error;
    }
  }

  async getAllAppliances(
    dto?: AppliancesFilterQueryParamsDto,
  ): Promise<GetAppliancesPaginatedResponseDto> {
    try {
      const page: number | null = dto?.page ? dto.page : null;
      const limit: number | undefined = dto?.limit ? dto.limit : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
      };

      const officeLocation = dto?.location;
      const category: string[] = Array.isArray(dto?.category)
        ? dto.category
        : dto?.category
        ? [dto.category]
        : [];
      const whereOptions: Prisma.ApplianceWhereInput = dto?.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
              {
                category: {
                  name: {
                    contains: dto.searchInput,
                    mode: 'insensitive',
                  },
                },
              },
              {
                modelNumber: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : undefined;
      const options: Prisma.ApplianceFindManyArgs = {
        take: limit,
        skip,
        orderBy,
      };

      const [appliances, count] = await this.prisma.$transaction([
        this.prisma.appliance.findMany({
          where: {
            ...whereOptions,
            ...(officeLocation
              ? {
                  location: {
                    name: { equals: officeLocation, mode: 'insensitive' },
                  },
                }
              : {}),
            ...(category.length > 0
              ? {
                  category: {
                    name: {
                      in: category,
                      mode: 'insensitive',
                    },
                  },
                }
              : undefined),
            isDeleted: false,
          },
          skip,
          ...options,
          include: {
            category: true,
            manufacturer: true,
            location: true,
          },
        }),
        this.prisma.appliance.count({
          where: {
            ...whereOptions,
            ...(officeLocation
              ? {
                  location: {
                    name: { equals: officeLocation, mode: 'insensitive' },
                  },
                }
              : {}),
            ...(category.length > 0
              ? {
                  category: {
                    name: {
                      in: category,
                      mode: 'insensitive',
                    },
                  },
                }
              : undefined),
            isDeleted: false,
          },
        }),
      ]);
      return {
        data: appliances.map((appliance) => new ApplianceData(appliance)),
        count: count,
      };
    } catch (error) {
      this.logger.error('Get appliances error', error);
      throw error;
    }
  }

  async getApplianceById(applianceId: string): Promise<ApplianceData> {
    try {
      const appliance = await this.prisma.appliance.findFirst({
        where: {
          id: applianceId,
          isDeleted: false,
        },
        include: {
          category: true,
          manufacturer: true,
          location: true,
        },
      });

      if (!appliance) {
        throw new NotFoundException(APPLIANCE_NOT_FOUND);
      }

      return new ApplianceData(appliance);
    } catch (error) {
      this.logger.error('Failed to get appliance by Id', error);
      throw error;
    }
  }

  async updateAppliance(
    applianceId: string,
    applianceReqDto: ApplianceReqDto,
    user: User,
  ): Promise<ApplianceData> {
    let appliance: ApplianceData;
    try {
      const updateApplianceTranscation = await this.prisma.$transaction(
        async (prisma) => {
          appliance = await prisma.appliance.findFirst({
            where: { id: applianceId, isDeleted: false },
            select: this.selectArgs,
          });

          if (!appliance) {
            this.logger.log(`asset with id: ${applianceId} not found`);
            throw new NotFoundException(APPLIANCE_NOT_FOUND);
          }

          /**
           * Updates field groups for a specific entity, disconnecting existing field groups and connecting new ones.
           **/
          await updateFieldGroups(
            prisma,
            'appliance',
            applianceId,
            appliance,
            applianceReqDto,
          );
          const updatedAppliance = await prisma.appliance.update({
            where: {
              id: applianceId,
            },
            data: {
              name: applianceReqDto.name,
              category: applianceReqDto.categoryId
                ? {
                    connect: {
                      id: applianceReqDto.categoryId,
                    },
                  }
                : undefined,
              manufacturer: applianceReqDto.manufacturerId
                ? {
                    connect: {
                      id: applianceReqDto.manufacturerId,
                    },
                  }
                : undefined,
              location: applianceReqDto.location
                ? {
                    connect: {
                      id: applianceReqDto.location,
                    },
                  }
                : undefined,
              modelNumber: applianceReqDto.modelNumber,
              note: applianceReqDto.note,
              customFields: applianceReqDto.customFields,
              applianceImageUrl: applianceReqDto.applianceImageUrl,
            },
            select: this.selectArgs,
          });
          this.logger.log(
            `Successfully updated accessory with id ${applianceId}`,
          );
          // If update successfully, delete previous image attached with appliance
          if (
            updatedAppliance &&
            appliance.applianceImageUrl &&
            appliance.applianceImageUrl !== applianceReqDto.applianceImageUrl
          ) {
            this.awsService.deleteFile(appliance.applianceImageUrl);
            this.logger.log(
              'Appliance image uploaded on s3 bucket deleted successfully',
            );
          }

          await prisma.history.create({
            data: {
              changeInTable: ChangesOcccuredIn.APPLIANCE,
              action: HistoryActions.UPDATED,
              date: new Date(),
              entityId: updatedAppliance.id,
              log: {
                userId: user.id,
                name: user.name,
                applianceId: updatedAppliance.id,
                updatedFields: getUpdatedFields(appliance, updatedAppliance),
              },
            },
          });

          return updatedAppliance;
        },
      );
      return updateApplianceTranscation;
    } catch (error) {
      // If something went wrong, delete new uploaded Image.
      if (
        applianceReqDto.applianceImageUrl &&
        appliance.applianceImageUrl !== applianceReqDto.applianceImageUrl
      ) {
        await this.awsService.deleteFile(applianceReqDto.applianceImageUrl);
        this.logger.log(
          'Appliance image uploaded on s3 bucket deleted successfully',
        );
      }
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error('Update the appliance error', error);
      throw error;
    }
  }

  async deleteAppliance(
    applianceId: string,
    user: User,
  ): Promise<{ success: boolean }> {
    const applianceTrnsaction = await this.prisma.$transaction(
      async (prisma) => {
        const appliance = await prisma.appliance.findFirst({
          where: {
            id: applianceId,
            isDeleted: false,
          },
        });

        if (!appliance) {
          this.logger.log(`${APPLIANCE_NOT_FOUND} for id ${applianceId}`);
          throw new NotFoundException(APPLIANCE_NOT_FOUND);
        }
        const deletedAppliance = await prisma.appliance.update({
          where: {
            id: applianceId,
          },
          data: {
            isDeleted: true,
            customFields: {},
          },
        });

        this.logger.log(`Successfully deleted appliance of id ${applianceId}`);

        /**
         * Disconnects existing field groups associated with the specified accessory.
         **/
        await disconnectFieldGroups(
          prisma,
          'appliance',
          applianceId,
          appliance,
        );
        this.logger.log(`Appliance disconnected from all field groups`);

        // If accessory deleted successfully, delete image attached with appliance.
        if (appliance.applianceImageUrl && deletedAppliance) {
          await this.awsService.deleteFile(appliance.applianceImageUrl);

          this.logger.log(
            'Appliance image uploaded on s3 bucket deleted successfully',
          );
        }

        // Delete appliance documents
        const documentId = await prisma.document.findMany({
          where: {
            entityId: applianceId,
          },
          select: {
            id: true,
          },
        });
        if (documentId) {
          documentId.map(async (document) => {
            await this.documentService.deleteDocument(document.id, user);
          });
          this.logger.log(
            `The documents related to appliance ID:${applianceId} deleted successfully`,
          );
        }

        await prisma.history.create({
          data: {
            changeInTable: ChangesOcccuredIn.APPLIANCE,
            action: HistoryActions.DELETED,
            date: new Date(),
            entityId: deletedAppliance.id,
            log: {
              userId: user.id,
              name: user.name,
              applianceId: deletedAppliance.id,
            },
          },
        });
        this.logger.log(`History for ${applianceId} created sucessfully`);
        return { success: true };
      },
    );
    return applianceTrnsaction;
  }

  async createAppliancePurchaseDetails(
    dto: CreatePurchaseDto,
    entityId: string,
    user: User,
  ): Promise<PurchaseResponseDto> {
    try {
      return this.purchaseService.createPurchase(
        TypeOfCategory.APPLIANCE,
        dto,
        entityId,
        user,
      );
    } catch (error) {
      this.logger.error('create appliance purchase error', error);
      throw error;
    }
  }

  async getPurchaseHistory(
    entityId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllPurchasesResponse> {
    return this.purchaseService.getEntityPurchaseDetails(entityId, queryParams);
  }

  /**
   * Retrieves the history of a appliance based on its ID and specified query parameters.
   *
   * @param {string} applianceId - The ID of the appliance for which the history is requested.
   * @param {GetAllQueryParamsDto} queryParams - Additional query parameters to filter and paginate the history.
   * @returns {Promise<GetEntityHistoryResponse>} - A promise that resolves to an object containing the history entries and
   * the total count.
   * @throws {NotFoundException} - If the appliance with the provided ID is not found.
   */
  async getApplianceHistory(
    applianceId: string,
    queryParams: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const appliance = await this.prisma.appliance.count({
      where: {
        id: applianceId,
        isDeleted: false,
      },
    });

    if (!appliance) {
      this.logger.log(`Appliance with id ${applianceId} not found`);
      throw new NotFoundException(APPLIANCE_NOT_FOUND);
    }

    const page: number | null = queryParams?.page ? queryParams.page : null;
    const limit: number | undefined = queryParams?.limit
      ? queryParams.limit
      : undefined;
    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [queryParams?.sortBy || 'createdAt']: queryParams?.sortOrder || 'desc',
    };

    // TODO: Search functionality to be implemented

    const history = await this.prisma.history.findMany({
      where: {
        entityId: applianceId,
      },
      select: {
        action: true,
        date: true,
        log: true,
        changeInTable: true,
      },
      orderBy,
      take: limit,
      skip,
    });

    const count = await this.prisma.history.count({
      where: {
        entityId: applianceId,
      },
    });

    this.logger.log(`Fetched history for appliance with id ${applianceId}`);

    return { history, count };
  }

  /**
   * Updates a purchase record.
   * @param {UpdatePurchaseDto} dto - The DTO containing the updated purchase information.
   * @param {string} purchaseId - The ID of the purchase record to update.
   * @param {User} user - The user performing the update.
   * @returns {Promise<PurchaseResponseDto>} A Promise resolving to the updated purchase record.
   */
  async updatePurchaseRecord(
    dto: UpdatePurchaseDto,
    purchaseId: string,
    user: User,
    applianceId: string,
  ): Promise<PurchaseResponseDto> {
    return this.purchaseService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      applianceId,
    );
  }

  async downloadAppliances(): Promise<string> {
    const appliances = await this.prisma.appliance.findMany({
      where: {
        isDeleted: false,
      },
      select: {
        name: true,
        category: {
          select: {
            name: true,
          },
        },
        manufacturer: {
          select: {
            name: true,
          },
        },
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        modelNumber: true,
        totalQuantity: true,
        note: true,
        customFields: true,
      },
    });
    const parsedDataPromises = appliances.map(
      async ({ category, manufacturer, location, customFields, ...rest }) => {
        const categoryName = category?.name;
        const manufacturerName = manufacturer?.name;
        const locationName = location?.name;

        const customFieldData = await getCustomFieldsData(
          customFields,
          this.prisma,
        );
        return {
          ...rest,
          categoryName,
          manufacturerName,
          locationName,
          ...customFieldData,
        };
      },
    );
    const parsedData = await Promise.all(parsedDataPromises);
    const buffer = jsonToSheet(parsedData);
    const { fileName } = await this.awsService.uploadExcelFile(
      buffer,
      'downloads/appliances',
    );
    return fileName;
  }
}
