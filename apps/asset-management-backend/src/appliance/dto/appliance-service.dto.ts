import { ApiProperty } from '@nestjs/swagger';
import { serviceRating } from '@prisma-clients/asset-management-backend';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  Min,
  ValidateNested,
} from 'class-validator';
import { MinMax } from 'src/common/custom-validators';
import { SuccessResponsePayloadDto } from 'src/common/dto/success-response-payload.dto';

export class ApplianceServiceBaseReqDto {
  @ApiProperty({
    description: 'Appliance service date',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDateString()
  date?: Date;
  @ApiProperty({
    description: 'Appliance next service date',
    type: Date,
    required: false,
  })
  @IsOptional()
  @IsDateString()
  @MinMax('date')
  nextServiceDate?: Date;
  @ApiProperty({
    description: 'Note about appliance service details',
    type: String,
    required: false,
    example: 'Note about appliance service details',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  note?: string;
  @ApiProperty({
    description: 'Contact Name of the servicer',
    type: String,
    required: false,
    example: 'John',
  })
  @IsOptional()
  @IsString()
  contactName?: string;
  @ApiProperty({
    description: 'Contact Number of the servicer',
    type: String,
    required: false,
    example: '6363636363',
  })
  @IsOptional()
  @IsString()
  contactNumber?: string;
  @ApiProperty({
    description: 'Cost of the service',
    type: 'number',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  cost?: number;
  @ApiProperty({
    description: 'Rating for the service',
    enum: serviceRating,
    example: serviceRating.GOOD,
    required: false,
  })
  @IsOptional()
  @IsEnum(serviceRating)
  rating?: serviceRating;
  @ApiProperty({
    description: 'Id of the supplier',
    type: 'uuid',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  supplierId?: string;
  @ApiProperty({
    description: 'Appliance Service Image Url',
    type: 'string',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceImageUrl?: string;
}
export class CreateApplianceServiceInfoReqDto extends ApplianceServiceBaseReqDto {
  @ApiProperty({
    description: 'Appliance Id',
    type: String,
    required: true,
    example: 'e77e5e60-1eb7-4180-9f50-8c2e39cf54c2',
  })
  @IsNotEmpty()
  @IsUUID()
  applianceId: string;
}

interface IApplianceService {
  id: string;
  date: Date;
  applianceId: string;
  nextServiceDate: Date;
  note: string;
  contactName: string;
  contactNumber: string;
  cost: number;
  rating: serviceRating;
  supplier?: {
    id: string;
    name: string;
  };
  serviceImageUrl: string;
  createdAt: Date;
  updatedAt: Date;
}

export class ApplianceServiceDto implements IApplianceService {
  constructor(dto?: IApplianceService) {
    this.id = dto.id;
    this.date = dto.date;
    this.applianceId = dto.applianceId;
    this.nextServiceDate = dto.nextServiceDate;
    this.note = dto.note;
    this.contactName = dto.contactName;
    this.contactNumber = dto.contactNumber;
    this.cost = dto.cost;
    this.rating = dto.rating;
    this.supplier = dto.supplier;
    this.serviceImageUrl = dto.serviceImageUrl;
    this.createdAt = dto.createdAt;
    this.updatedAt = dto.updatedAt;
  }
  @ApiProperty()
  id: string;
  @ApiProperty()
  date: Date;
  @ApiProperty()
  applianceId: string;
  @ApiProperty()
  nextServiceDate: Date;
  @ApiProperty()
  note: string;
  @ApiProperty()
  createdAt: Date;
  @ApiProperty()
  updatedAt: Date;
  @ApiProperty()
  contactName: string;
  @ApiProperty()
  contactNumber: string;
  @ApiProperty()
  cost: number;
  @ApiProperty()
  rating: serviceRating;
  @ApiProperty()
  supplier: {
    id: string;
    name: string;
  };
  @ApiProperty()
  serviceImageUrl: string;
}

export class ApplianceServiceResponseDto extends SuccessResponsePayloadDto {
  @ApiProperty({
    description: 'Object containing Appliance service details',
    type: ApplianceServiceDto,
  })
  data: ApplianceServiceDto;
}

export class GetAllApplianceServicesRequestDto {
  @ApiProperty({
    description: 'Limit per page',
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;
  @ApiProperty({
    description: 'Offset per page',
    required: false,
  })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

export class GetAllApplianceServicesDto {
  @ApiProperty({
    type: [ApplianceServiceDto],
    description: 'List of appliance services',
  })
  items: ApplianceServiceDto[];
  @ApiProperty({
    type: Number,
    description: 'Current limit applied',
  })
  currentLimit: number;
  @ApiProperty({
    type: Number,
    description: 'Current offset applied',
  })
  currentOffset: number;
  @ApiProperty({
    type: Number,
    description: 'Total count of appliance services',
  })
  totalCount: number;
}

export class GetAllApplianceServicesResPayloadDto extends SuccessResponsePayloadDto {
  @ApiProperty({
    description: 'Appliance services data',
    type: GetAllApplianceServicesDto,
  })
  @ValidateNested()
  @Type(() => GetAllApplianceServicesDto)
  data: GetAllApplianceServicesDto;
}

export class UpdateApplianceServiceInfoReqDto extends ApplianceServiceBaseReqDto {
  @ApiProperty({
    description: 'Appliance Id',
    type: String,
    required: false,
    example: 'e77e5e60-1eb7-4180-9f50-8c2e39cf54c8',
  })
  @IsOptional()
  @IsUUID()
  applianceId?: string;
}
