import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  ValidateNested,
  MaxLength,
  IsUUID,
} from 'class-validator';
import { EntityDto } from 'src/common/dto/entity.dto';
import { SuccessResponsePayloadDto } from 'src/common/dto/success-response-payload.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { CreatePurchaseDto } from 'src/purchase/dto/purchase.dto';
import { JsonValue } from 'types';

export class ApplianceReqDto {
  @ApiProperty({
    description: 'Name of the appliance',
  })
  @IsOptional()
  @IsString()
  name?: string;
  @ApiProperty({
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
  })
  @IsOptional()
  @IsUUID()
  location?: string;
  @ApiProperty({
    description: 'Model number of the appliance',
  })
  @IsOptional()
  @IsString()
  modelNumber?: string;
  @ApiProperty({
    description: 'URL for asset image',
    type: 'string',
    example: 'https://picsum.photos/200/300',
    required: false,
  })
  @IsOptional()
  applianceImageUrl: string;

  @ApiProperty({
    description: 'Description note of the appliance',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  note?: string;
  @ApiProperty({
    description: 'Category Id to which appliance belongs to',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;
  @ApiProperty({
    description: 'Id of the manufacturer',
  })
  @IsOptional()
  @IsString()
  manufacturerId?: string;

  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class CreateApplianceReqDto extends ApplianceReqDto {
  @ApiProperty({
    description: 'Purchase details for appliance',
    type: CreatePurchaseDto,
    required: false,
  })
  @IsOptional()
  @Type(() => CreatePurchaseDto)
  purchaseInfo?: CreatePurchaseDto;
}

interface IEntity {
  id: string;
  name: string;
}

export class Entity implements IEntity {
  constructor(dto?: IEntity) {
    this.id = dto.id;
    this.name = dto.name;
  }
  @ApiProperty()
  id: string;
  @ApiProperty()
  name: string;
}

interface IAppliance {
  id: string;
  name: string;
  category: Entity;
  manufacturer: Entity;
  location: EntityDto;
  modelNumber: string;
  applianceImageUrl: string;
  totalQuantity: number;
  note: string;
  customFields?: JsonValue;
}

export class ApplianceData implements IAppliance {
  constructor(dto?: IAppliance) {
    this.id = dto.id;
    this.name = dto.name;
    this.category = dto?.category ? new Entity(dto.category) : null;
    this.manufacturer = dto?.manufacturer ? new Entity(dto.manufacturer) : null;
    this.location = dto.location;
    this.modelNumber = dto.modelNumber;
    this.applianceImageUrl = dto.applianceImageUrl;
    this.totalQuantity = dto.totalQuantity;
    this.note = dto.note;
    this.customFields = dto.customFields;
  }
  @ApiProperty()
  id: string;
  @ApiProperty()
  name: string;
  @ApiProperty()
  category: Entity;
  @ApiProperty()
  manufacturer: Entity;
  @ApiProperty()
  location: EntityDto;
  @ApiProperty()
  modelNumber: string;
  @ApiProperty()
  applianceImageUrl: string;
  @ApiProperty()
  totalQuantity: number;
  @ApiProperty()
  note: string;
  @ApiProperty({
    description: 'Custom fields',
    required: false,
  })
  @IsOptional()
  customFields?: JsonValue;
}

export class GetAppliancesPaginatedResponseDto {
  @ApiProperty({
    type: [ApplianceData],
    description: 'List of appliances',
  })
  data: ApplianceData[];
  @ApiProperty({
    type: Number,
    description: 'Total count of appliance',
  })
  count: number;
}

export class GetApplianceResPayloadDto extends SuccessResponsePayloadDto {
  @ApiProperty({
    description: 'Appliance data',
  })
  @ValidateNested()
  @Type(() => GetAppliancesPaginatedResponseDto)
  data: GetAppliancesPaginatedResponseDto;
  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  count: number;
}

export class AppliancesFilterQueryParamsDto extends GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Filter appliance based on category',
    type: [String],
    example: 'laptop',
    required: false,
  })
  @IsOptional()
  category?: string[];

  @ApiProperty({
    description: 'Order assets based on location',
    example: '2d3afc9f-3b4f-4e2a-b875-8f99add89aae',
    required: false,
  })
  @IsOptional()
  location?: string;
}
