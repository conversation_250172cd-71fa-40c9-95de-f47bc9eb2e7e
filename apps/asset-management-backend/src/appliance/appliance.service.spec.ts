import { Test, TestingModule } from '@nestjs/testing';
import { ApplianceService } from './appliance.service';
import { CreateApplianceReqDto } from './dto/appliance.dto';
import {
  Appliance,
  Category,
  Currency,
  EvaluationFrequency,
  Manufacturer,
  Supplier,
  TransactionFrequency,
} from '@prisma-clients/asset-management-backend';
import { NotFoundException } from '@nestjs/common';
import { isUUID } from 'class-validator';
import {
  APPLIANCE_NOT_FOUND,
  CATEGORY_NOT_FOUND,
  MANUFACTURER_NOT_FOUND,
  SUPPLIER_NOT_FOUND,
} from 'src/constants';
import { Request } from 'express';
import { AppModule } from 'src/app.module';
import { PrismaService } from 'src/prisma/prisma.service';

describe('ApplianceService', () => {
  let service: ApplianceService;
  let prismaService: PrismaService;

  const supplier: Supplier = {
    id: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
    name: 'example-supplier',
    address: 'example',
    zipCode: '123456',
    contactName: null,
    contactEmail: null,
    contactPhoneNumber: null,
    note: null,
    supplierImageUrl: null,
    serviceType: 'Internet',
    evaluationFrequency: EvaluationFrequency.QUARTERLY,
    transactionFrequency: TransactionFrequency.ONE_TIME,
    selectionCriteria: [
      'Quality records of previously demonstrated capabilities',
    ],
    agreement: [],
    departmentId: 'f6a8cc6e-2ab8-4b63-a9f8-6d94aa75964a',
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    notifyTo: ['123e4567-e89b-12d3-a456-426614174000'],
  };

  const manufacturer: Manufacturer = {
    id: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
    name: 'example',
    contactName: null,
    contactEmail: null,
    contactPhoneNumber: null,
    manufacturerImageUrl: null,
    note: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const category: Category = {
    id: '9d625315-81be-4e76-a857-58d57f4c94f4',
    name: 'example',
    note: null,
    typeOfCategory: 'APPLIANCE',
    categoryImageUrl: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
  };

  const purchasedUser = {
    id: '9d625315-81be-4e76-a857-58d57f4c94f4',
    name: 'John2',
  };

  const dto: CreateApplianceReqDto = {
    name: 'panasonic ac',
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    modelNumber: 'pan001',
    applianceImageUrl: '"/images/pan001.jpg',
    note: 'Good quality',
    categoryId: '9d625315-81be-4e76-a857-58d57f4c94f4',
    manufacturerId: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
    purchaseInfo: {
      orderNumber: 'cc-110',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      supplierId: supplier.id,
      purchasedById: purchasedUser.id,
    },
  };

  const applianceDetails = {
    id: '04fa8727-28dc-435d-8298-91b375b5925f',
    name: 'panasonic ac',
    categoryId: '9d625315-81be-4e76-a857-58d57f4c94f4',
    supplierId: 'a42d29c1-b80e-48e9-9b22-d4853c63d815',
    manufacturerId: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
    locationId: '04fa8727-28dc-445d-8298-91b375b5925f',
    modelNumber: 'pan001',
    applianceImageUrl: '"/images/pan001.jpg',
    totalQuantity: 10,
    note: 'Good quality',
    category: category,
    supplier: supplier,
    manufacturer: manufacturer,
    createdAt: new Date(),
    isDeleted: false,
    updatedAt: new Date(),
    customFields: {},
  };

  const appliance = {
    id: '04fa8727-28dc-435d-8298-91b375b5925f',
    name: 'panasonic ac',
    category: {
      id: '9d625315-81be-4e76-a857-58d57f4c94f4',
      name: 'example',
    },
    manufacturer: {
      id: '7cdabbc5-1e5b-4aa8-b0dd-4b02b743f7d6',
      name: 'example',
    },
    location: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    modelNumber: 'pan001',
    applianceImageUrl: '"/images/pan001.jpg',
    totalQuantity: 10,
    note: 'Good quality',
  };

  const request = {
    user: {
      id: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      name: 'John',
      email: '<EMAIL>',
      roles: ['ADMIN'],
    },
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<ApplianceService>(ApplianceService);
    prismaService = service['prisma'];

    expect(isUUID(appliance.id)).toBe(true);
    expect(isUUID(supplier.id)).toBe(true);
    expect(isUUID(manufacturer.id)).toBe(true);
    expect(isUUID(category.id)).toBe(true);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAppliance', () => {
    it('should create a appliance when not exists', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(appliance);
      const result = await service.createAppliance(dto, request.user);
      expect(result).toEqual(appliance);
    });

    it('should throw not found exception when supplier does not exist', async () => {
      jest
        .spyOn(service, 'createAppliance')
        .mockRejectedValue(new Error(SUPPLIER_NOT_FOUND));

      expect(async () => {
        await service.createAppliance(dto, request.user);
      }).rejects.toThrowError(new NotFoundException(SUPPLIER_NOT_FOUND));
    });

    it('should throw not found exception when manufacturer does not exist', async () => {
      jest
        .spyOn(service, 'createAppliance')
        .mockRejectedValue(new Error(MANUFACTURER_NOT_FOUND));

      expect(
        async () => await service.createAppliance(dto, request.user),
      ).rejects.toThrowError(new NotFoundException(MANUFACTURER_NOT_FOUND));
    });

    it('should throw not found exception when category does not exist', async () => {
      jest
        .spyOn(service, 'createAppliance')
        .mockRejectedValue(new Error(CATEGORY_NOT_FOUND));
      expect(
        async () => await service.createAppliance(dto, request.user),
      ).rejects.toThrowError(new NotFoundException(CATEGORY_NOT_FOUND));
    });
  });

  describe('get all appliances service', () => {
    it('should return all the appliances in an array when no input provided', async () => {
      const count = 1; // Assuming there is one appliance in the mock data

      jest
        .spyOn(prismaService.appliance, 'findMany')
        .mockResolvedValue([applianceDetails]);

      jest
        .spyOn(prismaService.appliance, 'count')
        .mockResolvedValue(count as number);

      // Wrap the calls inside Promise.all to ensure they are Prisma Client promises
      jest
        .spyOn(prismaService, '$transaction')
        .mockResolvedValue(
          await Promise.all([
            prismaService.appliance.findMany({}),
            prismaService.appliance.count({}),
          ]),
        );

      await service.getAllAppliances();
      expect(prismaService.appliance.findMany).toHaveBeenCalled();
      expect(prismaService.appliance.count).toHaveBeenCalled();
      expect(prismaService.$transaction).toHaveBeenCalled();
    });
  });

  describe('get appliance by appliance id', () => {
    it('should return the appliance details', async () => {
      jest
        .spyOn(prismaService.appliance, 'findFirst')
        .mockResolvedValue(appliance as unknown as Appliance);

      const result = await service.getApplianceById(applianceDetails.id);
      expect(prismaService.appliance.findMany).toHaveBeenCalled();

      expect(result).toEqual(appliance);
    });

    it('should throw not found exception when appliance not found', async () => {
      jest
        .spyOn(prismaService.appliance, 'findFirst')
        .mockRejectedValue(new Error(APPLIANCE_NOT_FOUND));
      expect(
        async () => await service.getApplianceById('1'),
      ).rejects.toThrowError(new NotFoundException(APPLIANCE_NOT_FOUND));
    });
  });

  describe('update appliance service', () => {
    it('should update the appliance with specified id with given data', async () => {
      jest.spyOn(prismaService, '$transaction').mockResolvedValue(appliance);

      const result = await service.updateAppliance(
        appliance.id,
        dto,
        request.user,
      );
      expect(result).toEqual(appliance);
    });
  });
});
