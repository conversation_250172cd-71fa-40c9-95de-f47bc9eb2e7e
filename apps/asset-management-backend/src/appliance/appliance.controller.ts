import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApplianceService } from './appliance.service';
import {
  ApplianceData,
  ApplianceReqDto,
  AppliancesFilterQueryParamsDto,
  CreateApplianceReqDto,
  GetAppliancesPaginatedResponseDto,
} from './dto/appliance.dto';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { Request } from 'express';
import {
  CreatePurchaseDto,
  PurchaseResponseDto,
  UpdatePurchaseDto,
} from 'src/purchase/dto/purchase.dto';
import {
  INTERNAL_ERROR,
  INVALID_ENTITY_TYPE,
  INVALID_UUID_FORMAT,
} from 'src/constants/message-constants';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Appliance')
@Controller('appliance')
@UseGuards(PermissionGuard)
export class ApplianceController {
  constructor(private readonly applianceService: ApplianceService) {}

  @ApiOperation({
    summary: 'API for creating the new appliance record',
    description:
      'Api to create appliance record with fields provided in the request dto.',
  })
  @ApiOkResponse({
    description: 'Successfully created new appliance record',
    type: HTTPResponseDto<ApplianceData>,
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @Post()
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.APPLIANCE))
  async createAppliance(
    @Body() dto: CreateApplianceReqDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<ApplianceData>> {
    const { user } = request;
    const appliance = await this.applianceService.createAppliance(dto, user);
    return {
      statusCode: HttpStatus.CREATED,
      data: appliance,
      message: 'Successfully created record for appliance',
    };
  }

  @ApiOperation({
    summary: 'API to get the all appliances details',
    description: 'Api to get all appliances',
  })
  @ApiOkResponse({
    description: 'Successfully fetched the appliances record',
    type: GetAllResponseDto<GetAppliancesPaginatedResponseDto>,
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @Get()
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.APPLIANCE))
  async getAllAppliances(
    @Query() dto: AppliancesFilterQueryParamsDto,
  ): Promise<GetAllResponseDto<ApplianceData[]>> {
    const appliances = await this.applianceService.getAllAppliances(dto);

    return {
      statusCode: HttpStatus.OK,
      data: appliances.data,
      count: appliances.count,
      message: 'Successfully fetched appliance record',
    };
  }

  @ApiOperation({
    summary: 'API to get the appliance details',
    description: 'Api to get appliance record by providing appliance id',
  })
  @ApiOkResponse({
    description: 'Successfully fetched the appliance record',
    type: GetAllResponseDto<GetAppliancesPaginatedResponseDto>,
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @Get('/:applianceId')
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.APPLIANCE))
  async getApplianceById(
    @Param('applianceId') applianceId: string,
  ): Promise<HTTPResponseDto<ApplianceData>> {
    const appliance = await this.applianceService.getApplianceById(applianceId);

    return {
      statusCode: HttpStatus.OK,
      data: appliance,
      message: 'Successfully fetched appliance record',
    };
  }

  @ApiOperation({
    summary: 'API to update the appliance details',
    description: 'Api to update the appliance record by providing appliance id',
  })
  @ApiOkResponse({
    description: 'Successfully updated the appliance record',
    type: HTTPResponseDto<ApplianceData>,
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'applianceId',
    type: String,
    required: true,
  })
  @Put(':applianceId')
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.APPLIANCE))
  async updateAppliance(
    @Param('applianceId') applianceId: string,
    @Body() dto: ApplianceReqDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<ApplianceData>> {
    const { user } = request;
    const appliance = await this.applianceService.updateAppliance(
      applianceId,
      dto,
      user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: appliance,
      message: 'Successfully updated record for appliance',
    };
  }

  @ApiOperation({
    summary: 'API to delete the appliance details',
    description: 'Api to delete the appliance record by providing appliance id',
  })
  @ApiOkResponse({
    description: 'Successfully deleted the appliance record',
    schema: {
      example: true,
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'applianceId',
    type: 'string',
    required: true,
  })
  @Delete(':applianceId')
  @ApiBearerAuth('access-token')
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.APPLIANCE))
  async deleteAppliance(
    @Param('applianceId', new ParseUUIDPipe()) applianceId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    await this.applianceService.deleteAppliance(applianceId, user);

    return {
      statusCode: HttpStatus.OK,
      data: true,
      message: `Deleted appliance record with id ${applianceId}`,
    };
  }

  @Post('purchase/:applianceId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created purchase details for appliance',
    type: HTTPResponseDto<PurchaseResponseDto>,
  })
  @ApiBadRequestResponse({
    description: 'Invalid entity type',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_ENTITY_TYPE,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'applianceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter appliance Id to create purchase record',
  })
  @ApiOperation({
    description: 'This api allows to create purchase details for a appliance',
    summary: 'Create purchase details for a appliance',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.APPLIANCE))
  async createAppliancePurchaseDetails(
    @Param('applianceId', new ParseUUIDPipe()) applianceId: string,
    @Body() dto: CreatePurchaseDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const purchaseDetails =
      await this.applianceService.createAppliancePurchaseDetails(
        dto,
        applianceId,
        user,
      );

    return {
      statusCode: HttpStatus.CREATED,
      data: purchaseDetails,
      message: 'Successfully created purchase details for appliance',
    };
  }

  @Get('purchases/:applianceId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched purchase history for appliance',
    type: GetAllResponseDto<PurchaseResponseDto[]>,
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'applianceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter appliance Id to fetch purchase history',
  })
  @ApiOperation({
    description: 'This API allows to fetch purchase history for appliance',
    summary: 'Fetch purchase history of a appliance',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.APPLIANCE))
  async getPurchaseHistory(
    @Param('applianceId', new ParseUUIDPipe()) applianceId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<PurchaseResponseDto[]>> {
    const { purchaseDetails, count } =
      await this.applianceService.getPurchaseHistory(applianceId, queryParams);

    return {
      statusCode: HttpStatus.OK,
      data: purchaseDetails,
      count,
      message: 'Successfully fetched purchase history for appliance',
    };
  }

  @Get('history/:applianceId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched appliance history',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<HistoryResponsePayload[]>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'applianceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter appliance Id to fetch history',
  })
  @ApiOperation({
    description: 'This API allows to fetch appliance history',
    summary: 'Fetch appliance history',
  })
  @ApiExtraModels(GetAllResponseDto<PurchaseResponseDto[]>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.APPLIANCE))
  async getApplianceHistory(
    @Param('applianceId', new ParseUUIDPipe()) applianceId: string,
    @Query() queryParams: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const { history, count } = await this.applianceService.getApplianceHistory(
      applianceId,
      queryParams,
    );

    return {
      statusCode: HttpStatus.OK,
      data: history,
      count: count,
      message: 'appliance history fetched successfully',
    };
  }

  @Put('purchase-update/:purchaseId/:applianceId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Purchase details updated Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<PurchaseResponseDto>),
    },
  })
  @ApiBadRequestResponse({
    description: 'Invalid purchase ID',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: INVALID_UUID_FORMAT,
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    name: 'purchaseId',
    type: 'string',
    example: '6ea45197-c3cd-4434-b853-db3de8b86322',
    description: 'Enter the purchase ID to update purchase record',
  })
  @ApiOperation({
    description: 'This API updates the purchase record',
    summary: 'Update the purchase record',
  })
  @ApiExtraModels(HTTPResponseDto<PurchaseResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.APPLIANCE))
  async updatePurchaseRecord(
    @Param('purchaseId', new ParseUUIDPipe()) purchaseId: string,
    @Body() dto: UpdatePurchaseDto,
    @Req() request: Request,
    @Param('applianceId', new ParseUUIDPipe()) applianceId: string,
  ): Promise<HTTPResponseDto<PurchaseResponseDto>> {
    const { user } = request;
    const updatedPurchase = await this.applianceService.updatePurchaseRecord(
      dto,
      purchaseId,
      user,
      applianceId,
    );
    return {
      statusCode: HttpStatus.OK,
      data: updatedPurchase,
      message: 'Purchase record updated successfully',
    };
  }

  @Post('/download')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully downloaded the appliances info',
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to download the appliances info',
  })
  async downloadAccessories(): Promise<HTTPResponseDto<string>> {
    const fileName = await this.applianceService.downloadAppliances();
    return {
      data: fileName,
      message: 'Downloaded',
      statusCode: HttpStatus.OK,
    };
  }
}
