import { Test, TestingModule } from '@nestjs/testing';
import { ApplianceController } from './appliance.controller';
import { ApplianceService } from './appliance.service';
import { CreateApplianceReqDto } from './dto/appliance.dto';
import { isUUID } from 'class-validator';
import { Currency } from '@prisma-clients/asset-management-backend';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { Request } from 'express';
import { AppModule } from 'src/app.module';

describe('ApplianceController', () => {
  let controller: ApplianceController;
  let service: ApplianceService;

  const dto: CreateApplianceReqDto = {
    name: 'panasonic ac',
    location: '04fa8727-28dc-445d-8298-91b375b5925f',
    modelNumber: 'pan001',
    applianceImageUrl: '"/images/pan001.jpg',
    note: 'Good quality',
    categoryId: '04fa8727-28dc-445d-8298-91b375b5925f',
    manufacturerId: '04fa8727-28dc-785d-8298-91b375b5925f',
    purchaseInfo: {
      orderNumber: 'order-number',
      purchaseCost: 1000,
      currency: Currency.USD,
      purchaseDate: new Date(),
      quantity: 100,
      supplierId: '5d7f3bda-f529-4387-9434-cf6ab340979e',
      purchasedById: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    },
  };

  const appliance = {
    id: '04fa8727-28dc-435d-8298-91b375b5925f',
    name: 'string',
    category: {
      id: '04fa8727-28dc-445d-8298-91b375b5925f',
      name: 'ac',
    },
    supplier: {
      id: '04fa8727-28dc-435d-8298-91b375b5925f',
      name: 'devi suppliers',
    },
    manufacturer: {
      id: '04fa8727-28dc-785d-8298-91b375b5925f',
      name: 'panasonic',
    },
    location: {
      id: '04fa8727-28dc-785d-8298-91b375b5925f',
      name: 'Bangalore',
    },
    modelNumber: 'string',
    applianceImageUrl: 'string',
    totalQuantity: 10,
    note: 'string',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const appliances = {
    data: [appliance],
    count: 20,
  };

  const request = {
    id: '5d7f3bda-f529-4387-9434-cf6ab340979e',
    name: 'John',
    email: '<EMAIL>',
    roles: ['Admin'],
  } as unknown as Request;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<ApplianceController>(ApplianceController);
    service = module.get<ApplianceService>(ApplianceService);

    expect(isUUID(appliance.id)).toBe(true);
    expect(isUUID(dto.manufacturerId)).toBe(true);
    expect(isUUID(dto.categoryId)).toBe(true);
    expect(isUUID(dto.purchaseInfo.supplierId)).toBe(true);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  // describe('create appliances api', () => {
  //   it('should return a status of 201 for successfully creating appliance', async () => {
  //     jest.spyOn(service, 'createAppliance').mockResolvedValue(appliance);

  //     const result = await controller.createAppliance(dto, request);

  //     expect(result.statusCode).toBe(201);
  //     expect(result.data).toEqual(appliance);
  //     expect(result.message).toBe('Successfully created record for appliance');
  //   });

  //   it('should throw exception when the service function throws', () => {
  //     jest
  //       .spyOn(service, 'createAppliance')
  //       .mockRejectedValue(new Error('Failed to create appliance'));

  //     expect(async () => {
  //       await controller.createAppliance(dto, request);
  //     }).rejects.toThrow('Failed to create appliance');
  //   });
  // });

  describe('get all appliances api', () => {
    const getAppliancesDto = {
      searchInput: 'panason',
      page: 2,
      limit: 10,
      sortBy: SORT_BY.CREATED_AT,
      sortOrder: SORT_ORDER.ASC,
    };
    it('should return a status code of 200 for fetching all appliances', async () => {
      jest.spyOn(service, 'getAllAppliances').mockResolvedValue(appliances);

      const result = await controller.getAllAppliances(getAppliancesDto);

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(appliances.data);
      expect(result.message).toBe('Successfully fetched appliance record');
    });

    it('should throw exception when the service function throws', () => {
      jest
        .spyOn(service, 'getAllAppliances')
        .mockRejectedValue(new Error('Failed to fetch appliances'));

      expect(async () => {
        await controller.getAllAppliances(getAppliancesDto);
      }).rejects.toThrow('Failed to fetch appliances');
    });
  });

  describe('update appliance api', () => {
    it('should return the updated appliance object with status code of 200', async () => {
      jest.spyOn(service, 'updateAppliance').mockResolvedValue(appliance);

      const result = await controller.updateAppliance(
        appliance.id,
        dto,
        request,
      );

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(appliance);
      expect(result.message).toBe('Successfully updated record for appliance');
    });

    it('should throw exception when the service function throws', () => {
      jest
        .spyOn(service, 'updateAppliance')
        .mockRejectedValue(new Error('Failed to update appliance'));

      expect(async () => {
        await controller.updateAppliance(appliance.id, dto, request);
      }).rejects.toThrow('Failed to update appliance');
    });
  });

  describe('delete appliance api (soft delete)', () => {
    it('should soft delete the appliance with a status code of 200', async () => {
      jest
        .spyOn(service, 'deleteAppliance')
        .mockResolvedValue({ success: true });

      const result = await controller.deleteAppliance(appliance.id, request);

      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(true);
      expect(result.message).toBe(
        `Deleted appliance record with id ${appliance.id}`,
      );
    });

    it('should throw exception when the service function throws', () => {
      jest
        .spyOn(service, 'deleteAppliance')
        .mockRejectedValue(new Error('Failed to delete appliance'));

      expect(async () => {
        await controller.deleteAppliance(appliance.id, request);
      }).rejects.toThrow('Failed to delete appliance');
    });
  });
});
