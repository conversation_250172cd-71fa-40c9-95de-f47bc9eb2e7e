import { Test, TestingModule } from '@nestjs/testing';
import { AwsService } from './aws.service';
import { AppModule } from 'src/app.module';

describe('AwsService', () => {
  let service: AwsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    service = module.get<AwsService>(AwsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
