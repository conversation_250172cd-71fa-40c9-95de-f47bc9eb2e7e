import {
  Controller,
  HttpStatus,
  Logger,
  Param,
  ParseEnumPipe,
  Post,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AwsService } from './aws.service';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { EntityType } from './dto/aws.dto';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { INTERNAL_ERROR } from 'src/constants/message-constants';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { S3Guard } from './s3.guard';

@ApiTags('AWS')
@Controller('aws')
export class AwsController {
  private logger = new Logger('AwsController');
  constructor(private readonly awsService: AwsService) {}

  @Post('upload-file/:entityType')
  @ApiBearerAuth('access-token')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOkResponse({
    description: 'Successfully fetched asset',
    schema: { $ref: getSchemaPath(HTTPResponseDto<{ fileName: string }>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to upload image to ',
    summary: 'Fetches asset with given Id',
  })
  @ApiParam({
    name: 'entityType',
    enum: EntityType,
    example: 'ASSET',
    description: 'Enter entity of image to upload',
  })
  @ApiExtraModels(HTTPResponseDto<{ fileName: string }>)
  @UseGuards(S3Guard)
  async uploadFile(
    @UploadedFile() file: Express.Multer.File,
    @Param('entityType', new ParseEnumPipe(EntityType)) entityType: string,
  ): Promise<HTTPResponseDto<{ fileName: string }>> {
    this.logger.log('API to Upload File on S3 Bucket');
    const uploadedFileName = await this.awsService.uploadFile(
      file.buffer,
      file.mimetype,
      entityType,
    );

    return {
      statusCode: HttpStatus.OK,
      data: uploadedFileName,
      message: 'File uploaded successfully',
    };
  }

  @Post('upload-files/:entityType')
  @ApiBearerAuth('access-token')
  @UseInterceptors(FilesInterceptor('file'))
  @ApiOkResponse({
    description: 'Successfully fetched asset',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<{ fileName: string[] }>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to upload image to ',
    summary: 'Fetches asset with given Id',
  })
  @ApiParam({
    name: 'entityType',
    enum: EntityType,
    example: 'ASSET',
    description: 'Enter entity of image to upload',
  })
  @ApiExtraModels(HTTPResponseDto<{ fileName: string[] }>)
  @UseGuards(S3Guard)
  async uploadFiles(
    @UploadedFiles()
    files: Array<Express.Multer.File>,
    @Param('entityType') entityType: string,
  ): Promise<HTTPResponseDto<{ fileName: string[] }>> {
    this.logger.log('API to Upload File on S3 Bucket');

    const uploadedFileNames: string[] = [];
    for (const file of files) {
      const uploadedFileName = await this.awsService.uploadFile(
        file.buffer,
        file.mimetype,
        entityType,
      );
      uploadedFileNames.push(uploadedFileName.fileName);
    }

    return {
      statusCode: HttpStatus.OK,
      data: { fileName: uploadedFileNames },
      message: 'File uploaded successfully',
    };
  }
}
