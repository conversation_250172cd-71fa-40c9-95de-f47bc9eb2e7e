import {
  SESv2Client,
  SendEmailCommand,
  SendEmailCommandOutput,
} from '@aws-sdk/client-sesv2';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { AssignmentResponseDto } from 'src/assignment/dto/create-assignment.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  userAcknowledgementEmail,
  userAcknowledgedEmail,
  entityExpirationEmail,
  insuranceExpiryAlert,
  assignmentNotifyEmail,
  unassignmentNotifyEmail,
  reminderEmail,
  acknowledgmentReminderEmail,
  notifyForSupplierEvaluation,
} from '@email-templates/asset-management';
import {
  DeleteObjectCommand,
  GetObjectCommand,
  ListObjectsV2Command,
  ListObjectsV2Output,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as crypto from 'crypto';
import {
  AcknowledgementReminderDto,
  AssignmentNotifyDto,
  EntityExpiryEmailDto,
  InsuranceExpiryAlertDto,
  NotifyUserForSupplierEvaluationDto,
  ReminderEmailDto,
  UnassignmentNotifyDto,
} from './dto/aws.dto';
import {
  camelCaseToCapitalizedWords,
  getGlobalNotifyUsersEmail,
  upperSnakeCaseToCamelCase,
} from 'src/utility';
import { format } from 'date-fns';
import { HistoryActions } from '@prisma-clients/asset-management-backend';

type ResponseMetadata = SendEmailCommandOutput['$metadata'];
type TableName = 'asset' | 'manufacturer' | 'customFields';
interface WhereInput {
  [key: string]: string;
}
interface SelectedArgs {
  [key: string]: boolean | SelectedArgs;
}

@Injectable()
export class AwsService {
  private logger = new Logger('AwsService');
  private sesClient: SESv2Client;
  private s3Client: S3Client;

  constructor(private readonly prisma: PrismaService) {
    this.sesClient = new SESv2Client({ region: process.env.AWS_REGION });
    this.s3Client = new S3Client();
  }

  // Generates a random file name based on content type, categorizing it as either an image or a document.
  private generateFileName = (contentType: string, bytes = 32): string => {
    const randomFileName = crypto.randomBytes(bytes).toString('hex');
    const fileName = contentType.startsWith('image/')
      ? `images/${randomFileName}`
      : `documents/${randomFileName}`;
    return fileName;
  };

  /**
   * Retireves entities from the database, based on the table name passed with the where condition provided, and selects the
   * properties mentioned.
   * @param { TableName } tableName - The name of the table from which entities will be retrieved.
   * @param { WhereInput } whereInput - An object representing the filtering conditions for the query.
   * @param { SelectedArgs } selectedArgs - An object specifying the properties to be selected for the retrieved entities.
   */
  async retrieveEntitiesBasedOnTableAndFilters(
    tableName: TableName,
    whereInput: WhereInput,
    selectedArgs: SelectedArgs,
  ) {
    return await this.prisma[tableName.toString()].findFirst({
      where: {
        ...whereInput,
      },
      select: {
        ...selectedArgs,
      },
    });
  }

  /**
   * Sends an acknowledgement email for a given assignment.
   * @async
   * @param {AssignmentResponseDto} assignmentDetails - Details of the assignment.
   * @returns {Promise<ResponseMetadata>} - Metadata of the response.
   */
  async sendAcknowledgementEmail(
    assignmentDetails: AssignmentResponseDto,
  ): Promise<ResponseMetadata> {
    const entityDetails = await this.retrieveEntitiesBasedOnTableAndFilters(
      'asset',
      { id: assignmentDetails.entityId },
      {
        id: true,
        assetName: true,
        assetTag: true,
        assetModel: true,
        customFields: true,
        serialNumber: true,
        location: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    );

    const manufacturer =
      entityDetails.assetModel && entityDetails.assetModel['manufacturerId']
        ? await this.retrieveEntitiesBasedOnTableAndFilters(
            'manufacturer',
            { id: entityDetails.assetModel.manufacturerId },
            {
              name: true,
            },
          )
        : undefined;

    const operatingSystemFieldDetails =
      await this.retrieveEntitiesBasedOnTableAndFilters(
        'customFields',
        { fieldName: 'Operating System' },
        { id: true },
      );

    const ramFieldDetails = await this.retrieveEntitiesBasedOnTableAndFilters(
      'customFields',
      { fieldName: 'Ram- G B' },
      { id: true },
    );

    const customFieldData = entityDetails.customFields
      ? entityDetails.customFields['data']
      : undefined;

    const operatingSystem = operatingSystemFieldDetails
      ? customFieldData[operatingSystemFieldDetails.id]
      : undefined;
    const ram = ramFieldDetails
      ? customFieldData[ramFieldDetails.id]
      : undefined;
    const response = await this.sesClient.send(
      new SendEmailCommand({
        FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
        Destination: {
          ToAddresses: [assignmentDetails.user.email],
        },
        Content: {
          Simple: {
            Subject: {
              Data: `[Asset Management] - ${
                entityDetails.assetModel?.modelName.toUpperCase() ?? 'New'
              } Asset Assigned to ${assignmentDetails.user.name}`,
            },
            Body: {
              Html: {
                Data: userAcknowledgementEmail({
                  id: assignmentDetails.id,
                  user: {
                    id: assignmentDetails.user.id,
                    name: assignmentDetails.user.name,
                    email: assignmentDetails.user.email,
                  },
                  entityDetails: {
                    id: entityDetails.id,
                    entityName: entityDetails.assetName,
                    assetTag: entityDetails.assetTag,
                    model: entityDetails.assetModel?.modelName,
                    serialNumber: entityDetails.serialNumber,
                    operatingSystem,
                    manufacturer: manufacturer?.name,
                    ram,
                    location: entityDetails?.location?.name?.toLowerCase(),
                  },
                }),
              },
            },
          },
        },
      }),
    );

    return response.$metadata;
  }

  /**
   * Sends an email to the system admin acknowledging an assignment.
   * @async
   * @param {AssignmentResponseDto} assignmentDetails - Details of the assignment.
   * @returns {Promise<ResponseMetadata>} - Metadata of the response.
   */
  async sendAcknowledgedEmail(
    assignmentDetails: AssignmentResponseDto,
  ): Promise<ResponseMetadata> {
    const entityDetails = await this.prisma.asset.findUnique({
      where: {
        id: assignmentDetails.entityId,
      },
      select: {
        id: true,
        assetName: true,
        assetTag: true,
        assetModel: true,
        customFields: true,
        serialNumber: true,
        location: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    const manufacturer =
      entityDetails.assetModel && entityDetails.assetModel['manufacturerId']
        ? await this.prisma.manufacturer.findUnique({
            where: {
              id: entityDetails.assetModel.manufacturerId,
            },
            select: {
              name: true,
            },
          })
        : undefined;

    // To get Operating system and RAM values, first retrieve the id's for these custom Fields, and
    // then get the values for the id's in customFields of an asset.
    const operatingSystemFieldDetails =
      await this.prisma.customFields.findFirst({
        where: {
          fieldName: 'Operating System',
        },
        select: {
          id: true,
        },
      });

    const ramFieldDetails = await this.prisma.customFields.findFirst({
      where: {
        fieldName: 'RAM - GB',
      },
      select: {
        id: true,
      },
    });
    const customFieldData = entityDetails.customFields
      ? entityDetails.customFields['data']
      : undefined;

    const operatingSystem = operatingSystemFieldDetails
      ? customFieldData[operatingSystemFieldDetails.id]
      : undefined;
    const ram = ramFieldDetails
      ? customFieldData[ramFieldDetails.id]
      : undefined;

    const response = await this.sesClient.send(
      new SendEmailCommand({
        FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
        Destination: {
          ToAddresses: [process.env.SYSTEM_ADMIN_EMAIL],
        },
        Content: {
          Simple: {
            Subject: {
              Data: `[Asset Management] - ${
                entityDetails.assetModel?.modelName.toUpperCase() ?? ''
              }  Asset assignment acknowledgement`,
            },
            Body: {
              Html: {
                Data: userAcknowledgedEmail({
                  user: { name: assignmentDetails.user.name },
                  entityDetails: {
                    entityName: entityDetails.assetName,
                    assetTag: entityDetails.assetTag,
                    model: entityDetails.assetModel?.modelName,
                    serialNumber: entityDetails.serialNumber,
                    operatingSystem,
                    manufacturer: manufacturer?.name,
                    ram,
                    location: entityDetails.location.name.toLowerCase(),
                  },
                }),
              },
            },
          },
        },
      }),
    );

    return response.$metadata;
  }

  /**
   * Sends an email notification to the system administrator alerting them of assets and licenses expiring soon, as well as appliances due for service.
   *
   * @param dto The email metadata.
   * @returns Metadata of the sent email.
   */
  async sendEmailOnEntityExpiry(dto: EntityExpiryEmailDto) {
    const response = await this.sesClient.send(
      new SendEmailCommand({
        FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
        Destination: {
          ToAddresses: [process.env.SYSTEM_ADMIN_EMAIL],
        },
        Content: {
          Simple: {
            Subject: {
              Data: '[TEST] - (CodeCraft Asset-Management) Entity Expiration and due for service',
            },
            Body: {
              Html: {
                Data: entityExpirationEmail(dto),
              },
            },
          },
        },
      }),
    );

    return response.$metadata;
  }

  /**
   * Retrieves the signed URL of a file associated with a document ID.
   * @param {string} fileName - The file name.
   * @returns {Promise<string>} A Promise resolving to an object containing the file URL.
   * @throws {NotFoundException} Throws a NotFoundException if the document or file is not found.
   */
  async getFileUrl(fileName: string): Promise<string> {
    const listCommand = new ListObjectsV2Command({
      Bucket: process.env.AWS_BUCKET_NAME,
    });
    const objectLists: ListObjectsV2Output =
      await this.s3Client.send(listCommand);
    const filePath = objectLists.Contents?.find(
      (obj) => obj.Key?.endsWith(fileName),
    );
    if (filePath === undefined) {
      this.logger.error('The document not found in s3 bucket');
      throw new NotFoundException('The file not found');
    }
    const file = new GetObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: filePath.Key,
    });
    const fileUrl = await getSignedUrl(this.s3Client, file, {
      expiresIn: Number(process.env.URL_EXPIRE_TIME),
    });
    return fileUrl;
  }

  /**
   * Uploads an image to S3 bucket.
   * @param {Buffer} file - The image file to upload.
   * @param {string} contentType - The content type of the image.
   * @param {string} entityType - The type of entity associated with the image (e.g., user, product).
   * @returns {Promise<string>} A promise that resolves with the uploaded file name.
   * @throws {BadRequestException} Throws an error if the file is invalid.
   * @throws {InternalServerErrorException} Throws an error if the upload fails.
   */
  async uploadFile(
    file: Buffer,
    contentType: string,
    entityType: string,
  ): Promise<{ fileName: string }> {
    if (!Object.keys(file).length) {
      throw new BadRequestException('File is empty or invalid');
    }
    const fileName = this.generateFileName(contentType);
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: `${entityType}/${fileName}`,
      ContentType: contentType,
      Body: file,
    });

    const response = await this.s3Client.send(command);

    if (response.$metadata.httpStatusCode === 200) {
      return { fileName };
    }

    throw new InternalServerErrorException('Failed to upload image');
  }

  /**
   * Deletes a file from the AWS S3 bucket.
   * @param {string} fileName - The name of the file to be deleted.
   * @throws {Error} If there's an error while deleting the file from the S3 bucket.
   * @returns {Promise<void>} A Promise that resolves when the file is successfully deleted.
   */
  async deleteFile(fileName: string): Promise<boolean> {
    try {
      let actualFileName = '';
      if (fileName) {
        actualFileName = fileName.split('/').slice(3).join('/');
      }
      const object = new DeleteObjectCommand({
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: actualFileName,
      });
      await this.s3Client.send(object);
      this.logger.log('The file removed from the s3 bucket');
      return true;
    } catch (error) {
      this.logger.log(`Failed to delete file from s3 bucket ${error.message}`);
      throw new InternalServerErrorException('Failed to delete file from s3');
    }
  }

  /**
   * Sends email alerts for insurance expiration.
   * @param {InsuranceExpireAlertDto[]} dto - Array of InsuranceExpireAlertDto objects containing insurance expiration details.
   */
  async insuranceExpiryEmail(dto: InsuranceExpiryAlertDto[]) {
    const emailFormat = new SendEmailCommand({
      FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
      Destination: {
        ToAddresses: [process.env.SYSTEM_ADMIN_EMAIL],
      },
      Content: {
        Simple: {
          Subject: {
            Data: '"CodeCraft Asset-Management"- Insurance Expiration Alert',
          },
          Body: {
            Html: {
              Data: insuranceExpiryAlert(dto),
            },
          },
        },
      },
    });
    const response = await this.sesClient.send(emailFormat);
    return response.$metadata;
  }

  /**
   * Sends an email notification for assignment.
   * @param {AssignmentNotifyDto} dto - Data transfer object containing assignment details.
   * @returns A promise that resolves when all acknowledgment reminder emails are sent.
   */
  async assignmentNotifyEmail(dto: AssignmentNotifyDto, tag?: string) {
    const entityTable = upperSnakeCaseToCamelCase(dto.typeOfAssignment);
    const selectField =
      entityTable !== 'asset'
        ? { name: true, category: { select: { name: true } } }
        : {
            assetName: true,
            location: {
              select: {
                id: true,
                name: true,
              },
            },
            assetTag: true,
            serialNumber: true,
          };
    const entity = await this.prisma[entityTable].findFirst({
      where: {
        id: dto.entityId,
      },
      select: selectField,
    });
    const entityName = entity.assetName ?? entity.name;

    const notifyUsersEmail = dto.notifyUser.map((user) => user.email);
    const globalNotifyUserEmails = await getGlobalNotifyUsersEmail(
      dto.typeOfAssignment,
      HistoryActions.ASSIGNED,
      this.prisma,
    );
    const combinedNotifyUsers = [
      ...notifyUsersEmail,
      ...globalNotifyUserEmails,
    ];
    const notifyUsers = [...new Set(combinedNotifyUsers)];

    let toAddresses: null | string[] = null;
    let ccAddresses: null | string[] = null;

    // Check if user email is available
    const userEmail = dto.user?.email;

    if (entityTable === 'asset') {
      if (notifyUsers.length > 0) {
        toAddresses = notifyUsers;
        ccAddresses = undefined;
      } else {
        // If no notify users and no user email, exit the function
        if (!userEmail) {
          return;
        }
        // If user email exists but no notify users
        toAddresses = [userEmail];
        ccAddresses = undefined;
      }
    } else {
      if (userEmail) {
        toAddresses = [userEmail];
        ccAddresses = notifyUsers.length > 0 ? notifyUsers : undefined;
      } else {
        // If no user email, send only to notify users
        if (notifyUsers.length > 0) {
          toAddresses = notifyUsers;
          ccAddresses = undefined;
        } else {
          // No user and no notify users to send email to, exit the function
          return;
        }
      }
    }

    const resourceName = camelCaseToCapitalizedWords(entityTable);
    const emailFormat = new SendEmailCommand({
      FromEmailAddress:
        entityTable === 'consumable'
          ? process.env.OPERATIONS_EMAIL
          : process.env.ASSET_MANAGEMENT_EMAIL,
      Destination: {
        ToAddresses: toAddresses,
        CcAddresses: ccAddresses,
      },
      Content: {
        Simple: {
          Subject: {
            Data: `"CodeCraft Asset-Management"- ${resourceName} Assigned to ${
              dto.user?.name ?? 'User'
            }`,
          },
          Body: {
            Html: {
              Data: assignmentNotifyEmail({
                user: dto.user,
                entityName: entityName,
                assignedDate: dto.date,
                assignedNote: dto.note,
                tag: tag,
                entityType: resourceName,
                assetId: entity.assetTag ?? undefined,
                category: entity.category?.name ?? undefined,
                location: entity.location?.name?.toLowerCase() ?? undefined,
                serialNumber: entity?.serialNumber ?? undefined,
              }),
            },
          },
        },
      },
    });

    const response = await this.sesClient.send(emailFormat);
    return response.$metadata;
  }

  /**
   * Sends an email notification for unassignment.
   * @param {UnassignmentNotifyDto} dto - Data transfer object containing unassignment details.
   * @param {string} assignedUserEmail - Email address of the assigned user.
   * @returns A promise that resolves when all acknowledgment reminder emails are sent.
   */
  async unassignmentNotifyEmail(
    dto: UnassignmentNotifyDto,
    assignedUserEmail: string,
  ) {
    const entityTable = upperSnakeCaseToCamelCase(dto.typeOfCategory);
    const selectField =
      entityTable !== 'asset'
        ? { name: true, category: { select: { name: true } } }
        : {
            assetName: true,
            location: {
              select: {
                id: true,
                name: true,
              },
            },
            assetTag: true,
            serialNumber: true,
          };
    const entity = await this.prisma[entityTable].findFirst({
      where: {
        id: dto.entityId,
      },
      select: selectField,
    });
    const entityName = entity.assetName ?? entity.name;
    const notifyUsersEmail =
      dto.notifyUser !== undefined
        ? dto.notifyUser.map((user) => {
            return user.email;
          })
        : undefined;
    const globalNotifyUserEmails = await getGlobalNotifyUsersEmail(
      dto.typeOfCategory,
      HistoryActions.ASSIGNED,
      this.prisma,
    );
    const combinedNotifyUsers = [
      ...notifyUsersEmail,
      ...globalNotifyUserEmails,
    ];
    const notifyUsers = [...new Set(combinedNotifyUsers)];
    const resourceName = camelCaseToCapitalizedWords(entityTable);
    const emailFormat = new SendEmailCommand({
      FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
      Destination: {
        ToAddresses: [assignedUserEmail],
        CcAddresses: notifyUsers,
      },
      Content: {
        Simple: {
          Subject: {
            Data: `"CodeCraft Asset-Management"- Unassignment of ${resourceName} from ${dto.assignedUser}`,
          },
          Body: {
            Html: {
              Data: unassignmentNotifyEmail({
                assignedUserName: dto.assignedUser,
                entityName: entityName,
                assignedDate: dto.assignedDate,
                unAssignedDate: dto.unAssignedDate,
                unAssignedNote: dto.unAssignedNote,
                entityType: resourceName,
                assetId: entity.assetTag ?? undefined,
                location: entity.location?.name?.toLowerCase() ?? undefined,
                category: entity.category?.name ?? undefined,
                serialNumber: entity.serialNumber ?? undefined,
              }),
            },
          },
        },
      },
    });
    const response = await this.sesClient.send(emailFormat);
    return response.$metadata;
  }

  /**
   * Sends an email reminder for asset acknowledgment.
   * @param {AcknowledgementRemainderDto} dto - Data transfer object containing acknowledgment reminder details.
   * @returns A promise that resolves when all acknowledgment reminder emails are sent.
   */
  async acknowledgmentReminderEmail(dto: AcknowledgementReminderDto) {
    const asset = await this.prisma.asset.findFirst({
      where: {
        id: dto.entityId,
        isDeleted: false,
      },
      select: {
        assetName: true,
        serialNumber: true,
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        assetTag: true,
      },
    });
    if (asset) {
      const emailFormat = new SendEmailCommand({
        FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
        Destination: {
          ToAddresses: [dto.user.email],
        },
        Content: {
          Simple: {
            Subject: {
              Data: '"CodeCraft Asset-Management"- Asset Acknowledgment Remainder',
            },
            Body: {
              Html: {
                Data: acknowledgmentReminderEmail({
                  assignedDate: dto.date,
                  assignedNote: dto.note,
                  assignedUser: dto.user.name,
                  entityName: asset.assetName,
                  serialNumber: asset.serialNumber,
                  location: asset.location.name.toLowerCase(),
                  assetId: asset.assetTag,
                }),
              },
            },
          },
        },
      });
      const response = await this.sesClient.send(emailFormat);
      return response.$metadata;
    }
    return;
  }

  async ReminderEmailNotification(dto: ReminderEmailDto) {
    const currentDate = format(new Date(), 'iiii, d MMMM');

    const emailFormat = new SendEmailCommand({
      FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
      Destination: {
        ToAddresses: [dto.user],
      },
      Content: {
        Simple: {
          Subject: {
            Data: '"CodeCraft Asset-Management" - Reminder',
          },
          Body: {
            Html: {
              Data: reminderEmail({
                currentDate,
                user: dto.user,
                reminders: dto.reminders,
              }),
            },
          },
        },
      },
    });
    const response = await this.sesClient.send(emailFormat);
    return response.$metadata;
  }

  async uploadExcelFile(
    file: Buffer,
    entityType: string,
  ): Promise<{ fileName: string }> {
    return this.uploadFile(
      file,
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      entityType,
    );
  }

  /** notifying user for creation of new supplier */
  async notifyUserForSupplierEvaluation(
    dto: NotifyUserForSupplierEvaluationDto,
  ) {
    const emailFormat = new SendEmailCommand({
      FromEmailAddress: process.env.ASSET_MANAGEMENT_EMAIL,
      Destination: {
        ToAddresses: [...dto.users.map((user) => user.email)],
      },
      Content: {
        Simple: {
          Subject: {
            Data: '"CodeCraft Asset-Management" - Reminder to Supplier evaluation',
          },
          Body: {
            Html: {
              Data: notifyForSupplierEvaluation({
                supplierName: dto.supplierName,
                location: dto.supplierLocation.join(','),
              }),
            },
          },
        },
      },
    });
    const response = await this.sesClient.send(emailFormat);
    return response.$metadata;
  }

  /**
   * Uploads an image to S3 bucket.
   * @param {Buffer} file - The image file to upload.
   * @param {string} contentType - The content type of the image.
   * @param {string} entityType - The type of entity associated with the image (e.g., user, product).
   * @returns {Promise<string>} A promise that resolves with the uploaded file name.
   * @throws {BadRequestException} Throws an error if the file is invalid.
   * @throws {InternalServerErrorException} Throws an error if the upload fails.
   */
  async uploadItDeclarationForm(
    file: Buffer,
    fileName: string,
  ): Promise<{ fileName: string }> {
    if (!Object.keys(file).length) {
      throw new BadRequestException('File is empty or invalid');
    }
    const command = new PutObjectCommand({
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: `it-declaration-forms/${fileName}`,
      ContentType:
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      Body: file,
    });

    const response = await this.s3Client.send(command);

    if (response.$metadata.httpStatusCode === 200) {
      return { fileName };
    }

    throw new InternalServerErrorException('Failed to upload file');
  }
}
