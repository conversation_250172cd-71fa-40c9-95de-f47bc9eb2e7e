import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { AbilityFactory } from 'src/ability/ability.factory/ability.factory';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { PERMISSION_DENIED_MESSAGE } from 'src/constants/message-constants';

@Injectable()
export class S3Guard implements CanActivate {
  constructor(private readonly abilityFactory: AbilityFactory) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const entityType = request.params.entityType;
    const ability = this.abilityFactory.defineAbility(user);

    const canPerformAction =
      ability.can(Action.CREATE, entityType as Subject) ||
      ability.can(Action.UPDATE, entityType as Subject);

    if (!canPerformAction) {
      throw new ForbiddenException(PERMISSION_DENIED_MESSAGE);
    }

    return true;
  }
}
