import { Test, TestingModule } from '@nestjs/testing';
import { AwsController } from './aws.controller';
import { AppModule } from 'src/app.module';

describe('AwsController', () => {
  let controller: AwsController;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    controller = module.get<AwsController>(AwsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
