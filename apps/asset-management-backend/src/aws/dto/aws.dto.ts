import {
  TypeOfCategory,
  TypeOfPolicy,
} from '@prisma-clients/asset-management-backend';

export enum EntityType {
  Asset = 'assets',
  Accessory = 'accessories',
  Consumable = 'consumables',
  Liscence = 'licenses',
  Manufacturer = 'manufacturers',
  Supplier = 'suppliers',
  Appliance = 'appliances',
  AssetModel = 'asset-model',
  ApplianceService = 'appliances-service',
  AssetAudit = 'asset-audit',
  Insurance = 'insurance',
  Policy = 'policies',
  Service = 'services',
}

export class EntityExpiryEmailDto {
  dates: {
    currentDate: string;
    assetExpiry: string;
    licenseExpiry: string;
    applianceExpiry: string;
  };
  entityData: {
    assets: {
      assetName: string;
    }[];
    licenses: {
      name: string;
    }[];
    appliances: {
      appliances: {
        id: string;
        name: string;
      }[];
    }[];
  };
}

export class InsuranceExpiryAlertDto {
  id: string;
  name: string;
  endDate: Date;
  company: { name: string };
  vendor: { name: string };
}

export class AssignmentNotifyDto {
  user: {
    id?: string;
    name?: string;
    email?: string;
  };
  entityId: string;
  notifyUser: {
    id?: string;
    name?: string;
    email?: string;
  }[];
  typeOfAssignment: TypeOfCategory;
  note: string;
  date: Date;
}

export class UnassignmentNotifyDto {
  assignedUser: string;
  entityId: string;
  notifyUser: {
    id?: string;
    name?: string;
    email?: string;
  }[];
  typeOfCategory: TypeOfCategory;
  assignedDate: Date;
  unAssignedDate: Date;
  unAssignedNote: string;
}

export class PolicyReminderDto {
  title?: string;
  policy: {
    name: string;
    startDate: Date;
    endDate: Date;
  };
  notes?: string;
  date: Date;
  typeOfPolicy: TypeOfPolicy;
  remainingDays: number;
}

export interface AcknowledgementReminderDto {
  user: {
    id: string;
    name: string;
    email: string;
  };
  entityId: string;
  date: Date;
  note: string;
}

type ReminderCategory = 'POLICY' | 'ASSET' | 'SOFTWARE_LICENSE' | 'APPLIANCE';
export class ReminderDto {
  title: string;
  notes?: string;
  endDate: Date;
  asset?: {
    assetName: string;
  };
  softwareLicense?: {
    name: string;
  };
  policy?: {
    name: string;
  };
  appliance?: {
    name: string;
  };
  typeOfCategory: ReminderCategory;
}

export class ReminderEmailDto {
  user: string;
  reminders: ReminderDto[];
}

export class UserDetailsDto {
  id: string;
  name: string;
  email: string;
}

export class NotifyUserForSupplierEvaluationDto {
  supplierName: string;
  evaluationFreq: string;
  users: UserDetailsDto[];
  supplierLocation: string[];
}
