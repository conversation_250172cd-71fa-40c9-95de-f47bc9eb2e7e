import { Logger, NotFoundException } from '@nestjs/common';
import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import {
  AccessoryResponseDto,
  UpdateAccessoryDto,
} from 'src/accessory/dto/accessory.dto';
import {
  ApplianceData,
  ApplianceReqDto,
} from 'src/appliance/dto/appliance.dto';
import { GetAssetResponseDto, UpdateAssetDto } from 'src/asset/dto/asset.dto';
import {
  Accessory,
  Appliance,
  Asset,
  HistoryActions,
  Service,
  SoftwareLicense,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import {
  GetConsumableResponseDto,
  UpdateConsumableDto,
} from 'src/consumables/dto/consumable.dto';
import {
  LicenseDto,
  UpdateLicenseDto,
} from 'src/software-license/dto/software-license.dto';
import {
  GetPolicyResponseDto,
  UpdatePolicyDto,
} from 'src/policies/dto/policies.dto';
import {
  GetServiceResponseDto,
  ServiceRequestDto,
} from 'src/service/dto/service-dto';
import * as XLSX from 'xlsx';
import { JsonValue } from 'types';
import { PrismaService } from 'src/prisma/prisma.service';

const logger: Logger = new Logger('Exception');

type UpdatedField = {
  fieldUpdated: string;
  from: string | number;
  to: string | number;
};

type EntityName =
  | 'accessory'
  | 'asset'
  | 'softwareLicense'
  | 'consumable'
  | 'appliance'
  | 'policy'
  | 'service';

/**
 * Deeply compares two values to determine if they are equal using recursion.
 * @param {*} a - The first value to compare.
 * @param {*} b - The second value to compare.
 * @returns {boolean} Returns true if the values are deeply equal, otherwise returns false.
 */
export function deepCompare(a: any, b: any): boolean {
  if (a === null && b === null) {
    return true;
  }

  if (typeof a !== typeof b || a === null || b === null) {
    return false;
  }

  if (a instanceof Date && b instanceof Date) {
    // Compare dates by their timestamps
    return a.getTime() === b.getTime();
  }

  if (typeof a === 'object' && typeof b === 'object') {
    // If both the params are of type object, get the keys
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);

    if (keysA.length !== keysB.length) {
      return false;
    }

    for (const key of keysA) {
      // recursion to check if each values are equal
      if (!deepCompare(a[key], b[key])) {
        return false;
      }
    }

    return true;
  }

  return a === b;
}

export function getUpdatedFields<A extends object>(
  existingData: A,
  updatedData: A,
): UpdatedField[] {
  return Object.keys(updatedData)
    .map((key: string) => {
      if (!deepCompare(updatedData[key], existingData[key])) {
        return {
          fieldUpdated: key,
          from: existingData[key],
          to: updatedData[key],
        };
      }
    })
    .filter(Boolean);
}

export function checkErrorAndThrowNotFoundError(error: any): any {
  const entitiesToCheck: string[] = [
    'Category',
    'Supplier',
    'Manufacturer',
    'User',
    'Assignment',
  ];
  const stringToCheck: string = error?.meta?.cause.toString();
  const matchingEntity: string = entitiesToCheck.find((entity) =>
    stringToCheck.includes(entity),
  );

  if (matchingEntity) {
    const notFoundError = `${matchingEntity} not found`;
    logger.log(notFoundError);
    throw new NotFoundException(notFoundError);
  }
}

export function upperSnakeCaseToCamelCase(enumValue: string): string {
  const words = enumValue.toLowerCase().split('_');
  const camelCaseWords = words.map((word, index) =>
    index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1),
  );
  return camelCaseWords.join('');
}

export function setDateWithZeroTime(date: Date) {
  const dateWithZeroTime = date.toString().split('T')[0] + 'T00:00:00Z';
  return dateWithZeroTime;
}

const UUID_PATTERN =
  /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/;

/**
 * This function validates an input array of uuid's. It validates all the values inside the array and checks if all values are UUID.
 * @returns true if validation passes, false if fails
 */
export function IsArrayOfUUIDs(validationOptions?: ValidationOptions) {
  return function (object: any, propertyName: string) {
    registerDecorator({
      name: 'isArrayOfUUIDs',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (!Array.isArray(value)) {
            return false;
          }
          for (const uuid of value) {
            if (!UUID_PATTERN.test(uuid)) {
              return false;
            }
          }
          return true;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be an array of valid UUIDs`;
        },
      },
    });
  };
}

export function toPascalCaseWithSpace(enumValue: string): string {
  const words = enumValue.toLowerCase().split(' ');
  const pascalCaseWord = words.map(
    (word) => (word = word.charAt(0).toUpperCase() + word.slice(1)),
  );
  return pascalCaseWord.join(' ');
}

export function camelCasetoPascalCaseWithSpaces(enumValue: string): string {
  const words = enumValue.split(/(?=[A-Z])|_/);

  const pascalCaseWithSpace = words
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  return pascalCaseWithSpace;
}

/**
 * Updates field groups for a specific entity.
 *
 * prisma - The Prisma client instance.
 * entityName - The name of the entity model in the Prisma schema.
 * entityId - The ID of the entity to update.
 * entityInfo - The response DTO containing information about the entity.
 * entityDto - The DTO containing updated information for the entity.
 * returns - A Promise that resolves when the update operation is complete.
 */
export async function updateFieldGroups(
  prisma,
  entityName: EntityName,
  entityId: string,
  entityInfo:
    | AccessoryResponseDto
    | ApplianceData
    | GetAssetResponseDto
    | GetConsumableResponseDto
    | LicenseDto
    | GetPolicyResponseDto
    | GetServiceResponseDto,
  entityDto:
    | UpdateAccessoryDto
    | ApplianceReqDto
    | UpdateAssetDto
    | UpdateConsumableDto
    | UpdateLicenseDto
    | UpdatePolicyDto
    | ServiceRequestDto,
) {
  const existingFieldGroups = entityInfo.customFields?.['fieldgroups'] ?? [];

  const newFieldGroups = entityDto.customFields['fieldgroups'] || [];

  const replacedFieldGroups =
    existingFieldGroups?.filter((id) => !newFieldGroups?.includes(id)) || true;

  const addedFieldGroups =
    newFieldGroups?.filter((id) => !existingFieldGroups?.includes(id)) || true;

  if (replacedFieldGroups.length > 0) {
    await prisma[entityName].update({
      where: { id: entityId },
      data: {
        fieldGroups: {
          disconnect: replacedFieldGroups.map((id) => ({ id })),
        },
      },
    });
  }

  if (addedFieldGroups.length > 0) {
    await prisma[entityName].update({
      where: { id: entityId },
      data: {
        fieldGroups: {
          connect: addedFieldGroups.map((id) => ({ id })),
        },
      },
    });
  }
}

/**
 * Disconnects existing field groups associated with the specified accessory.
 *
 * prisma - The Prisma client instance.
 * entityName - The name of the entity model in the Prisma schema.
 * entityId - The ID of the entity to disconnect field groups from.
 * entityInfo - The response DTO containing information about the entity.
 * returns - A Promise that resolves when the disconnect operation is complete.
 */
export async function disconnectFieldGroups(
  prisma,
  entityName: EntityName,
  entityId: string,
  entityInfo:
    | Accessory
    | Appliance
    | Asset
    | GetConsumableResponseDto
    | SoftwareLicense
    | Partial<GetPolicyResponseDto>
    | Service,
) {
  const existingFieldGroups =
    (entityInfo.customFields && entityInfo.customFields['fieldgroups']) || [];
  if (existingFieldGroups.length > 0) {
    await prisma[entityName].update({
      where: {
        id: entityId,
      },
      data: {
        fieldGroups: {
          disconnect: existingFieldGroups.map((id) => ({ id })),
        },
      },
    });
  }
}

export function camelCaseToCapitalizedWords(camelCaseWord: string): string {
  return camelCaseWord
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());
}

export function jsonToSheet(
  data: Array<Record<string, string | number | boolean | null | undefined>>,
): Buffer {
  const allHeaders = new Set<string>();
  data.forEach((item) => {
    Object.keys(item).forEach((key) => {
      allHeaders.add(key);
    });
  });
  const headers = Array.from(allHeaders);

  const normalizedData = data.map((item) => {
    const normalizedItem: Record<
      string,
      string | number | boolean | undefined | null
    > = {};
    headers.forEach((header) => {
      normalizedItem[header] = item[header] !== undefined ? item[header] : '';
    });
    return normalizedItem;
  });

  const sheet = XLSX.utils.json_to_sheet(normalizedData);
  const range = XLSX.utils.decode_range(sheet['!ref'] as string);

  for (let C = range.s.c; C <= range.e.c; ++C) {
    const cellAddress = XLSX.utils.encode_cell({ c: C, r: 0 });
    if (!sheet[cellAddress]) continue;

    const originalHeader = headers[C];
    const formattedHeader = originalHeader
      ? camelCaseToCapitalizedWords(originalHeader)
      : originalHeader;
    sheet[cellAddress].v = formattedHeader;
  }
  const book = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(book, sheet, 'ResourcesData');
  const bookOutput = XLSX.write(book, { bookType: 'xlsx', type: 'buffer' });
  return bookOutput;
}

export async function getCustomFieldsData(
  customFields: JsonValue,
  prisma: PrismaService,
) {
  const customFieldData = {};
  if (
    customFields !== null &&
    typeof customFields === 'object' &&
    'data' in customFields
  ) {
    const parsedCustomFileds = Object.entries(customFields.data).map(
      async ([customFieldId, value]) => {
        const { fieldName } = await prisma.customFields.findFirst({
          where: {
            isDeleted: false,
            id: customFieldId,
          },
          select: {
            fieldName: true,
          },
        });
        customFieldData[fieldName] = value;
      },
    );
    await Promise.all(parsedCustomFileds);
    return customFieldData;
  }
  return customFieldData;
}

export async function getGlobalNotifyUsersEmail(
  typeOfCategory: TypeOfCategory,
  action: HistoryActions,
  prisma: PrismaService,
): Promise<string[]> {
  const globalNotifyUsers = await prisma.globalNotification.findMany({
    where: {
      typeOfCategory: typeOfCategory,
      actions: {
        has: action,
      },
      isDeleted: false,
    },
    select: {
      users: true,
      additionalEmails: true,
    },
  });

  const globalNotifyUserEmails = globalNotifyUsers.flatMap((notification) => {
    const userEmails = notification.users.map((user) => user.email);
    const additionalEmails = notification.additionalEmails;
    return userEmails.concat(additionalEmails);
  });
  return globalNotifyUserEmails;
}
