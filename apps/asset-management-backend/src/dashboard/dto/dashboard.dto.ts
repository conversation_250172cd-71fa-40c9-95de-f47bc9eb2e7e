import { ApiProperty } from '@nestjs/swagger';

class TotalAndAssignedDto {
  @ApiProperty({ description: 'total count of all the entities', example: 10 })
  totalCount: number;

  @ApiProperty({ description: 'total quantity of entities', example: 5 })
  totalQuantity?: number;

  @ApiProperty({ example: 5 })
  assigned?: number;
}

export class GetTotalAndAssignedResponseDto {
  @ApiProperty({ type: TotalAndAssignedDto })
  licenses?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  accessories?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  consumables?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  appliances?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  users?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  assets?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  policies?: TotalAndAssignedDto;

  @ApiProperty({ type: TotalAndAssignedDto })
  services?: TotalAndAssignedDto;
}
