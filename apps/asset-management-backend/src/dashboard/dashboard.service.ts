import { Injectable, Logger } from '@nestjs/common';
import { GetTotalAndAssignedResponseDto } from './dto/dashboard.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { Subject } from 'src/common/enums/ability.enum';

import { User } from 'types';

@Injectable()
export class DashboardService {
  private logger = new Logger('DashboardService');
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Asynchronously retrieves total quantity of licenses and total count for consumables, accessories, appliances, assets, and users.
   * and fetch assignment count of licenses, consumables, accessories, assets.
   * @returns {Promise<GetTotalAndAssignedResponseDto>} A promise that resolves to an object containing the total
   * and assigned information.
   */
  async getTotalAndAssignedCount(
    user: User,
  ): Promise<GetTotalAndAssignedResponseDto> {
    try {
      const userPermissions = user.role.permissions;

      const entityCountsAndAssignments: GetTotalAndAssignedResponseDto = {};

      if (userPermissions[Subject.SOFTWARE_LICENSE]) {
        const licensesData = await this.prisma.softwareLicense.findMany({
          where: {
            isDeleted: false,
          },
        });

        const licenseCount = await this.prisma.softwareLicense.count({
          where: {
            isDeleted: false,
          },
        });
        const licensesTotalQuantity = licensesData.reduce(
          (acc, curr) => acc + curr.totalQuantity,
          0,
        );
        const licensesAssigned = licensesData.reduce(
          (acc, curr) => acc + (curr.totalQuantity - curr.availableQuantity),
          0,
        );

        entityCountsAndAssignments.licenses = {
          totalCount: licenseCount,
          totalQuantity: licensesTotalQuantity,
          assigned: licensesAssigned,
        };
      }

      if (userPermissions[Subject.CONSUMABLE]) {
        const consumablesData = await this.prisma.consumable.findMany({
          where: {
            isDeleted: false,
          },
        });
        const consumablesCount = await this.prisma.consumable.count({
          where: {
            isDeleted: false,
          },
        });
        const consumablesTotalQuantity = consumablesData.reduce(
          (acc, curr) => acc + curr.totalQuantity,
          0,
        );
        const consumablesAssigned = consumablesData.reduce(
          (acc, curr) => acc + (curr.totalQuantity - curr.availableQuantity),
          0,
        );

        entityCountsAndAssignments.consumables = {
          totalCount: consumablesCount,
          totalQuantity: consumablesTotalQuantity,
          assigned: consumablesAssigned,
        };
      }
      if (userPermissions[Subject.ACCESSORY]) {
        const accessoriesData = await this.prisma.accessory.findMany({
          where: {
            isDeleted: false,
          },
        });
        const accessoriesCount = await this.prisma.accessory.count({
          where: {
            isDeleted: false,
          },
        });

        const accessoriesTotalQuantity = accessoriesData.reduce(
          (acc, curr) => acc + curr.totalQuantity,
          0,
        );
        const accessoriesAssigned = accessoriesData.reduce(
          (acc, curr) => acc + (curr.totalQuantity - curr.availableQuantity),
          0,
        );
        entityCountsAndAssignments.accessories = {
          totalCount: accessoriesCount,
          totalQuantity: accessoriesTotalQuantity,
          assigned: accessoriesAssigned,
        };
      }
      if (userPermissions[Subject.ASSET]) {
        const assetsCount = await this.prisma.asset.count({
          where: {
            isDeleted: false,
          },
        });

        const assetAssignmentCount = await this.prisma.assignment.count({
          where: {
            typeOfAssignment: 'ASSET',
          },
        });

        entityCountsAndAssignments.assets = {
          totalCount: assetsCount,
          assigned: assetAssignmentCount,
        };
      }

      if (userPermissions[Subject.APPLIANCE]) {
        const appliancesCount = await this.prisma.appliance.count({
          where: {
            isDeleted: false,
          },
        });

        entityCountsAndAssignments.appliances = {
          totalCount: appliancesCount,
        };
      }

      if (userPermissions[Subject.USER]) {
        const usersCount = await this.prisma.user.count({
          where: {
            isDeleted: false,
          },
        });

        entityCountsAndAssignments.users = {
          totalCount: usersCount,
        };
      }
      if (userPermissions[Subject.POLICY]) {
        const policyCount = await this.prisma.policy.count({
          where: {
            isDeleted: false,
          },
        });
        entityCountsAndAssignments.policies = {
          totalCount: policyCount,
        };
      }

      if (userPermissions[Subject.SERVICE]) {
        const servicesCount = await this.prisma.service.count({
          where: {
            isDeleted: false,
          },
        });

        entityCountsAndAssignments.services = {
          totalCount: servicesCount,
        };
      }
      this.logger.log(
        `Successfully fetched total and assignment count of all entities`,
      );

      return entityCountsAndAssignments;
    } catch (error) {
      this.logger.error(`Failed to fetch data: ${error}`);
      throw error;
    }
  }
}
