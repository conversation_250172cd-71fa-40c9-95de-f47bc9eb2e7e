import {
  <PERSON>,
  Get,
  HttpStatus,
  Logger,
  Req,
  UseGuards,
} from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { HTTPResponseDto } from 'src/common/http/response.dto';
import { GetTotalAndAssignedResponseDto } from './dto/dashboard.dto';
import { Request } from 'express';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { PolicyHandler } from 'src/ability/policy.handler';
import { Action, Subject } from 'src/common/enums/ability.enum';

@ApiTags('Dashboard')
@UseGuards(PermissionGuard)
@ApiBearerAuth('access-token')
@Controller('dashboard')
export class DashboardController {
  private logger = new Logger('DashboardController');
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('entityCount')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description:
      'Successfully fetched total and assignment count of all entities',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetTotalAndAssignedResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Internal Server Error',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Internal Server Error',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    summary: 'Get total and assignment count of all entities',
    description:
      'Successfully fetched total and assignment count of all entities',
  })
  @ApiExtraModels(HTTPResponseDto<GetTotalAndAssignedResponseDto>)
  @CheckPolicies(
    new PolicyHandler(Action.READ, [
      Subject.ASSET,
      Subject.ACCESSORY,
      Subject.APPLIANCE,
      Subject.CONSUMABLE,
      Subject.SOFTWARE_LICENSE,
      Subject.USER,
      Subject.POLICY,
    ]),
  )
  async getAllEntityAssignmentAndTotalCount(
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetTotalAndAssignedResponseDto>> {
    this.logger.log('API to fetch total and assignment count for all entites');
    const data = await this.dashboardService.getTotalAndAssignedCount(
      request.user,
    );
    return {
      statusCode: HttpStatus.OK,
      data: data,
      message:
        'Successfully fetched total and assignment count of all entities',
    };
  }
}
