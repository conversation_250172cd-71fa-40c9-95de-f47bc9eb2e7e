import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AbilityFactory } from './ability.factory/ability.factory';
import { CHECK_POLICIES_KEY } from 'src/decorators';
import { PureAbility } from '@casl/ability';
import { IPolicyHandler } from 'src/ability/policy.handler';
import { PERMISSION_DENIED_MESSAGE } from 'src/constants/message-constants';

@Injectable()
export class PermissionGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private caslAbilityFactory: AbilityFactory,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const { user } = context.switchToHttp().getRequest();
    const policyHandlers =
      this.reflector.get<IPolicyHandler[]>(
        CHECK_POLICIES_KEY,
        context.getHandler(),
      ) || [];

    // Creates a CASL ability instance based on the provided user's permissions.
    const ability = this.caslAbilityFactory.defineAbility(user);

    // Checks if the user's permissions satisfy all policy handlers
    const hasPermissions = policyHandlers.every((handler) =>
      this.execPolicyHandler(handler, ability),
    );

    if (!hasPermissions) {
      throw new ForbiddenException(PERMISSION_DENIED_MESSAGE);
    }
    return hasPermissions;
  }

  /**
   * Executes a policy handler to check permissions.
   * @param handler - The policy handler to execute.
   * @param ability - The CASL ability instance to check permissions against.
   * @returns - A boolean indicating whether the handler allows the permissions.
   */
  private execPolicyHandler(handler: IPolicyHandler, ability: PureAbility) {
    return handler.handle(ability);
  }
}
