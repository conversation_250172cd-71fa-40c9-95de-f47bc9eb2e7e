import { PureAbility } from '@casl/ability';
import { Action, Subject } from 'src/common/enums/ability.enum';

export interface IPolicyHandler {
  /**
   * Checks if the provided ability allows the specified actions on the given subjects.
   * @param ability The PureAbility instance to check permissions against.
   * @returns A boolean indicating whether the ability allows the actions on the subjects.
   */
  handle(ability: PureAbility): boolean;
}

/**
 * A concrete implementation of the IPolicyHandler interface.
 */
export class PolicyHandler implements IPolicyHandler {
  /**
   * Initializes a new instance of the PolicyHandler class.
   *
   * @param actions - The action or array of actions to check permissions for.
   * @param subjects - The subject or array of subjects to check permissions against.
   */
  constructor(
    private readonly actions: Action | Action[],
    private readonly subjects: Subject | Subject[],
  ) {}

  handle(ability: PureAbility) {
    const actionsArray = Array.isArray(this.actions)
      ? this.actions
      : [this.actions];
    const subjectsArray = Array.isArray(this.subjects)
      ? this.subjects
      : [this.subjects];
    return actionsArray.some((action) =>
      subjectsArray.some((subject) => ability.can(action, subject)),
    );
  }
}
