import { AbilityBuilder, AbilityClass, PureAbility } from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { Action, DefaultRoles, Subject } from 'src/common/enums/ability.enum';
import { User } from 'types';

export type AppAbility = PureAbility<[Action, Subject]>;

@Injectable()
export class AbilityFactory {
  /**
   * Defines the CASL ability for the provided user
   * @param user - The user for whom the CASL ability is defined.
   * @returns  A CASL ability instance based on the user's role and permissions.
   */
  defineAbility(user: User) {
    const { can, cannot, build } = new AbilityBuilder(
      PureAbility as AbilityClass<AppAbility>,
    );

    /**
     * Define CASL abilities based on the provided user's role and permissions.
     * @param user - The user object containing role and permissions information.
     * @param can - CASL's `can` function to grant permissions.
     * @param cannot - CASL's `cannot` function to deny permissions.
     * @param Action - Enum containing action types (e.g., CREATE, READ).
     * @param Subject - Enum containing subject types (e.g., USER, ROLE).
     * @param DefaultRoles - Enum containing default role names (e.g., ADMIN, SYSTEM_ADMIN).
     */

    if (user.role.name.toLowerCase() === DefaultRoles.ADMIN) {
      /**
       * Grants the ability to perform the 'MANAGE' action on all subjects.
       *
       * This method allows the user to manage (create, read, update, delete) any subject within the application.
       *
       */
      can(Action.MANAGE, Subject.ALL);
    } else if (user.role.name.toLowerCase() === DefaultRoles.SYSTEM_ADMIN) {
      /**
       * Grants/Revokes specific actions on designated subjects for user abilities.
       * - can([Action.CREATE, Action.READ, Action.UPDATE], Subject.ALL): Grants create, read, and update on all.
       * - cannot([Action.UPDATE], Subject.USER): Revokes update on USER.
       * - cannot([Action.CREATE, Action.READ, Action.UPDATE], Subject.ROLE): Revokes create, read, and update on ROLE.
       * - cannot(Action.DELETE, Subject.ALL): Revokes delete on all.
       */
      can([Action.CREATE, Action.READ, Action.UPDATE], Subject.ALL);
      cannot([Action.UPDATE], Subject.USER);
      cannot([Action.CREATE, Action.READ, Action.UPDATE], Subject.ROLE);
      cannot(Action.DELETE, Subject.ALL);
    } else if (user.role && user.role.permissions) {
      /**
       * Assigns specific actions to corresponding entities based on user permissions.
       *
       * Iterates over user's role permissions and grants permissions accordingly.
       *
       * Example:
       * If user has permissions like:
       * {
       *   'assets': ['read', 'create'],
       *   'users': ['read']
       * }
       *
       * The function will grant read and create actions on 'assets' and read action on 'users'.
       */

      const permissions = user.role.permissions;
      Object.entries(permissions).forEach(([entity, actions]) => {
        actions.forEach((action: Action) => {
          can(action, entity as Subject);
        });
      });
    }

    return build();
  }
}
