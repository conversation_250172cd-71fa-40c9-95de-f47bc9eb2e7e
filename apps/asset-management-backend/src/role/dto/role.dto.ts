import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { JsonValue } from 'types';

export type Permission = Record<Subject, Action[]>;

export class CreateRoleDto {
  @ApiProperty({
    description: 'role name',
    type: 'string',
    example: 'admin',
    required: false,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'permissions for particular role',
  })
  @IsNotEmpty()
  permissions: Permission;
}

export class GetRoleResponseDto {
  @ApiProperty({
    description: 'role Id',
    type: 'uuid',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'role name',
    type: 'string',
    example: 'admin',
    required: false,
  })
  name: string;

  @ApiProperty({
    description: 'permissions for particular role',
  })
  permissions: JsonValue;
}
