import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateRoleDto, GetRoleResponseDto } from './dto/role.dto';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { Prisma } from '@prisma-clients/asset-management-backend';
import {
  ROLE_NAME_EXIST,
  ROLE_NOT_FOUND,
  ROLE_PERMISSIONS_EXIST,
} from 'src/constants/message-constants';
import { deepCompare } from 'src/utility';

@Injectable()
export class RoleService {
  private logger = new Logger('RoleService');
  constructor(private readonly prisma: PrismaService) {}

  /**
   * Creates a new role based on the provided DTO.
   * Checks for the existence of a role with the given name and permissions.
   * If the role already exists or has conflicting permissions with an existing role, it throws a conflict exception.
   * @param dto -  The dto containing role information.
   * @returns -  The newly created role.
   * @throws {ConflictException} - If the role name or permissions conflict with existing roles.
   */
  async createRole(dto: CreateRoleDto): Promise<GetRoleResponseDto> {
    try {
      const { name, permissions } = dto;

      const existingRole = await this.prisma.role.findFirst({
        where: {
          name: {
            equals: name,
            mode: 'insensitive',
          },
          isDeleted: false,
        },
      });

      if (existingRole) {
        throw new ConflictException(ROLE_NAME_EXIST);
      }

      const existingRoles = await this.prisma.role.findMany({
        where: {
          isDeleted: false,
        },
      });
      for (const role of existingRoles) {
        if (deepCompare(dto.permissions, role.permissions)) {
          throw new ConflictException(ROLE_PERMISSIONS_EXIST);
        }
      }

      const createdRole = await this.prisma.role.create({
        data: {
          name,
          permissions,
        },
      });

      this.logger.log(`Created role with ID: ${createdRole.id}`);

      return createdRole;
    } catch (error) {
      this.logger.log(`Failed to create role: ${error}`);
      throw error;
    }
  }

  /**
   * Updates an existing role with the provided role ID and DTO.
   * @param roleId -  The ID of the role to update.
   * @param dto -  The dto containing updated role information.
   * @returns - The updated role
   * @throws {NotFoundException} - If the role with the given ID is not found.
   * @throws {ConflictException} - If the updated role name or permissions conflict with existing roles.
   */
  async updateRole(
    roleId: string,
    dto: CreateRoleDto,
  ): Promise<GetRoleResponseDto> {
    try {
      const { name, permissions } = dto;

      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          isDeleted: false,
        },
      });
      if (!role) {
        throw new NotFoundException(ROLE_NOT_FOUND);
      }

      const roleCount = await this.prisma.role.count({
        where: {
          NOT: [
            {
              name: {
                equals: role.name,
                mode: 'insensitive',
              },
            },
          ],
          OR: [
            {
              name: {
                equals: dto.name,
                mode: 'insensitive',
              },
            },
          ],
          isDeleted: false,
        },
      });

      if (roleCount) {
        throw new ConflictException(ROLE_NAME_EXIST);
      }

      const existingRoles = await this.prisma.role.findMany({
        where: {
          NOT: {
            id: roleId,
          },
          isDeleted: false,
        },
      });
      for (const role of existingRoles) {
        if (deepCompare(dto.permissions, role.permissions)) {
          throw new ConflictException(ROLE_PERMISSIONS_EXIST);
        }
      }

      const updatedRole = await this.prisma.role.update({
        where: {
          id: roleId,
        },
        data: {
          name,
          permissions,
        },
      });

      this.logger.log(`Updated role with ID: ${roleId}`);

      return updatedRole;
    } catch (error) {
      this.logger.log(`Failed to update role: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves a role by its unique id.
   * @param roleId - The ID of the role to retrieve.
   * @returns The retrieved role.
   * @throws {NotFoundException} - If the role with the given ID is not found.
   */
  async getRoleById(roleId: string): Promise<GetRoleResponseDto> {
    try {
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          isDeleted: false,
        },
      });
      if (!role) {
        throw new NotFoundException(ROLE_NOT_FOUND);
      }
      return role;
    } catch (error) {
      this.logger.log(`Failed to retrive role: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves a list of roles based on the provided query filters.
   * @param queryFilters - Optional query filters to apply.
   * @returns - A promise that resolves to an object containing the retrieved roles and the total count of roles.
   */
  async getAllRoles(queryFilters?: GetAllQueryParamsDto) {
    try {
      const page: number | null = queryFilters?.page ? queryFilters.page : null;
      const limit: number | undefined = queryFilters?.limit
        ? queryFilters.limit
        : undefined;
      const skip: number = page && limit ? (page - 1) * limit : 0;

      const orderBy = {
        [queryFilters?.sortBy || 'createdAt']:
          queryFilters?.sortOrder || 'desc',
      };

      const whereOptions: Prisma.RoleWhereInput = queryFilters?.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: queryFilters.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : undefined;

      const roles = await this.prisma.role.findMany({
        where: {
          ...whereOptions,
          isDeleted: false,
        },
        orderBy,
        take: limit,
        skip,
      });

      const count = await this.prisma.role.count({
        where: {
          ...whereOptions,
          isDeleted: false,
        },
      });
      return { roles, count };
    } catch (error) {
      this.logger.log(`Failed to retrive roles: ${error}`);
      throw error;
    }
  }

  /**
   * Deletes a role based on the provided role ID.
   * @param roleId - The ID of the role to be deleted.
   * @returns - A promise that resolves to `true` if the role was successfully deleted.
   * @throws {NotFoundException} - If the role with the given ID is not found.
   * @throws {ConflictException} - If the role is assigned to one or more users and cannot be deleted.
   */
  async deleteRole(roleId: string): Promise<boolean> {
    try {
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          isDeleted: false,
        },
      });
      if (!role) {
        throw new NotFoundException(ROLE_NOT_FOUND);
      }

      const assignedUsers = await this.prisma.user.findMany({
        where: {
          role: {
            User: {
              some: {
                roleId,
              },
            },
          },
        },
      });

      if (assignedUsers.length > 0) {
        throw new ConflictException(
          'Role is assigned to one or more users and cannot be deleted',
        );
      }
      await this.prisma.role.update({
        where: {
          id: roleId,
        },
        data: {
          isDeleted: true,
        },
      });

      this.logger.log(`Deleted role with ID: ${roleId}`);

      return true;
    } catch (error) {
      this.logger.log(`Failed to delete role: ${error}`);
      throw error;
    }
  }
}
