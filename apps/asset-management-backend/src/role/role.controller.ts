import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RoleService } from './role.service';
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { CreateRoleDto, GetRoleResponseDto } from './dto/role.dto';
import {
  INTERNAL_ERROR,
  ROLE_EXIST,
  ROLE_NOT_FOUND,
} from 'src/constants/message-constants';
import { PermissionGuard } from 'src/ability/ability.guard';
import { CheckPolicies } from 'src/decorators';
import { Action, Subject } from 'src/common/enums/ability.enum';
import { PolicyHandler } from 'src/ability/policy.handler';

@ApiTags('Role')
@Controller('roles')
@UseGuards(PermissionGuard)
export class RoleController {
  private logger = new Logger('RoleController');
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully created Role',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetRoleResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: ROLE_EXIST,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a new roles if not exists',
    summary: 'Create role',
  })
  @ApiExtraModels(HTTPResponseDto<GetRoleResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.CREATE, Subject.ROLE))
  async createRole(
    @Body() dto: CreateRoleDto,
  ): Promise<HTTPResponseDto<GetRoleResponseDto>> {
    this.logger.log('API to create role with permissions');
    const role = await this.roleService.createRole(dto);
    return {
      statusCode: HttpStatus.CREATED,
      data: role,
      message: 'role created successfully',
    };
  }

  @Put(':roleId')
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Successfully updated Role',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetRoleResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiConflictResponse({
    description: 'Conflict error',
    content: {
      'application/json': {
        examples: {
          example1: {
            value: {
              statusCode: HttpStatus.CONFLICT,
              message: ROLE_EXIST,
              error: 'Conflict',
            },
            summary: 'Example:Name',
          },
        },
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows update role',
    summary: 'Update role',
  })
  @ApiExtraModels(HTTPResponseDto<GetRoleResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.UPDATE, Subject.ROLE))
  async updateRole(
    @Param('roleId', new ParseUUIDPipe()) roleId: string,
    @Body() dto: CreateRoleDto,
  ): Promise<HTTPResponseDto<GetRoleResponseDto>> {
    this.logger.log('API to update role with permissions');
    const role = await this.roleService.updateRole(roleId, dto);
    return {
      statusCode: HttpStatus.OK,
      data: role,
      message: 'role updated successfully',
    };
  }

  @Get(':roleId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched role',
    schema: { $ref: getSchemaPath(HTTPResponseDto<GetRoleResponseDto>) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Role with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ROLE_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch role with specified Id',
    summary: 'Fetches role with given Id',
  })
  @ApiParam({
    name: 'roleId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter role ID',
  })
  @ApiExtraModels(HTTPResponseDto<GetRoleResponseDto>)
  @CheckPolicies(new PolicyHandler(Action.READ, Subject.ROLE))
  async getRoleById(@Param('roleId', new ParseUUIDPipe()) roleId: string) {
    this.logger.log('API to fetch role with specified Id');

    const role = await this.roleService.getRoleById(roleId);

    return {
      statusCode: HttpStatus.OK,
      data: role,
      message: 'Successfully fetched role',
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched Role/s',
    schema: { $ref: getSchemaPath(GetRoleResponseDto) },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all roles ',
    summary: 'Fetches all roles',
  })
  @ApiExtraModels(GetRoleResponseDto)
  @CheckPolicies(new PolicyHandler(Action.READ, [Subject.ROLE, Subject.USER]))
  async getAllRoles(
    @Query() queryFilters?: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetRoleResponseDto[]>> {
    this.logger.log('API to fetch all roles');
    const { roles, count } = await this.roleService.getAllRoles(queryFilters);

    return {
      statusCode: HttpStatus.OK,
      data: roles,
      count,
      message: 'Successfully fetched roles',
    };
  }

  @Delete(':roleId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Deleted role Successfully',
    schema: {
      example: true,
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Not found error',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: ROLE_NOT_FOUND,
        error: 'Not found',
      },
    },
  })
  @ApiBadRequestResponse({
    description: 'Bad request response error',
    schema: {
      example: {
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Validation failed (uuid is expected)',
        error: 'Bad request response',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to soft delete the role',
    summary: 'Delete role',
  })
  @ApiParam({
    name: 'roleId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter role Id to delete',
  })
  @CheckPolicies(new PolicyHandler(Action.DELETE, Subject.ROLE))
  async deleteRole(@Param('roleId', new ParseUUIDPipe()) roleId: string) {
    this.logger.log('API to delete role');

    const roleDeletedStatus = await this.roleService.deleteRole(roleId);

    return {
      statusCode: HttpStatus.OK,
      data: roleDeletedStatus,
      message: 'Role deleted successfully',
    };
  }
}
