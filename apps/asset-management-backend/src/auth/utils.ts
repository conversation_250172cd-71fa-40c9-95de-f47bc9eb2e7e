import { Logger, UnauthorizedException } from '@nestjs/common';
import { OAuth2Client, TokenPayload } from 'google-auth-library';

/**
 * Extracts the Bearer token from the Authorization header.
 *
 * @param authorization - The Authorization header containing the Bearer token.
 * @returns The Bearer token if present, otherwise `undefined`.
 */
export function getBearerToken(authorization: string): string | undefined {
  const [type, token] = authorization?.split(' ') ?? [];
  return type === 'Bearer' ? token : undefined;
}

/**
 * Retrieves the payload from the provided ID token using Google's OAuth2Client.
 *
 * @param idToken - The ID token to extract the payload from.
 * @returns A Promise resolving to the extracted TokenPayload.
 * @throws An UnauthorizedException if verification fails.
 */
export async function getPayloadFromIdToken(
  idToken: string,
): Promise<TokenPayload> {
  const logger = new Logger('getPayloadFromIdToken');

  try {
    // Create an OAuth2Client and verify the ID token
    const client = new OAuth2Client();
    const ticket = await client.verifyIdToken({
      idToken: idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    // Return the payload from the verified ID token
    return ticket.getPayload();
  } catch (error) {
    // Log the error and throw an UnauthorizedException
    logger.error(error.message);
    throw new UnauthorizedException();
  }
}
