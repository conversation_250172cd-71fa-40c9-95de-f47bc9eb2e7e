import { Injectable } from '@nestjs/common';
import { Credentials, OAuth2Client } from 'google-auth-library';

@Injectable()
export class AuthDevService {
  private readonly googleClient: OAuth2Client;

  constructor() {
    this.googleClient = new OAuth2Client({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      redirectUri: 'http://localhost:5000/api/v1/auth/dev/google/callback',
    });
  }

  getGoogleAuthUrl(): string {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email',
    ];
    const authUrl = this.googleClient.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
    });
    return authUrl;
  }

  async getGoogleUserTokens(code: string): Promise<Credentials> {
    const { tokens } = await this.googleClient.getToken(code);
    return tokens;
  }
}
