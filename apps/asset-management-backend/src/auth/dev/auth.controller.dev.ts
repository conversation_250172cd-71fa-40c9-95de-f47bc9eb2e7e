import { Controller, Get, Res, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response, Request } from 'express';
import { AuthDevService } from './auth.service.dev';
import { Public } from 'src/decorators';

/**
 * Controller for authentication-related development endpoints.
 *
 * @remarks
 * This controller is intended for development purposes only and contains endpoints
 * that facilitate authentication functionality in development only.
 * The code within this controller is not meant for production use and will be removed
 * in production environments.
 */
@ApiTags('Authentication Development')
@Public()
@Controller('auth/dev')
export class AuthDevController {
  constructor(private readonly authDevService: AuthDevService) {}

  @ApiOperation({ summary: 'Initiate Google login' })
  @ApiResponse({ status: 302, description: 'Redirects to Google login page' })
  @Get('google/login')
  async googleLogin(@Res() res: Response) {
    const authUrl = this.authDevService.getGoogleAuthUrl();
    return res.redirect(authUrl);
  }

  @ApiOperation({
    summary: 'Handle Google callback and exchange code for tokens',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns access token from the application',
  })
  @Get('google/callback')
  async googleCallback(@Req() req: Request) {
    // Extract authorization code from the query parameters
    const { code } = req.query as { code: string };

    // Use AuthDevService to get Google user tokens based on the received code
    const { id_token } = await this.authDevService.getGoogleUserTokens(code);

    // Set up headers with the obtained ID token for the subsequent fetch request
    const headers = new Headers();
    headers.set('Authorization', `Bearer ${id_token}`);

    // Perform a fetch request to obtain the access token from the application
    // Note: A redirect-based approach cannot be used here because the ID token, necessary for authentication,
    // is securely included in the HTTP headers. Redirects typically lose custom headers, making them unsuitable for this purpose.
    return fetch('http://localhost:5000/api/v1/auth/access-token', {
      headers,
    }).then((res) => res.json());
  }
}
