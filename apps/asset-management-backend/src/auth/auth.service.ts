import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { getPayloadFromIdToken } from './utils';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly prisma: PrismaService,
  ) {}

  async getAccessToken(idToken: string): Promise<string> {
    const payload = await getPayloadFromIdToken(idToken);
    const user = await this.prisma.user.findFirst({
      where: {
        email: payload.email,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: {
          select: {
            name: true,
            permissions: true,
          },
        },
      },
    });

    if (!user) {
      throw new UnauthorizedException();
    }

    return this.jwtService.signAsync(user);
  }
}
