import { Controller, Get, Headers, HttpStatus } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Public } from 'src/decorators';
import { getBearerToken } from './utils';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Get('access-token')
  @ApiExcludeEndpoint()
  async getAccessToken(@Headers('authorization') authorization: string) {
    const idToken = getBearerToken(authorization);
    const accessToken = await this.authService.getAccessToken(idToken);

    return {
      statusCode: HttpStatus.OK,
      message: 'user authorized successfully',
      accessToken,
    };
  }
}
