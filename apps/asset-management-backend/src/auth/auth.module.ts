import { DynamicModule, Module } from '@nestjs/common';
import { AuthGuard } from './auth.guard';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';

/**
 * Retrieves an array of module imports for authentication based on the current environment.
 *
 * @returns An array of dynamic modules for authentication.
 */
function getImports() {
  const defaultImports = [
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: {
        expiresIn: process.env.JWT_EXPIRY,
      },
    }),
  ];

  // Check if the environment is set to development
  if (process.env.NODE_ENV === 'development') {
    /**
     * Additional import for development environment.
     * @type {Promise<DynamicModule>}
     */
    const devImport = import('./dev/auth.module.dev');

    // Append the development import to the default imports
    return [
      ...defaultImports,
      devImport.then(
        (module) => module.AuthDevModule as unknown as DynamicModule,
      ),
    ];
  }

  // Return default imports for production environment
  return defaultImports;
}

@Module({
  imports: getImports(),
  providers: [
    Reflector,
    AuthService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
  controllers: [AuthController],
})
export class AuthModule {}
