import {
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  GetAllGlobalNotificationResponseDto,
  GetGlobalNotificationResponseDto,
  GlobalNotificationDto,
} from './dtos/global-notification-dto';
import { User } from 'types';
import {
  ChangesOcccuredIn,
  HistoryActions,
  Prisma,
} from '@prisma-clients/asset-management-backend';
import { checkErrorAndThrowNotFoundError, getUpdatedFields } from 'src/utility';
import {
  GLOBAL_NOTIFICATION_ALREADY_EXIST,
  GLOBAL_NOTIFICATION_NOT_FOUND,
} from 'src/constants/message-constants';
import { GetAllQueryParamsDto } from 'src/common/http/response.dto';
import { SORT_BY, SORT_ORDER } from 'src/common/enums/sort.enum';
import { GetEntityHistoryResponse } from 'src/common/dto/history-response.dto';

@Injectable()
export class GlobalNotificationService {
  constructor(private readonly prisma: PrismaService) {}
  private logger = new Logger('GlobalNotificationService');
  private selectArgs = {
    id: true,
    name: true,
    typeOfCategory: true,
    users: {
      select: {
        id: true,
        name: true,
        email: true,
      },
    },
    actions: true,
    isDeleted: true,
    additionalEmails: true,
  };

  async createNotification(
    dto: GlobalNotificationDto,
    user: User,
  ): Promise<GetGlobalNotificationResponseDto> {
    try {
      const existingNotification =
        await this.prisma.globalNotification.findFirst({
          where: {
            name: dto.name,
            isDeleted: false,
          },
        });
      if (existingNotification) {
        this.logger.error(
          'The global notification already exists with the provided name',
        );
        throw new ConflictException(GLOBAL_NOTIFICATION_ALREADY_EXIST);
      }
      const createdNotification = await this.prisma.globalNotification.create({
        data: {
          name: dto.name,
          typeOfCategory: dto.typeOfCategory,
          actions: dto.actions,
          users: dto.userIds
            ? {
                connect: dto.userIds?.map((id) => ({ id })),
              }
            : undefined,
          additionalEmails: dto.additionalEmails,
        },
        select: this.selectArgs,
      });
      this.logger.log('The global notification created successfully');
      await this.prisma.history.create({
        data: {
          action: HistoryActions.CREATED,
          date: new Date(),
          changeInTable: ChangesOcccuredIn.GLOBAL_NOTIFICATION,
          entityId: createdNotification.id,
          log: {
            userId: user.id,
            name: user.name,
            serviceId: createdNotification.id,
          },
        },
      });
      this.logger.log(
        'The history for global notification "create" created successfully',
      );
      return createdNotification;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(`Failed to create a service record: ${error.message}`);
      throw error;
    }
  }

  async deleteNotification(
    notificationId: string,
    user: User,
  ): Promise<boolean> {
    const prismaTransction = await this.prisma.$transaction(async (prisma) => {
      const notification = await prisma.globalNotification.findFirst({
        where: {
          id: notificationId,
          isDeleted: false,
        },
        select: this.selectArgs,
      });
      if (!notification) {
        this.logger.error(
          `The notification not found with ID:${notificationId}`,
        );
        throw new NotFoundException(GLOBAL_NOTIFICATION_NOT_FOUND);
      }
      await prisma.globalNotification.update({
        where: {
          id: notificationId,
        },
        data: {
          isDeleted: true,
        },
      });
      await prisma.history.create({
        data: {
          action: HistoryActions.DELETED,
          changeInTable: ChangesOcccuredIn.GLOBAL_NOTIFICATION,
          date: new Date(),
          entityId: notification.id,
          log: {
            userId: user.id,
            name: user.name,
            serviceId: notification.id,
          },
        },
      });
      return true;
    });
    return prismaTransction;
  }

  async updateNotification(
    dto: GlobalNotificationDto,
    notificationId: string,
    user: User,
  ): Promise<GetGlobalNotificationResponseDto> {
    const prismaTransction = await this.prisma.$transaction(async (prisma) => {
      const notification = await prisma.globalNotification.findFirst({
        where: {
          id: notificationId,
        },
        select: this.selectArgs,
      });
      if (!notification) {
        this.logger.error(
          `The notification not found with ID:${notificationId}`,
        );
        throw new NotFoundException(GLOBAL_NOTIFICATION_NOT_FOUND);
      }
      const existingNotification =
        await this.prisma.globalNotification.findFirst({
          where: {
            name: dto.name,
            isDeleted: false,
            NOT: {
              id: notificationId,
            },
          },
          select: this.selectArgs,
        });
      if (existingNotification) {
        this.logger.error(
          'The global notification already exists with the provided name',
        );
        throw new ConflictException(GLOBAL_NOTIFICATION_ALREADY_EXIST);
      }
      const updatedNotification = await prisma.globalNotification.update({
        where: {
          id: notificationId,
        },
        data: {
          name: dto.name,
          actions: dto.actions,
          typeOfCategory: dto.typeOfCategory,
          users: dto.userIds
            ? {
                set: dto.userIds?.map((id) => ({ id })),
              }
            : undefined,
          additionalEmails: dto.additionalEmails,
        },
        select: this.selectArgs,
      });
      this.logger.log(
        'The global notification updated successfully with the provided data',
      );
      await prisma.history.create({
        data: {
          action: HistoryActions.UPDATED,
          changeInTable: ChangesOcccuredIn.GLOBAL_NOTIFICATION,
          date: new Date(),
          entityId: updatedNotification.id,
          log: {
            userId: user.id,
            name: user.name,
            serviceId: updatedNotification.id,
            updatedFields: getUpdatedFields(notification, updatedNotification),
          },
        },
      });
      return updatedNotification;
    });
    return prismaTransction;
  }

  async getNotificationById(
    notificationId: string,
  ): Promise<GetGlobalNotificationResponseDto> {
    const notification = await this.prisma.globalNotification.findFirst({
      where: {
        id: notificationId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });
    if (!notification) {
      this.logger.error(`The notification not found with ID:${notificationId}`);
      throw new NotFoundException(GLOBAL_NOTIFICATION_NOT_FOUND);
    }
    return notification;
  }
  async getAllNotifications(
    dto: GetAllQueryParamsDto,
  ): Promise<GetAllGlobalNotificationResponseDto> {
    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };

    const whereOptions: Prisma.GlobalNotificationWhereInput = {
      isDeleted: false,
      ...(dto.searchInput
        ? {
            OR: [
              {
                name: {
                  contains: dto.searchInput,
                  mode: 'insensitive',
                },
              },
            ],
          }
        : undefined),
    };
    const [count, notifications] = await this.prisma.$transaction([
      this.prisma.globalNotification.count({
        where: whereOptions,
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.globalNotification.findMany({
        where: whereOptions,
        orderBy,
        take: limit,
        skip,
        select: this.selectArgs,
      }),
    ]);
    return {
      count,
      notifications,
    };
  }

  async getNotificationHistory(
    notificationId: string,
    dto: GetAllQueryParamsDto,
  ): Promise<GetEntityHistoryResponse> {
    const notification = await this.prisma.globalNotification.findFirst({
      where: {
        id: notificationId,
        isDeleted: false,
      },
      select: this.selectArgs,
    });
    if (!notification) {
      this.logger.error(`The notification not found with ID:${notificationId}`);
      throw new NotFoundException(GLOBAL_NOTIFICATION_NOT_FOUND);
    }

    const page: number | null = dto?.page ? dto.page : null;
    const limit: number | undefined = dto?.limit ? dto.limit : undefined;

    const skip: number = page && limit ? (page - 1) * limit : 0;

    const orderBy = {
      [dto?.sortBy || SORT_BY.CREATED_AT]: dto?.sortOrder || SORT_ORDER.DESC,
    };
    const [count, notificationHistory] = await this.prisma.$transaction([
      this.prisma.history.count({
        where: {
          entityId: notificationId,
        },
        orderBy,
        take: limit,
        skip,
      }),
      this.prisma.history.findMany({
        where: {
          entityId: notificationId,
        },
        orderBy,
        take: limit,
        skip,
      }),
    ]);
    return {
      count,
      history: notificationHistory,
    };
  }
}
