import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  Req,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiFoundResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { GlobalNotificationService } from './global-notification.service';
import {
  GetGlobalNotificationResponseDto,
  GlobalNotificationDto,
} from './dtos/global-notification-dto';
import { Request } from 'express';
import {
  GetAllQueryParamsDto,
  GetAllResponseDto,
  HTTPResponseDto,
} from 'src/common/http/response.dto';
import { HistoryResponsePayload } from 'src/common/dto/history-response.dto';
import {
  GLOBAL_NOTIFICATION_NOT_FOUND,
  INTERNAL_ERROR,
} from 'src/constants/message-constants';

@Controller('global-notification')
@ApiTags('Global-Notification')
export class GlobalNotificationController {
  constructor(
    private readonly notificationService: GlobalNotificationService,
  ) {}

  @Post()
  @ApiBearerAuth('access-token')
  @ApiCreatedResponse({
    description: 'Global notification created Successfully',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto),
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to create a global notification',
    summary: 'Create a global notification',
  })
  @ApiExtraModels(HTTPResponseDto<GetGlobalNotificationResponseDto>)
  async createNotification(
    @Body() dto: GlobalNotificationDto,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<GetGlobalNotificationResponseDto>> {
    const { user } = request;
    const createdNotification =
      await this.notificationService.createNotification(dto, user);
    return {
      message: 'Global notification created successfully',
      data: createdNotification,
      statusCode: HttpStatus.CREATED,
    };
  }

  @Delete('/:notificationId')
  @ApiBearerAuth('access-token')
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOkResponse({
    description: 'Successfully deleted a global notification',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetGlobalNotificationResponseDto>),
    },
  })
  @ApiParam({
    name: 'serviceId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter notificationId to delete global notification record',
  })
  @ApiNotFoundResponse({
    description: 'Global notification with specified Id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: GLOBAL_NOTIFICATION_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiOperation({
    description: 'This api allows to delete a global notification',
    summary: 'Delete a global notification',
  })
  @ApiExtraModels(HTTPResponseDto<boolean>)
  async deleteNotification(
    @Param('notificationId', new ParseUUIDPipe()) notificationId: string,
    @Req() request: Request,
  ): Promise<HTTPResponseDto<boolean>> {
    const { user } = request;
    const isDeleted = await this.notificationService.deleteNotification(
      notificationId,
      user,
    );
    return {
      message: 'Global notification deleted successfully',
      data: isDeleted,
      statusCode: HttpStatus.OK,
    };
  }

  @Put('/:notificationId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully updated a global notification',
    schema: {
      $ref: getSchemaPath(HTTPResponseDto<GetGlobalNotificationResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Global notification with id not found',
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: GLOBAL_NOTIFICATION_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to update the global notification details',
    summary: 'Update global notification',
  })
  @ApiParam({
    name: 'notificationId',
    type: 'string',
    example: '21ae2ed4-30a0-417b-9427-032cb208afe6',
    description: 'Enter notification Id to update',
  })
  async updateNotification(
    @Param('notificationId', new ParseUUIDPipe()) notificationId: string,
    @Req() request: Request,
    @Body() dto: GlobalNotificationDto,
  ): Promise<HTTPResponseDto<GetGlobalNotificationResponseDto>> {
    const { user } = request;
    const updatedNotification =
      await this.notificationService.updateNotification(
        dto,
        notificationId,
        user,
      );
    return {
      message: 'Global notification updated successfully',
      data: updatedNotification,
      statusCode: HttpStatus.OK,
    };
  }

  @Get('/:notificationId')
  @ApiOperation({
    summary: 'API to get the global notification details',
    description: 'API to get the global notification by providing service Id',
  })
  @ApiFoundResponse({
    description: 'Successfully fetched the global notification',
  })
  @ApiNotFoundResponse({
    description: GLOBAL_NOTIFICATION_NOT_FOUND,
    schema: {
      example: {
        statusCode: HttpStatus.NOT_FOUND,
        message: GLOBAL_NOTIFICATION_NOT_FOUND,
        error: 'Not Found',
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Something went wrong',
        error: 'Internal Server Error',
      },
    },
  })
  @ApiParam({
    description: 'Notification Id',
    name: 'NotificationId',
    required: true,
    example: '0334f4f3-48e6-42e6-9c5d-168c62bc7295',
  })
  @ApiBearerAuth('access-token')
  async getNotificationById(
    @Param('notificationId', new ParseUUIDPipe()) notificationId: string,
  ): Promise<HTTPResponseDto<GetGlobalNotificationResponseDto>> {
    const notification =
      await this.notificationService.getNotificationById(notificationId);
    return {
      message: 'Global notification fetched successfully',
      data: notification,
      statusCode: HttpStatus.FOUND,
    };
  }

  @Get()
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all global notifications',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetGlobalNotificationResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all global notifications',
    summary: 'Fetches all global notifications',
  })
  @ApiExtraModels(GetAllResponseDto<GetGlobalNotificationResponseDto>)
  async getAllNotifications(
    @Query() dto: GetAllQueryParamsDto,
  ): Promise<GetAllResponseDto<GetGlobalNotificationResponseDto[]>> {
    const data = await this.notificationService.getAllNotifications(dto);
    return {
      message: 'Fetched all global notifications',
      data: data.notifications,
      count: data.count,
      statusCode: HttpStatus.OK,
    };
  }

  @Get('/history/:notificationId')
  @ApiBearerAuth('access-token')
  @ApiOkResponse({
    description: 'Successfully fetched all global notification histories',
    schema: {
      $ref: getSchemaPath(GetAllResponseDto<GetGlobalNotificationResponseDto>),
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Invalid token',
    schema: {
      example: {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: 'Something went wrong',
    schema: {
      example: {
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: INTERNAL_ERROR,
        error: 'Internal Server Error',
      },
    },
  })
  @ApiOperation({
    description: 'This API allows to fetch all global notification history',
    summary: 'Fetches all global notification history',
  })
  async getNotificationHistory(
    @Query() dto: GetAllQueryParamsDto,
    @Param('notificationId', new ParseUUIDPipe()) notificationId: string,
  ): Promise<GetAllResponseDto<HistoryResponsePayload[]>> {
    const data = await this.notificationService.getNotificationHistory(
      notificationId,
      dto,
    );
    return {
      message: 'History fetched successfully',
      data: data.history,
      count: data.count,
      statusCode: HttpStatus.OK,
    };
  }
}
