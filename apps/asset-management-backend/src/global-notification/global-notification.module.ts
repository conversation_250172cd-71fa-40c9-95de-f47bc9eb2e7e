import { Module } from '@nestjs/common';
import { GlobalNotificationController } from './global-notification.controller';
import { GlobalNotificationService } from './global-notification.service';
import { PrismaService } from 'src/prisma/prisma.service';

@Module({
  controllers: [GlobalNotificationController],
  providers: [GlobalNotificationService, PrismaService],
})
export class GlobalNotificationModule {}
