import { ApiProperty } from '@nestjs/swagger';
import {
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

export class GlobalNotificationDto {
  @ApiProperty({
    description: 'Global notification name',
    type: 'string',
    example: 'Assignment alert',
    required: true,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Notify users data',
    required: true,
  })
  @IsUUID('4', { each: true, message: 'Each user ID must be a valid UUID' })
  userIds?: string[];

  @ApiProperty({
    description: 'The category type',
    enum: TypeOfCategory,
    example: TypeOfCategory.ACCESSORY,
    required: true,
  })
  @IsEnum(TypeOfCategory)
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'The type of action',
    enum: HistoryActions,
    example: HistoryActions.ASSIGNED,
  })
  @IsOptional()
  @IsEnum(HistoryActions, {
    each: true,
    message: 'Please provide the correct actions',
  })
  actions?: HistoryActions[];

  @ApiProperty({
    description: 'The additional emails',
    example: '<EMAIL>',
  })
  @IsOptional()
  additionalEmails?: string[];
}

export class UpdateGlobalNotificationDto extends GlobalNotificationDto {
  @ApiProperty({
    description: 'The global notification ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  @IsString()
  @IsUUID()
  id: string;
}

export class GetGlobalNotificationResponseDto {
  @ApiProperty({
    description: 'The global notification ID',
    type: 'string',
    example: '01eb719c-98e2-4b2f-ab04-3a313bff2d70',
  })
  id: string;

  @ApiProperty({
    description: 'The global notification name',
    type: 'string',
    example: 'Assign alert name',
  })
  name: string;

  @ApiProperty({
    description: 'The category type',
    enum: TypeOfCategory,
    example: TypeOfCategory.ACCESSORY,
  })
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'The type of action',
    enum: HistoryActions,
    example: HistoryActions.ASSIGNED,
  })
  actions: HistoryActions[];

  @ApiProperty({
    description: 'Whether the global notification was deleted or not',
    type: 'boolean',
    example: true,
  })
  isDeleted: boolean;

  @ApiProperty({
    description: 'The notified users data',
  })
  users: {
    id: string;
    name: string;
    email: string;
  }[];

  @ApiProperty({
    description: 'The additional notified users emails',
  })
  additionalEmails: string[];
}

export class GetAllGlobalNotificationResponseDto {
  @ApiProperty({
    description: 'The global notification list',
  })
  notifications: GetGlobalNotificationResponseDto[];

  @ApiProperty({
    description: 'The global notification list count',
    type: 'number',
    example: 2,
  })
  count: number;
}
