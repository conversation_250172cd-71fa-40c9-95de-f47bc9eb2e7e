import { ExecutionContext, createParamDecorator } from '@nestjs/common';
import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';
import { camelCaseToCapitalizedWords } from 'src/utility';

@ValidatorConstraint({ name: 'minMax', async: false })
export class MinMaxValidator implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = args.object[relatedPropertyName];
    const numericValue = +value;

    if (!relatedValue) return true;

    if (isNaN(numericValue)) {
      // If the value is not a number, assume it's a date
      const dateValue = new Date(value);
      const relatedDateValue = new Date(relatedValue);
      return dateValue < relatedDateValue;
    }

    // If the value is a number, perform numeric comparison
    return (
      !isNaN(relatedValue) &&
      !isNaN(numericValue) &&
      numericValue < relatedValue
    );
  }

  defaultMessage(args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;

    return `The ${camelCaseToCapitalizedWords(
      args.property,
    )} must be less than ${camelCaseToCapitalizedWords(relatedPropertyName)}`;
  }
}

export function MinMax(
  minPropertyName: string,
  validationOptions?: ValidationOptions,
) {
  return function (object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: minPropertyName,
      options: validationOptions,
      constraints: [propertyName],
      validator: MinMaxValidator,
    });
  };
}

// TODO:Needs to add swagger docs for custom decorators
export const QueryParam = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const req = context.switchToHttp().getRequest();
    return { ...req.query, ...req.params };
  },
);

// TODO:Needs to add swagger docs for custom decorators
export const BodyParam = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const req = context.switchToHttp().getRequest();
    return { ...req.body, ...req.params };
  },
);
