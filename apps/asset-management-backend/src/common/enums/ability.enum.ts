export enum Action {
  MANAGE = 'manage',
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
}

export enum Subject {
  DASHBOARD = 'dashboard',
  ASSET = 'assets',
  SOFTWARE_LICENSE = 'licenses',
  ACCESSORY = 'accessories',
  CONSUMABLE = 'consumables',
  APPLIANCE = 'appliances',
  ASSET_MODEL = 'assetsModel',
  SUPPLIER = 'suppliers',
  MANUFACTURER = 'manufacturers',
  STATUS = 'status',
  CATEGORY = 'categories',
  USER = 'users',
  ROLE = 'roles',
  INSURANCE = 'insurance',
  CUSTOM_FIELD = 'customFields',
  POLICY = 'policies',
  SERVICE = 'services',
  LOCATION = 'locations',
  DEPARTMENT = 'department',
  ALL = 'all',
}

export enum DefaultRoles {
  ADMIN = 'admin',
  SYSTEM_ADMIN = 'system admin',
  EMPLOYEE = 'employee',
}
