import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class EntityDto {
  @ApiProperty({
    description: 'Entity ID',
    type: 'uuid',
    example: '1a0b1f45-a181-496e-8870-a6ba9a39bf5f',
  })
  id: string;

  @ApiProperty({
    description: 'Entity Name',
    type: 'string',
    example: 'Microsoft Office',
  })
  name: string;
}

export class assignmentEntityDto extends EntityDto {
  @ApiProperty({
    description: 'Entity Name',
    type: 'string',
    example: 'Microsoft Office',
  })
  @IsString()
  assetName: string;
}
