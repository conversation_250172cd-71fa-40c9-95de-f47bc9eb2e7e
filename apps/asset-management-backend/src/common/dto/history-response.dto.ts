import { ApiProperty } from '@nestjs/swagger';
import {
  HistoryActions,
  TypeOfCategory,
} from '@prisma-clients/asset-management-backend';
import { JsonValue } from 'types';

export class HistoryResponsePayload {
  @ApiProperty({
    description: 'Action performed on the entity',
    enum: HistoryActions,
    example: HistoryActions.CREATED,
  })
  action: HistoryActions;

  @ApiProperty({
    description: 'Date of the action',
    type: 'date',
    example: '2021-01-01T00:00:00.000Z',
  })
  date: Date;

  @ApiProperty({
    description: 'Log of the action',
    type: 'object',
    example: {
      action: HistoryActions.CREATED,
      date: '2021-01-01T00:00:00.000Z',
      entity: TypeOfCategory.CONSUMABLE,
    },
  })
  log: JsonValue;
}

export class GetEntityHistoryResponse {
  @ApiProperty({
    description: 'History of the specified entity id',
    type: HistoryResponsePayload,
    isArray: true,
  })
  history: HistoryResponsePayload[];

  @ApiProperty({
    description: 'Total count of history',
    type: 'number',
    example: '10',
  })
  count: number;
}
