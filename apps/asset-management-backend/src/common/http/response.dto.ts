import { HttpStatus } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { SORT_BY, SORT_ORDER } from '../enums/sort.enum';
import { Type } from 'class-transformer';

export class HTTPResponseDto<T> {
  @ApiProperty({
    description: 'HTTP status code indicating the result of the operation.',
    type: 'number',
    example: 200,
  })
  statusCode: HttpStatus;

  @ApiProperty({
    description: 'The data payload of the response.',
  })
  data: T;

  @ApiProperty({
    description:
      'Descriptive message providing additional information about the response.',
    type: 'string',
    example: 'Successfully created/retrieved/updated/deleted/uploaded data',
  })
  message: string;
}

export class GetAllResponseDto<T> extends HTTPResponseDto<T> {
  @ApiProperty({
    description: 'Total number of data found',
    example: '10',
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  count: number;
}

export class GetAllQueryParamsDto {
  @ApiProperty({
    description: 'Search by name',
    example: 'Data',
    required: false,
  })
  @IsOptional()
  @IsString()
  searchInput: string;

  @ApiProperty({
    description: 'Page specified by user',
    type: 'number',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page: number;

  @ApiProperty({
    description: 'Data per page specified by user',
    type: 'number',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  limit: number;

  @ApiProperty({
    description: 'Sort based on fields',
    type: 'string',
    example: 'createdAt',
    required: false,
    enum: SORT_BY,
  })
  @IsOptional()
  @IsEnum(SORT_BY)
  sortBy: SORT_BY;

  @ApiProperty({
    description: 'Order based on ascending or descending',
    type: 'string',
    example: 'asc',
    required: false,
    enum: SORT_ORDER,
  })
  @IsOptional()
  @IsEnum(SORT_ORDER)
  sortOrder: SORT_ORDER;
}
