/**
 * Interface for history logs related to asset models.
 */
export interface AssetModelHistoryLogsInterface {
  /**
   * Unique identifier of the person associated with the history log entry.
   * For create and delete: ID of the performer.
   * For update: ID of the updater.
   */
  id: string;

  /**
   * Name of the person associated with the history log entry.
   * For create and delete: Name of the performer.
   * For update: Name of the updater.
   */
  name: string;

  /**
   * Unique identifier of the associated asset model.
   */
  assetModelId: string;

  /**
   * Optional. Updated fields with new values (for update operations).
   */
  updatedField?: Record<string, unknown>[];
}
