import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PrismaService } from 'src/prisma/prisma.service';
import { CronExpression } from '@nestjs/schedule';
import { AwsService } from 'src/aws/aws.service';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { differenceInDays } from 'date-fns';
import { getStartOfDay, getEndOfDay } from './cron_utils';
@Injectable()
export class CronService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}
  private readonly logger = new Logger(CronService.name);

  /**
   * Generate a date with a specific offset from the current date.
   * @param {number} offsetInDays - The number of days to offset from the current date.
   * @returns {Date} - The generated offset date.
   */
  generateOffsetDate(offsetInDays: number): Date {
    const currentDate = new Date();
    const offsetDate = new Date(
      currentDate.getTime() + offsetInDays * 24 * 60 * 60 * 1000,
    );
    return offsetDate;
  }

  /**
   * Cron job to send email alerts for expiring insurance policies.
   * Triggered every day at 11 AM.
   */
  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async insuranceExpiryAlert() {
    const endDate = this.generateOffsetDate(7);
    endDate.setUTCHours(0, 0, 0, 0);
    const insurances = await this.prisma.insurance.findMany({
      where: {
        endDate: endDate,
        isDeleted: false,
      },
      select: {
        id: true,
        name: true,
        endDate: true,
        company: {
          select: {
            name: true,
          },
        },
        vendor: {
          select: {
            name: true,
          },
        },
      },
    });
    if (insurances.length !== 0) {
      this.awsService.insuranceExpiryEmail(insurances);
    }
  }

  /**
   * Sends acknowledgment reminder emails once in a week for pending assignments.
   */
  @Cron(CronExpression.EVERY_DAY_AT_11AM)
  async sendAcknowledgeReminderEmail() {
    const assignments = await this.prisma.assignment.findMany({
      where: {
        isPending: true,
        typeOfAssignment: TypeOfCategory.ASSET,
      },
      include: {
        user: true,
      },
    });
    const currentDate = new Date();
    if (assignments?.length > 0) {
      assignments.map(async (assignment) => {
        const daysDifference = differenceInDays(currentDate, assignment.date);
        if (daysDifference % 7 == 0) {
          await this.awsService.acknowledgmentReminderEmail(assignment);
          this.logger.log('The reminder email sent successfully');
        }
      });
    }
    return;
  }

  /**
   * Notify user 7 days before the evaluation frequency of supplier
   */
  @Cron(CronExpression.EVERY_DAY_AT_11AM)
  async notifyUserToEvaluateSupplier(): Promise<void> {
    const suppliers = await this.prisma.supplier.findMany({
      where: {
        isDeleted: false,
        OR: [
          {
            evaluationFrequency: 'ANNUALLY',
            OR: [
              {
                createdAt: {
                  gte: new Date(getStartOfDay(353)), // 7 days left to full yr
                  lte: new Date(getEndOfDay(353)),
                },
              },
              {
                VendorEvaluation: {
                  some: {
                    createdAt: {
                      gte: new Date(getStartOfDay(353)), // 7 days left to full yr
                      lte: new Date(getEndOfDay(353)),
                    },
                  },
                },
              },
            ],
          },
          {
            evaluationFrequency: 'HALF_YEARLY',
            OR: [
              {
                createdAt: {
                  gte: new Date(getStartOfDay(173)), // 7 days left half yr
                  lte: new Date(getEndOfDay(173)),
                },
              },
              {
                VendorEvaluation: {
                  some: {
                    createdAt: {
                      gte: new Date(getStartOfDay(173)), // 7 days left half yr
                      lte: new Date(getEndOfDay(173)),
                    },
                  },
                },
              },
            ],
          },
          {
            evaluationFrequency: 'QUARTERLY',
            OR: [
              {
                createdAt: {
                  gte: new Date(getStartOfDay(83)), // 7 days left to quarter yr
                  lte: new Date(getEndOfDay(83)),
                },
              },
              {
                VendorEvaluation: {
                  some: {
                    createdAt: {
                      gte: new Date(getStartOfDay(83)), // 7 days left to quarter yr
                      lte: new Date(getEndOfDay(83)),
                    },
                  },
                },
              },
            ],
          },
        ],
      },
      select: {
        name: true,
        location: {
          select: {
            id: true,
            name: true,
          },
        },
        notifyTo: true,
        evaluationFrequency: true,
      },
    });

    suppliers.forEach(async (supplier) => {
      if (supplier.notifyTo?.length) {
        /** finding users emails to notify */
        const users = await this.prisma.user.findMany({
          where: {
            id: {
              in: supplier.notifyTo,
            },
          },
          select: {
            id: true,
            name: true,
            email: true,
          },
        });

        /** triggering email to each user for a supplier */
        this.awsService.notifyUserForSupplierEvaluation({
          users,
          supplierName: supplier.name,
          supplierLocation: supplier.location.map((loc) => loc.name),
          evaluationFreq: supplier.evaluationFrequency,
        });
        this.logger.log(
          'Email is sent to notify user. Where users are: ',
          supplier.notifyTo.map((userId) => userId).join(', '),
        );
      }
    });
  }
}
