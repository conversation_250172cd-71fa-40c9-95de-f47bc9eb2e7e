/* eslint-disable prettier/prettier */

export const getStartOfDay = (daysAgo: number): Date => {
  const currentDate = new Date();

  return new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    currentDate.getDate() - daysAgo,
    0, // hours
    0, // minutes
    0, // seconds
    0, // milliseconds
  );
};

export const getEndOfDay = (daysAgo: number): Date => {
  const currentDate = new Date();

  return new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    currentDate.getDate() - daysAgo,
    23, // hours
    59, // minutes
    59, // seconds
    999, // milliseconds
  );
};
