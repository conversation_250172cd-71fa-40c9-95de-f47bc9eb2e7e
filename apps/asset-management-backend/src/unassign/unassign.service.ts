import { Injectable, Logger } from '@nestjs/common';
import { Prisma } from '@prisma-clients/asset-management-backend';
import { PrismaService } from 'src/prisma/prisma.service';
import { checkErrorAndThrowNotFoundError } from 'src/utility';
import { UnassignmentCreateDto } from './dtos/unassignment-dto';
import { User } from 'types';
import { AwsService } from 'src/aws/aws.service';

@Injectable()
export class UnassignService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly awsService: AwsService,
  ) {}
  private logger = new Logger('UnassignService');
  async createUnassignmentRecord(
    unassignmentDto: UnassignmentCreateDto,
    user: User,
  ): Promise<boolean> {
    try {
      const notifyUserIds =
        unassignmentDto.notifyUser && unassignmentDto.notifyUser.length > 0
          ? unassignmentDto.notifyUser.map((user) => ({ id: user.id }))
          : undefined;

      const unassignedDetails = await this.prisma.unAssignedDetails.create({
        data: {
          assignedDate: unassignmentDto.assignedDate,
          user: user.id ? { connect: { id: user.id } } : undefined,
          assignedNote: unassignmentDto.assignedNote,
          unAssignedNote: unassignmentDto.unAssignedNote,
          assignedUser: unassignmentDto.assignedUser,
          notifyUser: {
            connect: notifyUserIds,
          },
          entityId: unassignmentDto.entityId,
          typeOfCategory: unassignmentDto.typeOfCategory,
          unAssignedDate: new Date(),
        },
        select: {
          notifyUser: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          typeOfCategory: true,
          entityId: true,
          assignedUser: true,
          assignedDate: true,
          unAssignedDate: true,
          unAssignedNote: true,
        },
      });

      // Prepare the emails to notify
      if (
        unassignmentDto.assignedUserEmail ||
        (notifyUserIds && notifyUserIds.length > 0)
      ) {
        const emailsToNotify = unassignmentDto.assignedUserEmail
          ? [unassignmentDto.assignedUserEmail]
          : unassignedDetails.notifyUser.map((user) => user.email);

        // Send an email to each email address in emailsToNotify
        for (const email of emailsToNotify) {
          await this.awsService.unassignmentNotifyEmail(
            unassignedDetails,
            email,
          );
        }

        this.logger.log('The mail sent successfully to mentioned notify users');
      }

      return true;
    } catch (error) {
      if (
        error instanceof Prisma.PrismaClientKnownRequestError &&
        error?.code === 'P2025'
      ) {
        checkErrorAndThrowNotFoundError(error);
      }
      this.logger.error(
        `Failed to create unassignment record: ${error.message}`,
      );
      throw error;
    }
  }
}
