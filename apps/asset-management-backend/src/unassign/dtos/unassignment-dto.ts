import { ApiProperty } from '@nestjs/swagger';
import { TypeOfCategory } from '@prisma-clients/asset-management-backend';
import { IsDateString, IsOptional, IsString, IsUUID } from 'class-validator';
import { User } from 'types';

export class UnassignmentCreateDto {
  @ApiProperty({
    description: 'The date and time when the asset was assigned',
    type: 'Date',
    example: '2024-03-14T10:00:01.199Z',
  })
  @IsOptional()
  @IsDateString()
  assignedDate: Date;

  @ApiProperty({
    description:
      'Additional information or notes regarding the assignment of the asset',
    type: 'string',
    example: 'This Asset is Currently Assigned',
  })
  @IsOptional()
  @IsString()
  assignedNote: string;

  @ApiProperty({
    description:
      'The username of the user to whom the asset is currently assigned',
    type: 'string',
    example: 'john_doe',
  })
  @IsOptional()
  @IsString()
  assignedUser: string;

  @ApiProperty({
    description: 'The unassigned user',
  })
  @IsOptional()
  user: User;

  @ApiProperty({
    description:
      'Additional information or notes regarding the unassignment of the asset',
    type: 'string',
    example: [
      'Asset returned to warehouse',
      'Transferred to another department',
    ],
  })
  @IsOptional()
  @IsString()
  unAssignedNote: string;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  notifyUser: { id: string }[];

  @ApiProperty({
    description: 'The unassign entity ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  @IsUUID()
  entityId: string;

  @ApiProperty({
    description: 'The unassign entity ID',
    enum: TypeOfCategory,
    example: 'ASSET',
  })
  @IsOptional()
  typeOfCategory: TypeOfCategory;

  @ApiProperty({
    description: 'The unassigned user email',
    type: 'string',
    example: '<EMAIL>',
  })
  @IsOptional()
  assignedUserEmail: string;
}

export class UnassignmentRequestDto {
  @ApiProperty({
    description: 'The unassign note',
    type: 'string',
    example: 'The asset has been unassigned',
  })
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'The notify user ID',
    type: 'string',
    example: '0fd1053c-6a07-4fe2-a661-1de4c97b5e48',
  })
  @IsOptional()
  notifyUser: { id: string }[];
}
