import { Module } from '@nestjs/common';
import { AppService } from './app.service';
import { ManufacturerModule } from './manufacturer/manufacturer.module';
import { AuthModule } from './auth/auth.module';
import { ApplianceModule } from './appliance/appliance.module';
import { StatusModule } from './status/status.module';
import { CategoryModule } from './category/category.module';
import { ConsumablesModule } from './consumables/consumables.module';
import { AccessoryModule } from './accessory/accessory.module';
import { AssetModule } from './asset/asset.module';
import { SupplierModule } from './supplier/supplier.module';
import { SoftwareLicenseModule } from './software-license/software-license.module';
import { AssignmentModule } from './assignment/assignment.module';
import { AssetModelModule } from './asset-model/asset-model.module';
import { PurchaseModule } from './purchase/purchase.module';
import { UsersModule } from './users/users.module';
import { DepartmentModule } from './department/department.module';
import { AuditModule } from './audit/audit.module';
import { AwsModule } from './aws/aws.module';
import { PrismaModule } from './prisma/prisma.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { VendorEvaluationModule } from './vendor-evaluation/vendor-evaluation.module';
import { ScheduleModule } from '@nestjs/schedule';
import { NotificationService } from './notification/notification.service';
import { DocumentModule } from './document/document.module';
import { NotificationModule } from './notification/notification.module';
import { InsuranceModule } from './insurance/insurance.module';
import { AppController } from './app.controller';
import { CronService } from './cron/cron.service';
import { CustomFieldModule } from './custom-field/custom-field.module';
import { AbilityModule } from './ability/ability.module';
import { RoleModule } from './role/role.module';
import { FieldGroupModule } from './field-group/field-group.module';
import { PoliciesModule } from './policies/policies.module';
import { ContractsModule } from './contracts/contracts.module';
import { AssetImportModule } from './asset-import/asset-import.module';
import { ReminderModule } from './reminder/reminder.module';
import { UnassignModule } from './unassign/unassign.module';
import { ServiceModule } from './service/service.module';
import { GlobalNotificationModule } from './global-notification/global-notification.module';
import { TransferModule } from './transfer/transfer.module';
import { LocationModule } from './location/location.module';

@Module({
  imports: [
    AuthModule,
    StatusModule,
    SupplierModule,
    ApplianceModule,
    ManufacturerModule,
    AssetModule,
    CategoryModule,
    ConsumablesModule,
    AccessoryModule,
    AssetModelModule,
    CategoryModule,
    SoftwareLicenseModule,
    AssignmentModule,
    PurchaseModule,
    UsersModule,
    DepartmentModule,
    AuditModule,
    AwsModule,
    PrismaModule,
    DashboardModule,
    VendorEvaluationModule,
    ScheduleModule.forRoot(),
    DocumentModule,
    NotificationModule,
    InsuranceModule,
    CustomFieldModule,
    AbilityModule,
    RoleModule,
    FieldGroupModule,
    PoliciesModule,
    ContractsModule,
    AssetImportModule,
    ReminderModule,
    UnassignModule,
    ServiceModule,
    GlobalNotificationModule,
    TransferModule,
    LocationModule,
  ],
  controllers: [AppController],
  providers: [AppService, NotificationService, CronService],
})
export class AppModule {}
