import { PrismaClient as AMPrismaClient } from '@prisma-clients/asset-management-backend';
import { Logger } from '@nestjs/common';

export async function migratePolicyLocations() {
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('MigratePolicyLocations');
  try {
    logger.log('Migration of policy locations started...');
    // Fetch all policies with the old single location field
    const policies = await amPrisma.policy.findMany({
      where: {
        location: {
          NOT: null,
        },
      },
    });

    // Update each policy to set locations array and remove the single location field
    const migrationPromises = policies.map((policy) =>
      amPrisma.policy.update({
        where: { id: policy.id },
        data: {
          locations: {
            connect: policy.locationId ? { id: policy.locationId } : undefined,
          },
          location: null,
        },
      }),
    );

    // Wait for all updates to complete
    await Promise.all(migrationPromises);

    logger.log('Migration of policy locations completed successfully');
  } catch (error) {
    logger.error('Migration of policy locations failed:', error);
  } finally {
    await amPrisma.$disconnect();
  }
}

migratePolicyLocations();
