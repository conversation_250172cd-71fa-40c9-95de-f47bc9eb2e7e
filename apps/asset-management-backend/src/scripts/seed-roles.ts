import { PrismaClient as AMPrismaClient } from '@prisma-clients/asset-management-backend';
import { Logger } from '@nestjs/common';

export async function seedRoles() {
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('SeedRoles');
  try {
    logger.log('Started to seeding roles...');
    await amPrisma.role.createMany({
      data: [
        {
          name: 'admin',
          permissions: {
            roles: ['read', 'update', 'delete', 'create'],
            users: ['read', 'create', 'update', 'delete'],
            assets: ['read', 'create', 'update', 'delete'],
            status: ['read', 'create', 'update', 'delete'],
            licenses: ['read', 'create', 'update', 'delete'],
            dashboard: ['read'],
            insurance: ['read', 'create', 'update', 'delete'],
            suppliers: ['create', 'update', 'read', 'delete'],
            appliances: ['read', 'create', 'update', 'delete'],
            categories: ['read', 'create', 'update', 'delete'],
            accessories: ['read', 'create', 'update', 'delete'],
            assetsModel: ['read', 'create', 'update', 'delete'],
            consumables: ['read', 'create', 'update', 'delete'],
            manufacturers: ['read', 'create', 'update', 'delete'],
            customFields: ['read', 'create', 'update', 'delete'],
            department: ['read', 'create', 'delete'],
          },
        },
        {
          name: 'system admin',
          permissions: {
            users: ['read'],
            assets: ['read', 'create', 'update'],
            status: ['read', 'create', 'update'],
            licenses: ['read', 'create', 'update'],
            dashboard: ['read'],
            insurance: ['read', 'create', 'update'],
            suppliers: ['create', 'update', 'read'],
            appliances: ['read', 'create', 'update'],
            categories: ['read', 'create', 'update'],
            accessories: ['read', 'create', 'update'],
            assetsModel: ['read', 'create', 'update'],
            consumables: ['read', 'create', 'update'],
            manufacturers: ['read', 'create', 'update'],
            customFields: ['read', 'create', 'update'],
            department: ['read'],
          },
        },
        {
          name: 'employee',
          permissions: {},
        },
      ],
    });
    logger.log('Roles seeding completed');
  } catch (error) {
    logger.error(error);
  } finally {
    await amPrisma.$disconnect();
  }
}

seedRoles();
