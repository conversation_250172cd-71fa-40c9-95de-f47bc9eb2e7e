import { PrismaClient as AMPrismaClient } from '@prisma-clients/asset-management-backend';
import { Logger } from '@nestjs/common';

export async function updateAdminRoles() {
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('UpdateRoles');
  try {
    logger.log('Started to update admin roles...');
    await amPrisma.role.updateMany({
      where: {
        name: 'admin',
      },
      data: {
        permissions: {
          roles: ['read', 'update', 'delete', 'create'],
          users: ['read', 'create', 'update', 'delete'],
          assets: ['read', 'create', 'update', 'delete'],
          status: ['read', 'create', 'update', 'delete'],
          licenses: ['read', 'create', 'update', 'delete'],
          dashboard: ['read'],
          suppliers: ['create', 'update', 'read', 'delete'],
          appliances: ['read', 'create', 'update', 'delete'],
          categories: ['read', 'create', 'update', 'delete'],
          accessories: ['read', 'create', 'update', 'delete'],
          assetsModel: ['read', 'create', 'update', 'delete'],
          consumables: ['read', 'create', 'update', 'delete'],
          manufacturers: ['read', 'create', 'update', 'delete'],
          customFields: ['read', 'create', 'update', 'delete'],
          services: ['read', 'create', 'update', 'delete'],
          policies: ['read', 'create', 'update', 'delete'],
          globalNotifications: ['read', 'create', 'update', 'delete'],
          locations: ['read', 'create', 'update', 'delete'],
          department: ['read', 'create', 'delete'],
        },
      },
    });
    logger.log('Admin Roles updated successfully');
  } catch (error) {
    logger.error(error);
  } finally {
    await amPrisma.$disconnect();
  }
}
export async function updateSystemAdminRoles() {
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('UpdateRoles');
  try {
    logger.log('Started to update system admin roles...');
    await amPrisma.role.updateMany({
      where: {
        name: 'system admin',
      },
      data: {
        permissions: {
          users: ['read'],
          assets: ['read', 'create', 'update'],
          status: ['read', 'create', 'update'],
          licenses: ['read', 'create', 'update'],
          dashboard: ['read'],
          suppliers: ['create', 'update', 'read'],
          appliances: ['read', 'create', 'update'],
          categories: ['read', 'create', 'update'],
          accessories: ['read', 'create', 'update'],
          assetsModel: ['read', 'create', 'update'],
          consumables: ['read', 'create', 'update'],
          manufacturers: ['read', 'create', 'update'],
          customFields: ['read', 'create', 'update'],
          services: ['read', 'create', 'update'],
          policies: ['read', 'create', 'update'],
          locations: ['read', 'create', 'update'],
          department: ['read'],
        },
      },
    });
    logger.log('System admin roles updated successfully');
  } catch (error) {
    logger.error(error);
  } finally {
    await amPrisma.$disconnect();
  }
}
updateAdminRoles();
updateSystemAdminRoles();
