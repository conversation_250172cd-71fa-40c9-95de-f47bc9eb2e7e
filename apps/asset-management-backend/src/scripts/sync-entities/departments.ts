import { Logger } from '@nestjs/common';
import { Department } from '@prisma-clients/asset-management-backend';

export async function syncDepartments(tsPrisma, amPrisma, logger: Logger) {
  logger.log('Start syncing departments...');

  const tsDepartments = await tsPrisma.department.findMany({
    where: {
      deleted: false,
    },
    select: {
      departmentName: true,
      id: true,
      resource: {
        select: {
          kekaId: true,
        },
      },
    },
  });

  logger.log(
    `TS departments fetched successfully. Departments count: ${tsDepartments.length}`,
  );

  const departmentSyncPromises = tsDepartments.map(async (tsDepartment) => {
    const existingDepartment = await amPrisma.department.findFirst({
      where: { id: tsDepartment.id },
    });
    let department: Department;
    if (existingDepartment) {
      department = await amPrisma.department.update({
        where: { id: existingDepartment.id },
        data: {
          name: tsDepartment.departmentName,
        },
      });
    } else {
      department = await amPrisma.department.create({
        data: {
          id: tsDepartment.id,
          name: tsDepartment.departmentName,
        },
      });
    }

    return department;
  });

  await Promise.all(departmentSyncPromises);
  logger.log('Departments synced');
}
