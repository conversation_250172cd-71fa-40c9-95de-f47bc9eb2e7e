import { PrismaClient as TSPrismaClient } from '@prisma-clients/timesheet-backend';
import { PrismaClient as AMPrismaClient } from '@prisma-clients/asset-management-backend';
import { syncDepartments } from './departments';
import { syncUsers } from './users';
import { Logger } from '@nestjs/common';

export async function syncEntities() {
  const tsPrisma = new TSPrismaClient();
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('SyncEntities');

  try {
    logger.log('Started to sync entities...');
    await syncDepartments(tsPrisma, amPrisma, logger);
    await syncUsers(tsPrisma, amPrisma, logger);
    logger.log('Synced entities successfully');
  } finally {
    await tsPrisma.$disconnect();
    await amPrisma.$disconnect();
  }
}

async function main(command: string) {
  const tsPrisma = new TSPrismaClient();
  const amPrisma = new AMPrismaClient();
  const logger = new Logger('SyncEntities');

  try {
    if (command === 'all') {
      await syncEntities();
      return;
    }

    if (command === 'departments') {
      await syncDepartments(tsPrisma, amPrisma, logger);
      return;
    }

    if (command === 'users') {
      await syncUsers(tsPrisma, amPrisma, logger);
      return;
    }
  } catch (error) {
    logger.error(error);
  } finally {
    await tsPrisma.$disconnect();
    await amPrisma.$disconnect();
    process.exit(1);
  }
}

const commands = ['all', 'departments', 'users'];

const command = process.argv[2];

if (command) {
  if (commands.includes(command)) {
    main(command);
  } else {
    console.error(`Invalid command: ${command}`);
  }
}
