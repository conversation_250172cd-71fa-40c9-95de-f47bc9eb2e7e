import { Logger } from '@nestjs/common';

export async function syncUsers(tsPrisma, amPrisma, logger: Logger) {
  logger.log('Start syncing users...');

  const tsUsers = await tsPrisma.resource.findMany({
    select: {
      name: true,
      kekaId: true,
      email: true,
      phoneNumber: true,
      canAccessAssetManagement: true,
      departmentId: true,
    },
  });

  logger.log(`TS users fetched successfully. Users count: ${tsUsers.length}`);

  const employeeRole = await amPrisma.role.findFirst({
    where: {
      name: {
        equals: 'employee',
        mode: 'insensitive',
      },
      isDeleted: false,
    },
    select: {
      id: true,
    },
  });

  const adminRole = await amPrisma.role.findFirst({
    where: {
      name: {
        equals: 'admin',
        mode: 'insensitive',
      },
      isDeleted: false,
    },
    select: {
      id: true,
    },
  });

  const usersSyncPromises = tsUsers.map(async (tsUser) => {
    const existingUser = await amPrisma.user.findFirst({
      where: { employeeId: tsUser.kekaId },
    });

    let roleId = '';

    if (
      tsUser.kekaId.toLowerCase() === 'cc1' ||
      tsUser.kekaId.toLowerCase() === 'cc2'
    ) {
      roleId = adminRole.id;
    } else {
      roleId = employeeRole.id;
    }

    if (existingUser) {
      await amPrisma.user.update({
        where: { id: existingUser.id },
        data: {
          name: tsUser.name,
          email: tsUser.email,
          phoneNumber: tsUser.phoneNumber,
          employeeId: tsUser.kekaId,
          ...(existingUser.roleId ? {} : { roleId }),
          departmentId: tsUser.departmentId,
        },
      });
    } else {
      await amPrisma.user.create({
        data: {
          name: tsUser.name,
          email: tsUser.email,
          phoneNumber: tsUser.phoneNumber,
          employeeId: tsUser.kekaId,
          roleId,
          departmentId: tsUser.departmentId,
        },
      });
    }
  });

  await Promise.all(usersSyncPromises);
  logger.log('Users synced');
}
