{"name": "asset-management-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "3.485.0", "@aws-sdk/client-sesv2": "3.511.0", "@aws-sdk/s3-request-presigner": "3.485.0", "@casl/ability": "6.7.0", "@casl/prisma": "1.4.1", "@email-templates/asset-management": "workspace:*", "@nestjs/common": "9.0.0", "@nestjs/core": "9.0.0", "@nestjs/jwt": "10.0.2", "@nestjs/platform-express": "9.0.0", "@nestjs/schedule": "3.0.1", "@nestjs/swagger": "6.3.0", "@prisma-clients/asset-management-backend": "workspace:*", "@prisma-clients/timesheet-backend": "workspace:*", "class-transformer": "0.5.1", "class-validator": "0.14.0", "csv-parser": "3.0.0", "date-fns": "2.29.3", "dotenv": "16.0.3", "fast-csv": "5.0.2", "google-auth-library": "9.4.1", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.2.0", "xlsx": "0.18.5", "zod": "3.21.4"}, "devDependencies": {"@nestjs/cli": "9.2.0", "@nestjs/schematics": "9.0.0", "@nestjs/testing": "9.0.0", "@types/express": "4.17.13", "@types/jest": "29.2.4", "@types/multer": "1.4.7", "@types/node": "18.11.18", "@types/supertest": "2.0.11", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "8.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "5.0.0", "jest": "29.3.1", "source-map-support": "0.5.20", "supertest": "6.1.3", "ts-jest": "29.0.3", "ts-loader": "9.2.3", "ts-node": "10.0.0", "tsconfig-paths": "4.1.1", "typescript": "4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)": "<rootDir>/../src/$1"}}}