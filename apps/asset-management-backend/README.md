# Guidelines for setting up the project

## Prerequisites

- Docker Desktop

- PostgreSQL should not be installed in your system

## Changes need to be made

- Rename .env.example -> .env

- Run the following command in this project

  `docker-compose up -d`

- Once this command succeeds Uncomment the code which you commented in [docker-compose.yml](docker-compose.yml)

- Check for the status of containers in Docker Desktop. If both the containers are running, then in your browser of your choice, go to [localhost:5050](http://localhost:5050)

- Once the pgAdmin is running, contact the [Lead backend developer of this project](https://gitlab.codecrafttech.com/azhan.khan)

## How to create a module

- Run the following command inside src
  `nest g res {module-name}`
