#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# while read -r file; do
#   # Extract package name from the file path
#   package_name=$(echo "$file" | awk -F/ '{print $2}')
  
#   # Check if the file is in 'packages' or 'apps' directory
#   if [[ "$file" == packages/* || "$file" == apps/* ]]; then
#     # Check if the commit message starts with the package name
#     grep -q "^$package_name: " ${1} || {
#       echo "error: Commit message must start with '$package_name: ' for changes in '$package_name' package. Ex: '$package_name: commit message'" >&2
#       exit 1
#     }
#   fi
# done < <(git diff --cached --name-only)



npm run commitlint ${1}

exit 0